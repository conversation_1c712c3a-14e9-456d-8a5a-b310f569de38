HELP.md
.gradle
/.kotlin/
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

### STS ###
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

### IntelliJ IDEA ###
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/
/**/http-requests/outputs/
/**/http-requests/http-client.private.env.json


### NetBeans ###
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

### VS Code ###
.vscode/

.DS_Store
/.envrc
.env

### generated code ###
/Dockerfile
/**/generated/
/**/*generated*/
/employee-client/api/
/hcm-p08-damage-report-adapter/api/
/legal-hold-in-adapter/api/
/outbox-message-relay/api/
/pace-p40-internal-order-number-client/http-requests/http-client.private.env.json
/pvcc-p40-monetary-benefit-adapter/api/
/shared/api/
/**/gradle.lockfile
/**/settings-gradle.lockfile
/security-scan/
/vehicle-migration-data-in-adapter/api/

### terraform ###
.terraform.lock.hcl
.terraform


### draw.io ###
*.drawio.bkp
