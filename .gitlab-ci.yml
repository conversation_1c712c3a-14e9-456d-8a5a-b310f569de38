stages:
  - lint
  - build
  - test
  - init
  - push
  - deploy
  - release
  - prod

include:
  - project: "porsche/builddeploy-templates"
    file: .gitlab-ci-template.yml
    ref: master
  - project: 'FP20/infrastructure/gitlab-ci'
    file: import.yml
    ref: main

variables:
  TAG: $CI_COMMIT_SHORT_SHA
  ECR_REPO_NAME: ${CI_PROJECT_NAME}

image:
  name: "public.ecr.aws/amazoncorretto/amazoncorretto:21.0.6"

workflow:
  rules:
    # enable merge-request pipelines
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"
    # do not run duplicate pipelines for branch & merge-request
    - if: $CI_COMMIT_BRANCH && $CI_OPEN_MERGE_REQUESTS
      when: never
    # default pipelines
    - if: $CI_COMMIT_BRANCH
    # if there is a tag
    - if: $CI_COMMIT_TAG

terraform-fmt:
  extends: .terraform-fmt
  stage: lint
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - "terraform/**/*"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - "terraform/**/*"

terraform-validate:
  extends: .terraform-validate
  stage: lint
  parallel:
    matrix:
      - TF_STACK_PATH:
          - "terraform/iam"
          - "terraform/initialize"
          - "terraform/damage-management-service"
  rules:
    - if: $CI_MERGE_REQUEST_TARGET_BRANCH_NAME == $CI_DEFAULT_BRANCH
      changes:
        - "terraform/**/*"
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - "terraform/**/*"

build:
  stage: build
  script:
    - ./gradlew :configuration-default:dependencies --write-locks
    - ./gradlew :configuration-default:build -x test
    - ./gradlew :configuration-default:createDockerfile
  tags:
    - large
  cache:
    policy: pull-push
    paths:
      - build
      - .gradle
  artifacts:
    when: always
    expire_in: 1 day
    paths:
      - build
      - Dockerfile
      - security-scan
      - "**/build"
  rules:
    - if: $CI_PIPELINE_SOURCE == "pipeline" # Do not run for triggered pipelines from other pipelines
      when: never
    - if: $CI_COMMIT_BRANCH =~ /^release/ # do not work on release branch
      when: never
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event" # run on any branch

check_and_build_doc_as_code:
  image: ${CI_REGISTRY}/fp20/financial-services/build-images/doctoolchain:3.4.0
  stage: lint
  variables:
    DTC_CONFIG_FILE: docs/doctoolchain/docToolchainConfig.groovy
  script:
    - dtcw generateHTML
    - dtcw htmlSanityCheck
  artifacts:
    expire_in: 1 week
    paths:
      - ./build/html5/arc42/arc42.html
  rules:
    # only run on main if any .adoc has changes
    - changes:
          - /**/*.adoc
  allow_failure: true

gitlab-push:
  stage: build
  extends: .gitlab-push
  needs:
    - build
  rules:
    # only run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

publish_doc_as_code:
  image: ${CI_REGISTRY}/fp20/financial-services/build-images/doctoolchain:3.4.0
  stage: push
  variables:
    DTC_CONFIG_FILE: docs/doctoolchain/docToolchainConfig.groovy
  script:
    - dtcw publishToConfluence -PconfluenceUser=$BUILD_USER -PconfluencePass=$CONFLUENCE_TOKEN
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      when: manual
  allow_failure: true

test-unit:
  stage: test
  script:
    - ./gradlew unitTests
  cache:
    policy: pull-push
    paths:
      - build
      - .gradle
  artifacts:
    when: always
    expire_in: 1 day
    paths:
      - "**/reports" #detailed reports for manual download
  rules:
    - if: $CI_PIPELINE_SOURCE == "pipeline" # Do not run for triggered pipelines from other pipelines
      when: never
    - if: $CI_COMMIT_TAG # do not run on tag
      when: never
    - if: $CI_COMMIT_BRANCH =~ /^release/ # do no run if branch starts with  release
      when: never
      # run if it's not main and not merge request
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE != "merge_request_event"

test-all:
  services:
    - name: ${CI_DEPENDENCY_PROXY_DIRECT_GROUP_IMAGE_PREFIX}/localstack/localstack:latest
      alias: localstack
      variables:
        SERVICES: s3,kms
    - name: ${CI_DEPENDENCY_PROXY_DIRECT_GROUP_IMAGE_PREFIX}/postgres:16.3-alpine3.19
      alias: postgres
      variables:
        POSTGRES_USER: postgres
        POSTGRES_PASSWORD: postgres
        POSTGRES_DB: emh_damage_management
        POSTGRES_INITDB_ARGS: "-c max_connections=200"
  stage: test
  variables:
    #host has to match service alias
    TEST_S3_ENDPOINT: http://localstack:4566
    #bucket to match service 'initialBuckets'
    STORAGE_VEHICLE_MIGRATION_BUCKET: test-vehicle-migration
    STORAGE_LEGAL_HOLD_BUCKET: test-legal-hold
    DATASOURCE_URL: *****************************************************
  script:
    - ./gradlew test
    - ./gradlew :configuration-default:testCodeCoverageReport
  tags:
    - large
  cache:
    policy: pull-push
    paths:
      - build
      - .gradle
  artifacts:
    when: always
    expire_in: 1 day
    paths:
      - build/reports #required for aggregated testCoverageReports
      - "**/reports" #detailed reports for manual download
  rules:
    # run on non-main branch, as soon as there is a merge-request
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "merge_request_event"
    # run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

# despite the implied name .fs-trivy-scan does rootfs instead of fs scan (which only includes artifacts and such, but does not scan lockfiles)
# also provided image is massively outdated (and did not support gradle)
# so, we built our own pipeline
security-scan:
  extends: .security-scan
  stage: test
  before_script:
    - cp .trivyignore security-scan/.trivyignore
  variables:
    SCANPATH: security-scan #lockfile gets provided by build step
  rules:
    # run on non-main branch, as soon as there is a merge-request
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "merge_request_event"

sonarqube:
  extends: .sonarqube
  stage: init
  cache:
    paths:
      - build
      - .gradle
  rules:
    # run on non-main branch, as soon as there is a merge-request
    - if: $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "merge_request_event"
      changes:
        - "**/*.kt"
    # run on main if actual code changes happened
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - "**/*.kt"

terraform-dev-iam:
  extends: .terraform-apply
  stage: init
  variables:
    TF_STACK_PATH: "terraform/iam"
    BACKEND_CONFIG_PATH: "../envs/dev.tfbackend"
    TF_VAR_PATH: "../envs/dev.tfvars"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_DEV}
  rules:
    # run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

terraform-staging-iam:
  extends: .terraform-apply
  stage: init
  variables:
    TF_STACK_PATH: "terraform/iam"
    BACKEND_CONFIG_PATH: "../envs/staging.tfbackend"
    TF_VAR_PATH: "../envs/staging.tfvars"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_STAGING}
  rules:
    # run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH


terraform-dev-initialize:
  extends: .terraform-apply
  stage: init
  needs: [ "terraform-dev-iam" ]
  variables:
    TF_STACK_PATH: "terraform/initialize"
    BACKEND_CONFIG_PATH: "../envs/dev.tfbackend"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_DEV}
    TF_VAR_ecr_repo_name: ${CI_PROJECT_NAME}
  rules:
    # run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - terraform/**/*

terraform-staging-initialize:
  extends: .terraform-apply
  stage: init
  needs: [ "terraform-staging-iam" ]
  variables:
    TF_STACK_PATH: "terraform/initialize"
    BACKEND_CONFIG_PATH: "../envs/staging.tfbackend"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_STAGING}
    TF_VAR_ecr_repo_name: ${CI_PROJECT_NAME}
  rules:
    # run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH
      changes:
        - terraform/**/*

ecr-push-dev:
  stage: push
  extends: .aws-ecr-push-scan
  variables:
    ACCOUNT_ID: ${AWS_ACCOUNT_ID_DEV}
    AWS_REGION: "eu-west-1"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_DEV}
    ECR_REPO_NAME: ${CI_PROJECT_NAME}
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"
  allow_failure: true

ecr-push-staging:
  stage: push
  extends: .aws-ecr-push-scan
  variables:
    ACCOUNT_ID: ${AWS_ACCOUNT_ID_STAGING}
    AWS_REGION: "eu-west-1"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_STAGING}
    ECR_REPO_NAME: ${CI_PROJECT_NAME}
  rules:
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH && $CI_PIPELINE_SOURCE == "push"

terraform-dev:
  extends: .terraform-apply
  stage: deploy
  environment:
    name: dev
  variables:
    TF_STACK_PATH: "terraform/${CI_PROJECT_NAME}"
    BACKEND_CONFIG_PATH: "../envs/dev.tfbackend"
    TF_VAR_PATH: "../envs/dev.tfvars"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_DEV}
    TF_VAR_commit_hash: $CI_COMMIT_SHORT_SHA
  rules:
    # run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

upload-manifest-dev:
  stage: deploy
  extends: .upload-manifest
  variables:
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_DEV}
    AWS_ACCOUNT_ID: ${AWS_ACCOUNT_ID_DEV}
  needs:
    - terraform-dev
  rules:
    - !reference [.dev-deployment-rules, rules]

terraform-staging:
  extends: .terraform-apply
  stage: deploy
  environment:
    name: staging
  needs:
    - terraform-dev
  variables:
    TF_STACK_PATH: "terraform/${CI_PROJECT_NAME}"
    BACKEND_CONFIG_PATH: "../envs/staging.tfbackend"
    TF_VAR_PATH: "../envs/staging.tfvars"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_STAGING}
    TF_VAR_commit_hash: $CI_COMMIT_SHORT_SHA
  rules:
    # run on main
    - if: $CI_COMMIT_BRANCH == $CI_DEFAULT_BRANCH

upload-manifest-staging:
  stage: deploy
  extends: .upload-manifest
  variables:
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_STAGING}
    AWS_ACCOUNT_ID: ${AWS_ACCOUNT_ID_STAGING}
  needs:
    - terraform-staging
  rules:
    - !reference [.staging-deployment-rules, rules]

pre-release:
  stage: release
  extends: .pre-release

semantic-release:
  stage: release
  extends: .semantic-release

terraform-prod-initialize:
  extends: .terraform-apply
  stage: prod
  needs: [ "terraform-prod-iam" ]
  variables:
    TF_STACK_PATH: "terraform/initialize"
    BACKEND_CONFIG_PATH: "../envs/prod.tfbackend"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_PROD}
    TF_VAR_ecr_repo_name: ${CI_PROJECT_NAME}
  rules:
    - if: $CI_PIPELINE_SOURCE != "web"
      when: never
    - if: $CI_COMMIT_TAG

terraform-prod-iam:
  extends: .terraform-apply
  stage: prod
  variables:
    TF_STACK_PATH: "terraform/iam"
    BACKEND_CONFIG_PATH: "../envs/prod.tfbackend"
    TF_VAR_PATH: "../envs/prod.tfvars"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_PROD}
  rules:
    - if: $CI_PIPELINE_SOURCE != "web"
      when: never
    - if: $CI_COMMIT_TAG

ecr-push-prod:
  stage: prod
  extends: .aws-ecr-push-scan
  needs:
    - terraform-prod-iam
  variables:
    ACCOUNT_ID: ${AWS_ACCOUNT_ID_PROD}
    AWS_REGION: "eu-west-1"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_PROD}
    ECR_REPO_NAME: ${CI_PROJECT_NAME}
  rules:
    - if: $CI_PIPELINE_SOURCE != "web"
      when: never
    - if: $CI_COMMIT_TAG

terraform-prod:
  extends: .terraform-apply
  stage: prod
  environment:
    name: production
  when: manual
  needs:
    - ecr-push-prod
  variables:
    TF_STACK_PATH: "terraform/${CI_PROJECT_NAME}"
    BACKEND_CONFIG_PATH: "../envs/prod.tfbackend"
    TF_VAR_PATH: "../envs/prod.tfvars"
    AWS_OIDC_ROLE_ARN: ${AWS_OIDC_ROLE_ARN_PROD}
    TF_VAR_commit_hash: $CI_COMMIT_SHORT_SHA
  rules:
    - if: $CI_PIPELINE_SOURCE != "web"
      when: never
    - if: $CI_COMMIT_TAG