= Changelog

== 1.1.9-SNAPSHOT

- https://skyway.porsche.com/jira/browse/FPT1-1153[[Task\][FPT1-11531\] HCM will use damageDate instead of creationDate]

== 1.1.8

- https://skyway.porsche.com/jira/browse/FPT1-1151[[Bug\][FPT1-1151\] replace usingCostCenter transport property]

== 1.1.7

- https://skyway.porsche.com/jira/browse/FPT1-986[[Task\][FPT1-986\] add usingCostCenter to vehicle migration]
- https://skyway.porsche.com/jira/browse/FPT1-985[[Task\][FPT1-985\] closedDate will not be updated on closing a reopened damage file]
- https://skyway.porsche.com/jira/browse/FPT1-1004[[Bug\][FPT1-1004\] occuredAt is correctly set to damageDate]

== 1.1.6

- https://skyway.porsche.com/jira/browse/FPT1-530[[Task\][FPT1-530\] deactivate unmanaged vehicles in repairfix]
- https://skyway.porsche.com/jira/browse/FPT1-930[[Task\][FPT1-930\] sync car reference id]
- https://skyway.porsche.com/jira/browse/FPT1-745[[Task\][FPT1-745\] sync number of damages]

== 1.1.5

- https://skyway.porsche.com/jira/browse/FPT1-784[[Task\][FPT1-784\] update gobd damageFiles closed threshold for STAGING]

== 1.1.4

- https://skyway.porsche.com/jira/browse/FPT1-772[[Task\][FPT1-772\] update default insurance company]

== 1.1.3

- https://skyway.porsche.com/jira/browse/FPT1-731[[Bug\][FPT1-731\] updates repairfix  & employee API]

== 1.1.2

- https://skyway.porsche.com/jira/browse/FPT1-577[[Bug\][FPT1-577\] handle 40 character limit for PACE order name]

== 1.1.1

- https://skyway.porsche.com/jira/browse/FPT1-543[[Bug\][FPT1-543\] Handle null values during migration]

== 1.1.0 - PACE and PVCC Bugfixes

- https://skyway.porsche.com/jira/browse/FPT1-446[[Task\][FPT1-446\] Request and persist internal order number and provide to RepairFix]
- https://skyway.porsche.com/jira/browse/FPT1-512[[Task\][FPT1-512\] Remove PVCC KafkaListener since its only for Debugging Purposes]
- https://skyway.porsche.com/jira/browse/FPT1-528[[Bug\][FPT1-528\] InternalOrderNumberUpdateJobs fail on single exception]
- https://skyway.porsche.com/jira/browse/FPT1-526[[Bug\][FPT1-526\] Vehicles with / in their VIN cannot be updated using VIN as path parameter]
- https://skyway.porsche.com/jira/browse/FPT1-533[[Task\][FPT1-533\] PACE E2E Test and Bugfixes]
- https://skyway.porsche.com/jira/browse/FPT1-534[[Bug\][FPT1-534\] Use repair fix car identifier to get/update vehicle]
- https://skyway.porsche.com/jira/browse/FPT1-535[[Task\][FPT1-535\] Always send PVCC MonetaryBenefit events, even if deductible or claim is missing]


== 1.0.0 - GoLive Release

- https://skyway.porsche.com/jira/browse/FPT1-386[[Task\][FPT1-386\] Update Leasing Type and claimClassification for MonetaryBenefitAccountableEvent(PVCC)]
- https://skyway.porsche.com/jira/browse/FPT1-484[[Task\][FPT1-484\] Save damageDate, damageResponsibility and estimatedCosts in damage file]
- https://skyway.porsche.com/jira/browse/FPT1-500[[Task\][FPT1-500\] Handle Failed Repairfix Update Of InternalOrderNumber]
- https://skyway.porsche.com/jira/browse/FPT1-514[[Bug\][FPT1-514\] Persist Leasing Type for Vehicles]
- https://skyway.porsche.com/jira/browse/FPT1-513[[Task\][FPT1-513\] L9.9 will be removed as a Leasing Fleet]
- https://skyway.porsche.com/jira/browse/FPT1-516[[Bug\][FPT1-516\] Pre/Post-Usage fleets should not onboard fleet managers]
- https://skyway.porsche.com/jira/browse/FPT1-524[[Bug\][FPT1-524\] Vehicle Onboarding does not support vehicle responsible person assignment, if driver is a fleet manager]
- https://skyway.porsche.com/jira/browse/FPT1-525[[Bug\][FPT1-525\] PoolFleetManager onboarding does not support visibility scope]
