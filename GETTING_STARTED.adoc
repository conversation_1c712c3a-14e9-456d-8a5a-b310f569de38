:toc:
:numbered:
= damage-management-service

== Getting started

Add your personal gitlab access token to your local or global gradle.properties file.

_<user>.gradle\gradle.properties_

----
REGISTRY_TOKEN=<token_value>
----

=== JDK setup

File > Project Structure > Project Settings > Project > SDK: coretto-21 Intellij IDEA > Build, Execution, Deployment > Build Tools > Gradle > Gradle JVM: coretto-21

Follow the instruction of all the modules you want to start up as they may contain additional requirements for services like databases or mocks.

=== General

* Setup Database +

==== Docker Compose

For ease of use there is a docker compose file in the project root directory(link:./compose.yaml[compose.yaml]), that contains the setup for the database, the localstack and the kafka server.

----
# start the services
docker compose up -d

# stop the services
docker compose down
----

==== Docker

----
docker run -d \
	--name postgres \
	-e POSTGRES_USER=postgres \
	-e POSTGRES_PASSWORD=postgres \
	-e POSTGRES_DB=emh_damage_management \
	-p 5432:5432 \
	postgres
----

=== vehicle-migration

* link:./excel-vehicle-migration/README.adoc#_localstack[Setup Localstack]

=== hcm-p08-damage-report-adapter

* link:./pvcc-p40-monetary-benefit-adapter/README.adoc#_spring_boot_kafka[Setup Kafka]

=== Running the Service without Spring Boot Plugin

If you're not using IntelliJ Ultimate (and thus lack Spring Boot support), you can start the service locally using Gradle:

----
./gradlew configuration-default:bootRun --args='--spring.profiles.active=debug'
----

== Integrate with your tools

* [ ] Configure Formatter using *.editorconfig*
** [ ] Intellij IDEA > Editor > Code Style > (x) Enable EditorConfig support
** [ ] Intellij IDEA > Editor > Code Style > Line separator: Unix and macOS (\n)
* [ ] Download ktfmt plugin (https://plugins.jetbrains.com/plugin/14912-ktfmt)
** [ ] Enable ktfmt plugin (File→Settings...→ktfmt Settings), _Enable ktfmt_
** [ ] Set CodeStyle to _Kotlinlang_
* [ ] Formatting will be done by .pre-commit hook or can be manually forced with `gradlew spotlessApply`
** [ ] run `+gradlew installGitHook-${OS}+` once for hook installation
*** [ ] Gradle > other > installGitHook-macos/windows
* [ ] Enable .editorconfig support
* [ ] Ensure proper line separators in settings
** [ ] Settings \-> Editor \-> Code Style \-> Line Separator: *Unix and MacOS (\n)*

== Architecture / Module overview

You need mermaidJS-support to view the following diagrams.
For IntelliJ you may install provided plugin (see https://www.jetbrains.com/guide/go/tips/mermaid-js-support-in-markdown/ for details).

We are using a multi-module approach with this project.
Currenly we distinguish between three different _types_ of modules:

=== Domain/Business modules

Domain- or Business-Modules do the heavy lifting and contain the _actual_ business logic needed to perform any operation.
We try to use separate modules for each *aggragate-root* or otherwise encapsulated functionality that should not have dependencies with other modules.
The idea is, that business modules do not have dependencies on any module, other than _shared_-ones.

Example: link:./p08-damage-report-adapter[hcm-p08-damage-report-adapter]

=== Shared module(s)

A module (currently we do not see the need for more than one of them) that holds any non-domain specific configuration or code that can be shared between different domian-modules (for example a common Jackson- or SpringSecurity-Configuration).

Example: link:./shared[shared]

=== Configuration modules

A configuration-module represent a single _configuration_ of this service that can be built and deployed.
It depends on a unique set of domain-modules to ensure proper functionality and also hold all necessary, additional configuration (and build-scripts) required for a proper test, build and deploy process.

Example: link:./configuration-default[configuration-default]

=== Module Dependencies

[mermaid]
....
classDiagram
    Shared <|-- Damage-File
    Shared <|-- Damage-File-GOBD
    Shared <|-- Damage-File-Kafka-Out-Adapter
    Shared <|-- Damage-File-Migration
    Shared <|-- Employee-Client
    Shared <|-- Excel-Vehicle-Migration
    Shared <|-- HCM-P08-Damage-Report-Adapter
    Shared <|-- Legal-Hold
    Shared <|-- Legal-Hold-In-Adapter
    Shared <|-- Legal-Hold-Out-Adapter
    Shared <|-- PACE-P40-Internal-Order-Number-Adapter
    Shared <|-- PVCC-P40-Monetary-Benefit-Adapter
    Shared <|-- Outbox-Message-Relay
    Shared <|-- Repairfix-Client
    Shared <|-- Vehicle-Migration
    Shared <|-- Vehicle-Migration-Data-In-Adapter
    Shared <|-- Vehicle-Migration-Message-Retry
    Damage-File <|-- Configuration-Default
    Damage-File <|-- HCM-P08-Damage-Report-Adapter
    Damage-File <|-- Damage-File-GOBD
    Damage-File <|-- Damage-File-Kafka-Out-Adapter
    Damage-File <|-- Damage-File-Migration
    Damage-File-Migration <|-- Configuration-Default
    Damage-File-Migration <|-- PACE-P40-Internal-Order-Number-Adapter
    Damage-File-GOBD <|-- Configuration-Default
    Damage-File-Kafka-Out-Adapter <|-- Configuration-Default
    Employee-Client  <|-- Excel-Vehicle-Migration
    Excel-Vehicle-Migration <|-- Configuration-Default
    HCM-P08-Damage-Report-Adapter <|-- Configuration-Default
    Legal-Hold <|-- Configuration-Default
    Legal-Hold <|-- Damage-File
    Legal-Hold <|-- Legal-Hold-In-Adapter
    Legal-Hold <|-- Legal-Hold-Out-Adapter
    Legal-Hold-In-Adapter <|-- Configuration-Default
    Legal-Hold-Out-Adapter <|-- Configuration-Default
    Outbox-Message-Relay <|-- Configuration-Default
    Outbox-Message-Relay <|-- Damage-File-Kafka-Out-Adapter
    Outbox-Message-Relay <|-- PVCC-P40-Monetary-Benefit-Adapter
    PACE-P40-Internal-Order-Number-Adapter <|-- Configuration-Default
    PVCC-P40-Monetary-Benefit-Adapter <|-- Configuration-Default
    Repairfix-Client <|-- Damage-File-Migration
    Repairfix-Client <|-- Excel-Vehicle-Migration
    Repairfix-Client <|-- Vehicle-Migration
    Vehicle-Adapter <|-- Configuration-Default
    Vehicle-Migration <|-- Vehicle-Migration-Data-In-Adapter
    Vehicle-Migration <|-- Vehicle-Migration-Message-Retry
    Vehicle-Migration-Data-In-Adapter <|-- Configuration-Default
    Vehicle-Migration-Message-Retry <|-- Vehicle-Migration-Data-In-Adapter
    Vehicle-Migration-Message-Retry <|-- Configuration-Default

    class Shared{
      shared, non-domain logic and configuration
    }
    class HCM-P08-Damage-Report-Adapter{
      adapter module for P08/HR-System providing damage reports
    }
    class Damage-File{
      domain module for damage file
    }
    class Damage-File-GOBD{
      application module for damage file gobd functionality (archiving)
    }
    class Damage-File-Kafka-Out-Adapter{
      adapter module to provide number of damages information to vehicle service using kafka
    }
    class Employee-Client{
        adapter module to talk to employe api (user service)
    }
    class Excel-Vehicle-Migration{
      business module for parsing vehicle exports
      and importing them in 3rd-party solutions
    }
    class Configuration-Default{
      the default configuration for building and deploying
      the entire servcie
    }
    class Repairfix-Client{
        shared repairfix module
        holds API and data models
    }
    class Damage-File-Migration{
        business module for migrating and synchronizing external damage files with damage-file module
    }
    class PVCC-P40-Monetary-Benefit-Adapter{
        adapter module for P40/PVCC-System providing monetary benefit information
    }
    class PACE-P40-Internal-Order-Number-Adapter{
        adapter module for P40/PACE-System requesting new internal order numbers for damage files
    }
    class Outbox-Message-Relay{
        adapter module to store and devliver outbound messages
    }
    class Vehicle-Adapter{
        adapter module providing access to vehicle REST APIs, currently used to obtain scrappedOrSold information
    }
    class Vehicle-Migration{
      business module offering alternative vehicle migration to repairfix based on single vehicle update received from vehicle-service
    }
    class Vehicle-Migration-Data-In-Adapter{
      adapter module consuming vehicle migration messages from vehicle services via kafka topic
    }
    class Vehicle-Migration-Message-Retry{
          application module providing retry functionality for failed vehicle-migration messages
    }
....

== Modules
:leveloffset: +2

TODO

#include::damage-file/README.adoc[]

#include::damage-file-gobd/README.adoc[]

#include::damage-report/README.adoc[]

#include::hcm-p08-damage-report-adapter/README.adoc[]

#include::shared/README.adoc[]

#include::configuration-default/README.adoc[]

include::docs/README.adoc[]

include::excel-vehicle-migration/README.adoc[]

include::repairfix-client/README.adoc[]

include::damage-file-migration/README.adoc[]

include::employee-client/README.adoc[]

include::legal-hold/README.adoc[]

include::legal-hold-in-adapter/README.adoc[]

include::legal-hold-out-adapter/README.adoc[]

include::outbox-message-relay/README.adoc[]

include::pvcc-p40-monetary-benefit-adapter/README.adoc[]

include::vehicle-adapter/README.adoc[]

include::terraform/README.adoc[]

include::wiremock/README.adoc[]

:leveloffset: -2

* link:shared[Shared]
* link:HCM-P08-Damage-Report-Adapter/README.md[HCM-P08-Damage-Report-Adapter]
* link:damage-file/README.adoc[Damage-File]
* link:excel-vehicle-migration/README.adoc[Vehicle-Migration]
* link:Configuration-Default/README.adoc[Configuration-Default]
* link:repairfix-client/README.adoc[Repairfix-Client]
* link:damage-file-migration/README.adoc[Damage-File-Migration]
* link:pvcc-p40-monetary-benefit-adapter/README.adoc[PVCC-P40-Monetary-Benefit-Adapter]
* link:outbox-message-relay/README.adoc[Outbox-Message-Relay]

== How to write a module

=== build.gradle.kts

Create a new module with your favorite name and copy an appropriate `build.gradle.kts` from another module.
This is easier than writing one from scratch.
Pick one based on the type of code generation you need (client, server or none at all)

=== Package/Class - Structure

All modules follow the https://blog.cleancoder.com/uncle-bob/2012/08/13/the-clean-architecture.html[clean architecture approach],sometimes also referred to as https://alistair.cockburn.us/hexagonal-architecture/[hexagonal architecture] or https://jeffreypalermo.com/2008/07/the-onion-architecture-part-1/[onion architecture].

A basic package structure looks llike the following graphic.
Depending on the type of your module, not all the following (top level) packages will be relevant for you.

Any package will always start with `+com.emh.{service-name}.{module-(prefix)name}+`, so in our case `+com.emh.damagemanagement.{module-name}+`

----
📦  {module-name}
└─ com.emh.damagemanagement.{module-name}
   ├─ adapter
   │  ├─ out
   │  │  └─ {adapter-name}
   │  │     ├─ config
   │  │     │  └─ {adapter-name}AdapterConfig.kt
   │  │     └─ {adapter-name}Adapter.kt
   │  └─ in
   │     └─ {adapter-name}
   │        ├─ config
   │        │  └─ {adapter-name}AdapterConfig.kt
   │        └─ {adapter-name}Adapter.kt
   ├─ application
   │  ├─ job
   │  │  └─ {some-cron}Job.kt
   │  ├─ port
   │  │  ├─ {external-domain}OutPort.kt
   │  │  └─ {internal-domain}UseCase.kt
   │  └─ {module-name}ApplicationService.kt
   ├─ domain
   │  ├─ service
   │  │  └─ {aggregate-root}{action}Service.kt
   │  ├─ {domain-entity}.kt
   │  ├─ {value-object}.kt
   │  ├─ {aggregate-root}.kt
   │  └─ {aggregate-root}Repository.kt
   └─ {module-name}ModuleConfiguration.kt
----

=== Wire your module

If your module is not used as a dependency by another module you have to declare a dependency in the link:./configuration-default/build.gradle.kts[configuration-module].

[,gradle]
----
dependencies {
    implementation(project(":my-new-module"))
}
----

If your module is used by another module, that is already wired in the configuration you can skip this step and instead declare the dependency in this module (the same way).

=== Setup code coverage report

. Verify that your module uses the `jacoco` plugin
+
[,gradle]
----
plugins {
  jacoco
}
----

. Add your module to the test code coverage report configuration in the link:./configuration-default/build.gradle.kts[configuration-module]
+
[,gradle]
----
    val filesToIncludeInCodeCoverage =
     setOf(
             project(":my-new--module")
     )
----

. Add your module paths to the link:./sonar-project.properties[sonar-project.properties]
+
----
sonar.sources=my-new-module/src/main/kotlin,\
sonar.tests=my-new-module/src/test/kotlin,\
sonar.java.binaries=my-new-module/build/classes/kotlin/main,\
----

=== Setup Test capabilities

. Copy the following files from any module (other than `shared`) and refactor them according to your needs
+
----
TestAnnotations.kt
TestConfigurations.kt
TestDataGenerator.kt
----

. Copy the `architecture` package from any module (other than `shared`) and fix the package path in the copied file

=== Update documentation

* [ ] add your module to the <<module-dependencies-,module overview>> part of this readme

== How to write database migration scripts

We are using https://www.liquibase.com/[Liquibase] for our migration scripts.
To write and place scripts correctly please follow these rules:

* changelogs are placed in link:./shared/src/main/resources/db/changelog[shared/resources/db/changelog/changes]
* a change-file should start with the name of the ticket it refers to, e.g. `FPT1-169-damage-file.yaml`
* scripts are written in .yaml
* a single changelog should only contain a single change (like a create or update statement, not both)
* use your name and a counter to uniquely identify each changeset you write
* each changeset should be written with prod-environment in mind
* do *NOT*, *EVER* change/modify *ANY* existing changeset, after it has been merged to main
* each changelog that you write has to be included in the link:./shared/src/main/resources/db/changelog/db.changelog-master.yaml[databaseChangelog].
Only append your changes at the bottom, to not mess up execution order
* commits with DB changes *REQUIRE* a review

== How to create a new module

Create a new module using your IDE and 'copy' the build.gradle.kts of your favorite domian/application-module.
Afterward you can modify the build-file to match your need.

Things to also do:

* link you new module in the link:./README.adoc#module-dependencies-[global Readme]
* declare/link all your external requirements in the link:./README.adoc#getting-started[global GettingStarted]
* prepare your new module for sonarqube integration
** build.gradle.kts +
`plugins { jacoco }`
** add project to configuration-module (_:configuration-default:build.gradle.kts_)
+
----
tasks.testCodeCoverageReport {
   outputs.upToDateWhen { false }
   val filesToIncludeInCodeCoverage =
       setOf(project(":my-new-module"),...)
   ...
}
----

** reference sources in link:./sonar-project.properties[sonar-properties]
+
----
  sonar.sources=my-new-module/src/main/kotlin,\
  ...
  sonar.tests=my-new-module/src/test/kotlin,\
  ...
  sonar.java.binaries=my-new-module/build/classes/kotlin/main,\
----

== Big Picture

See how DamageManagementService integrates into the "whole" landscape.

[mermaid]
....
classDiagram
  class DamageManagementService
  class VehicleService
  class UserService
  class Database
  class HCM_P08
  class PACE
  class Repairfix
  class PVCC

  VehicleService --> DamageManagementService : Vehicles
  DamageManagementService --> Database : DamageFiles
  DamageManagementService --> HCM_P08 : deductibles
  PACE --> DamageManagementService : internalOrderNumber
  UserService --> DamageManagementService : employees
  Repairfix --> DamageManagementService : damage reports
  DamageManagementService --> Repairfix : vehicles
  DamageManagementService --> Repairfix : fleets
  DamageManagementService --> Repairfix : vehicle responsible persons
  DamageManagementService --> PVCC : monetary benefit

  style DamageManagementService fill:#D5E8D4,stroke:#82B366,stroke-width:3px
  style VehicleService fill:#D5E8D4,stroke:#82B366,stroke-width:1px,stroke-dasharray: 5 5
  style UserService fill:#FFF2CC,stroke:#82B366,stroke-width:1px,stroke-dasharray: 5 5
  style Database fill:#FFF2CC,stroke:#D6B656,stroke-width:1px
  style HCM_P08 fill:#F5F5F5,stroke:#666666,stroke-width:1px
  style PACE fill:#F5F5F5,stroke:#666666,stroke-width:1px
  style PVCC fill:#F5F5F5,stroke:#666666,stroke-width:1px
  style Repairfix fill:#FFFFFF,stroke:#666666,stroke-width:1px
....
