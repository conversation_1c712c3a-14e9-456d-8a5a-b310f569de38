import org.jetbrains.kotlin.gradle.dsl.KotlinVersion
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile

buildscript {
    configurations.all {
        resolutionStrategy {
            /**
             * Forces old jackson version during build. This is done, as OpenApiGenerator has some
             * version conflicts with 2.17.1 (used by spring). Can be removed, once jackson versions
             * are aligned between spring and OpenApiGenerator.
             */
            force("com.fasterxml.jackson.core:jackson-annotations:2.16.2")
            force("com.fasterxml.jackson.core:jackson-databind:2.16.2")
        }
    }
}

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.spotless) apply false
    alias(libs.plugins.spring.dependency.management) apply false
    alias(libs.plugins.spring.boot) apply false
    alias(libs.plugins.kotlin.jpa) apply false
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring) apply false
    idea
}

// see
// https://docs.gitlab.com/ee/user/packages/maven_repository/?tab=gradle#authenticate-to-the-package-registry for details
val repositoryCredentialsTokenName by
    extra(if (null == System.getenv("CI_JOB_TOKEN")) "Private-Token" else "Job-Token")

/**
 * Provide personal access token to global gradle properties <user>\.gradle\gradle.properties
 *
 * ```
 *     REGISTRY_TOKEN=<token_value>
 * ```
 */
val repositoryCredentialsTokenValue by
    extra(System.getenv("CI_JOB_TOKEN") ?: findProperty("REGISTRY_TOKEN").toString())
val apiFolder by extra("/api")

allprojects {
    group = "com.emh.damagemanagement"
    //update this when you create your MR, also add your ticket to the CHANGELOG
    version = "1.1.9"

    repositories {
        mavenCentral()
        mavenLocal()
        maven {
            url = uri("https://cicd.skyway.porsche.com/api/v4/projects/23380/packages/maven")
            name = "Space-DMS"
            credentials(HttpHeaderCredentials::class) {
                name = repositoryCredentialsTokenName
                value = repositoryCredentialsTokenValue
            }
            authentication { create("header", HttpHeaderAuthentication::class) }
        }
        maven {
            url = uri("https://cicd.skyway.porsche.com/api/v4/projects/23878/packages/maven")
            name = "Space-EMHFXP"
            credentials(HttpHeaderCredentials::class) {
                name = repositoryCredentialsTokenName
                value = repositoryCredentialsTokenValue
            }
            authentication { create("header", HttpHeaderAuthentication::class) }
        }
        maven {
            url = uri("https://cicd.skyway.porsche.com/api/v4/projects/31402/packages/maven")
            name = "Shared-S3-Client"
            credentials(HttpHeaderCredentials::class) {
                name = repositoryCredentialsTokenName
                value = repositoryCredentialsTokenValue
            }
            authentication { create("header", HttpHeaderAuthentication::class) }
        }
        maven {
            url = uri("https://cicd.skyway.porsche.com/api/v4/projects/20718/packages/maven")
            name = "Space-FUHRPARK"
            credentials(HttpHeaderCredentials::class) {
                name = repositoryCredentialsTokenName
                value = repositoryCredentialsTokenValue
            }
            authentication { create("header", HttpHeaderAuthentication::class) }
        }
    }
}

subprojects {
    configurations {
        all {
            exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
            exclude(group = "commons-logging", module = "commons-logging")
        }
    }

    tasks.withType<Test> {
        useJUnitPlatform()
        minHeapSize = "256m"
        maxHeapSize = "1g"
        if (null != tasks.findByName("jacocoTestReport")) {
            finalizedBy("jacocoTestReport")
        }
        testLogging { events("passed", "skipped", "failed") }
    }

    tasks.register("integrationTests", Test::class) {
        useJUnitPlatform { includeTags("integration") }
    }

    tasks.register("unitTests", Test::class) {
        maxParallelForks = 4
        useJUnitPlatform { excludeTags("integration") }
    }

    tasks.withType<KotlinCompile> {
        compilerOptions {
            jvmTarget.set(org.jetbrains.kotlin.gradle.dsl.JvmTarget.JVM_21)
            freeCompilerArgs.add("-Xjsr305=strict")
            languageVersion.set(KotlinVersion.KOTLIN_2_2)
        }
    }

    tasks.register<Copy>("unpackApi") {
        duplicatesStrategy = DuplicatesStrategy.INCLUDE
        outputs.upToDateWhen { false }
        val resolvableApi by
            configurations.creating {
                extendsFrom(configurations.api.get())
                isCanBeResolved = true
            }
        configurations.named("resolvableApi").get().forEach {
            from(zipTree(it))
            into("$projectDir/$apiFolder")
        }
    }
}

tasks.register<Copy>("installGitHook-windows") {
    from(File("$projectDir/pre-commit-windows"))
    into(".git/hooks")
    rename("pre-commit-windows", "pre-commit")
    filePermissions { group { read = true } }
}

tasks.register<Copy>("installGitHook-macos") {
    from(File("$projectDir/pre-commit-macos"))
    into(".git/hooks")
    rename("pre-commit-macos", "pre-commit")
    filePermissions { group { read = true } }
}

tasks.wrapper {
    version = "8.14"
    distributionType = Wrapper.DistributionType.BIN
}
