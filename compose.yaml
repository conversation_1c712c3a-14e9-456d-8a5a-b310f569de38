version: '3.8'

services:
  postgres:
    image: postgres:alpine3.19
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: emh_damage_management

  kafka-server:
    image: bitnami/kafka:3.7.0-debian-12-r0
    hostname: kafka-server
    networks:
      - damage-management-network
    ports:
      - "9094:9094"
    environment:
      KAFKA_CFG_NODE_ID: "0"
      KAFKA_CFG_PROCESS_ROLES: "controller,broker"
      KAFKA_CFG_LISTENERS: "PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094"
      KAFKA_CFG_ADVERTISED_LISTENERS: "PLAINTEXT://kafka:9092,EXTERNAL://localhost:9094"
      KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP: "CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT"
      KAFKA_CFG_CONTROLLER_QUORUM_VOTERS: "0@kafka-server:9093"
      KAFKA_CFG_CONTROLLER_LISTENER_NAMES: "CONTROLLER"

  localstack:
    image: localstack/localstack:latest
    environment:
      SERVICES: s3,kms
    ports:
      - 4566:4566
    volumes:
      - ./localstack-setup.sh:/etc/localstack/init/ready.d/init-aws.sh
    networks:
      - damage-management-network
  localstacksetup:
    image: localstack/localstack:latest
    depends_on:
      - localstack
    restart: "no"
    entrypoint: [ "bash", "-c", "chmod 777 /etc/localstack/init/ready.d/init-aws.sh && /etc/localstack/init/ready.d/init-aws.sh" ]
    volumes:
      - ./localstack-setup.sh:/etc/localstack/init/ready.d/init-aws.sh


#    docker exec -it aa5c2d05b14e bash
#    bash /etc/localstack/init/ready.d/init-aws.sh

networks:
  damage-management-network:
