import com.bmuschko.gradle.docker.tasks.image.Dockerfile
import com.diffplug.spotless.LineEnding

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.docker)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    idea
    `maven-publish`
    id("jacoco-report-aggregation")
}

// required to ensure that :wiremock sourceSets are available
evaluationDependsOn(":wiremock")

dependencies {
    implementation(libs.bundles.starter)
    implementation(project(":damage-file"))
    implementation(project(":damage-file-gobd"))
    implementation(project(":damage-file-kafka-out-adapter"))
    implementation(project(":damage-file-migration"))
    implementation(project(":hcm-p08-damage-report-adapter"))
    implementation(project(":legal-hold-in-adapter"))
    implementation(project(":legal-hold-out-adapter"))
    implementation(project(":outbox-message-relay"))
    implementation(project(":pace-p40-internal-order-number-adapter"))
    implementation(project(":pvcc-p40-monetary-benefit-adapter"))
    implementation(project(":vehicle-adapter"))
    implementation(project(":excel-vehicle-migration"))
    implementation(project(":vehicle-migration-data-in-adapter"))
    implementation(project(":vehicle-migration"))
    implementation(project(":vehicle-migration-message-retry"))

    // for use on DEV
    implementation(project(":wiremock"))
}

sourceSets {
    // add :wiremock resources to our resources to resolve wiremock classloader issues with nested
    // jars
    main { resources { resources.srcDir(project(":wiremock").sourceSets.main.get().resources) } }
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

springBoot {
    buildInfo()
    mainClass = "com.emh.damagemanagement.DamageManagementServiceApplicationKt"
}

// configures files for code coverage report
tasks.testCodeCoverageReport {
    outputs.upToDateWhen { false }
    val filesToIncludeInCodeCoverage =
        setOf(
                project(":damage-file"),
                project(":damage-file-migration"),
                project(":damage-file-gobd"),
                project(":damage-file-kafka-out-adapter"),
                project(":excel-vehicle-migration"),
                project(":hcm-p08-damage-report-adapter"),
                project(":legal-hold"),
                project(":legal-hold-in-adapter"),
                project(":legal-hold-out-adapter"),
                project(":outbox-message-relay"),
                project(":pace-p40-internal-order-number-adapter"),
                project(":pvcc-p40-monetary-benefit-adapter"),
                project(":shared"),
                project(":vehicle-adapter"),
                project(":vehicle-migration-data-in-adapter"),
                project(":vehicle-migration"),
                project(":vehicle-migration-message-retry"),
            )
            .map {
                fileTree(it.layout.buildDirectory)
                    .matching {
                        include("**/classes/kotlin/main/**")
                        exclude("**/*generated*/**")
                        exclude("**/openapitools/**")
                    }
                    .files
            }
            .flatten()
            .distinct()
    classDirectories.setFrom(filesToIncludeInCodeCoverage)
    doLast {
        // copy reports to root
        copy {
            from("$projectDir/build/reports")
            into("$rootDir/build/reports")
        }
    }
}

val repositoryCredentialsTokenName: String by rootProject.extra
val repositoryCredentialsTokenValue: String by rootProject.extra

publishing {
    publications {
        create<MavenPublication>("maven") {
            group = rootProject.group.toString()
            artifactId = rootProject.name
            version =
                if (null == System.getenv("IMAGE_TAG")) "$version-SNAPSHOT"
                else "$version-${System.getenv("IMAGE_TAG")}"
            artifact(tasks["bootJar"])
        }
    }
    repositories {
        maven {
            url = uri("https://cicd.skyway.porsche.com/api/v4/projects/24099/packages/maven")
            credentials(HttpHeaderCredentials::class) {
                name = repositoryCredentialsTokenName
                value = repositoryCredentialsTokenValue
            }
            authentication { create("header", HttpHeaderAuthentication::class) }
        }
    }
}

// ########## BUILD DOCKER IMAGE ########
tasks.register<Dockerfile>("createDockerfile") {
    outputs.upToDateWhen { false }
    val bootJarName = "${project.name}-${rootProject.version}"
    val serviceJarName = "${rootProject.name}-${rootProject.version}"

    from("public.ecr.aws/amazoncorretto/amazoncorretto:21")
    volume("/tmp")
    //Update system packages and fix Python vulnerabilities (CVE-2025-6069)
    runCommand("yum update -y python python-libs && yum clean all && rm -rf /var/cache/yum")
    // open telemetry
    addFile(
        "https://github.com/aws-observability/aws-otel-java-instrumentation/releases/download/v1.32.0/aws-opentelemetry-agent.jar",
        "/app/aws-opentelemetry-agent.jar")
    environmentVariable(mapOf("JAVA_TOOL_OPTIONS" to "-javaagent:aws-opentelemetry-agent.jar"))
    runCommand("chmod 755 app/aws-opentelemetry-agent.jar")

    user("1000")
    workingDir("/app")
    label(mapOf("version" to "${rootProject.version}"))
    copyFile("/build/$bootJarName.jar", "$serviceJarName.jar")
    entryPoint("sh", "-c", "java \${JAVA_OPTS} -jar $serviceJarName.jar")
    exposePort(8080)
    environmentVariable(mapOf("JAVA_OPTS" to "-XX:+UseStringDeduplication"))

    doLast {
        // copy Dockerfile to root  (as ecr-task require Dockerfile to be there)
        copy {
            from("$projectDir/build/docker")
            into("$rootDir")
        }
        // copy bootJar to /build
        copy {
            from("$projectDir/build/libs")
            into("$rootDir/build")
        }
    }
    dependsOn("bootJar")
}

// #################### DEPENDENCY INSPECTION #####################
dependencyLocking {
    lockAllConfigurations()
    // custom naming as default seems not to work properly within pipeline
    lockFile = file("$rootDir/security-scan/gradle.lockfile")
}
