/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.boot.runApplication
import org.springframework.cache.annotation.EnableCaching
import org.springframework.scheduling.annotation.EnableAsync
import org.springframework.scheduling.annotation.EnableScheduling

@EnableAsync
@EnableScheduling
@EnableCaching
@EnableConfigurationProperties
@SpringBootApplication(scanBasePackages = ["com.emh.damagemanagement"])
@ConfigurationPropertiesScan(basePackages = ["com.emh.damagemanagement"])
class DamageManagementServiceApplication

fun main(args: Array<String>) {
    runApplication<DamageManagementServiceApplication>(*args)
}
