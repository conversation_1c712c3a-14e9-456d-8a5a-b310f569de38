spring:
  kafka:
    bootstrap-servers: localhost:9094
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    properties:
      sasl:
        mechanism: PLAINTEXT
        jaas:
          config: null
      security:
        protocol: PLAINTEXT
      session:
        timeout:
          ms: 45000
  cloud:
    aws:
      s3:
        path-style-access-enabled: true
        enabled: true
      # requires localstack (https://docs.localstack.cloud/) container to be running with http port open
      endpoint: http://localhost:4566
      region:
        static: us-east-1
      credentials:
        access-key: foo
        secret-key: bar
  security:
    oauth2:
      client:
        registration:
          employee:
            client-id: 575b41a7-052a-4265-a6a3-4f8cf64257bf
            client-secret: replace_but_dont_commit
            scope:
              - 1a6442c4-e25f-4459-9a7a-8e7a14386bae/.default #UserService
          vehicles:
            client-id: da6d503a-6101-4d6d-9677-4e8b1c6a9870
            client-secret: replace_but_dont_commit
            scope:
              - 8fb10d80-c8c9-4d03-9ffd-db5746f43a15/.default #VehicleService
        provider:
          employee:
            token-uri: http://localhost:8888/oauth2/v2.0/token
          vehicles:
            token-uri: http://localhost:8888/oauth2/v2.0/token
management:
  endpoints:
    enabled-by-default: true
#postgres
database:
  rds: false
  user: postgres
  pass: postgres
  url: ******************************************************
  dialect: org.hibernate.dialect.PostgreSQLDialect
  driver-class-name: org.postgresql.Driver
  schema: emh_damage_management

#h2 only for debugging purposes
#database:
#  rds: false
#  user: sa
#  pass: password
#  url: jdbc:h2:mem:testdb;IGNORECASE=TRUE;INIT=CREATE SCHEMA IF NOT EXISTS emh_damage_management\;SET SCHEMA emh_damage_management;
#  dialect: org.hibernate.dialect.H2Dialect
#  driverClassName: org.h2.Driver
#  schema: emh_damage_management

logging:
  level:
    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: ERROR
    org.springframework.web: ERROR
    root: INFO

server:
  port: 8081

employees:
  base-url: http://localhost:8888/api #points to wiremock server mapping

vehicles:
  base-url: http://localhost:8888


number-of-damages:
  kafka:
    producer:
      topic: ${NUMBER_OF_DAMAGES_TOPIC:FRA_emhs_dms_numer_of_damages_topic_dev}

repairfix:
  base-url: http://localhost:8888
  api-key: dummyApiKey
  fleet-groups:
    dienst-leasing: porsche-dienst-leasing
    pool: porsche-pool
    root: porsche
  damage-file-migration:
    # date format yyyy-mm-dd
    from-date: "2024-08-01"
  requests:
    page-delay-seconds: 1

storage:
  sse-kms-key: alias/valid-kms-key
  vehicle-migration:
    # has to match the one configured (or created) in localsatck
    bucket: test-vehicle-migration
  legal-hold:
    # has to match the one configured (or created) in localsatck
    bucket: test-legal-hold
  archive:
    # has to match the one configured (or created) in localsatck
    bucket: test-archive

vehicle-migration:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 0 0 ${random.int[0,4]} * * ?

damage-file-migration:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 0 0 0 * * ?
internal-order-number-update:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 0 0 0 * * ?
internal-oder-number-sync:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 0 0 0 * * ?
outbox-message-relay:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 1 0 0 * * ?

damage-file-gobd:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 0 * * * * ?
  number-of-days-for-closed: 3
  number-of-days-for-scraped-or-sold: 2

damage-file-archive:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 0 * * * * ?

wiremock:
  enabled: true
  port: 8888

kafka:
  retry:
    interval: 100
    maxAttempts: 1

vehicle-migration-data:
  kafka:
    listener:
      group-id: "FRA_emhs_dms_vehicle_migration_debug.consumer"
      topic: "FRA_emhs_dms_vehicle_migration_debug"
  vehicle-migration-kafka-integration-enabled: "true"

vehicle-migration-message-retry:
  scheduler:
    cron: 0 0 0 1 1 ? 2099