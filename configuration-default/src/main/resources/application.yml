spring:
  kafka:
    bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      auto-offset-reset: earliest
    properties:
      sasl:
        mechanism: PLAIN
        jaas:
          config: org.apache.kafka.common.security.plain.PlainLoginModule required username="${KAFKA_USER}" password="${KAFKA_PASSWORD}";
      security:
        protocol: SASL_SSL
      session:
        timeout:
          ms: 45000
  cloud:
    aws:
      s3:
        enabled: true #remove once credentials can be supplied
  security:
    oauth2:
      resourceserver:
        jwt:
          audiences: ${OAUTH2_DM_AUDIENCE:de2c475e-e393-425d-a89e-a1f394c90ae1}
          jwk-set-uri: ${OAUTH2_JWKS_URI:https://login.microsoftonline.com/common/discovery/keys}
          issuer-uri: https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/v2.0
      client:
        registration:
          # client for employee API currently provided by user-service
          employee:
            authorization-grant-type: client_credentials
            client-id: ${OAUTH2_CLIENT_ID_FOR_EMPLOYEE}
            client-secret: ${OAUTH2_CLIENT_SECRET_FOR_EMPLOYEE}
            # scope matches app id
            scope:
              - ${OAUTH2_EMPLOYEE_SCOPE}
          # client for vehicles API currently provided by vehicle-service
          vehicles:
            authorization-grant-type: client_credentials
            client-id: ${OAUTH2_CLIENT_ID_FOR_VEHICLES}
            client-secret: ${OAUTH2_CLIENT_SECRET_FOR_VEHICLES}
            # scope matches app id
            scope:
              - ${OAUTH2_VEHICLES_SCOPE}
          pace:
            authorization-grant-type: client_credentials
            client-id: ${OAUTH2_CLIENT_ID_FOR_PACE}
            client-secret: ${OAUTH2_CLIENT_SECRET_FOR_PACE}
            #very important, PACE Auth server will only work with form parameters
            client-authentication-method: client_secret_post
        provider:
          employee:
            token-uri: https://login.microsoftonline.com/${OAUTH2_AZURE_TENANT_ID_FOR_EMPLOYEE}/oauth2/v2.0/token
          vehicles:
            token-uri: https://login.microsoftonline.com/${OAUTH2_AZURE_TENANT_ID_FOR_VEHICLES}/oauth2/v2.0/token
          pace:
            token-uri: https://${OAUTH2_HOST_PACE}/oauth/token
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
  datasource:
    driver-class-name: ${database.driver-class-name}
    url: ${database.url}
    username: ${database.user}
    password: ${database.pass}
    hikari:
      jdbc-url: ${database.url}
      username: ${database.user}
      maximum-pool-size: ${database.maximumPoolSize}
      minimum-idle: ${database.minimumIdle}
      connection-timeout: 300
      pool-name: DMPool
      max-lifetime: 840000 # 14 min in milseconds
      driver-class-name: ${database.driver-class-name}
      idle-timeout: 300000 # 5 min in milseconds
  jpa:
    open-in-view: false
    properties:
      hibernate:
        ddl-auto: none
        jdbc:
          batch_size: 100
          time_zone: UTC
        dialect: ${database.dialect}
        default_schema: ${database.schema}
  sql:
    init:
      mode: never
  liquibase:
    change-log: classpath:/db/changelog/db.changelog-master.yaml
    liquibase-schema: ${database.schema}
    default-schema: ${database.schema}
    enabled: true
  quartz:
    job-store-type: jdbc
    jdbc:
      initialize-schema: never
    startup-delay: 30
    overwrite-existing-jobs: true
    properties:
      org.quartz.jobStore:
        isClustered: true
        class: org.springframework.scheduling.quartz.LocalDataSourceJobStore
        driverDelegateClass: org.quartz.impl.jdbcjobstore.PostgreSQLDelegate
        tablePrefix: ${database.schema}.qrtz_
        #looks required, as we get parallel executions regardless of cluster setting
        acquireTriggersWithinLock: true
      org.quartz.scheduler:
        instanceId: AUTO
        skipUpdateCheck: true

logging:
  level:
    root: info

caching:
  #24h cache eviction
  jwksTTL: 86400000

server:
  port: 8080
  servlet:
    context-path: /api
  forward-headers-strategy: framework
  tomcat:
    max-http-form-post-size: 10MB
    max-swallow-size: 10MB

management:
  endpoints:
    enabled-by-default: true
    web:
      exposure:
        include: health,info,metrics,mappings,scheduledtasks
      base-path: /damage/actuator
  endpoint:
    info:
      enabled: true
    health:
      enabled: true
      probes:
        enabled: true
    metrics:
      enabled: true
  health:
    livenessstate:
      enabled: true
    readinessstate:
      enabled: true
  metrics:
    export:
      cloudwatch:
        namespace: damage-management-service
        region: ${AWS_REGION:test-region}
        duration: 1

database:
  rds: ${RDS_ENABLED:true}
  user: ${DATASOURCE_USERNAME}
  pass: ${DATASOURCE_PASSWORD}
  url: ${DATASOURCE_URL}
  dialect: ${DATASOURCE_DIALECT:org.hibernate.dialect.PostgreSQLDialect}
  driver-class-name: ${DATASOURCE_DRIVER:org.postgresql.Driver}
  schema: emh_damage_management
  maximumPoolSize: 10
  minimumIdle: 5
  region: ${AWS_REGION:eu-west-1}

#currently provided by user-service
employees:
  base-url: ${EMPLOYEES_BASE_URL:}

#currently provided by vehicle-service
vehicles:
  base-url: ${VEHICLES_BASE_URL:}

number-of-damages:
  kafka:
    producer:
      topic: ${NUMBER_OF_DAMAGES_TOPIC:FRA_emhs_dms_numer_of_damages_topic_dev}

pace:
  base-url: ${PACE_BASE_URL:}

repairfix:
  base-url: ${REPAIRFIX_BASE_URL:}
  api-key: ${REPAIRFIX_API_KEY:}
  retry:
    max-attempts: ${REPAIRFIX_RETRY_MAX_ATTEMPTS:3}
    backoff-duration-in-millis: ${REPAIRFIX_RETRY_BACKOFF_DURATION_IN_MILLIS:2000}
  fleet-groups:
    dienst-leasing: ${REPAIRFIX_FLEET_GROUP_DIENST_LEASING}
    pool: ${REPAIRFIX_FLEET_GROUP_POOL}
    root: ${REPAIRFIX_FLEET_GROUP_ROOT}
  requests:
    # the page size to use for paginated request to repairfix (and subsequent processing from us)
    page-size: ${REPAIRFIX_REQUEST_PAGE_SIZE_KEY:10}
    # the delay in seconds when processing paged requests from repairfix (e.g. during damage file migration) to reduce strain on their API
    page-delay-seconds: ${REPAIRFIX_REQUEST_PAGE_DELAY_SECONDS:1}
  damage-file-migration:
    # date format yyyy-mm-dd
    from-date: ${BACK_OFFICE_GO_LIVE_DATE}
    thresholdInDays: ${DAMAGE_FILE_MIGRATION_THRESHOLD}


storage:
  sse: aws:kms
  sse-kms-key: ${KMS_KEY_ID}
  vehicle-migration:
    bucket: ${STORAGE_VEHICLE_MIGRATION_BUCKET:}
  legal-hold:
    bucket: ${STORAGE_LEGAL_HOLD_BUCKET:}
  archive:
    # has to match the one configured (or created) in localsatck
    bucket: ${STORAGE_ARCHIVE_BUCKET:}

feature-flags:
  metrics-registry: ${METRICS_REGISTRY:local} #logMetrics,cloudwatch
  vehicle-responsible-person-migration-uses-user-service: ${VEHICLE_RESPONSIBLE_PERSON_MIGRATION_USES_USER_SERVICE:false} #request data from user service during vehicle responsible person migration

monetary-benefit:
  accountable:
    kafka:
      listener:
        id: "monetary-benefit.accountable.consumer"
        group-id: ${MONETARY_BENEFIT_ACCOUNTABLE_CONSUMER_GROUP_ID:FRA_emhs_dms_monetary_benefit_accountable.consumer}
        topic: ${MONETARY_BENEFIT_ACCOUNTABLE_TOPIC:FRA_emhs_dms_monetary_benefit_accountable}
      producer:
        topic: ${MONETARY_BENEFIT_ACCOUNTABLE_TOPIC:FRA_emhs_dms_monetary_benefit_accountable}

kafka:
  consumer:
    auto-offset-reset: earliest
  retry:
    interval: 100
    maxAttempts: 2

vehicle-migration:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK - use "-" for disabling cron entirely
    cron: ${VEHICLE_MIGRATION_SCHEDULER_CRON:-}

damage-file-migration:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK - use "-" for disabling cron entirely
    cron: ${DAMAGE_FILE_MIGRATION_SCHEDULER_CRON:-}

internal-order-number-update:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK - use "-" for disabling cron entirely
    cron: ${INTERNAL_ORDER_NUMBER_UPDATE_SCHEDULER_CRON:-}

internal-oder-number-sync:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK - use "-" for disabling cron entirely
    cron: ${INTERNAL_ORDER_NUMBER_SYNC_SCHEDULER_CRON:-}

outbox-message-relay:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK - use "-" for disabling cron entirely
    cron: ${OUTBOX_MESSAGE_RELAY_SCHEDULER_CRON:-}

damage-file-gobd:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK - use "-" for disabling cron entirely
    cron: ${DAMAGE_FILE_DELETION_SCHEDULER_CRON:-}
  number-of-days-for-closed: ${NUMBER_OF_DAYS_FOR_CLOSED:15*365}
  number-of-days-for-scraped-or-sold: ${NUMBER_OF_DAYS_FOR_SCRAPED_OR_SOLD}

damage-file-archive:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: ${ARCHIVE_SCHEDULER_CRON:-}

vehicle-migration-data:
  kafka:
    listener:
      group-id: ${VEHICLE_MIGRATION_DATA_CONSUMER_GROUP_ID}
      topic: ${VEHICLE_MIGRATION_DATA_TOPIC}
  vehicle-migration-kafka-integration-enabled: ${VEHICLE_MIGRATION_KAFKA_INTEGRATION_ENABLED:false}

vehicle-migration-message-retry:
  scheduler:
    cron: ${VEHICLE_MIGRATION_MESSAGE_RETRY_CRON:-}

wiremock:
  enabled: ${WIREMOCK_ENABLED:false}
  port: ${WIREMOCK_PORT:8888}
