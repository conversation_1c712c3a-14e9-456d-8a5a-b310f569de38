/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd

class DamageFileGoBDException(message: String? = null, cause: Throwable? = null) : RuntimeException(message, cause)

class DamageFileArchiveException(message: String? = null, cause: Throwable? = null) : RuntimeException(message, cause)
