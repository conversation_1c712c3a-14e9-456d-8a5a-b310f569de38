/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.damagefile.gobd.adapter.s3

import com.emh.damagemanagement.damagefile.gobd.application.port.StorageException
import com.emh.damagemanagement.damagefile.gobd.application.port.StorageOutPort
import com.emh.shared.s3.client.adapter.S3StorageClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Profile
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

@Component
@Profile("!test")
class ArchiveS3StorageAdapter(
    @Qualifier("archive") private val storageProperties: StorageProperties,
    private val s3StorageClient: S3StorageClient,
) : StorageOutPort {
    init {
        val bucketExists = s3StorageClient.bucketExists(storageProperties.bucket)
        if (!bucketExists) {
            throw StorageException("Bucket [${storageProperties.bucket}] does not exist.")
        }
    }

    override fun uploadFile(key: String, damageFile: Resource) {
        s3StorageClient.uploadResource(key = key, resource = damageFile, bucket = storageProperties.bucket)
    }
}

@ConfigurationProperties("storage.archive") @Qualifier("archive") data class StorageProperties(val bucket: String)
