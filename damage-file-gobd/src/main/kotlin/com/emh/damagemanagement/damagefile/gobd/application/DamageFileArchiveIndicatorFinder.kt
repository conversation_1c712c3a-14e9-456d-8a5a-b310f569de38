/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.application

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicator
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicatorNotFoundException
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicatorRepository
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional(readOnly = true)
class DamageFileArchiveIndicatorFinder(
    private val damageFileArchiveIndicatorRepository: DamageFileArchiveIndicatorRepository
) {

    fun getDamageFileArchiveIndicator(key: DamageFileKey): DamageFileArchiveIndicator =
        damageFileArchiveIndicatorRepository.findByDamageFileKey(key)
            ?: throw DamageFileArchiveIndicatorNotFoundException(damageFileKey = key)

    fun findDamageFilesToArchive(): List<DamageFileArchiveIndicator> =
        damageFileArchiveIndicatorRepository.findAll(specificationIsNotArchived())

    fun findDamageFilesByKeys(keys: List<DamageFileKey>): List<DamageFileArchiveIndicator> =
        damageFileArchiveIndicatorRepository.findByDamageFileKeyIn(keys)

    companion object {
        private const val IS_ARCHIVED = "isArchived"

        private fun specificationIsNotArchived() =
            Specification<DamageFileArchiveIndicator> { root, _, criteriaBuilder ->
                // NOSONAR
                criteriaBuilder.equal(root.get<Boolean>(IS_ARCHIVED), false)
            }
    }
}
