/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.application

import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileClosedEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileReopenedEvent
import com.emh.damagemanagement.damagefile.gobd.DamageFileArchiveException
import com.emh.damagemanagement.damagefile.gobd.application.port.StorageOutPort
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicator
import com.emh.damagemanagement.damagefile.gobd.domain.service.DamageFileArchiveIndicatorService
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.emh.shared.s3.client.adapter.S3StorageClientException
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Transactional
@Component
class DamageFileArchiveService(
    private val damageFileArchiveIndicatorFinder: DamageFileArchiveIndicatorFinder,
    private val damageFileFinder: DamageFileFinder,
    private val storageOutPort: StorageOutPort,
    private val damageFileIndicatorArchiveService: DamageFileArchiveIndicatorService,
) {
    fun archiveDamageFiles() {
        val damageFileArchiveIndicator = damageFileArchiveIndicatorFinder.findDamageFilesToArchive()
        if (damageFileArchiveIndicator.isEmpty()) {
            log.info("No damage files found in DMS for archival process. Skipping further processing... ")
            return
        }
        val damageFileArchiveIndicatorKeys = damageFileArchiveIndicator.map { it.damageFileKey }
        val damageFilesToArchive = damageFileFinder.findAllByDamageKeys(damageFileArchiveIndicatorKeys)

        if (damageFilesToArchive.size != damageFileArchiveIndicatorKeys.size) {
            throw DamageFileArchiveException(
                "Damage files missing in DMS for archival process $${damageFileArchiveIndicatorKeys.joinToString(",")}"
            )
        }
        damageFilesToArchive.forEach {
            try {
                storageOutPort.uploadFile(
                    "${it.key.value}.json",
                    ByteArrayResource(it.toJsonNode().toString().toByteArray(Charsets.UTF_8)),
                )
                val damageFileArchive = damageFileArchiveIndicator.single { df -> df.damageFileKey == it.key }
                damageFileIndicatorArchiveService.archive(damageFileArchive)
            } catch (exception: S3StorageClientException) {
                log.error("Upload to S3 failed for damage-file ${it.key.value} with error $exception")
            }
        }
    }

    /** Will catch [DamageFileClosedEvent] and create an [DamageFileArchiveIndicator] */
    @EventListener
    fun handleDamageFileClosedEvent(event: DamageFileClosedEvent) {
        log.info(
            "Received DamageFileClosedEvent for damage file with key [${event.damageFileKey.value}]. Creating new DamageFileArchiveIndicator."
        )
        damageFileIndicatorArchiveService.createAndSaveDamageFileArchiveIndicator(damageFileKey = event.damageFileKey)
    }

    /** Will catch [DamageFileReopenedEvent] and delete archiving flag for given [DamageFile] */
    @EventListener
    fun handleDamageFileReopenedEvent(event: DamageFileReopenedEvent) {
        log.info(
            "Received DamageFileReopenedEvent for damage file with key [${event.damageFileKey.value}]. Resetting archive flag."
        )

        damageFileIndicatorArchiveService.deleteArchiveIndicator(event.damageFileKey)
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileArchiveService::class.java)
    }
}
