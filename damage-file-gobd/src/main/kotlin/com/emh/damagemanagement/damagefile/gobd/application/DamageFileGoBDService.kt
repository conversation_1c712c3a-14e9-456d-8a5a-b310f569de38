/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.application

import com.emh.damagemanagement.damagefile.application.port.AnonymizeDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.DeleteDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.FindDamageFileWithClosedUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.gobd.DamageFileGoBDException
import com.emh.damagemanagement.damagefile.gobd.application.port.VehicleScrappedOrSold
import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPort
import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPortException
import java.time.OffsetDateTime
import java.time.ZoneOffset
import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.stereotype.Component

@Component
class DamageFileGoBDService(
    private val findDamageFileWithClosedUseCase: FindDamageFileWithClosedUseCase,
    private val vehiclesOutPort: VehiclesOutPort,
    private val deleteDamageFileUseCase: DeleteDamageFileUseCase,
    private val damageFileGoBDProperties: DamageFileGoBDProperties,
    private val anonymizeDamageFileUseCase: AnonymizeDamageFileUseCase,
) {

    private val thresholdDateForClosedDamages: OffsetDateTime
        get() = OffsetDateTime.now().minusDays(damageFileGoBDProperties.numberOfDaysForClosed)

    private val thresholdDateForScrappedOrSold: OffsetDateTime
        get() = OffsetDateTime.now(ZoneOffset.UTC).minusDays(damageFileGoBDProperties.numberOfDaysForScrapedOrSold)

    private fun getClosedDamageFilesOutsideThreshold(): List<DamageFile> {
        return findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(thresholdDateForClosedDamages)
    }

    /** Deletes and/or anonymize damage files */
    fun deleteAndAnonymizeDamageFiles() {
        val closedDamageFiles = getClosedDamageFilesOutsideThreshold()
        if (closedDamageFiles.isEmpty()) {
            log.info(
                "No damage files found in DMS for GoBD Process, threshold date - ${thresholdDateForClosedDamages}. Skipping further processing... "
            )
            return
        }

        val vinsOfClosedDamageFiles = closedDamageFiles.map { it.vehicle.vin }
        val (toBeDeleted, toBeAnonymized) = getVinsToBeDeletedAndAnonymized(vinsOfClosedDamageFiles)
        if (toBeDeleted.isNotEmpty()) {
            val damageFileKeysToBeDeleted =
                closedDamageFiles.filter { toBeDeleted.contains(it.vehicle.vin) }.map { it.key }
            deleteDamageFileUseCase.deleteDamageFiles(damageFileKeysToBeDeleted)
        } else {
            log.info(
                "None of the damage files found in DMS have vehicles which are missing or scrapped/sold before threshold date - ${thresholdDateForScrappedOrSold}. Skipping deletion..."
            )
        }
        if (toBeAnonymized.isNotEmpty()) {
            val damageFileKeysToBeAnonymized =
                closedDamageFiles.filter { toBeAnonymized.contains(it.vehicle.vin) }.map { it.key }
            anonymizeDamageFileUseCase.anonymizeDamageFiles(damageFileKeysToBeAnonymized)
        } else {
            log.info(
                "None of the damage files found in DMS have vehicles which after scrapped/sold after threshold date - ${thresholdDateForScrappedOrSold}. Skipping anonymization..."
            )
        }
    }

    /**
     * Returns vins to be deleted and anonymized
     *
     * @param allVins list of vins of all closed damage files
     */
    private fun getVinsToBeDeletedAndAnonymized(allVins: List<String>): VINs {
        val vehiclesScrappedOrSoldStatus =
            try {
                vehiclesOutPort.getVehicleScrappedOrSoldStatus(allVins)
            } catch (exception: VehiclesOutPortException) {
                throw DamageFileGoBDException(
                    "Error while trying to fetch vehicles scrapped or sold status.",
                    exception,
                )
            }
        if (vehiclesScrappedOrSoldStatus.isEmpty()) {
            log.warn("Vehicle adapter did not return any vehicle status. Continuing deletion for all vins...")
            return VINs(toBeDeleted = allVins, toBeAnonymized = emptyList())
        }
        val missingVins = allVins - vehiclesScrappedOrSoldStatus.map { it.vin }
        if (missingVins.isNotEmpty()) {
            log.warn("Vehicle adapter did not return vehicle status for ${missingVins.size} vehicles.")
        }
        val scrappedOrSoldVins = vehiclesScrappedOrSoldStatus.filter { isVehicleScrappedOrSold(it) }.map { it.vin }
        val vinsInUse = vehiclesScrappedOrSoldStatus.filter { !isVehicleScrappedOrSold(it) }.map { it.vin }
        return VINs(toBeDeleted = missingVins + scrappedOrSoldVins, toBeAnonymized = vinsInUse)
    }

    private fun isVehicleScrappedOrSold(it: VehicleScrappedOrSold) =
        (it.isScrappedOrSold && requireNotNull(it.scrappedOrSoldDate) < thresholdDateForScrappedOrSold)

    // NOSONAR
    internal data class VINs(val toBeDeleted: List<String>, val toBeAnonymized: List<String>)

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileGoBDService::class.java)
    }
}

@ConfigurationProperties(prefix = "damage-file-gobd")
data class DamageFileGoBDProperties(val numberOfDaysForClosed: Long, val numberOfDaysForScrapedOrSold: Long)
