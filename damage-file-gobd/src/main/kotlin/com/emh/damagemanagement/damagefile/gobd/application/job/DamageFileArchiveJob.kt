/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.application.job

import com.emh.damagemanagement.damagefile.gobd.DamageFileArchiveException
import com.emh.damagemanagement.damagefile.gobd.application.DamageFileArchiveService
import java.util.*
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class DamageFileArchiveJob(private val damageFileArchiveService: DamageFileArchiveService) : Job {

    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled damage file archive job.")
        try {
            damageFileArchiveService.archiveDamageFiles()
            log.info("Finished damage file archive job")
        } catch (exception: DamageFileArchiveException) {
            log.warn("Damage file archive job failed.", exception)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileArchiveJob::class.java)
    }
}

@Configuration
class DamageFileArchiveJobConfig {

    @Bean
    @Qualifier("damageFileArchiveJob")
    fun damageFileArchiveJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(DamageFileArchiveJob::class.java)
            .storeDurably()
            .withIdentity("DamageFileArchiveJobDetail")
            .withDescription("Invoke Damage File GoBD Job")
            .build()
    }

    @Bean
    fun damageFileArchiveJobTrigger(
        @Qualifier("damageFileArchiveJob") job: JobDetail,
        @Value("\${damage-file-archive.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("DamageFileArchiveJobTrigger")
            .withDescription("Damage File Archive Job trigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
