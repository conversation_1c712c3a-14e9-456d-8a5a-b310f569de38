/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.application.job

import com.emh.damagemanagement.damagefile.gobd.DamageFileGoBDException
import com.emh.damagemanagement.damagefile.gobd.application.DamageFileGoBDService
import java.util.*
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class DamageFileGoBDJob(private val damageFileGoBDService: DamageFileGoBDService) : Job {

    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled damage file GoBD job.")
        try {
            damageFileGoBDService.deleteAndAnonymizeDamageFiles()
            log.info("Finished damage file GoBD job")
        } catch (exception: DamageFileGoBDException) {
            log.warn("Damage file GoBD job failed.", exception)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileGoBDJob::class.java)
    }
}

@Configuration
class DamageFileGoBDJobConfig {

    @Bean
    @Qualifier("damageFileGoBDJob")
    fun damageFileGoBDJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(DamageFileGoBDJob::class.java)
            .storeDurably()
            .withIdentity("DamageFileGoBDJobDetail")
            .withDescription("Invoke Damage File GoBD Job")
            .build()
    }

    @Bean
    fun damageFileGoBDJobTrigger(
        @Qualifier("damageFileGoBDJob") job: JobDetail,
        @Value("\${damage-file-gobd.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("DamageFileGoBDJobTrigger")
            .withDescription("Damage File GoBD Job trigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
