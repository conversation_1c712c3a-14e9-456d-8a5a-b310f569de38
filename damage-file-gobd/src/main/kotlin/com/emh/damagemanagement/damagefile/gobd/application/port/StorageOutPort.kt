/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.application.port

import org.springframework.core.io.Resource

fun interface StorageOutPort {
    fun uploadFile(key: String, damageFile: Resource)
}

class StorageException(override val message: String, override val cause: Throwable? = null) : RuntimeException()
