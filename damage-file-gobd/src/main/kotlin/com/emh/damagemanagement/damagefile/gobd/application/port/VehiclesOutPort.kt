/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.application.port

import java.time.OffsetDateTime

fun interface VehiclesOutPort {
    /**
     * Return [VehicleScrappedOrSold] for given vins.
     *
     * @throws [VehiclesOutPortException] in case of an error
     */
    fun getVehicleScrappedOrSoldStatus(vins: Collection<String>): List<VehicleScrappedOrSold>
}

data class VehicleScrappedOrSold(val vin: String, val scrappedOrSoldDate: OffsetDateTime?) {
    val isScrappedOrSold = null != scrappedOrSoldDate
}

class VehiclesOutPortException(message: String?, cause: Throwable?) : RuntimeException(message, cause)
