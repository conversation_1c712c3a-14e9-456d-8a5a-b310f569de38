/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.domain

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.shared.domain.Auditable
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import java.io.Serializable
import java.util.*

@Entity
@Table(name = "damage_file_archive_indicator")
class DamageFileArchiveIndicator(
    /** A reference to the damage file. */
    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "damage_file_key"))
    val damageFileKey: DamageFileKey
) : Auditable() {

    /** Internal PK. Use 'damageFileKey' when accessing / finding archival info. */
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: ArchiveId = ArchiveId()

    @Column(name = "archive")
    var isArchived: Boolean = false
        private set

    /** Marks damage file as archived */
    internal fun archive() {
        this.isArchived = true
    }
}

@Embeddable data class ArchiveId(@Basic val value: UUID = UUID.randomUUID()) : Serializable
