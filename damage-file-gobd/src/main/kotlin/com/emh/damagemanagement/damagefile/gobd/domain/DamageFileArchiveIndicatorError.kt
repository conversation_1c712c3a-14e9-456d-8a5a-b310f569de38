/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.domain

import com.emh.damagemanagement.damagefile.domain.DamageFileKey

class DamageFileArchiveIndicatorNotFoundException(val damageFileKey: DamageFileKey) :
    NoSuchElementException("Damage file archive indicator with key [${damageFileKey}] is not found")
