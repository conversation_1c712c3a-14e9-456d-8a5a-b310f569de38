/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.domain

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.repository.Repository

interface DamageFileArchiveIndicatorRepository :
    Repository<DamageFileArchiveIndicator, ArchiveId>, JpaSpecificationExecutor<DamageFileArchiveIndicator> {
    fun save(damageFileArchiveIndicator: DamageFileArchiveIndicator): DamageFileArchiveIndicator

    fun findByDamageFileKey(key: DamageFileKey): DamageFileArchiveIndicator?

    fun findByDamageFileKeyIn(keys: List<DamageFileKey>): List<DamageFileArchiveIndicator>

    fun deleteByDamageFileKey(key: <PERSON>ageF<PERSON><PERSON><PERSON>)
}
