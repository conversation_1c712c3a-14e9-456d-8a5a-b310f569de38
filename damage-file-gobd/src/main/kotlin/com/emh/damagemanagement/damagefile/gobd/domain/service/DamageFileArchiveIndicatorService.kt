package com.emh.damagemanagement.damagefile.gobd.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicator
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicatorRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class DamageFileArchiveIndicatorService(
    private val damageFileArchiveIndicatorRepository: DamageFileArchiveIndicatorRepository
) {

    /**
     * Will mark given [DamageFileArchiveIndicator] as archived.
     *
     * @param damageFileArchiveIndicator damage file archive indicator
     */
    fun archive(damageFileArchiveIndicator: DamageFileArchiveIndicator) {
        damageFileArchiveIndicator.archive()

        log.info("Marked damage file with key: [${damageFileArchiveIndicator.damageFileKey.value}] as archived.")
    }

    /**
     * Will reset given [DamageFileArchiveIndicator].
     *
     * @param damageFileArchiveIndicator damage file archive indicator
     */
    fun deleteArchiveIndicator(damageFileKey: DamageFileKey) {
        damageFileArchiveIndicatorRepository.deleteByDamageFileKey(damageFileKey)

        log.info("Deleted archive indicator for damage file with key: [${damageFileKey.value}].")
    }

    fun createAndSaveDamageFileArchiveIndicator(damageFileKey: DamageFileKey) {
        damageFileArchiveIndicatorRepository.save(DamageFileArchiveIndicator(damageFileKey = damageFileKey))
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileArchiveIndicatorService::class.java)
    }
}
