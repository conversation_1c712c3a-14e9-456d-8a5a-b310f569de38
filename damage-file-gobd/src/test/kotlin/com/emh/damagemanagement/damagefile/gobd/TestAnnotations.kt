package com.emh.damagemanagement.damagefile.gobd

import com.emh.damagemanagement.damagefile.TestCleanupProvider
import java.lang.annotation.Inherited
import org.junit.jupiter.api.Tag
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import org.springframework.transaction.annotation.Transactional

@Tag("integration")
@SpringBootTest(
    classes =
        [
            TestDamageFileGoBDApplication::class,
            TestVehiclesOutPortConfiguration::class,
            TestStorageOutPortConfiguration::class,
            TestCleanupProvider::class,
        ]
)
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
@Transactional
annotation class IntegrationTest
