package com.emh.damagemanagement.damagefile.gobd

import com.emh.damagemanagement.damagefile.gobd.application.port.StorageOutPort
import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPort
import org.mockito.kotlin.mock
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@SpringBootApplication(
    scanBasePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.damagefile.gobd.*",
            "com.emh.damagemanagement.damagefile.*",
        ]
)
@ConfigurationPropertiesScan(
    basePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.damagefile.gobd.*",
            "com.emh.damagemanagement.damagefile.*",
        ]
)
@EntityScan("com.emh.damagemanagement.damagefile.*")
@EnableJpaRepositories("com.emh.damagemanagement.damagefile.*")
class TestDamageFileGoBDApplication {

    fun main(args: Array<String>) {
        runApplication<TestDamageFileGoBDApplication>(*args)
    }
}

@TestConfiguration
class TestVehiclesOutPortConfiguration {
    @Bean @Primary fun vehicleOutPort(): VehiclesOutPort = mock()
}

@TestConfiguration
class TestStorageOutPortConfiguration {
    @Bean @Primary fun storageOutPort(): StorageOutPort = mock()
}
