package com.emh.damagemanagement.damagefile.gobd

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicator
import java.util.*
import kotlin.random.Random

/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
class DamageFileArchiveBuilder {
    private lateinit var damageFileKey: DamageFileKey
    private var archive: Boolean = false

    fun damageFileKey(damageFileKey: DamageFileKey) = apply { this.damageFileKey = damageFileKey }

    fun isArchived() = apply { this.archive = true }

    fun build(): DamageFileArchiveIndicator {
        return DamageFileArchiveIndicator(damageFileKey = damageFileKey)
    }

    companion object {
        fun buildSingle() = DamageFileArchiveBuilder().build()

        fun buildMultiple(count: Int): Set<DamageFileArchiveIndicator> =
            (1..count).map { DamageFileArchiveBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
