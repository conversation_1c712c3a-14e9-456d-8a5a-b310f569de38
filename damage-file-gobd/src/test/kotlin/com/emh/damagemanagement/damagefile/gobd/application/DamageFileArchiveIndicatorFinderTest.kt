package com.emh.damagemanagement.damagefile.gobd.application

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.gobd.DamageFileArchiveBuilder
import com.emh.damagemanagement.damagefile.gobd.IntegrationTest
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicatorRepository
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileArchiveIndicatorFinderTest {

    @Autowired private lateinit var entityManager: EntityManager
    @Autowired private lateinit var damageFileCreateOrUpdateUseCase: CreateOrUpdateDamageFileUseCase
    @Autowired private lateinit var damageFileArchiveIndicatorFinder: DamageFileArchiveIndicatorFinder
    @Autowired private lateinit var damageFileArchiveIndicatorRepository: DamageFileArchiveIndicatorRepository

    @Test
    fun `should return damage file archive indicator when archive is false`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()
        val damageFileArchive = DamageFileArchiveBuilder().damageFileKey(damageFile.key).build()
        damageFileArchiveIndicatorRepository.save(damageFileArchive)
        entityManager.flush()

        val damageFileArchiveIndicator = damageFileArchiveIndicatorFinder.findDamageFilesToArchive().single()

        assertThat(damageFileArchiveIndicator.damageFileKey).isEqualTo(damageFileArchive.damageFileKey)
    }

    @Test
    fun `should return empty list when all damage files have already been archived`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()
        val damageFileArchive = DamageFileArchiveBuilder().damageFileKey(damageFile.key).build()
        val damageFileArchiveEntity = damageFileArchiveIndicatorRepository.save(damageFileArchive)
        entityManager.flush()
        damageFileArchiveEntity.archive()

        val damageFileArchiveIndicator = damageFileArchiveIndicatorFinder.findDamageFilesToArchive()

        assertThat(damageFileArchiveIndicator).isEmpty()
    }
}
