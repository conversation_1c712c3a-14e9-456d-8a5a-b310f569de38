package com.emh.damagemanagement.damagefile.gobd.application

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFileReopenedEvent
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCloseService
import com.emh.damagemanagement.damagefile.gobd.DamageFileArchiveBuilder
import com.emh.damagemanagement.damagefile.gobd.IntegrationTest
import com.emh.damagemanagement.damagefile.gobd.application.port.StorageOutPort
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicatorNotFoundException
import com.emh.damagemanagement.damagefile.gobd.domain.DamageFileArchiveIndicatorRepository
import com.emh.damagemanagement.damagefile.gobd.domain.service.DamageFileArchiveIndicatorService
import com.emh.shared.s3.client.adapter.S3StorageClientException
import jakarta.persistence.EntityManager
import java.time.OffsetDateTime
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.ApplicationEventPublisher
import org.springframework.test.annotation.Rollback
import org.springframework.test.context.transaction.TestTransaction

@IntegrationTest
class DamageFileArchiveServiceTest {

    @Autowired private lateinit var damageFileCloseService: DamageFileCloseService
    @Autowired private lateinit var storageOutPort: StorageOutPort
    @Autowired private lateinit var damageFileCreateOrUpdateUseCase: CreateOrUpdateDamageFileUseCase
    @Autowired private lateinit var damageFileArchiveIndicatorFinder: DamageFileArchiveIndicatorFinder
    @Autowired private lateinit var damageFileArchiveIndicatorRepository: DamageFileArchiveIndicatorRepository
    @Autowired private lateinit var damageFileArchiveService: DamageFileArchiveService
    @Autowired private lateinit var damageFileIndicatorArchiveService: DamageFileArchiveIndicatorService
    @Autowired private lateinit var entityManager: EntityManager
    @Autowired @Qualifier("damage-file-cleanup") private lateinit var cleanupDamageFiles: () -> Unit
    @Autowired private lateinit var applicationEventPublisher: ApplicationEventPublisher

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(storageOutPort)
    }

    @Test
    fun `should set DamageFileArchiveIndicator to archived`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        val damageFileArchive = DamageFileArchiveBuilder().damageFileKey(damageFile.key).build()
        damageFileIndicatorArchiveService.createAndSaveDamageFileArchiveIndicator(damageFileArchive.damageFileKey)
        damageFileArchiveService.archiveDamageFiles()

        val uploadFile = argumentCaptor<String>()

        verify(storageOutPort).uploadFile(uploadFile.capture(), any())

        val damageFileArchiveStatus =
            damageFileArchiveIndicatorFinder.getDamageFileArchiveIndicator(damageFileArchive.damageFileKey)

        assertThat(uploadFile.firstValue).isEqualTo("${damageFile.key.value}.json")
        assertThat(damageFileArchiveStatus.isArchived).isEqualTo(true)
    }

    @Test
    fun `should set multiple DamageFileArchiveIndicators to archived`() {
        val damageFilesNew = DamageFileNewOrUpdateBuilder.buildMultiple(5)
        val damageFiles = damageFilesNew.map { damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(it) }
        val damageFilesArchive = damageFiles.map { DamageFileArchiveBuilder().damageFileKey(it.key).build() }
        damageFilesArchive.map { damageFileArchiveIndicatorRepository.save(it) }

        damageFileArchiveService.archiveDamageFiles()

        val uploadFilesArgumentCapture = argumentCaptor<String>()
        verify(storageOutPort, times(5)).uploadFile(uploadFilesArgumentCapture.capture(), any())

        val damageFileArchiveStatus =
            damageFileArchiveIndicatorFinder.findDamageFilesByKeys(damageFilesArchive.map { it.damageFileKey })

        assertThat(damageFileArchiveStatus).allSatisfy { assertThat(it.isArchived).isEqualTo(true) }
        assertThat(uploadFilesArgumentCapture.allValues)
            .containsExactlyInAnyOrderElementsOf(damageFiles.map { "${it.key.value}.json" })
    }

    @Test
    fun `should not upload multiple damage files if already archived`() {
        val damageFilesNew = DamageFileNewOrUpdateBuilder.buildMultiple(5)
        val totalDamageFiles = damageFilesNew.map { damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(it) }
        val damageFilesToBeArchived = totalDamageFiles.subList(0, 3)
        damageFilesToBeArchived
            .map { DamageFileArchiveBuilder().damageFileKey(it.key).build() }
            .map { damageFileArchiveIndicatorRepository.save(it) }
        val damageFilesAlreadyArchived =
            totalDamageFiles
                .subList(3, 5)
                .map { DamageFileArchiveBuilder().damageFileKey(it.key).build() }
                .map { damageFileArchiveIndicatorRepository.save(it) }

        damageFilesAlreadyArchived.map { damageFileIndicatorArchiveService.archive(it) }

        damageFileArchiveService.archiveDamageFiles()

        val uploadFile = argumentCaptor<String>()
        verify(storageOutPort, times(3)).uploadFile(uploadFile.capture(), any())

        val damageFileArchiveStatus =
            damageFileArchiveIndicatorFinder.findDamageFilesByKeys(damageFilesToBeArchived.map { it.key })

        assertThat(damageFileArchiveStatus).allSatisfy { assertThat(it.isArchived).isEqualTo(true) }
        assertThat(uploadFile.allValues)
            .containsExactlyInAnyOrderElementsOf(damageFilesToBeArchived.map { "${it.key.value}.json" })
    }

    @Test
    fun `should not upload the damage file if already archived`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        val damageFileArchiveIndicator = DamageFileArchiveBuilder().damageFileKey(damageFile.key).build()
        val damageFileArchived = damageFileArchiveIndicatorRepository.save(damageFileArchiveIndicator)
        damageFileIndicatorArchiveService.archive(damageFileArchived)
        entityManager.flush()

        damageFileArchiveService.archiveDamageFiles()

        verify(storageOutPort, never()).uploadFile(any(), any())
    }

    @Test
    fun `should listen to event and save to archive indicator table`() {
        val damageFile = DamageFileNewOrUpdateBuilder().build()
        val savedDamagedFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFile)
        damageFileCloseService.closeDamageFile(savedDamagedFile)
        val archive = damageFileArchiveIndicatorFinder.getDamageFileArchiveIndicator(savedDamagedFile.key)
        assertThat(archive.damageFileKey).isEqualTo(savedDamagedFile.key)
        assertThat(archive.isArchived).isEqualTo(false)
    }

    @Test
    @Rollback(false)
    fun `should not rollback entire transaction if upload to S3 fails for one damage file`() {
        val damageFilesNew = DamageFileNewOrUpdateBuilder.buildMultiple(2)
        val damageFiles = damageFilesNew.map { damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(it) }
        val damageFileArchiveIndicator = damageFiles.map { DamageFileArchiveBuilder().damageFileKey(it.key).build() }

        val damageFilesArchived = damageFileArchiveIndicator.map { damageFileArchiveIndicatorRepository.save(it) }
        val successfulDamageFile = damageFilesArchived[0]
        val failedDamageFile = damageFilesArchived[1]

        `when`(
                storageOutPort.uploadFile(
                    argThat { key -> key == "${failedDamageFile.damageFileKey.value}.json" },
                    any(),
                )
            )
            .thenThrow(S3StorageClientException("s3 upload failed"))
        TestTransaction.end()

        TestTransaction.start()
        assertThatNoException().isThrownBy { damageFileArchiveService.archiveDamageFiles() }
        assertThatNoException().isThrownBy { TestTransaction.end() }

        val archiveFailed =
            damageFileArchiveIndicatorFinder.getDamageFileArchiveIndicator(failedDamageFile.damageFileKey)
        val archiveSuccessful =
            damageFileArchiveIndicatorFinder.getDamageFileArchiveIndicator(successfulDamageFile.damageFileKey)
        assertThat(archiveFailed.isArchived).isEqualTo(false)
        assertThat(archiveSuccessful.isArchived).isEqualTo(true)

        // clean up DB
        cleanupDamageFiles()
    }

    @Test
    fun `should delete archive indicator on DamageFileReopenedEvent`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        damageFileCloseService.closeDamageFile(damageFile)
        val damageFileArchiveIndicatorFromFinder =
            damageFileArchiveIndicatorFinder.getDamageFileArchiveIndicator(damageFile.key)
        damageFileIndicatorArchiveService.archive(damageFileArchiveIndicatorFromFinder)
        entityManager.flush()

        applicationEventPublisher.publishEvent(
            DamageFileReopenedEvent(
                damageFileKey = damageFile.key,
                occurredOn = OffsetDateTime.now(),
                externalReference = null,
            )
        )

        assertThatExceptionOfType(DamageFileArchiveIndicatorNotFoundException::class.java).isThrownBy {
            damageFileArchiveIndicatorFinder.getDamageFileArchiveIndicator(damageFile.key)
        }
    }
}
