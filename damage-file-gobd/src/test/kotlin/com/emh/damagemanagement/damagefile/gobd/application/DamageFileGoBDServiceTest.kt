package com.emh.damagemanagement.damagefile.gobd.application

import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.VehicleBuilder
import com.emh.damagemanagement.damagefile.application.port.AnonymizeDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.DeleteDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.FindDamageFileWithClosedUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.gobd.DamageFileGoBDException
import com.emh.damagemanagement.damagefile.gobd.IntegrationTest
import com.emh.damagemanagement.damagefile.gobd.application.port.VehicleScrappedOrSold
import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPort
import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPortException
import java.time.OffsetDateTime
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.times
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [DamageFileGoBDServiceTest.TestConfiguration::class])
class DamageFileGoBDServiceTest {

    @Autowired private lateinit var damageFileGoBDService: DamageFileGoBDService
    @Autowired private lateinit var deleteDamageFileUseCase: DeleteDamageFileUseCase
    @Autowired private lateinit var anonymizeDamageFileUseCase: AnonymizeDamageFileUseCase
    @Autowired private lateinit var findDamageFileWithClosedUseCase: FindDamageFileWithClosedUseCase
    @Autowired private lateinit var vehiclesOutPort: VehiclesOutPort

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(
            vehiclesOutPort,
            deleteDamageFileUseCase,
            findDamageFileWithClosedUseCase,
            anonymizeDamageFileUseCase,
        )
    }

    @Test
    fun `should delete all closed damage files with scrapped and sold vehicles before threshold date`() {
        val soldVehicle = VehicleBuilder().vin("sold-vin").build()
        val scrappedVehicle = VehicleBuilder().vin("scrapped-vin").build()
        val noOfDaysSinceSold: Long = 3
        val noOfDaysSinceScrapped: Long = 3
        val listOfVehicles =
            listOf(
                VehicleScrappedOrSold(
                    vin = soldVehicle.vin,
                    scrappedOrSoldDate = OffsetDateTime.now().minusDays(noOfDaysSinceSold),
                ),
                VehicleScrappedOrSold(
                    vin = scrappedVehicle.vin,
                    scrappedOrSoldDate = OffsetDateTime.now().minusDays(noOfDaysSinceScrapped),
                ),
            )
        val closedDamageFiles =
            listOf(
                DamageFileBuilder().vehicle(soldVehicle).build(),
                DamageFileBuilder().vehicle(scrappedVehicle).build(),
            )
        val deleteDamageFileKeysArgumentCaptor = argumentCaptor<List<DamageFileKey>>()
        val vinsArgumentCaptor = argumentCaptor<List<String>>()

        `when`(findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(any()))
            .thenReturn(closedDamageFiles)
        `when`(vehiclesOutPort.getVehicleScrappedOrSoldStatus(any())).thenReturn(listOfVehicles)

        damageFileGoBDService.deleteAndAnonymizeDamageFiles()

        verify(anonymizeDamageFileUseCase, never()).anonymizeDamageFiles(any())
        verify(deleteDamageFileUseCase, times(1)).deleteDamageFiles(deleteDamageFileKeysArgumentCaptor.capture())
        verify(vehiclesOutPort, times(1)).getVehicleScrappedOrSoldStatus(vinsArgumentCaptor.capture())

        assertThat(deleteDamageFileKeysArgumentCaptor.firstValue.size).isEqualTo(2)
        assertThat(deleteDamageFileKeysArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(closedDamageFiles.map { it.key })
        assertThat(vinsArgumentCaptor.firstValue.size).isEqualTo(2)
        assertThat(vinsArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(listOf(soldVehicle.vin, scrappedVehicle.vin))
    }

    @Test
    fun `should anonymize files with scrapped and sold vehicles after threshold date`() {
        val scrappedVehicle = VehicleBuilder().vin("scrapped-vin").build()
        val soldVehicle = VehicleBuilder().vin("sold-vin").build()
        val noOfDaysSinceScrappedOrSold: Long = 1
        val listOfVehicles =
            listOf(
                VehicleScrappedOrSold(
                    vin = scrappedVehicle.vin,
                    scrappedOrSoldDate = OffsetDateTime.now().minusDays(noOfDaysSinceScrappedOrSold),
                ),
                VehicleScrappedOrSold(
                    vin = soldVehicle.vin,
                    scrappedOrSoldDate = OffsetDateTime.now().minusDays(noOfDaysSinceScrappedOrSold),
                ),
            )
        val closedDamageFiles =
            listOf(
                DamageFileBuilder().vehicle(scrappedVehicle).build(),
                DamageFileBuilder().vehicle(soldVehicle).build(),
            )
        val anonymizeDamageFileKeysArgumentCaptor = argumentCaptor<List<DamageFileKey>>()

        `when`(findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(any()))
            .thenReturn(closedDamageFiles)
        `when`(vehiclesOutPort.getVehicleScrappedOrSoldStatus(any())).thenReturn(listOfVehicles)

        damageFileGoBDService.deleteAndAnonymizeDamageFiles()

        verify(anonymizeDamageFileUseCase, times(1))
            .anonymizeDamageFiles(anonymizeDamageFileKeysArgumentCaptor.capture())
        verify(deleteDamageFileUseCase, never()).deleteDamageFiles(any())
        verify(vehiclesOutPort, times(1)).getVehicleScrappedOrSoldStatus(any())
        assertThat(anonymizeDamageFileKeysArgumentCaptor.firstValue.size).isEqualTo(2)
        assertThat(anonymizeDamageFileKeysArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(closedDamageFiles.map { it.key })
    }

    @Test
    fun `should delete damage files with missing and sold vehicles and anonymize with vehicle in use`() {
        val vehicleInUse = VehicleBuilder().vin("vin-in-use").build()
        val soldVehicle = VehicleBuilder().vin("sold-vin").build()
        val missingVehicle = VehicleBuilder().vin("missing-vin").build()

        val vehicleInUseStatus = VehicleScrappedOrSold(vin = vehicleInUse.vin, scrappedOrSoldDate = null)
        val soldVehicleStatus =
            VehicleScrappedOrSold(vin = soldVehicle.vin, scrappedOrSoldDate = OffsetDateTime.now().minusDays(3))
        val listOfVehicles = listOf(vehicleInUseStatus, soldVehicleStatus)

        val damageFileForSoldVin = DamageFileBuilder().vehicle(soldVehicle).build()
        val damageFileForVinInUse = DamageFileBuilder().vehicle(vehicleInUse).build()
        val damageFileForMissingVin = DamageFileBuilder().vehicle(missingVehicle).build()

        val closedDamageFiles = listOf(damageFileForSoldVin, damageFileForVinInUse, damageFileForMissingVin)

        val deleteDamageFileKeysArgumentCaptor = argumentCaptor<List<DamageFileKey>>()
        val anonymizeDamageFilesArgumentCaptor = argumentCaptor<List<DamageFileKey>>()

        `when`(findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(any()))
            .thenReturn(closedDamageFiles)
        `when`(vehiclesOutPort.getVehicleScrappedOrSoldStatus(any())).thenReturn(listOfVehicles)

        damageFileGoBDService.deleteAndAnonymizeDamageFiles()

        verify(anonymizeDamageFileUseCase, times(1)).anonymizeDamageFiles(anonymizeDamageFilesArgumentCaptor.capture())
        verify(deleteDamageFileUseCase, times(1)).deleteDamageFiles(deleteDamageFileKeysArgumentCaptor.capture())
        assertThat(deleteDamageFileKeysArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(listOf(damageFileForSoldVin.key, damageFileForMissingVin.key))
        assertThat(anonymizeDamageFilesArgumentCaptor.firstValue).contains(damageFileForVinInUse.key)
        verify(vehiclesOutPort, times(1)).getVehicleScrappedOrSoldStatus(any())
    }

    @Test
    fun `should delete damage files if vehicle service does not return any vehicles`() {
        val aVehicle = VehicleBuilder().build()
        val anotherVehicle = VehicleBuilder().build()
        val listOfVehicles: List<VehicleScrappedOrSold> = emptyList()
        val closedDamageFiles =
            listOf(DamageFileBuilder().vehicle(aVehicle).build(), DamageFileBuilder().vehicle(anotherVehicle).build())

        `when`(findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(any()))
            .thenReturn(closedDamageFiles)
        `when`(vehiclesOutPort.getVehicleScrappedOrSoldStatus(any())).thenReturn(listOfVehicles)

        damageFileGoBDService.deleteAndAnonymizeDamageFiles()

        verify(anonymizeDamageFileUseCase, never()).anonymizeDamageFiles(any())
        verify(deleteDamageFileUseCase, times(1)).deleteDamageFiles(any())
        verify(vehiclesOutPort, times(1)).getVehicleScrappedOrSoldStatus(any())
    }

    @Test
    fun `should do nothing if no closed damage files are present`() {
        val closedDamageFiles: List<DamageFile> = emptyList()

        `when`(findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(any()))
            .thenReturn(closedDamageFiles)
        damageFileGoBDService.deleteAndAnonymizeDamageFiles()

        verify(anonymizeDamageFileUseCase, never()).anonymizeDamageFiles(any())
        verify(deleteDamageFileUseCase, never()).deleteDamageFiles(any())
        verify(vehiclesOutPort, never()).getVehicleScrappedOrSoldStatus(any())
    }

    @Test
    fun `should delete damage files for sold vehicles and vehicles which could not be found`() {
        val closedDamageFiles = (1..10).map { DamageFileBuilder.buildSingle() }
        val damageFilesForMissingVehicles = (1..10).map { DamageFileBuilder.buildSingle() }
        val (statusInUse, scrappedOrSold) =
            closedDamageFiles
                .map {
                    VehicleScrappedOrSold(
                        vin = it.vehicle.vin,
                        scrappedOrSoldDate = if (Random.nextBoolean()) OffsetDateTime.now().minusDays(356) else null,
                    )
                }
                .partition { null == it.scrappedOrSoldDate }

        val deleteDamageFileKeysArgumentCaptor = argumentCaptor<List<DamageFileKey>>()
        `when`(findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(any()))
            .thenReturn(closedDamageFiles + damageFilesForMissingVehicles)
        `when`(vehiclesOutPort.getVehicleScrappedOrSoldStatus(any())).thenReturn(statusInUse + scrappedOrSold)

        damageFileGoBDService.deleteAndAnonymizeDamageFiles()

        val closedDamageFilesWithScrappedOrSoldVehicles =
            closedDamageFiles.filter { damageFile -> scrappedOrSold.any { damageFile.vehicle.vin == it.vin } }
        verify(deleteDamageFileUseCase, times(1)).deleteDamageFiles(deleteDamageFileKeysArgumentCaptor.capture())
        assertThat(deleteDamageFileKeysArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(
                closedDamageFilesWithScrappedOrSoldVehicles.map { it.key } +
                    damageFilesForMissingVehicles.map { it.key }
            )
    }

    @Test
    fun `should throw DamageFileGoBDException when fetching scrapped or sold fails with VehiclesOutPortException`() {
        val closedDamageFiles = listOf(DamageFileBuilder.buildSingle())

        `when`(findDamageFileWithClosedUseCase.findClosedDamageFilesWithClosedDateBefore(any()))
            .thenReturn(closedDamageFiles)
        `when`(vehiclesOutPort.getVehicleScrappedOrSoldStatus(any())).thenThrow(VehiclesOutPortException("5xx", null))

        assertThatExceptionOfType(DamageFileGoBDException::class.java).isThrownBy {
            damageFileGoBDService.deleteAndAnonymizeDamageFiles()
        }
    }

    internal class TestConfiguration {
        @Bean @Primary fun deleteDamageFileUseCase(): DeleteDamageFileUseCase = mock()

        @Bean @Primary fun anonymizeDamageFileUseCase(): AnonymizeDamageFileUseCase = mock()

        @Bean @Primary fun findDamageFileWithClosedUseCase(): FindDamageFileWithClosedUseCase = mock()
    }
}
