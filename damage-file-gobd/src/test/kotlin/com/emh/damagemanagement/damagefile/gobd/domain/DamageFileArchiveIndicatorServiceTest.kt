/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.domain

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.gobd.DamageFileArchiveBuilder
import com.emh.damagemanagement.damagefile.gobd.IntegrationTest
import com.emh.damagemanagement.damagefile.gobd.domain.service.DamageFileArchiveIndicatorService
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileArchiveIndicatorServiceTest {

    @Autowired private lateinit var repository: DamageFileArchiveIndicatorRepository

    @Autowired private lateinit var damageFileCreateOrUpdateUseCase: CreateOrUpdateDamageFileUseCase
    @Autowired private lateinit var damageFileArchiveIndicatorService: DamageFileArchiveIndicatorService

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should delete DamageFileArchiveIndicator by damage file key`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()
        val damageFileArchiveIndicator = DamageFileArchiveBuilder().damageFileKey(damageFile.key).build()
        repository.save(damageFileArchiveIndicator)
        entityManager.flush()

        damageFileArchiveIndicatorService.deleteArchiveIndicator(damageFile.key)
        entityManager.flush()

        val damageFileArchiveIndicatorFromRepository = repository.findByDamageFileKey(damageFile.key)
        assertThat(damageFileArchiveIndicatorFromRepository).isNull()
    }
}
