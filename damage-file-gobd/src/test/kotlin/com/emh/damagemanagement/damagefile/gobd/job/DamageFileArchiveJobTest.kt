/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.job

import com.emh.damagemanagement.damagefile.gobd.application.DamageFileArchiveService
import com.emh.damagemanagement.damagefile.gobd.application.job.DamageFileArchiveJob
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify

class DamageFileArchiveJobTest {

    private val damageFileArchiveService: DamageFileArchiveService = mock()
    private val damageFileArchiveJob: DamageFileArchiveJob =
        DamageFileArchiveJob(damageFileArchiveService = damageFileArchiveService)

    @Test
    fun `should archive damage files when scheduler is called`() {

        damageFileArchiveJob.execute(null)
        verify(damageFileArchiveService).archiveDamageFiles()
    }
}
