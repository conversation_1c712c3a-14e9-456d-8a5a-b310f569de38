/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.gobd.job

import com.emh.damagemanagement.damagefile.gobd.application.DamageFileGoBDService
import com.emh.damagemanagement.damagefile.gobd.application.job.DamageFileGoBDJob
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify

class DamageFileGoBDJobTest {

    private val damageFileGoBDService: DamageFileGoBDService = mock()
    private val damageFileDeletionJob: DamageFileGoBDJob =
        DamageFileGoBDJob(damageFileGoBDService = damageFileGoBDService)

    @Test
    fun `should delete and anonymize damage files when scheduler is called`() {
        damageFileDeletionJob.execute(null)
        verify(damageFileGoBDService).deleteAndAnonymizeDamageFiles()
    }
}
