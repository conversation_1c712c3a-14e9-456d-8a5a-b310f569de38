package com.emh.damagemanagement.damagefile.adapter

import org.springframework.boot.context.properties.EnableConfigurationProperties
import org.springframework.context.annotation.ComponentScan
import org.springframework.context.annotation.Configuration
import org.springframework.scheduling.annotation.EnableAsync

@EnableAsync
@Configuration
@ComponentScan(
    basePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.damagefile.*",
            "com.emh.damagemanagement.outboxmessagerelay.*",
        ]
)
@EnableConfigurationProperties
class DamageFileKafkaOutAdapterModuleConfiguration
