package com.emh.damagemanagement.damagefile.adapter.out.kafka

import com.emh.damagemanagement.damagefile.application.port.NumberOfDamagesUseCase
import com.emh.damagemanagement.damagefile.application.port.ReadDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFileClosedEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileCreatedEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileDeleteEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageFileReopenedEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileState
import com.emh.damagemanagement.outboxmessagerelay.application.port.RelayEventUseCase
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import java.time.OffsetDateTime
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class NumberOfDamagesAdapter(
    private val numberOfDamagesUseCase: NumberOfDamagesUseCase,
    private val relayEventUseCase: RelayEventUseCase,
    private val readDamageFileUseCase: ReadDamageFileUseCase,
) {

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToDamageFileCreateEvent(event: DamageFileCreatedEvent) {
        processStateChangeEvent(event.damageFileKey)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToDamageFileClosedEvent(event: DamageFileClosedEvent) {
        processStateChangeEvent(event.damageFileKey)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToDamageFileReopenedEvent(event: DamageFileReopenedEvent) {
        processStateChangeEvent(event.damageFileKey)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToDamageFileDeleteEvent(event: DamageFileDeleteEvent) {
        processStateChangeEvent(event.damageFileKey)
    }

    private fun processStateChangeEvent(damageFileKey: DamageFileKey) {
        val damageFile = readDamageFileUseCase.getDamageFile(damageFileKey)
        val vin = damageFile.vehicle.vin
        val totalDamages = numberOfDamagesUseCase.countByVehicleVin(vin)
        val openDamages = numberOfDamagesUseCase.countByVehicleVinAndState(vin, DamageFileState.OPEN)
        val reopenDamages = numberOfDamagesUseCase.countByVehicleVinAndState(vin, DamageFileState.REOPENED)
        val numberOfDamagesUpdatedEvent =
            NumberOfDamagesUpdatedEvent(
                vin = vin,
                externalReference = damageFile.vehicle.externalReference,
                totalNumberOfDamages = totalDamages,
                numberOfOpenDamages = openDamages + reopenDamages,
                occurredAt = OffsetDateTime.now(),
            )
        log.info("Processing Number of Damages: $numberOfDamagesUpdatedEvent")
        relayEventUseCase.relayImmediately(
            OutboxEvent(
                payload = numberOfDamagesUpdatedEvent.toJsonNode(),
                outboxEventType = OutboxEventType.DAMAGE_FILE_COUNT,
            )
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(NumberOfDamagesAdapter::class.java)
    }
}

data class NumberOfDamagesUpdatedEvent(
    val vin: String,
    val externalReference: String,
    val totalNumberOfDamages: Int,
    val numberOfOpenDamages: Int,
    val id: UUID = UUID.randomUUID(),
    val occurredAt: OffsetDateTime,
)
