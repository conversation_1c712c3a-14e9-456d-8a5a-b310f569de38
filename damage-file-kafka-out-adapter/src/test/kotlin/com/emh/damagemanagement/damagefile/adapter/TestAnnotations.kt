package com.emh.damagemanagement.damagefile.adapter

import com.emh.damagemanagement.damagefile.TestCleanupProvider
import com.emh.damagemanagement.shared.WebTestClientConfiguration
import com.emh.damagemanagement.shared.security.SecurityConfiguration
import java.lang.annotation.Inherited
import org.junit.jupiter.api.Tag
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import org.springframework.transaction.annotation.Transactional

@Tag("integration")
@Transactional
@Suppress("SpringPropertySource")
@SpringBootTest(classes = [TestDamageFileKafkaOutAdapterConfiguration::class, TestCleanupProvider::class])
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
annotation class IntegrationTest

@Tag("integration")
@Transactional
@Suppress("SpringPropertySource")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes =
        [
            TestDamageFileKafkaOutAdapterConfiguration::class,
            WebTestClientConfiguration::class,
            SecurityConfiguration::class,
        ],
)
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
annotation class WebServiceTest
