package com.emh.damagemanagement.damagefile.adapter

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@SpringBootApplication(scanBasePackages = ["com.emh.damagemanagement.*"])
@ConfigurationPropertiesScan("com.emh.damagemanagement.*")
@EntityScan("com.emh.damagemanagement.*")
@EnableJpaRepositories("com.emh.damagemanagement.*")
class TestDamageFileKafkaOutAdapterConfiguration {

    fun main(args: Array<String>) {
        runApplication<TestDamageFileKafkaOutAdapterConfiguration>(*args)
    }
}
