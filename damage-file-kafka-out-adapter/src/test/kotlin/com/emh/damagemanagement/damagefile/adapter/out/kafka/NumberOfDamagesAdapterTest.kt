package com.emh.damagemanagement.damagefile.adapter.out.kafka

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.adapter.IntegrationTest
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateService
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.outboxmessagerelay.application.port.RelayEventUseCase
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import org.assertj.core.api.Assertions.assertThat
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.doNothing
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.springframework.test.context.transaction.TestTransaction

@IntegrationTest
class NumberOfDamagesAdapterTest {

    @Autowired private lateinit var numberOfDamagesUseCase: NumberOfDamagesAdapter

    @MockitoSpyBean private lateinit var relayEventUseCase: RelayEventUseCase

    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService

    @Autowired @Qualifier("damage-file-cleanup") private lateinit var cleanupDamageFiles: () -> Unit

    @AfterEach
    fun resetMocks() {
        cleanupDamageFiles()
    }

    @Test
    fun `should send damage file count event upon damage file state change`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder.buildSingle()
        doNothing().`when`(relayEventUseCase).relayImmediately(any())
        val outboxEventCaptor = argumentCaptor<OutboxEvent>()

        damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()
        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(relayEventUseCase, times(1)).relayImmediately(outboxEventCaptor.capture())
                assertThat(outboxEventCaptor.firstValue.outboxEventType).isEqualTo(OutboxEventType.DAMAGE_FILE_COUNT)
            }
    }
}
