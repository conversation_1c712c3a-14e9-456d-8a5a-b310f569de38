import com.diffplug.spotless.LineEnding

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    alias(libs.plugins.kotlin.jpa)
    idea
    jacoco
}

evaluationDependsOn(":shared")

val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output
val damageFileSrcSetsOutput = project(":damage-file").sourceSets.test.get().output

dependencies {
    api(libs.api.dms)

    implementation(project(":shared"))
    implementation(project(":repairfix-client"))
    implementation(project(":employee-client"))
    implementation(project(":damage-file"))
    implementation(libs.bundles.base)
    implementation(libs.bundles.webclient)

    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(files(damageFileSrcSetsOutput))
    testImplementation(libs.bundles.tests)
    testImplementation(libs.okhttp)
    testImplementation(libs.mockwebserver) {
        // we are forcing okhttp:5.0.0-alpha, as this is required by aws.sdk.kotlin
        exclude("com.squareup.okhttp3:okhttp")
    }
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

sourceSets {
    test { kotlin { kotlin.srcDir(sharedTestSrcSetsOutput) } }
    test { resources { resources.srcDir(project(":shared").sourceSets.test.get().resources) } }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }
