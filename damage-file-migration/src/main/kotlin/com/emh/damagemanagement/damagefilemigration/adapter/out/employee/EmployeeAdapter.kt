/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.damagefilemigration.adapter.out.employee

import com.emh.damagemanagement.damagefilemigration.application.Employee
import com.emh.damagemanagement.damagefilemigration.application.port.EmployeeOutPort
import com.emh.damagemanagement.employee.adapter.DistinctEmployeeNotFoundException
import com.emh.damagemanagement.employee.adapter.EmployeeException
import com.emh.damagemanagement.employee.adapter.EmployeeNotFoundException
import com.emh.damagemanagement.employee.generated.adapter.out.rest.EmployeeApi
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClientException
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component
class EmployeeAdapter(private val employeeApi: Employee<PERSON>pi) : EmployeeOutPort {

    override fun getEmployeesByEmail(emails: List<String>): Mono<List<Employee>> {
        return Flux.fromIterable(emails)
            .flatMap { email ->
                getDistinctEmployeeByCompanyOrPrivateEmail(email)
                    .map { it.toEmployee() }
                    .onErrorResume(EmployeeException::class.java) {
                        log.error("Error fetching employee details. ${it.message}. Skipping...")
                        Mono.empty()
                    }
            }
            .collectList()
    }

    private fun getDistinctEmployeeByCompanyOrPrivateEmail(email: String): Mono<EmployeeDto> {
        return employeeApi
            .filterEmployees(
                companyEmail = email,
                firstName = null,
                lastName = null,
                accountingArea = null,
                employeeNumber = null,
                privateEmail = null,
            )
            .flatMap { employees ->
                when {
                    /** user-service returns empty list when employee is not found */
                    employees.isEmpty() -> {
                        log.warn("Employee not found with company email: $email. Trying again with private email")
                        getDistinctEmployeeByPrivateEmail(email)
                    }
                    employees.size > 1 ->
                        Mono.error(DistinctEmployeeNotFoundException("Multiple Employees found with email: $email"))
                    else -> Mono.just(employees.first())
                }
            }
            .onErrorMap(WebClientException::class.java) {
                EmployeeException("Error during request for employee with email: $email, ${it.message}", it)
            }
    }

    private fun getDistinctEmployeeByPrivateEmail(email: String): Mono<EmployeeDto> {
        return employeeApi
            .filterEmployees(
                companyEmail = null,
                firstName = null,
                lastName = null,
                accountingArea = null,
                employeeNumber = null,
                privateEmail = email,
            )
            .flatMap { employees ->
                when {
                    /** user-service returns empty list when employee is not found */
                    employees.isEmpty() ->
                        Mono.error(EmployeeNotFoundException("Employee not found with private email: $email"))
                    employees.size > 1 ->
                        Mono.error(
                            DistinctEmployeeNotFoundException("Multiple Employees found with private email: $email")
                        )
                    else -> Mono.just(employees.first())
                }
            }
            .onErrorMap(WebClientException::class.java) {
                EmployeeException("Error during request for employee with private email: $email, ${it.message}", it)
            }
    }

    companion object {
        private val log = LoggerFactory.getLogger(EmployeeAdapter::class.java)
    }
}
