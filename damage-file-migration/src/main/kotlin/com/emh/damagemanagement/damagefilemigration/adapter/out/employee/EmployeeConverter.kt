/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.damagefilemigration.adapter.out.employee

import com.emh.damagemanagement.damagefilemigration.application.Employee
import com.emh.damagemanagement.damagefilemigration.application.EmployeeType
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeDto
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeTypeDto

fun EmployeeDto.toEmployee(): Employee =
    Employee(
        key = this.key,
        firstName = this.firstName,
        employeeNumber = this.employeeNumber,
        lastName = this.lastName,
        companyEmail = this.companyEmail,
        accountingArea = this.accountingArea,
        type = this.type.toEmployeeType(),
        privateEmail = this.privateEmail,
        employeeGroup = this.employeeGroup,
    )

private fun EmployeeTypeDto.toEmployeeType(): EmployeeType =
    when (this) {
        EmployeeTypeDto.SUBSIDIARY_EMPLOYEE -> EmployeeType.SUBSIDIARY_EMPLOYEE
        EmployeeTypeDto.PAG_EMPLOYEE -> EmployeeType.PAG_EMPLOYEE
    }
