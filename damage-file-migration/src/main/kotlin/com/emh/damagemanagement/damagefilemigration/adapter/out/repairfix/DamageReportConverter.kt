/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix

import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.Vehicle
import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigration
import com.emh.damagemanagement.damagefilemigration.application.DamageReportUpdate
import com.emh.damagemanagement.repairfix.adapter.DAMAGE_REPORT_CLOSED_STATUS
import com.emh.damagemanagement.repairfix.adapter.toExteriorDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AccidentThirdPartyInformation
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponseDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ExteriorDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.OperationResult
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ReporterInfo
import com.fasterxml.jackson.databind.ObjectMapper
import java.time.OffsetDateTime

fun DamageReportMigration.toDamageFileMigration(): DamageFileMigration {
    val parsedUsingCostCenter =
        this.car.insurances?.firstOrNull()?.insurerAddress?.replace(INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX, "")
    // there is a chance that we maintained "UsingCostCenter:null" values in repairfix
    val usingCostCenter = if (USING_COST_CENTER_NULL == parsedUsingCostCenter) null else parsedUsingCostCenter
    return DamageFileMigration(
        vehicle =
            Vehicle(
                vin = this.damageReportListItem.car.vin,
                externalReference = this.damageReportListItem.car.id,
                /**
                 * FPT1-1273 By making the vGuid optional for FVM --> DMS we now have the issue that we cannot map these
                 * vehicles without vGuid to damage files and cannot save them into the database. We purposely decided
                 * to leave the vGuid as a requirement in damage files because the PVCC api requires the vGUID. We first
                 * have to clarify what to do here.
                 */
                vGuid =
                    requireNotNull(this.damageReportListItem.car.carPartnerId) {
                        "VGUID (carPartnerId) is missing for  ${this.damageReportListItem.id}"
                    },
                licensePlate = this.damageReportListItem.car.licensePlate,
                /**
                 * FPT1-514 With the changes in fleetId for pool vehicles we lost access to leasingArt for leasing
                 * vehicles. This is a problem downstream for PVCC, as this information is required there. As
                 * VehicleService is not ready yet, we have to find a place within repairfix to store and later retrieve
                 * leasingArt value. We are misusing insuranceBroker for now, as no proper field is available for this
                 * purpose.
                 */
                leasingArt =
                    this.car.insurances?.firstOrNull()?.insuranceBroker?.replace(INSURANCE_BROKER_LEASING_PREFIX, "")
                        ?: "",
                costCenter = this.car.insurances?.firstOrNull()?.costCenter,
                usingCostCenter = usingCostCenter,
                fleetId =
                    requireNotNull(this.damageReportListItem.car.fleetPartnerId) {
                        "LeasingArt (FleetPartnerId) is missing for ${this.damageReportListItem.id}"
                    },
            ),
        externalCreationDate = OffsetDateTime.parse(this.damageReportListItem.createdAt),
        // used to identify pool or leasing vehicle damage file later
        isPool = this.damageReportListItem.car.isPool,
        /**
         * For leasing vehicles, employeeToBeCharged is the vehicle responsible person (known as driver in repairfix).
         * For pool vehicles, employeeToBeCharged should be the driver of the vehicle. Repair-fix can only provide
         * driver's email(known as reporter in repairfix) which is used later to get driver's employee-number from
         * user-service.
         */
        employeeToBeCharged =
            when (!this.damageReportListItem.car.isPool) {
                true ->
                    EmployeeNumber(
                        requireNotNull(
                            requireNotNull(this.damageReportListItem.car.driver) {
                                    "Driver information is missing for damage report with id [${this.damageReportListItem.id}]."
                                }
                                .partnerId
                        ) {
                            "Employee number (partnerId) is missing for given driver for damage report with id [${this.damageReportListItem.id}]."
                        }
                    )
                else -> null
            },
        driverEmail =
            when (this.damageReportListItem.car.isPool) {
                true ->
                    requireNotNull(
                        requireNotNull(this.damageReportListItem.reporter) {
                                "Reporter information is missing for damage report with id [${this.damageReportListItem.id}]."
                            }
                            .email
                    ) {
                        "Email is missing for given reporter for damage report with id [${this.damageReportListItem.id}]."
                    }
                else -> null
            },
        deductibles = this.invoiceData.deductibles,
        claimsTowardsEmployee = this.invoiceData.claimsTowardsEmployee,
        // used to re-identify the source damage report later and determine if we have to create or
        // update
        externalReference = this.damageReportListItem.id,
        insuranceCoverage = this.damageReportDetails.insuranceCoverage,
        isClosed = DAMAGE_REPORT_CLOSED_STATUS.equals(this.damageReportListItem.status, true),
        damageResponsibility = this.damageReportDetails.damageResponsibility,
        damageDate =
            when (this.damageReportDetails.type) {
                DamageReportResponseDamageReport.Type.ExteriorDamageReport ->
                    requireNotNull(this.damageReportDetails.damageDateTime) {
                        "Damage date time is missing for exterior damage report with id [${this.damageReportListItem.id}]."
                    }
                else -> OffsetDateTime.parse(this.damageReportListItem.createdAt)
            },
        isExternal =
            requireNotNull(this.damageReportListItem.reporter?.companyAffiliation) {
                    "Company affiliation information is missing for damage report with id [${this.damageReportListItem.id}]."
                }
                .let { ReporterInfo.CompanyAffiliation.external == it },
        reporterEmployeeNumber = this.damageReportListItem.reporter?.personalNumber?.let { EmployeeNumber(it) },
    )
}

fun DamageReportResponse.toDamageReportDetails(objectMapper: ObjectMapper): DamageReportDetails =
    when (this.damageReport.type) {
        DamageReportResponseDamageReport.Type.ExteriorDamageReport -> {
            val damageReport = this.toExteriorDamageReport(objectMapper)
            val insuranceCoverage = determineInsuranceCoverage(damageReport.insuranceCoverage)
            /**
             * FPT1-1162 damageResponsibility moved into accidentThirdPartyInformation, that is only available for
             * ExteriorDamageReports. From now, we will take damageResponsibility from accidentThirdPartyInformation if
             * available, if not or if we are dealing with a non-exterior damage we assume a driver is at fault as
             * default (Luisa).
             */
            val damageResponsibility =
                determineDamageResponsibility(
                    damageResponsibility = damageReport.accidentThirdPartyInformation.damageResponsibility
                )

            DamageReportDetails(
                insuranceCoverage = insuranceCoverage,
                damageResponsibility = damageResponsibility,
                damageDateTime = damageReport.damageDateTime,
                type = this.damageReport.type,
            )
        }
        else ->
            DamageReportDetails(
                insuranceCoverage = InsuranceCoverage.NOT_SPECIFIED,
                // FPT1-1162 non-exterior damages will now default to DRIVER_CAUSED_ACCIDENT (see
                // comment above)
                damageResponsibility = DamageResponsibility.DRIVER_CAUSED_ACCIDENT,
                // repairfix does not provide damageDate for non-exterior damages -> all of them
                // will fail further
                // processing
                damageDateTime = null,
                type = this.damageReport.type,
            )
    }

private fun determineDamageResponsibility(
    damageResponsibility: AccidentThirdPartyInformation.DamageResponsibility?
): DamageResponsibility =
    when (damageResponsibility) {
        // FPT1-1169 (Luisa): if no information is provided, assume the driver is at fault
        null -> DamageResponsibility.DRIVER_CAUSED_ACCIDENT
        AccidentThirdPartyInformation.DamageResponsibility.driver_caused_accident ->
            DamageResponsibility.DRIVER_CAUSED_ACCIDENT
        AccidentThirdPartyInformation.DamageResponsibility.other_party_caused_accident ->
            DamageResponsibility.OTHER_PARTY_CAUSED_ACCIDENT
        AccidentThirdPartyInformation.DamageResponsibility.both_caused_accident ->
            DamageResponsibility.BOTH_CAUSED_ACCIDENT
        AccidentThirdPartyInformation.DamageResponsibility.responsibility_is_not_clear ->
            DamageResponsibility.RESPONSIBILITY_IS_NOT_CLEAR
    }

private fun determineInsuranceCoverage(insuranceCoverage: ExteriorDamageReport.InsuranceCoverage?): InsuranceCoverage =
    when (insuranceCoverage) {
        null -> InsuranceCoverage.NOT_SPECIFIED
        ExteriorDamageReport.InsuranceCoverage.not_covered -> InsuranceCoverage.NOT_COVERED
        ExteriorDamageReport.InsuranceCoverage.unknown -> InsuranceCoverage.UNKNOWN
        ExteriorDamageReport.InsuranceCoverage.full -> InsuranceCoverage.FULL
        ExteriorDamageReport.InsuranceCoverage.partial -> InsuranceCoverage.PARTIAL
        ExteriorDamageReport.InsuranceCoverage.third_party -> InsuranceCoverage.THIRD_PARTY
    }

fun OperationResult.toDamageReportUpdateResponse(externalId: String): DamageReportUpdate =
    DamageReportUpdate(externalId, this.success)

/** Custom prefix to be able to use insuranceBroker field, which requires 3 or more characters */
private const val INSURANCE_BROKER_LEASING_PREFIX = "LeasingArt:"
private const val INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX = "UsingCostCenter:"
private const val USING_COST_CENTER_NULL = "null"
