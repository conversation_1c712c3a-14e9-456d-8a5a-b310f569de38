/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix

import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportListItem
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponseDamageReport
import java.time.OffsetDateTime

data class DamageReportMigration(
    val damageReportListItem: DamageReportListItem,
    val invoiceData: InvoiceData,
    val damageReportDetails: DamageReportDetails,
    val car: Car,
)

data class InvoiceData(
    val deductibles: Set<MonetaryAmount> = emptySet(),
    val claimsTowardsEmployee: Set<MonetaryAmount> = emptySet(),
)

data class Amount(val value: MonetaryAmount? = null)

data class DamageReportDetails(
    val insuranceCoverage: InsuranceCoverage,
    val damageResponsibility: DamageResponsibility,
    val damageDateTime: OffsetDateTime? = null,
    val type: DamageReportResponseDamageReport.Type,
)
