/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix

import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.RepairfixDamageReportAdapter.Companion.START_PAGE
import com.emh.damagemanagement.damagefilemigration.application.DamageReportUpdate
import com.emh.damagemanagement.damagefilemigration.application.InternalOrderNumberMigrationUpdateException
import com.emh.damagemanagement.damagefilemigration.application.port.DamageFileMigrationChunk
import com.emh.damagemanagement.damagefilemigration.application.port.DamageFileOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberMigrationOutPort
import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.adapter.config.RepairfixRequestsProperties
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.CarsApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.DamageReportsApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.InvoicesApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportUpdateDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Invoice
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.InvoicesList
import java.math.BigDecimal
import java.time.Duration
import java.time.LocalDate
import java.time.format.DateTimeParseException
import kotlin.jvm.optionals.getOrDefault
import kotlin.math.floor
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component
class RepairfixDamageReportAdapter(
    private val damageReportsApi: DamageReportsApi,
    private val invoicesApi: InvoicesApi,
    private val carsApi: CarsApi,
    @Qualifier("repairfix") private val objectMapperBuilder: Jackson2ObjectMapperBuilder,
    private val repairfixRequestsProperties: RepairfixRequestsProperties,
    private val repairFixDamageFileMigrationProperties: RepairFixDamageFileMigrationProperties,
) : DamageFileOutPort, InternalOrderNumberMigrationOutPort {
    override fun getDamageFileUpdates(): Flux<DamageFileMigrationChunk> {
        val totalNumberOfDamageReports = getTotalNumberOfDamageReports()
        if (0 == totalNumberOfDamageReports) return Flux.empty()

        val maxPageNumber = getMaxPageNumber(totalNumberOfDamageReports)

        return Flux.fromIterable(START_PAGE..maxPageNumber)
            .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
            .flatMapSequential { pageNumber -> getDamageReportsPage(pageNumber = pageNumber) }
            .map { damageReportMigration ->
                damageReportMigration.mapNotNull {
                    try {
                        it.toDamageFileMigration()
                    } catch (exception: IllegalArgumentException) {
                        // skip damage reports that fail domain validation (like damage reports
                        // with cars without vGuid)
                        log.warn(
                            "Could not convert damage report to damage file due to constraint error {}. Skipping...",
                            exception.message,
                        )
                        log.trace(
                            "Could not convert damage report to damage file due to constraint error. Skipping...",
                            exception,
                        )
                        null
                    } catch (exception: DateTimeParseException) {
                        log.warn(
                            "Could not convert damage report to damage file due to date-time parse error. Skipping...",
                            exception,
                        )
                        null
                    }
                }
            }
    }

    override fun updateInternalOrderNumber(
        damageFileExternalId: String,
        damageFileInternalOrderNumber: String,
    ): DamageReportUpdate {
        val damageReportUpdateDto = DamageReportUpdateDto(damageFileInternalOrderNumber)
        return damageReportsApi
            .porscheDamageReportUpdateControllerUpdate(damageFileExternalId, damageReportUpdateDto)
            .map { it.toDamageReportUpdateResponse(damageFileExternalId) }
            .onErrorMap(RepairfixException::class.java) {
                InternalOrderNumberMigrationUpdateException("Could not update damage report externalId", it)
            }
            .blockOptional()
            .getOrDefault(DamageReportUpdate(damageFileExternalId, false))
    }

    private fun findInvoicesForDamageReport(damageReportId: String): Mono<InvoicesList> {
        return invoicesApi.porscheInvoicesControllerInvoicesList(damageReportId, null).onErrorResume(
            RepairfixException::class.java
        ) {
            log.error("Could not find invoices for the damage report Id: $damageReportId. ${it.message}", it)
            Mono.empty()
        }
    }

    /**
     * fetches deductible and claimTowardsEmployee for a damageReportId, will return null as value if the invoice type
     * is not returned by repair-fix
     *
     * @param damageReportId the damage report id
     */
    private fun findDeductibleAndClaimTowardsEmployee(damageReportId: String): Mono<InvoiceData> {

        val listOfInvoices = findInvoicesForDamageReport(damageReportId)

        val deductibles = findCostByInvoiceType(listOfInvoices, Invoice.Type.deductible)

        val claimsTowardsEmployee = findCostByInvoiceType(listOfInvoices, Invoice.Type.claimed_from_employee)

        return Mono.zip(deductibles, claimsTowardsEmployee).map { deductibleAndClaims ->
            InvoiceData(
                deductibles = deductibleAndClaims.t1.mapNotNull { it.value }.toSet(),
                claimsTowardsEmployee = deductibleAndClaims.t2.mapNotNull { it.value }.toSet(),
            )
        }
    }

    private fun findCostByInvoiceType(listOfInvoices: Mono<InvoicesList>, type: Invoice.Type) =
        listOfInvoices.map {
            it.invoices
                .filter { invoice -> invoice.type == type }
                .map { invoice -> Amount(value = MonetaryAmount.of(invoice.costsNet.abs())) }
        }

    private fun findDamageReport(damageReportId: String): Mono<DamageReportResponse> {
        return damageReportsApi.porscheDamageReportControllerDamageReportById(damageReportId).onErrorResume(
            RepairfixException::class.java
        ) {
            log.error("Could not find damage report details with Id: $damageReportId. ${it.message}", it)
            Mono.empty()
        }
    }

    /**
     * Will return damage report details for a single damage report based on type
     *
     * @param damageReportId the damage report id
     */
    private fun findDamageReportDetails(damageReportId: String): Mono<DamageReportDetails> =
        findDamageReport(damageReportId)
            .map { it.toDamageReportDetails(objectMapper = objectMapperBuilder.build()) }
            .onErrorResume(IllegalArgumentException::class.java) {
                log.error("Could not map damage report Id: $damageReportId. ${it.message}")
                Mono.empty()
            }

    /**
     * Will return a single page of damage reports sorted by created-timestamp ascending.
     *
     * @param pageNumber the number of the page to be fetched
     */
    private fun getDamageReportsPage(pageNumber: Int): Mono<List<DamageReportMigration>> =
        damageReportsApi
            .porscheDamageReportsControllerList(
                limit = BigDecimal.valueOf(repairfixRequestsProperties.pageSize.toLong()),
                page = BigDecimal.valueOf(pageNumber.toLong()),
                vin = null,
                carPartnerId = null,
                status = null,
                fleetPartnerId = null,
                reportPartnerData = null,
                sortOrder = DamageReportsApi.SortOrderPorscheDamageReportsControllerList.ASC,
                sortBy = DamageReportsApi.SortByPorscheDamageReportsControllerList.Created,
                tag = null,
                fromDate = getFromDate(),
                toDate = LocalDate.now().toString(),
                closeReason = null,
                dateFilterField = DamageReportsApi.DateFilterFieldPorscheDamageReportsControllerList.updatedAt,
                type = null,
            )
            .map { it.damageReports }
            .flatMapIterable { damageReports -> damageReports }
            .flatMap { damageReport ->
                val invoiceData = findDeductibleAndClaimTowardsEmployee(damageReport.id)
                val car = getCar(damageReport.car.id)
                val damageReportDetails = findDamageReportDetails(damageReport.id)

                Mono.zip(Mono.just(damageReport), invoiceData, car, damageReportDetails)
            }
            .map {
                DamageReportMigration(
                    damageReportListItem = it.t1,
                    invoiceData = it.t2,
                    car = it.t3,
                    damageReportDetails = it.t4,
                )
            }
            .collectList()
            .onErrorMap(RepairfixException::class.java) {
                if (it.type == RepairfixException.Type.BUSINESS) log.warn("Error while fetching cars. ${it.message}")
                it.toDamageFileMigrationException()
            }

    /** Fetches the total number of cars for given filters. */
    private fun getTotalNumberOfDamageReports(): Int {
        return damageReportsApi
            .porscheDamageReportsControllerList(
                // we do not care about the result and use 1 to minimize the return payload
                // (also note, that using 0 work like 'unlimited')
                limit = BigDecimal.valueOf(1L),
                page = BigDecimal.valueOf(START_PAGE.toLong()),
                vin = null,
                carPartnerId = null,
                status = null,
                fleetPartnerId = null,
                reportPartnerData = null,
                sortOrder = DamageReportsApi.SortOrderPorscheDamageReportsControllerList.ASC,
                sortBy = DamageReportsApi.SortByPorscheDamageReportsControllerList.Created,
                tag = null,
                fromDate = getFromDate(),
                toDate = LocalDate.now().toString(),
                closeReason = null,
                dateFilterField = DamageReportsApi.DateFilterFieldPorscheDamageReportsControllerList.updatedAt,
                type = null,
            )
            .map { damageReportsList -> damageReportsList.total.toInt() }
            .onErrorMap(RepairfixException::class.java) {
                log.warn("Error while trying to get total number of damage reports. ${it.message}")
                it.toDamageFileMigrationException()
            }
            .block() ?: 0
    }

    private fun getCar(id: String): Mono<Car> {
        return carsApi
            .porscheCarsControllerGetCar(id)
            .map { it.car }
            .onErrorResume(RepairfixException::class.java) {
                log.error("Error while fetching car with id [$id].", it)
                Mono.empty()
            }
    }

    /**
     * Returns the max page number for given count of elements, [DEFAULT_PAGE_SIZE] and [START_PAGE] Will NOT work if '1
     * !=[START_PAGE]'.
     */
    private fun getMaxPageNumber(count: Int): Int {
        val offset = if (0 == count.mod(repairfixRequestsProperties.pageSize)) 0 else START_PAGE

        return floor(count.toDouble().div(repairfixRequestsProperties.pageSize.toDouble())).toInt() + offset
    }

    /**
     * Returns the fromDate depending on the current date and the threshold. If the difference between the current date
     * and fromDate is greater than the threshold we use fromDate as current date minus the threshold in days. If not we
     * use the configured fromDate. This is done to avoid fetching damage file migrations before the go live date.
     */
    private fun getFromDate(): String {
        val toDate = LocalDate.now()

        val fromDate = LocalDate.parse(repairFixDamageFileMigrationProperties.fromDate)
        val newFromDate =
            if (toDate > fromDate.plusDays(repairFixDamageFileMigrationProperties.thresholdInDays)) {
                toDate.minusDays(repairFixDamageFileMigrationProperties.thresholdInDays).toString()
            } else {
                fromDate.toString()
            }
        // Change to debug level after testing
        log.info(
            "Calculated newFromDate for damage file migration is: $newFromDate. Threshold used was: ${repairFixDamageFileMigrationProperties.thresholdInDays}"
        )
        return newFromDate
    }

    companion object {
        // be aware pages are starting with 1, changing this also requires to refactor
        // [getMaxPageNumber]
        private const val START_PAGE = 1
        private val log = LoggerFactory.getLogger(RepairfixDamageReportAdapter::class.java)
    }
}

@ConfigurationProperties("repairfix.damage-file-migration")
data class RepairFixDamageFileMigrationProperties(val fromDate: String, val thresholdInDays: Long)
