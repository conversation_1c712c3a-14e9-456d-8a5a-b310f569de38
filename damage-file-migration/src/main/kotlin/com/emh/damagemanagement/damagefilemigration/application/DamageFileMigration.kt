/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.damagefilemigration.application

import com.emh.damagemanagement.damagefile.domain.DamageReport
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.Vehicle
import com.emh.damagemanagement.damagefile.domain.service.DamageFileNewOrUpdate
import java.time.OffsetDateTime

data class DamageFileMigration(
    val vehicle: Vehicle,
    val externalCreationDate: OffsetDateTime? = null,
    var employeeToBeCharged: EmployeeNumber? = null,
    val deductibles: Set<MonetaryAmount> = emptySet(),
    val claimsTowardsEmployee: Set<MonetaryAmount> = emptySet(),
    val externalReference: String? = null,
    val insuranceCoverage: InsuranceCoverage,
    val isClosed: Boolean,
    val isPool: Boolean,
    val driverEmail: String? = null,
    val damageResponsibility: DamageResponsibility,
    val damageDate: OffsetDateTime,
    val isExternal: Boolean,
    /**
     * Employee number provided by repairfix, if entered during damage report creation. Do not use it as
     * employeeToBeCharged information (this is set based on separate business logic). We just keep this (redundant)
     * information for some edge case verification processes.
     */
    val reporterEmployeeNumber: EmployeeNumber?,
)

fun DamageFileMigration.toDamageFileNewOrUpdate(): DamageFileNewOrUpdate =
    DamageFileNewOrUpdate(
        damageReport = DamageReport(),
        vehicle =
            Vehicle(
                vin = this.vehicle.vin,
                vGuid = this.vehicle.vGuid,
                licensePlate = this.vehicle.licensePlate,
                leasingArt = this.vehicle.leasingArt,
                costCenter = this.vehicle.costCenter,
                usingCostCenter = this.vehicle.usingCostCenter,
                fleetId = this.vehicle.fleetId,
                externalReference = this.vehicle.externalReference,
            ),
        // FPT1-1045 sum all claims
        claimTowardsEmployee = this.claimsTowardsEmployee.sumOf { it.value }.let { MonetaryAmount(it) },
        // FPT1-1045 sum all deductibles
        deductible = this.deductibles.sumOf { it.value }.let { MonetaryAmount(it) },
        externalReference = this.externalReference,
        employeeToBeCharged = this.employeeToBeCharged,
        externalCreationDate = this.externalCreationDate,
        insuranceCoverage = this.insuranceCoverage,
        isClosed = this.isClosed,
        damageResponsibility = this.damageResponsibility,
        damageDate = this.damageDate,
        isExternal = isExternal,
    )
