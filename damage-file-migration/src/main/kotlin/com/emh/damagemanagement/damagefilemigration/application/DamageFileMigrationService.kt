/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application

import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefilemigration.application.port.DamageFileOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.EmployeeOutPort
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono

@Component
class DamageFileMigrationService(
    private val damageFileOutPort: DamageFileOutPort,
    private val employeeOutPort: EmployeeOutPort,
    private val createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase,
) {

    fun migrateAllDamageReports() {
        damageFileOutPort
            .getDamageFileUpdates()
            .flatMapSequential { damageFileMigrationChunk ->
                val (poolVehicleDamageFileUpdates, leasingVehicleDamageFileUpdates) =
                    damageFileMigrationChunk.partition { it.isPool }
                employeeOutPort
                    .getEmployeesByEmail(
                        poolVehicleDamageFileUpdates.filterNot { it.isExternal }.map { it.driverEmail!! }
                    )
                    .map {
                        val actualPoolVehicleDamageFileUpdates =
                            poolVehicleDamageFileUpdates.mapNotNull { damageFileMigration ->
                                if (damageFileMigration.isExternal) {
                                    // FPT1-482 do not try to match email for external drivers
                                    damageFileMigration
                                } else {
                                    findEmployeeMatch(
                                            damageFileMigration = damageFileMigration,
                                            reporterEmployeeNumber = damageFileMigration.reporterEmployeeNumber,
                                            employees = it,
                                            driverEmail = damageFileMigration.driverEmail,
                                        )
                                        ?.let {
                                            // FPT1-482 treat ANÜs as external
                                            val isExternal = it.isANUE
                                            damageFileMigration.copy(
                                                employeeToBeCharged =
                                                    if (isExternal) EmployeeNumber.external
                                                    else EmployeeNumber(it.employeeNumber),
                                                isExternal = isExternal,
                                            )
                                        }
                                }
                            }
                        val damageFilesToBeUpdated =
                            (actualPoolVehicleDamageFileUpdates + leasingVehicleDamageFileUpdates).map {
                                it.toDamageFileNewOrUpdate()
                            }
                        damageFilesToBeUpdated
                    }
                    .switchIfEmpty(Mono.just(leasingVehicleDamageFileUpdates.map { it.toDamageFileNewOrUpdate() }))
            }
            .map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFilesAsync(it)
                it.size
            }
            .subscribe(
                { numberOfDamageFiles -> log.info("Migrating total $numberOfDamageFiles damage files...") },
                { error -> log.error("Error during damage file migration.", error) },
            )
    }

    /**
     * Find a matching employee based on a list of initial employee matches (company and private email). If an
     * employeeNumber was provided, it will be validated against the match.
     */
    private fun findEmployeeMatch(
        employees: List<Employee>,
        damageFileMigration: DamageFileMigration,
        driverEmail: String?,
        reporterEmployeeNumber: EmployeeNumber?,
    ): Employee? {
        val emailMatch =
            employees.firstOrNull { driverEmail.equals(it.companyEmail, true) }
                ?: employees.firstOrNull { driverEmail.equals(it.privateEmail, true) }

        if (null != emailMatch && null != reporterEmployeeNumber) {
            /*
             * FPT1-482 for "normal" poolVehicle drivers this will not be the case, as no employee number is provided during damage processing.
             * There are, however, edge cases where retirees will drive, and damage, a pool replacement vehicle.
             * In this case, employee number serves as additional verification.
             */
            val employeeNumberMatch =
                EmployeeNumber.sanitized(emailMatch.employeeNumber) ==
                    EmployeeNumber.sanitized(reporterEmployeeNumber.value)
            if (!employeeNumberMatch) {
                log.warn(
                    "Provided employee number [${reporterEmployeeNumber.value}] does not match the one from matched employee [${emailMatch.employeeNumber}] for damage file with id ${damageFileMigration.externalReference}"
                )
            }

            return if (employeeNumberMatch) emailMatch else null
        }
        if (emailMatch == null) {
            log.warn(
                "No employee found for the provided email ${driverEmail} for damage file with id ${damageFileMigration.externalReference}"
            )
        }
        return emailMatch
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileMigrationService::class.java)
    }
}
