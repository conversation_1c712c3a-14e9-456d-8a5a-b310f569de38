/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application

data class Employee(
    val key: String,
    val employeeNumber: String,
    val firstName: String,
    val lastName: String,
    val companyEmail: String,
    val privateEmail: String?,
    val accountingArea: String,
    val type: EmployeeType,
    val employeeGroup: String?,
) {
    /*
     * ANÜ - aka Arbeitnehmerüberlassung.
     *
     *     FPT1-482 Employees identified as ANÜ are treated as 'external' for further processing
     */
    val isANUE = employeeGroup == ANUE_EMPLOYEE_GROUP

    companion object {
        const val ANUE_EMPLOYEE_GROUP = "9"
    }
}

enum class EmployeeType {
    PAG_EMPLOYEE,
    SUBSIDIARY_EMPLOYEE,
}
