/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application

import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.FindDamageFilesWithoutInternalOrderUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileAlreadyClosedException
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberMigrationOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPortException
import com.emh.damagemanagement.damagefilemigration.domain.InternalOrderNumberSyncIndicator
import com.emh.damagemanagement.damagefilemigration.domain.InternalOrderNumberSyncIndicatorNotFoundException
import com.emh.damagemanagement.damagefilemigration.domain.service.InternalOrderNumberSyncIndicatorService
import com.emh.damagemanagement.legal.hold.domain.LegalHoldInPlaceException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class InternalOrderNumberUpdateService(
    private val internalOrderNumberOutPort: InternalOrderNumberOutPort,
    private val internalOrderNumberMigrationOutPort: InternalOrderNumberMigrationOutPort,
    private val findDamageFileWithoutInternalOrderUseCase: FindDamageFilesWithoutInternalOrderUseCase,
    private val damageFileFinder: DamageFileFinder,
    private val createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase,
    private val internalOrderNumberSyncIndicatorService: InternalOrderNumberSyncIndicatorService,
) {
    fun updateAllEmptyInternalOrderNumbersAndCreateSyncIndicators() {
        val damageFiles = findDamageFileWithoutInternalOrderUseCase.findOpenDamageFilesWithoutInternalOrderNumber()

        damageFiles.forEach { damageFile ->
            try {
                getInternalOrderNumberAndUpdateInDamageFiles(damageFile)
            } catch (exception: IllegalArgumentException) {
                log.warn(
                    "Error while trying to get new internal order number for damageFile with key [${damageFile.key.value}]. Skipping.",
                    exception,
                )
            } catch (exception: InternalOrderNumberOutPortException) {
                log.warn(
                    "Error while trying to get new internal order number for damageFile with key [${damageFile.key.value}]. Skipping.",
                    exception,
                )
            } catch (exception: InternalOrderNumberUpdateException) {
                log.warn(
                    "Error while trying set new internal order number to damageFile with key [${damageFile.key.value}]. Skipping.",
                    exception,
                )
            }
        }
    }

    fun syncInternalOrderNumberToExternalSystem() {
        val internalOrderNumbersForSynchronization =
            internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()

        internalOrderNumbersForSynchronization.forEach { syncIndicator ->
            try {
                syncInternalOrderNumberToExternalSystemAndUpdateSyncIndicator(syncIndicator)
            } catch (exception: IllegalArgumentException) {
                log.warn(
                    "Error while trying to sync internal order number [${syncIndicator.internalOrderNumber.value}] " +
                        "of damage file with key [${syncIndicator.damageFileKey.value}] with external system. " +
                        "Skipping",
                    exception,
                )
            } catch (exception: InternalOrderNumberMigrationUpdateException) {
                log.warn(
                    "Error while trying to sync internal order number [${syncIndicator.internalOrderNumber.value}] " +
                        "of damage file with key [${syncIndicator.damageFileKey.value}] with external system. " +
                        "Skipping",
                    exception,
                )
            }
        }
    }

    /** @throws InternalOrderNumberMigrationUpdateException */
    private fun syncInternalOrderNumberToExternalSystemAndUpdateSyncIndicator(
        syncIndicator: InternalOrderNumberSyncIndicator
    ) {
        log.info(
            "Starting internal order number sync for damage file with key [${syncIndicator.damageFileKey.value}] using internal order number [${syncIndicator.internalOrderNumber.value}]."
        )
        val damageFile = damageFileFinder.getDamageFile(syncIndicator.damageFileKey)

        val externalReference = requireNotNull(damageFile.externalReference) { "External reference is missing" }
        val internalOrderNumber = requireNotNull(damageFile.internalOrderNumber) { "Internal order number is missing" }

        val updateResult =
            internalOrderNumberMigrationOutPort.updateInternalOrderNumber(externalReference, internalOrderNumber.value)

        // on error log and return
        if (!updateResult.isSuccess) {
            log.error("Error synchronizing external system for damageFile with externalId ${updateResult.externalId}")
            return
        }

        // on success proceed with updating syncIndicator
        try {
            internalOrderNumberSyncIndicatorService.setSynchronizeIndicatorToDone(damageFile.key)
            log.debug(
                "Successfully synchronized external system for damageFile with externalId ${updateResult.externalId}"
            )
        } catch (exception: InternalOrderNumberSyncIndicatorNotFoundException) {
            throw InternalOrderNumberMigrationUpdateException(message = exception.message, cause = exception)
        }
        log.info(
            "Finished internal order number sync for damage file with key [${syncIndicator.damageFileKey.value}] using internal order number [${syncIndicator.internalOrderNumber.value}]."
        )
    }

    private fun getInternalOrderNumberAndUpdateInDamageFiles(damageFile: DamageFile) {

        val costCenter = requireNotNull(damageFile.vehicle.costCenter) { "Cost center is missing" }
        val usingCostCenter = requireNotNull(damageFile.vehicle.usingCostCenter) { "Using cost center is missing" }
        val licensePlate = requireNotNull(damageFile.vehicle.licensePlate) { "Licence plate is missing" }
        val estimateCosts = 0.0
        val driverIsAtFault = damageFile.damageResponsibility.driverAtFault
        val damageDate = damageFile.damageDate.toLocalDate()

        val responseInternalOrderNumber =
            internalOrderNumberOutPort.requestInternalOrderNumber(
                employeeNumber = damageFile.employeeToBeCharged.value,
                costCenter = costCenter,
                usingCostCenter = usingCostCenter,
                driverIsAtFault = driverIsAtFault,
                licensePlate = licensePlate,
                vin = damageFile.vehicle.vin,
                estimatedCosts = estimateCosts,
                damageDate = damageDate,
            )
        val internalOrderNumber = InternalOrderNumber(responseInternalOrderNumber.value)

        try {
            createOrUpdateDamageFileUseCase.updateInternalOrderNumber(
                damageFileKey = damageFile.key,
                internalOrderNumber = internalOrderNumber,
            )
        } catch (exception: DamageFileAlreadyClosedException) {
            throw InternalOrderNumberUpdateException(message = exception.message, cause = exception)
        } catch (exception: LegalHoldInPlaceException) {
            throw InternalOrderNumberUpdateException(message = exception.message, cause = exception)
        } catch (exception: ConcurrentModificationException) {
            throw InternalOrderNumberUpdateException(message = exception.message, cause = exception)
        }

        internalOrderNumberSyncIndicatorService.createOrUpdate(damageFile.key, internalOrderNumber)
    }

    companion object {
        private val log = LoggerFactory.getLogger(InternalOrderNumberUpdateService::class.java)
    }
}
