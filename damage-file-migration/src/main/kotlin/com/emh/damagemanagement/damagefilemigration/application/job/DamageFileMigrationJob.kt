/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application.job

import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigrationException
import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigrationService
import java.util.*
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class DamageFileMigrationJob(private val damageFileMigrationService: DamageFileMigrationService) : Job {

    override fun execute(executionContext: JobExecutionContext?) {
        log.info("Starting scheduled damage file migration.")
        try {
            damageFileMigrationService.migrateAllDamageReports()
        } catch (exception: DamageFileMigrationException) {
            log.warn("Error during damage file migration ", exception)
        }
        log.info("Finished damage file migration startup (processing is done asynchronously).")
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileMigrationJob::class.java)
    }
}

@Configuration
class DamageFileMigrationJobConfig {

    @Bean
    @Qualifier("damageFileMigrationJob")
    fun damageFileMigrationJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(DamageFileMigrationJob::class.java)
            .storeDurably()
            .withIdentity("DamageFileMigrationJobDetail")
            .withDescription("Invoke damage file migration job")
            .build()
    }

    @Bean
    fun damageFileMigrationJobTrigger(
        @Qualifier("damageFileMigrationJob") job: JobDetail,
        @Value("\${damage-file-migration.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("DamageFileMigrationJobTrigger")
            .withDescription("DamageFile migration job trigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
