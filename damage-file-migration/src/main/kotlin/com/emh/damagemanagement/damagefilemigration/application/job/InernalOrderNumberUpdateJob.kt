/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application.job

import com.emh.damagemanagement.damagefilemigration.application.InternalOrderNumberMigrationUpdateException
import com.emh.damagemanagement.damagefilemigration.application.InternalOrderNumberUpdateService
import java.util.*
import org.quartz.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class InternalOrderNumberUpdateJob(private val internalOrderNumberUpdateService: InternalOrderNumberUpdateService) :
    Job {

    override fun execute(executionContext: JobExecutionContext?) {
        log.info("Starting scheduled internal order number update job")
        try {
            internalOrderNumberUpdateService.updateAllEmptyInternalOrderNumbersAndCreateSyncIndicators()
        } catch (exception: InternalOrderNumberMigrationUpdateException) {
            log.warn("Internal order number update job failed ", exception)
        }
        log.info("Finished scheduled internal order number update job")
    }

    companion object {
        private val log = LoggerFactory.getLogger(InternalOrderNumberUpdateJob::class.java)
    }
}

@Configuration
class InternalOrderNumberUpdateJobConfiguration {

    @Bean
    @Qualifier("internalOrderNumberUpdateJob")
    fun internalOrderNumberUpdateJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(InternalOrderNumberUpdateJob::class.java)
            .storeDurably()
            .withIdentity("InternalOrderNumberUpdateJobDetail")
            .withDescription("Invoke internal order number update job")
            .build()
    }

    @Bean
    fun internalOrderNumberUpdateJobTrigger(
        @Qualifier("internalOrderNumberUpdateJob") job: JobDetail,
        @Value("\${internal-order-number-update.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("InternalOrderNumberUpdateJobTrigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
