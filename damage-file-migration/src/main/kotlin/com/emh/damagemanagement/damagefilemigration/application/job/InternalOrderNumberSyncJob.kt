package com.emh.damagemanagement.damagefilemigration.application.job

import com.emh.damagemanagement.damagefilemigration.application.InternalOrderNumberMigrationUpdateException
import com.emh.damagemanagement.damagefilemigration.application.InternalOrderNumberUpdateService
import java.util.*
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class InternalOrderNumberSyncJob(private val internalOrderNumberUpdateService: InternalOrderNumberUpdateService) : Job {

    override fun execute(executionContext: JobExecutionContext?) {
        log.info("Starting scheduled internal order number synchronize job")
        try {
            internalOrderNumberUpdateService.syncInternalOrderNumberToExternalSystem()
        } catch (exception: InternalOrderNumberMigrationUpdateException) {
            log.warn("Internal order number synchronise job failed ", exception)
        }
        log.info("Finished scheduled internal order number synchronise job")
    }

    companion object {
        private val log = LoggerFactory.getLogger(InternalOrderNumberSyncJob::class.java)
    }
}

@Configuration
class InternalOrderNumberSyncJobConfiguration {

    @Bean
    @Qualifier("internalOrderNumberSyncJob")
    fun internalOrderNumberSyncJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(InternalOrderNumberSyncJob::class.java)
            .storeDurably()
            .withIdentity("internalOrderNumberSyncJobDetail")
            .withDescription("Invoke internal order number synchronise job")
            .build()
    }

    @Bean
    fun internalOrderNumberSyncJobTrigger(
        @Qualifier("internalOrderNumberSyncJob") job: JobDetail,
        @Value("\${internal-oder-number-sync.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("internalOrderNumberSyncJobTrigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
