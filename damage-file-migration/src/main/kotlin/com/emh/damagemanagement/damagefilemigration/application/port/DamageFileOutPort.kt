/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application.port

import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigration
import reactor.core.publisher.Flux

typealias DamageFileMigrationChunk = List<DamageFileMigration>

fun interface DamageFileOutPort {

    /** Return all damage file updates regardless of duration. */
    fun getDamageFileUpdates(): Flux<DamageFileMigrationChunk>
}
