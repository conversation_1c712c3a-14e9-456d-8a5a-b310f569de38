/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application.port

import com.emh.damagemanagement.damagefilemigration.application.Employee
import reactor.core.publisher.Mono

fun interface EmployeeOutPort {

    /** Returns employee information for given emails. */
    fun getEmployeesByEmail(emails: List<String>): Mono<List<Employee>>
}
