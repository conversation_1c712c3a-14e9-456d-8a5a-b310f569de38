package com.emh.damagemanagement.damagefilemigration.application.port

import com.emh.damagemanagement.damagefilemigration.application.DamageReportUpdate

/**
 * Outgoing port to the external damage report system, where we have to synchronize internal order number as well.
 *
 * Updates repairfix's damage report. Updates damage report's externalId with our damageFile internal order number
 */
fun interface InternalOrderNumberMigrationOutPort {
    /** Update repairfix's damage report */
    fun updateInternalOrderNumber(
        damageFileExternalId: String,
        damageFileInternalOrderNumber: String,
    ): DamageReportUpdate
}
