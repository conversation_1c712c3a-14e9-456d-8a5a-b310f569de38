/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application.port

import java.time.LocalDate

/** Outgoing port for requesting internal order numbers. Currently implemented for PACE. */
fun interface InternalOrderNumberOutPort {

    fun requestInternalOrderNumber(
        employeeNumber: String,
        costCenter: String,
        usingCostCenter: String,
        driverIsAtFault: Boolean,
        licensePlate: String,
        vin: String,
        estimatedCosts: Double,
        damageDate: LocalDate,
    ): InternalOrderNumber

    @JvmInline value class InternalOrderNumber(val value: String)
}

class InternalOrderNumberOutPortException(message: String?, cause: Throwable?) : RuntimeException(message, cause)
