package com.emh.damagemanagement.damagefilemigration.domain

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.shared.domain.Auditable
import com.emh.damagemanagement.shared.domain.DomainEvent
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "internal_order_number_sync_indicator")
class InternalOrderNumberSyncIndicator(
    /** A reference to the damage file. */
    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "damage_file_key"))
    val damageFileKey: DamageFile<PERSON>ey,
    internalOrderNumber: InternalOrderNumber,
) : Auditable() {

    /** Internal PK. Use 'damageFileKey' when accessing / finding archival info. */
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: InternalOrderNumberSyncIndicatorId = InternalOrderNumberSyncIndicatorId()

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "internal_order_number", nullable = false))
    var internalOrderNumber: InternalOrderNumber = internalOrderNumber
        private set

    @Column(name = "external_system_sync_done", nullable = false)
    var externalSystemSyncIsDone: Boolean = false
        private set

    internal fun setExternalSystemSyncToDone(): InternalOrderNumberSyncDoneEvent {
        externalSystemSyncIsDone = true
        return InternalOrderNumberSyncDoneEvent(occurredOn = OffsetDateTime.now(), damageFileKey = this.damageFileKey)
    }

    /** resets sync indicator with a new internalOrderNumber */
    internal fun updateAndReset(internalOrderNumber: InternalOrderNumber) {
        this.externalSystemSyncIsDone = false
        this.internalOrderNumber = internalOrderNumber
    }
}

@Embeddable data class InternalOrderNumberSyncIndicatorId(@Basic val value: UUID = UUID.randomUUID()) : Serializable

data class InternalOrderNumberSyncDoneEvent(
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val damageFileKey: DamageFileKey,
) : DomainEvent
