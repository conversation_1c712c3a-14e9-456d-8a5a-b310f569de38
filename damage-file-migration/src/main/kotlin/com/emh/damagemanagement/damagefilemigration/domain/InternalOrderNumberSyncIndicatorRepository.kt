package com.emh.damagemanagement.damagefilemigration.domain

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.repository.Repository

interface InternalOrderNumberSyncIndicatorRepository :
    Repository<InternalOrderNumberSyncIndicator, InternalOrderNumberSyncIndicatorId>,
    JpaSpecificationExecutor<InternalOrderNumberSyncIndicator> {

    fun save(internalOrderNumberSyncIndicator: InternalOrderNumberSyncIndicator): InternalOrderNumberSyncIndicator

    fun findByDamageFileKey(damageFileKey: DamageFileKey): InternalOrderNumberSyncIndicator?

    fun findByExternalSystemSyncIsDoneFalse(): List<InternalOrderNumberSyncIndicator>
}
