package com.emh.damagemanagement.damagefilemigration.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefilemigration.domain.InternalOrderNumberSyncIndicator
import com.emh.damagemanagement.damagefilemigration.domain.InternalOrderNumberSyncIndicatorNotFoundException
import com.emh.damagemanagement.damagefilemigration.domain.InternalOrderNumberSyncIndicatorRepository
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class InternalOrderNumberSyncIndicatorService(
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val repository: InternalOrderNumberSyncIndicatorRepository,
) {

    /**
     * Will either create a new syncIndicator or flip the synced flag, if we have to request a new internalOrderNumber
     * for a damage file
     */
    fun createOrUpdate(damageFileKey: DamageFileKey, internalOrderNumber: InternalOrderNumber) {
        val existingInternalOrderNumberSyncIndicator = repository.findByDamageFileKey(damageFileKey)

        // reset sync flag if one already exists
        if (null != existingInternalOrderNumberSyncIndicator) {
            existingInternalOrderNumberSyncIndicator.updateAndReset(internalOrderNumber = internalOrderNumber)
            return
        }

        // create a new one otherwise
        repository.save(
            InternalOrderNumberSyncIndicator(internalOrderNumber = internalOrderNumber, damageFileKey = damageFileKey)
        )
    }

    fun setSynchronizeIndicatorToDone(damageFileKey: DamageFileKey) {
        val syncIndicator = repository.findByDamageFileKey(damageFileKey)
        if (syncIndicator != null && !syncIndicator.externalSystemSyncIsDone) {
            syncIndicator.setExternalSystemSyncToDone().also { updateEvent ->
                log.info("Updated sync indicator as done, with key [${damageFileKey}].")
                applicationEventPublisher.publishEvent(updateEvent)
            }
        } else {
            throw InternalOrderNumberSyncIndicatorNotFoundException(damageFileKey)
        }
    }

    fun findInternalOrderNumbersForSynchronization(): List<InternalOrderNumberSyncIndicator> {
        return repository.findByExternalSystemSyncIsDoneFalse()
    }

    companion object {
        private val log = LoggerFactory.getLogger(InternalOrderNumberSyncIndicatorService::class.java)
    }
}
