package com.emh.damagemanagement.damagefilemigration

import com.emh.damagemanagement.damagefilemigration.application.port.DamageFileOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.EmployeeOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberMigrationOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPort
import com.fasterxml.jackson.databind.ObjectMapper
import org.mockito.kotlin.mock
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@SpringBootApplication(
    scanBasePackages =
        [
            "com.emh.damagemanagement.damagefile.*",
            "com.emh.damagemanagement.damagefilemigration.*",
            "com.emh.damagemanagement.repairfix.*",
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.employee.*",
        ]
)
@ConfigurationPropertiesScan(
    "com.emh.damagemanagement.damagefile.*",
    "com.emh.damagemanagement.damagefilemigration.*",
    "com.emh.damagemanagement.repairfix.*",
    "com.emh.damagemanagement.shared.*",
    "com.emh.damagemanagement.employee.*",
)
@EntityScan("com.emh.damagemanagement.damagefile.*", "com.emh.damagemanagement.damagefilemigration.*")
@EnableJpaRepositories("com.emh.damagemanagement.damagefile.*", "com.emh.damagemanagement.damagefilemigration.*")
class TestDamageFileMigrationApplication {

    fun main(args: Array<String>) {
        runApplication<TestDamageFileMigrationApplication>(*args)
    }
}

@TestConfiguration
class TestObjectMapperConfiguration {
    @Bean
    @Primary
    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    fun objectMapper(@Qualifier("repairfix") jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder): ObjectMapper =
        jackson2ObjectMapperBuilder.createXmlMapper(false).build()
}

@TestConfiguration
class InternalOrderNumberUpdateServiceConfiguration {

    @Bean @Primary fun internalOrderNumberOutPort(): InternalOrderNumberOutPort = mock()

    @Bean @Primary fun internalOrderNumberUpdateOutPort(): InternalOrderNumberMigrationOutPort = mock()
}

@TestConfiguration
class DamageFileMigrationServiceConfiguration {

    @Bean @Primary fun damageFileOutPort(): DamageFileOutPort = mock()

    @Bean @Primary fun employeeOutPort(): EmployeeOutPort = mock()
}
