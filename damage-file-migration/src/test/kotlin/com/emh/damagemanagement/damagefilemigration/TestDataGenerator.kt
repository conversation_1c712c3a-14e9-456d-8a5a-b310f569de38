/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration

import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.Vehicle
import com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.DamageReportDetails
import com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.DamageReportMigration
import com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.InvoiceData
import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigration
import com.emh.damagemanagement.damagefilemigration.application.Employee
import com.emh.damagemanagement.damagefilemigration.application.EmployeeType
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeDto
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeTypeDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarDriver
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarInsurance
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportCar
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportListItem
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponseDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ReporterInfo
import com.emh.damagemanagement.shared.createRandomAlphanumericString
import com.emh.damagemanagement.shared.createRandomString
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.OffsetDateTime
import java.util.*
import kotlin.random.Random
import net.datafaker.Faker

class DamageReportListItemBuilder {
    private var subFleet = createRandomString(Random.nextInt(4, 23))
    private var car: DamageReportCar = DamageReportCarBuilder.buildSingle()
    private var reporterInfo: ReporterInfo? = ReporterInfoBuilder.buildSingle()

    fun subFleet(subFleet: String) = apply { this.subFleet = subFleet }

    fun car(vin: String, vGuid: String, driver: EmployeeNumber) = apply {
        this.car = DamageReportCarBuilder().vin(vin).vGuid(vGuid).driver(driver).build()
    }

    fun car(car: DamageReportCar) = apply { this.car = car }

    fun reporterInfo(reporterInfo: ReporterInfo) = apply { this.reporterInfo = reporterInfo }

    fun damageWithoutReporterInfo() = apply { this.reporterInfo = null }

    fun build(): DamageReportListItem {
        return DamageReportListItem(
            id = UUID.randomUUID().toString(),
            reportPartnerData = createRandomString(Random.nextInt(4, 23)),
            status = "new",
            reporterNameLink = createRandomString(Random.nextInt(4, 23)),
            reporter = reporterInfo,
            subFleet = subFleet,
            type = DamageReportResponseDamageReport.Type.entries.random().toString(),
            createdAt = OffsetDateTime.now().toString(),
            location = createRandomString(Random.nextInt(4, 23)),
            reporterName = createRandomString(Random.nextInt(4, 23)),
            tags = emptyList(),
            car = car,
            isAccident = Random.nextBoolean(),
            isMinor = Random.nextBoolean(),
            updatedAt = OffsetDateTime.now().toString(),
            isBreakdown = false,
        )
    }

    companion object {
        fun buildSingle() = DamageReportListItemBuilder().build()

        fun buildMultiple(count: Int): Set<DamageReportListItem> =
            (1..count).map { DamageReportListItemBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class DamageReportMigrationBuilder {

    private var damageReportListItem = DamageReportListItemBuilder().build()
    private var car = CarBuilder.buildSingle()
    private var damageReportDetails = DamageReportDetailsBuilder.buildSingle()

    fun damageReport(damageReport: DamageReportListItem) = apply { this.damageReportListItem = damageReport }

    fun car(car: Car) = apply { this.car = car }

    fun damageReportDetails(damageReportDetails: DamageReportDetails) = apply {
        this.damageReportDetails = damageReportDetails
    }

    fun build(): DamageReportMigration {
        return DamageReportMigration(
            damageReportListItem = damageReportListItem,
            damageReportDetails = damageReportDetails,
            invoiceData =
                InvoiceData(
                    deductibles = setOf(MonetaryAmount.of(BigDecimal(Math.random()))),
                    claimsTowardsEmployee = setOf(MonetaryAmount.of(BigDecimal(Math.random()))),
                ),
            car = car,
        )
    }

    companion object {
        fun buildSingle() = DamageReportMigrationBuilder().build()

        fun buildMultiple(count: Int): Set<DamageReportMigration> =
            (1..count).map { DamageReportMigrationBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class DamageReportCarBuilder {
    private var vin: String = createRandomAlphanumericString(12)
    private var vGuid: String = createRandomString(Random.nextInt(4, 23))
    private var driver: CarDriver? =
        CarDriver(
            email = createRandomString(Random.nextInt(4, 23)),
            partnerId = createRandomString(Random.nextInt(4, 23)),
        )
    private var isPool: Boolean = Random.nextBoolean()

    fun vin(vin: String) = apply { this.vin = vin }

    fun vGuid(vGuid: String) = apply { this.vGuid = vGuid }

    fun driver(employeeNumber: EmployeeNumber) = apply {
        this.driver = CarDriver(email = createRandomString(Random.nextInt(4, 23)), partnerId = employeeNumber.value)
    }

    fun withoutDriver() = apply { this.driver = null }

    fun driverWithoutPartnerId() = apply { this.driver = CarDriver(email = createRandomString(Random.nextInt(4, 23))) }

    fun isPool(isPool: Boolean) = apply { this.isPool = isPool }

    fun build(): DamageReportCar {
        return DamageReportCar(
            id = UUID.randomUUID().toString(),
            carPartnerId = vGuid,
            createdAt = OffsetDateTime.now(),
            vin = vin,
            make = createRandomString(Random.nextInt(4, 23)),
            updatedAt = OffsetDateTime.now(),
            model = createRandomString(Random.nextInt(4, 23)),
            isActive = true,
            isPool = isPool,
            driver = this.driver,
            licensePlate = createRandomString(Random.nextInt(4, 23)),
            fleetPartnerId = createRandomString(Random.nextInt(4, 23)),
        )
    }

    companion object {
        fun buildSingle() = DamageReportCarBuilder().build()

        fun buildMultiple(count: Int): Set<DamageReportCar> =
            (1..count).map { DamageReportCarBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class CarBuilder {
    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var vGuid: String = createRandomString(Random.nextInt(4, 23))
    private var driver: CarDriver? =
        CarDriver(
            email = createRandomString(Random.nextInt(4, 23)),
            partnerId = createRandomString(Random.nextInt(4, 23)),
        )
    private var insurance: CarInsurance =
        CarInsurance(
            id = UUID.randomUUID(),
            insuranceCompany = createRandomString(Random.nextInt(4, 23)),
            costCenter = createRandomString(Random.nextInt(4, 23)),
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
            insuranceBroker = "LeasingArt:${createRandomString(Random.nextInt(4, 23))}",
            insurerAddress = "UsingCostCenter:${createRandomString(Random.nextInt(4, 23))}",
        )

    fun vin(vin: String) = apply { this.vin = vin }

    fun vGuid(vGuid: String) = apply { this.vGuid = vGuid }

    fun driver(employeeNumber: EmployeeNumber) = apply {
        this.driver = CarDriver(email = createRandomString(Random.nextInt(4, 23)), partnerId = employeeNumber.value)
    }

    fun costCenter(
        depreciationRelevantCostCenter: String? = null,
        usingCostCenter: String? = null,
        leasingArt: String? = null,
    ) = apply {
        this.insurance =
            CarInsurance(
                id = UUID.randomUUID(),
                insuranceCompany = createRandomString(Random.nextInt(4, 23)),
                costCenter = depreciationRelevantCostCenter,
                createdAt = OffsetDateTime.now(),
                updatedAt = OffsetDateTime.now(),
                insuranceBroker = "LeasingArt:${leasingArt ?: createRandomString(Random.nextInt(4, 23))}",
                insurerAddress = "UsingCostCenter:${usingCostCenter ?: createRandomString(Random.nextInt(4, 23))}",
            )
    }

    fun build(): Car {
        return Car(
            id = UUID.randomUUID(),
            carPartnerId = vGuid,
            createdAt = OffsetDateTime.now(),
            vin = vin,
            make = createRandomString(Random.nextInt(4, 23)),
            updatedAt = OffsetDateTime.now(),
            model = createRandomString(Random.nextInt(4, 23)),
            isActive = true,
            isPool = Random.nextBoolean(),
            driver = this.driver,
            licensePlate = createRandomString(Random.nextInt(4, 23)),
            fleetPartnerId = createRandomString(Random.nextInt(4, 23)),
            bodyType = Car.BodyType.entries.random(),
            fleetId = createRandomString(Random.nextInt(4, 23)),
            availability = Car.Availability.AVAILABLE,
            insurances = listOf(insurance),
        )
    }

    companion object {
        fun buildSingle() = CarBuilder().build()

        fun buildMultiple(count: Int): Set<Car> = (1..count).map { CarBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class EmployeeDtoBuilder {

    private val faker = Faker()

    private var firstName: String = createRandomString(Random.nextInt(4, 23))
    private var lastName: String = createRandomString(Random.nextInt(4, 23))
    private var employeeNumber: String = faker.numerify("00######")
    private var accountingArea: String = createRandomString(4)

    fun build(): EmployeeDto {
        return EmployeeDto(
            key = createRandomString(Random.nextInt(4, 23)),
            firstName = firstName,
            lastName = lastName,
            employeeNumber = employeeNumber,
            accountingArea = accountingArea,
            companyEmail = createRandomString(Random.nextInt(4, 23)),
            type = EmployeeTypeDto.entries.random(),
            privateEmail = createRandomString(Random.nextInt(4, 23)),
            street = createRandomString(Random.nextInt(4, 10)),
            postalCode = createRandomString(Random.nextInt(4, 9)),
            city = createRandomString(Random.nextInt(4, 15)),
            country = createRandomString(Random.nextInt(4, 8)),
            internalCompanyPhoneNumber = createRandomString(Random.nextInt(4, 23)),
        )
    }

    companion object {

        fun buildSingle(): EmployeeDto = EmployeeDtoBuilder().build()

        fun buildMultiple(count: Int): List<EmployeeDto> = (1..count).map { EmployeeDtoBuilder().build() }

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class ReporterInfoBuilder {

    var email: String? = createRandomString(Random.nextInt(4, 23))

    fun email(email: String?) = apply { this.email = email }

    fun build(): ReporterInfo {
        return ReporterInfo(
            name = createRandomString(Random.nextInt(4, 10)),
            phone = createRandomString(Random.nextInt(4, 13)),
            email = email,
            companyAffiliation = ReporterInfo.CompanyAffiliation.entries.random(),
            personalNumber = EmployeeNumber(createRandomString(Random.nextInt(4, 23))).toString(),
        )
    }

    companion object {
        fun buildSingle(): ReporterInfo = ReporterInfoBuilder().build()
    }
}

class DamageFileMigrationBuilder {
    private var employeeToBeCharged: EmployeeNumber? = EmployeeNumber(createRandomString(Random.nextInt(4, 23)))
    private var reporterEmployeeNumber: EmployeeNumber? = null
    private var deductibles: Set<MonetaryAmount> =
        setOf(
            MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP)),
            MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP)),
        )
    private var claimsTowardsEmployee: Set<MonetaryAmount> =
        setOf(
            MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP)),
            MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP)),
        )
    private var externalReference: String = createRandomString(Random.nextInt(4, 23))
    private var vehicle: Vehicle =
        Vehicle(
            vin = createRandomString(22),
            vGuid = createRandomString(23),
            licensePlate = createRandomString(12),
            leasingArt = createRandomString(5),
            costCenter = createRandomString(9),
            usingCostCenter = createRandomString(9),
            fleetId = createRandomString(23),
            externalReference = createRandomString(36),
        )
    private var creationDate: OffsetDateTime = OffsetDateTime.now()
    private var closed: Boolean = false
    private var isExternal: Boolean = false
    private var insuranceCoverage: InsuranceCoverage = InsuranceCoverage.entries.random()
    private var isPool: Boolean = Random.nextBoolean()
    private var driverEmail: String = createRandomString(Random.nextInt(4, 23))

    fun externalReference(externalReference: String) = apply { this.externalReference = externalReference }

    fun insuranceCoverage(insuranceCoverage: InsuranceCoverage) = apply { this.insuranceCoverage = insuranceCoverage }

    fun isPool(isPool: Boolean) = apply { this.isPool = isPool }

    fun isExternal(isExternal: Boolean) = apply { this.isExternal = isExternal }

    fun driverEmail(driverEmail: String) = apply { this.driverEmail = driverEmail }

    fun employeeToBeCharged(employeeNumber: EmployeeNumber?) = apply { this.employeeToBeCharged = employeeNumber }

    fun reporterEmployeeNumber(reporterEmployeeNumber: EmployeeNumber?) = apply {
        this.reporterEmployeeNumber = reporterEmployeeNumber
    }

    fun build(): DamageFileMigration {
        return DamageFileMigration(
            vehicle = vehicle,
            externalCreationDate = creationDate,
            employeeToBeCharged = employeeToBeCharged,
            deductibles = deductibles,
            claimsTowardsEmployee = claimsTowardsEmployee,
            externalReference = externalReference,
            insuranceCoverage = insuranceCoverage,
            isClosed = closed,
            isPool = isPool,
            driverEmail = driverEmail,
            damageResponsibility = DamageResponsibility.entries.random(),
            damageDate = OffsetDateTime.now(),
            isExternal = isExternal,
            reporterEmployeeNumber = reporterEmployeeNumber,
        )
    }

    companion object {

        fun buildSingle(): DamageFileMigration = DamageFileMigrationBuilder().build()

        fun buildMultiple(count: Int): List<DamageFileMigration> =
            (1..count).map { DamageFileMigrationBuilder().build() }

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class EmployeeBuilder {

    private val faker = Faker()

    private var firstName: String = createRandomString(Random.nextInt(4, 23))
    private var lastName: String = createRandomString(Random.nextInt(4, 23))
    private var employeeNumber: String = faker.numerify("00######")
    private var accountingArea: String = createRandomString(4)
    private var companyEmail: String = createRandomString(Random.nextInt(4, 23))
    private var privateEmail: String = createRandomString(Random.nextInt(4, 23))
    private var employeeGroup: String? = null

    fun employeeNumber(employeeNumber: String) = apply { this.employeeNumber = employeeNumber }

    fun companyEmail(companyEmail: String) = apply { this.companyEmail = companyEmail }

    fun privateEmail(privateEmail: String) = apply { this.privateEmail = privateEmail }

    fun employeeGroup(employeeGroup: String?) = apply { this.employeeGroup = employeeGroup }

    fun build(): Employee {
        return Employee(
            key = createRandomString(Random.nextInt(4, 23)),
            firstName = firstName,
            lastName = lastName,
            employeeNumber = employeeNumber,
            accountingArea = accountingArea,
            companyEmail = companyEmail,
            type = EmployeeType.entries.random(),
            privateEmail = privateEmail,
            employeeGroup = employeeGroup,
        )
    }

    companion object {

        fun buildSingle(): EmployeeDto = EmployeeDtoBuilder().build()

        fun buildMultiple(count: Int): List<EmployeeDto> = (1..count).map { EmployeeDtoBuilder().build() }

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class DamageReportDetailsBuilder {

    var type: DamageReportResponseDamageReport.Type = DamageReportResponseDamageReport.Type.entries.random()

    var damageDateTime: OffsetDateTime? = OffsetDateTime.now()

    fun type(type: DamageReportResponseDamageReport.Type) = apply { this.type = type }

    fun damageDateTime(damageDateTime: OffsetDateTime?) = apply { this.damageDateTime = damageDateTime }

    fun build(): DamageReportDetails =
        DamageReportDetails(
            insuranceCoverage = InsuranceCoverage.entries.random(),
            damageResponsibility = DamageResponsibility.entries.random(),
            damageDateTime = damageDateTime,
            type = type,
        )

    companion object {
        fun buildSingle(): DamageReportDetails = DamageReportDetailsBuilder().build()
    }
}
