/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.damagefilemigration.adapter.out.employee

import com.emh.damagemanagement.damagefilemigration.EmployeeDtoBuilder
import com.emh.damagemanagement.damagefilemigration.IntegrationTest
import com.emh.damagemanagement.damagefilemigration.application.Employee
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.fasterxml.jackson.databind.ObjectMapper
import java.io.BufferedReader
import java.math.BigDecimal
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ClassPathResource
import org.springframework.security.oauth2.client.ClientAuthorizationException
import org.springframework.test.annotation.DirtiesContext

@IntegrationTest
class EmployeeAdapterTest {
    @Autowired private lateinit var employeeAdapter: EmployeeAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    private val tokenResponse =
        ClassPathResource("token_response.json").inputStream.bufferedReader().use(BufferedReader::readText)

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8090)
    }

    private val validTokenResponse =
        MockResponse.Builder().body(tokenResponse).addHeader("Content-Type", "application/json").code(200).build()

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")

    @Test
    @DirtiesContext
    fun `should request oauth token and set jwt in authorization header`() {
        val employee = EmployeeDtoBuilder.buildSingle()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(employee))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        Assertions.assertThatNoException().isThrownBy {
            employeeAdapter.getEmployeesByEmail(listOf("<EMAIL>")).block()!!.toList()
            employeeAdapter.getEmployeesByEmail(listOf("<EMAIL>")).block()!!.toList()
        }
        val tokenRequest: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val employeeRequest: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val employeeRequest2: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val jwt = objectMapper.readTree(tokenResponse)["access_token"].textValue()
        assertThat(tokenRequest.url.toString()).contains("oauth/token")
        assertThat(employeeRequest.headers["Authorization"]).isEqualTo("Bearer $jwt")
        assertThat(employeeRequest2.headers["Authorization"]).isEqualTo("Bearer $jwt")
        assertThat(employeeRequest2.url.toString()).contains("employees")
        // should be true, as we assume that token get properly cached
        assertThat(mockWebServer.requestCount).isEqualTo(3)
    }

    @Test
    @DirtiesContext
    fun `should throw ClientAuthorizationException when token could not be obtained`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder().code(401).build()
                }
            }

        Assertions.assertThatExceptionOfType(ClientAuthorizationException::class.java).isThrownBy {
            employeeAdapter.getEmployeesByEmail(listOf("<EMAIL>")).block()!!.toList()
        }
    }

    @Test
    @DirtiesContext
    fun `should return employees by email`() {
        val employee = EmployeeDtoBuilder.buildSingle()
        val anotherEmployee = EmployeeDtoBuilder.buildSingle()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("companyEmail") == employee.companyEmail ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(listOf(employee)))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("companyEmail") == anotherEmployee.companyEmail ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(listOf(anotherEmployee)))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val employeesFromAdapter =
            employeeAdapter
                .getEmployeesByEmail(listOf(employee.companyEmail, anotherEmployee.companyEmail))
                .block()!!
                .toList()
        assertThat(employeesFromAdapter)
            .extracting("companyEmail")
            .containsExactlyInAnyOrderElementsOf(listOf(employee.companyEmail, anotherEmployee.companyEmail))
    }

    @Test
    @DirtiesContext
    fun `should skip the employee if not found`() {
        val employee = EmployeeDtoBuilder.buildSingle()
        val employeeNotFound = EmployeeDtoBuilder.buildSingle()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("companyEmail") == employee.companyEmail ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(listOf(employee)))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("companyEmail") == employeeNotFound.companyEmail ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(emptyList<Employee>()))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val employeesFromAdapter =
            employeeAdapter
                .getEmployeesByEmail(listOf(employee.companyEmail, employeeNotFound.companyEmail))
                .block()!!
                .toList()
        assertThat(employeesFromAdapter)
            .extracting("companyEmail")
            .containsExactlyInAnyOrderElementsOf(listOf(employee.companyEmail))
    }

    @Test
    @DirtiesContext
    fun `should retry with private email if initial company email match does not provide a result`() {
        val employee = EmployeeDtoBuilder.buildSingle()
        val email = "<EMAIL>"
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("companyEmail") == email ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(emptyList<Employee>()))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("privateEmail") == email ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(listOf(employee)))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val employeesFromAdapter = employeeAdapter.getEmployeesByEmail(listOf(email)).block()!!.toList()
        assertThat(employeesFromAdapter).hasSize(1)
        assertThat(mockWebServer.requestCount).isEqualTo(3)
    }

    @Test
    @DirtiesContext
    fun `should skip the employee if more than one employees found`() {
        val employee = EmployeeDtoBuilder.buildSingle()
        val multipleEmployees = EmployeeDtoBuilder.buildMultiple(5)
        val emailOfMultipleEmployees = multipleEmployees.first().companyEmail
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("companyEmail") == employee.companyEmail ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(listOf(employee)))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains("/employees") &&
                            request.url.queryParameter("companyEmail") == emailOfMultipleEmployees ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(multipleEmployees))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val employeesFromAdapter =
            employeeAdapter
                .getEmployeesByEmail(listOf(employee.companyEmail, emailOfMultipleEmployees))
                .block()!!
                .toList()
        assertThat(employeesFromAdapter)
            .extracting("companyEmail")
            .containsExactlyInAnyOrderElementsOf(listOf(employee.companyEmail))
    }

    @Test
    @DirtiesContext
    fun `should skip the employee if user service responds with 401 Unauthorized error`() {
        val employee = EmployeeDtoBuilder.buildSingle()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        request.url.toString().contains("/employees") -> return MockResponse.Builder().code(401).build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val employeesFromAdapter = employeeAdapter.getEmployeesByEmail(listOf(employee.companyEmail)).block()!!.toList()
        assertThat(employeesFromAdapter).isEmpty()
    }
}
