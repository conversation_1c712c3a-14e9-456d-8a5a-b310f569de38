/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.damagefilemigration.adapter.out.employee

import com.emh.damagemanagement.damagefilemigration.EmployeeDtoBuilder
import com.emh.damagemanagement.damagefilemigration.application.Employee
import com.emh.damagemanagement.damagefilemigration.application.EmployeeType
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeDto
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeTypeDto
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class EmployeeConverterTest {

    @ParameterizedTest
    @MethodSource("employeeDtoProvider")
    fun `should convert EmployeeDto to Employee`(employeeDto: EmployeeDto) {
        val employee = employeeDto.toEmployee()

        assertThat(employee).satisfies(employeeDtoRequirements(employeeDto))
    }

    companion object {

        private fun employeeDtoRequirements(employeeDto: EmployeeDto) =
            Consumer<Employee> { employee: Employee ->
                assertThat(employee.firstName).isEqualTo(employeeDto.firstName)
                assertThat(employee.lastName).isEqualTo(employeeDto.lastName)
                assertThat(employee.accountingArea).isEqualTo(employeeDto.accountingArea)
                assertThat(employee.companyEmail).isEqualTo(employeeDto.companyEmail)
                assertThat(employee.key).isEqualTo(employeeDto.key)
                assertThat(employee.employeeNumber).isEqualTo(employeeDto.employeeNumber)
                assertThat(employee.type).satisfies(employeeTypeDtoRequirements(employeeDto.type))
            }

        private fun employeeTypeDtoRequirements(employeeTypeDto: EmployeeTypeDto) =
            Consumer<EmployeeType> { employeeType: EmployeeType ->
                when (employeeType) {
                    EmployeeType.PAG_EMPLOYEE -> assertThat(employeeTypeDto).isEqualTo(EmployeeTypeDto.PAG_EMPLOYEE)
                    EmployeeType.SUBSIDIARY_EMPLOYEE ->
                        assertThat(employeeTypeDto).isEqualTo(EmployeeTypeDto.SUBSIDIARY_EMPLOYEE)
                }
            }

        @JvmStatic
        fun employeeDtoProvider(): Stream<Arguments> =
            EmployeeDtoBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
