/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix

import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefilemigration.CarBuilder
import com.emh.damagemanagement.damagefilemigration.DamageReportCarBuilder
import com.emh.damagemanagement.damagefilemigration.DamageReportDetailsBuilder
import com.emh.damagemanagement.damagefilemigration.DamageReportListItemBuilder
import com.emh.damagemanagement.damagefilemigration.DamageReportMigrationBuilder
import com.emh.damagemanagement.damagefilemigration.ReporterInfoBuilder
import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigration
import com.emh.damagemanagement.repairfix.adapter.DAMAGE_REPORT_CLOSED_STATUS
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AccidentThirdPartyInformation
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponseDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ReporterInfo
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.function.Consumer
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatIllegalArgumentException
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.util.ReflectionUtils

class DamageReportConverterTest {

    private val determineDamageResponsibilityFunction =
        ReflectionUtils.findMethod(
            Class.forName("com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.DamageReportConverterKt"),
            "determineDamageResponsibility",
            AccidentThirdPartyInformation.DamageResponsibility::class.java,
        )!!

    @ParameterizedTest
    @MethodSource("damageReportMigrationProvider")
    fun `should convert DamageReportMigration to DamageFileMigration`(damageReportMigration: DamageReportMigration) {
        val damageFileMigration = damageReportMigration.toDamageFileMigration()

        assertThat(damageFileMigration).satisfies(damageReportMigrationRequirements(damageReportMigration))
    }

    @Test
    fun `should parse repairfix datetime strings`() {
        val repairfixDateTime = "2019-01-01T11:29:38.964Z"
        val parsedOffsetDateTime = OffsetDateTime.parse(repairfixDateTime)

        assertThat(parsedOffsetDateTime.offset).isEqualTo(ZoneOffset.UTC)
        assertThat(parsedOffsetDateTime.hour).isEqualTo(11)
    }

    @Test
    fun `should throw exception when driver is not present for leasing vehicle damage report`() {
        val car = DamageReportCarBuilder().isPool(false).withoutDriver().build()
        val damageReportListItem = DamageReportListItemBuilder().car(car).build()
        val damageReportMigration = DamageReportMigrationBuilder().damageReport(damageReportListItem).build()
        assertThatIllegalArgumentException()
            .isThrownBy { damageReportMigration.toDamageFileMigration() }
            .withMessage("Driver information is missing for damage report with id [${damageReportListItem.id}].")
    }

    @Test
    fun `should throw exception when partnerId is not present for leasing vehicle damage report`() {
        val car = DamageReportCarBuilder().isPool(false).driverWithoutPartnerId().build()
        val damageReportListItem = DamageReportListItemBuilder().car(car).build()
        val damageReportMigration = DamageReportMigrationBuilder().damageReport(damageReportListItem).build()
        assertThatIllegalArgumentException()
            .isThrownBy { damageReportMigration.toDamageFileMigration() }
            .withMessage(
                "Employee number (partnerId) is missing for given driver for damage report with id [${damageReportListItem.id}]."
            )
    }

    @Test
    fun `should throw exception when reporter is not present for pool vehicle damage report`() {
        val car = DamageReportCarBuilder().isPool(true).build()
        val damageReportListItem = DamageReportListItemBuilder().damageWithoutReporterInfo().car(car).build()
        val damageReportMigration = DamageReportMigrationBuilder().damageReport(damageReportListItem).build()
        assertThatIllegalArgumentException()
            .isThrownBy { damageReportMigration.toDamageFileMigration() }
            .withMessage("Reporter information is missing for damage report with id [${damageReportListItem.id}].")
    }

    @Test
    fun `should throw exception when reporter email is not present for pool vehicle damage report`() {
        val car = DamageReportCarBuilder().isPool(true).build()
        val reporterInfo = ReporterInfoBuilder().email(null).build()
        val damageReportListItem = DamageReportListItemBuilder().reporterInfo(reporterInfo).car(car).build()
        val damageReportMigration = DamageReportMigrationBuilder().damageReport(damageReportListItem).build()
        assertThatIllegalArgumentException()
            .isThrownBy { damageReportMigration.toDamageFileMigration() }
            .withMessage("Email is missing for given reporter for damage report with id [${damageReportListItem.id}].")
    }

    @Test
    fun `should throw exception when damage date time is not present for external damage report type`() {
        val damageReportDetails =
            DamageReportDetailsBuilder()
                .type(DamageReportResponseDamageReport.Type.ExteriorDamageReport)
                .damageDateTime(null)
                .build()
        val damageReportMigration = DamageReportMigrationBuilder().damageReportDetails(damageReportDetails).build()
        assertThatIllegalArgumentException()
            .isThrownBy { damageReportMigration.toDamageFileMigration() }
            .withMessage(
                "Damage date time is missing for exterior damage report with id [${damageReportMigration.damageReportListItem.id}]."
            )
    }

    @Test
    fun `should map damageResponsibility to driver caused accident if no responsibility was provided`() {
        ReflectionUtils.makeAccessible(determineDamageResponsibilityFunction)
        val convertedDamageResponsibility =
            ReflectionUtils.invokeMethod(determineDamageResponsibilityFunction, null, null)

        assertThat(convertedDamageResponsibility).isEqualTo(DamageResponsibility.DRIVER_CAUSED_ACCIDENT)
    }

    @Test
    fun `should correctly detect null usingCostCenter if null-string is supplied`() {
        val car = CarBuilder().costCenter(usingCostCenter = "null").build()
        val damageReportMigration = DamageReportMigrationBuilder().car(car).build()
        val damageFileMigration = damageReportMigration.toDamageFileMigration()

        assertThat(damageFileMigration.vehicle.usingCostCenter).isNull()
    }

    companion object {
        fun damageReportMigrationRequirements(damageReportMigration: DamageReportMigration) =
            Consumer<DamageFileMigration> { damageFileMigration: DamageFileMigration ->
                // currently nothing here, do not forget to update once we have proper mapping in
                // place
                assertThat(damageFileMigration.vehicle.vin)
                    .isEqualTo(damageReportMigration.damageReportListItem.car.vin)
                assertThat(damageFileMigration.vehicle.vGuid)
                    .isEqualTo(damageReportMigration.damageReportListItem.car.carPartnerId)
                assertThat(damageFileMigration.vehicle.licensePlate)
                    .isEqualTo(damageReportMigration.damageReportListItem.car.licensePlate)
                assertThat(damageFileMigration.vehicle.fleetId)
                    .isEqualTo(damageReportMigration.damageReportListItem.car.fleetPartnerId)
                val insuranceBroker = damageReportMigration.car.insurances?.firstOrNull()?.insuranceBroker
                if (null == insuranceBroker) {
                    assertThat(damageFileMigration.vehicle.leasingArt).isEmpty()
                } else {
                    assertThat(damageFileMigration.vehicle.leasingArt)
                        .isEqualTo(insuranceBroker.replace("LeasingArt:", ""))
                }
                assertThat(damageFileMigration.vehicle.costCenter)
                    .isEqualTo(damageReportMigration.car.insurances?.firstOrNull()?.costCenter)
                assertThat(damageFileMigration.vehicle.externalReference)
                    .isEqualTo(damageReportMigration.damageReportListItem.car.id)
                assertThat(damageFileMigration.deductibles).isEqualTo(damageReportMigration.invoiceData.deductibles)
                assertThat(damageFileMigration.claimsTowardsEmployee)
                    .isEqualTo(damageReportMigration.invoiceData.claimsTowardsEmployee)
                assertThat(damageFileMigration.externalReference)
                    .isEqualTo(damageReportMigration.damageReportListItem.id)
                assertThat(damageFileMigration.externalCreationDate)
                    .usingComparator(OffsetDateTime::compareTo)
                    .isEqualTo(OffsetDateTime.parse(damageReportMigration.damageReportListItem.createdAt))
                assertThat(damageFileMigration.insuranceCoverage)
                    .isEqualTo(damageReportMigration.damageReportDetails.insuranceCoverage)
                assertThat(damageFileMigration.isClosed)
                    .isEqualTo(
                        DAMAGE_REPORT_CLOSED_STATUS.equals(damageReportMigration.damageReportListItem.status, true)
                    )
                assertThat(damageFileMigration.isPool).isEqualTo(damageReportMigration.damageReportListItem.car.isPool)
                if (damageReportMigration.damageReportListItem.car.isPool) {
                    assertThat(damageFileMigration.driverEmail)
                        .isEqualTo(damageReportMigration.damageReportListItem.reporter!!.email)
                    assertThat(damageFileMigration.employeeToBeCharged).isNull()
                } else {
                    assertThat(damageFileMigration.employeeToBeCharged!!.value)
                        .isEqualTo(damageReportMigration.damageReportListItem.car.driver!!.partnerId)
                    assertThat(damageFileMigration.driverEmail).isNull()
                }
                if (null !== damageReportMigration.damageReportListItem.reporter?.companyAffiliation) {
                    assertThat(damageFileMigration.isExternal)
                        .isEqualTo(
                            ReporterInfo.CompanyAffiliation.external ==
                                damageReportMigration.damageReportListItem.reporter!!.companyAffiliation
                        )
                }
                if (
                    damageReportMigration.damageReportDetails.type.equals(
                        DamageReportResponseDamageReport.Type.ExteriorDamageReport
                    )
                ) {
                    assertThat(damageFileMigration.damageDate)
                        .isEqualTo(damageReportMigration.damageReportDetails.damageDateTime)
                } else
                    assertThat(damageFileMigration.damageDate)
                        .isEqualTo(damageReportMigration.damageReportListItem.createdAt)
                assertThat(damageFileMigration.damageResponsibility)
                    .isEqualTo(damageReportMigration.damageReportDetails.damageResponsibility)
            }

        @JvmStatic
        fun damageReportMigrationProvider(): Set<DamageReportMigration> = DamageReportMigrationBuilder.buildMultiple(10)
    }
}
