package com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix

import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefilemigration.CarBuilder
import com.emh.damagemanagement.damagefilemigration.DamageReportListItemBuilder
import com.emh.damagemanagement.damagefilemigration.IntegrationTest
import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigration
import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigrationException
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.*
import com.fasterxml.jackson.databind.ObjectMapper
import java.io.BufferedReader
import java.math.BigDecimal
import java.time.LocalDate
import java.util.*
import java.util.concurrent.TimeUnit
import java.util.function.Supplier
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.*
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ClassPathResource
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@TestPropertySource(properties = ["repairfix.base-url=http://localhost:8181"])
class RepairfixDamageReportAdapterTest {

    @Autowired private lateinit var repairfixDamageReportAdapter: RepairfixDamageReportAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    @Value("\${repairfix.damage-file-migration.from-date}") private lateinit var fromDate: String
    private val deductibleAndClaimTowardsEmployeeInvoice =
        ClassPathResource("invoices/deductible-claim-towards-employee-invoice.json")

    private val deductibleInvoice = ClassPathResource("invoices/deductible-invoice.json")

    private val claimTowardsEmployeeInvoice = ClassPathResource("invoices/claim-towards-employee-invoice.json")

    private val repairInvoice = ClassPathResource("invoices/repair-invoice.json")

    private val emptyInvoice = ClassPathResource("invoices/empty-invoice.json")

    private val invoiceWithDeductibleAndClaimTowardsEmployee =
        deductibleAndClaimTowardsEmployeeInvoice.inputStream.bufferedReader().use(BufferedReader::readText)

    private val carWithCostCenter = CarDetails(car = CarBuilder.buildSingle())

    private val invoiceWithOnlyDeductible = deductibleInvoice.inputStream.bufferedReader().use(BufferedReader::readText)

    private val invoiceWithOnlyClaimTowardsEmployee =
        claimTowardsEmployeeInvoice.inputStream.bufferedReader().use(BufferedReader::readText)
    private val invoiceWithOnlyRepair = repairInvoice.inputStream.bufferedReader().use(BufferedReader::readText)
    private val invoiceWithNoRecords = emptyInvoice.inputStream.bufferedReader().use(BufferedReader::readText)

    private val fullInsuranceAndDriverCausedAccidentDamageReportPath =
        ClassPathResource("damage-reports/full-insurance-driver-caused-accident-exterior-damage-report.json")
    private val partialInsuranceAndOtherPartyCausedAccidentDamageReportPath =
        ClassPathResource("damage-reports/partial-insurance-other-party-caused-accident-exterior-damage-report.json")
    private val thirdPartyBothCausedAccidentInsuranceDamageReportPath =
        ClassPathResource("damage-reports/third-party-insurance-both-caused-accident-exterior-damage-report.json")
    private val unknownInsuranceAndResponsibilityNotClearDamageReportPath =
        ClassPathResource("damage-reports/unknown-insurance-responsibility-not-clear-exterior-damage-report.json")
    private val nullInsuranceAndNullDamageResponsibilityDamageReportPath =
        ClassPathResource("damage-reports/no-insurance-no-damage-responsibility-exterior-damage-report.json")
    private val notCoveredInsuranceDamageReportPath =
        ClassPathResource("damage-reports/not-covered-insurance-exterior-damage-report.json")
    private val interiorDamageReportPath = ClassPathResource("damage-reports/interior-damage-report.json")
    private val updateDamageReportPath = ClassPathResource("damage-reports/update-damage-report.json")
    private val damageReportWithMalformedDataPath =
        ClassPathResource("damage-reports/malformed-exterior-damage-report.json")

    private val fullInsuranceAndDriverCausedAccidentExteriorDamageReport =
        fullInsuranceAndDriverCausedAccidentDamageReportPath.inputStream.bufferedReader().use(BufferedReader::readText)

    private val partialInsuranceAndOtherPartyCausedAccidentDamageReport =
        partialInsuranceAndOtherPartyCausedAccidentDamageReportPath.inputStream
            .bufferedReader()
            .use(BufferedReader::readText)

    private val thirdPartyInsuranceAndBothCausedAccidentDamageReport =
        thirdPartyBothCausedAccidentInsuranceDamageReportPath.inputStream.bufferedReader().use(BufferedReader::readText)

    private val unknownInsuranceAndRespNotClearDamageReport =
        unknownInsuranceAndResponsibilityNotClearDamageReportPath.inputStream
            .bufferedReader()
            .use(BufferedReader::readText)

    private val noInsuranceAndNoDamageResponsibilityDamageReport =
        nullInsuranceAndNullDamageResponsibilityDamageReportPath.inputStream
            .bufferedReader()
            .use(BufferedReader::readText)

    private val notCoveredInsuranceDamageReport =
        notCoveredInsuranceDamageReportPath.inputStream.bufferedReader().use(BufferedReader::readText)

    private val interiorDamageReport =
        interiorDamageReportPath.inputStream.bufferedReader().use(BufferedReader::readText)

    private val updateDamageReport = updateDamageReportPath.inputStream.bufferedReader().use(BufferedReader::readText)

    private val malformedDamageReport =
        damageReportWithMalformedDataPath.inputStream.bufferedReader().use(BufferedReader::readText)

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8181)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val unauthorizedResponse =
        UnauthorizedErrorResponse(
            statusCode = BigDecimal.valueOf(401),
            message = "You need to be authenticated for this operation.",
            error = "Unauthorized",
        )

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")

    private val unprocessableEntityResponse =
        UnprocessableEntityErrorResponse(status = BigDecimal.valueOf(422), message = "I am an error of business.")

    private val successfulOperationResult = OperationResult(true)

    private val unsuccessfulOperationResult = OperationResult(false)

    @Test
    fun `should set apikey in authorization header`() {
        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(0))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        assertThatNoException().isThrownBy { repairfixDamageReportAdapter.getDamageFileUpdates().blockLast() }
        val request: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        // if this fails, your application-test is faulty
        assertThat(request.headers["Authorization"]).isEqualTo("Bearer apiKey")
    }

    @Test
    fun `should return all damage reports`() {
        val resultPages =
            (1..2).map {
                DamageReportsListData(
                    damageReports = DamageReportListItemBuilder.buildMultiple(10).toList(),
                    total = BigDecimal.valueOf(20),
                )
            }

        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(20))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.queryParameterNames.contains("page") ->
                            MockResponse.Builder()
                                .body(
                                    objectMapper.writeValueAsString(
                                        resultPages[Integer.valueOf(request.url.queryParameter("page")) - 1]
                                    )
                                )
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/cars/")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carWithCostCenter))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/[^/]+/invoices$")) ->
                            MockResponse.Builder()
                                .body(invoiceWithDeductibleAndClaimTowardsEmployee)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/[^/]")) ->
                            MockResponse.Builder()
                                .body(interiorDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val damageFilesFromAdapter: List<DamageFileMigration> =
            repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten().sortedBy {
                it.externalReference!!
            }

        val damageFilesFromResultPages = resultPages.flatMap { page -> page.damageReports }.sortedBy { it.id }

        assertThat(damageFilesFromAdapter).zipSatisfy(damageFilesFromResultPages) {
            damageFileNewOrUpdate: DamageFileMigration,
            damageReportListItem: DamageReportListItem ->
            assertThat(damageFileNewOrUpdate.externalReference).isEqualTo(damageReportListItem.id)
        }

        val requestForTotalNumberOfDamageReports: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val requestForPage1: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val requestForPage2: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        assertThat(requestForTotalNumberOfDamageReports.url.queryParameter("fromDate")).isEqualTo(fromDate)
        assertThat(requestForPage1.url.queryParameter("fromDate")).isEqualTo(fromDate)
        assertThat(requestForPage2.url.queryParameter("fromDate")).isEqualTo(fromDate)
        assertThat(requestForTotalNumberOfDamageReports.url.queryParameter("toDate"))
            .isEqualTo(LocalDate.now().toString())
        assertThat(requestForPage1.url.queryParameter("toDate")).isEqualTo(LocalDate.now().toString())
        assertThat(requestForPage2.url.queryParameter("toDate")).isEqualTo(LocalDate.now().toString())
        assertThat(requestForTotalNumberOfDamageReports.url.queryParameter("dateFilterField")).isEqualTo("updatedAt")
        assertThat(requestForPage1.url.queryParameter("dateFilterField")).isEqualTo("updatedAt")
        assertThat(requestForPage2.url.queryParameter("dateFilterField")).isEqualTo("updatedAt")
    }

    @Test
    fun `should return emptyList if no damage reports exist`() {
        val totalDamageReportsResultPage = DamageReportsListData(damageReports = emptyList(), total = BigDecimal.ZERO)

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        val listOfAllDamageReports =
            repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten()

        assertThat(listOfAllDamageReports).isEmpty()
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw DamageFileMigrationException in case of retryable 4xx response when getting all damage reports`() {
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unauthorizedResponse))
                .addHeader("Content-Type", "application/json")
                .code(401)
                .build()
        )

        assertThatExceptionOfType(DamageFileMigrationException::class.java).isThrownBy {
            repairfixDamageReportAdapter.getDamageFileUpdates().blockLast()
        }
    }

    @Test
    fun `should filter deductible and claimTowardsEmployee if present`() {

        val damageReportForDeductibleAndClaimTowardsEmployeeInvoice = DamageReportListItemBuilder().build()
        val damageReportForDeductibleInvoice = DamageReportListItemBuilder().build()
        val damageReportForClaimTowardsEmployeeInvoice = DamageReportListItemBuilder().build()
        val damageReportForRepairInvoice = DamageReportListItemBuilder().build()
        val damageReportWithNoInvoices = DamageReportListItemBuilder().build()

        val resultPages =
            listOf(
                DamageReportsListData(
                    damageReports =
                        listOf(
                            damageReportForDeductibleAndClaimTowardsEmployeeInvoice,
                            damageReportForDeductibleInvoice,
                            damageReportForClaimTowardsEmployeeInvoice,
                            damageReportForRepairInvoice,
                            damageReportWithNoInvoices,
                        ),
                    total = BigDecimal.valueOf(5),
                )
            )

        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(5))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains(
                                "/damage-reports/${damageReportForDeductibleAndClaimTowardsEmployeeInvoice.id}/invoices"
                            ) ->
                            MockResponse.Builder()
                                .body(invoiceWithDeductibleAndClaimTowardsEmployee)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/cars/")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carWithCostCenter))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains("/damage-reports/${damageReportForDeductibleInvoice.id}/invoices") ->
                            MockResponse.Builder()
                                .body(invoiceWithOnlyDeductible)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains("/damage-reports/${damageReportForClaimTowardsEmployeeInvoice.id}/invoices") ->
                            MockResponse.Builder()
                                .body(invoiceWithOnlyClaimTowardsEmployee)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains("/damage-reports/${damageReportForRepairInvoice.id}/invoices") ->
                            MockResponse.Builder()
                                .body(invoiceWithOnlyRepair)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains("/damage-reports/${damageReportWithNoInvoices.id}/invoices") ->
                            MockResponse.Builder()
                                .body(invoiceWithNoRecords)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/[^/]")) ->
                            MockResponse.Builder()
                                .body(interiorDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val damageFilesFromAdapter: List<DamageFileMigration> =
            repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten()

        val damageFileWithDeductibleAndClaimTowardsEmployee =
            damageFilesFromAdapter.single {
                damageReportForDeductibleAndClaimTowardsEmployeeInvoice.id == it.externalReference
            }

        val damageFileWithOnlyDeductible =
            damageFilesFromAdapter.single { damageReportForDeductibleInvoice.id == it.externalReference }

        val damageFileWithClaimTowardsEmployee =
            damageFilesFromAdapter.single { damageReportForClaimTowardsEmployeeInvoice.id == it.externalReference }

        val damageFileWithRepair =
            damageFilesFromAdapter.single { damageReportForRepairInvoice.id == it.externalReference }

        val damageFileWithNoInvoices =
            damageFilesFromAdapter.single { damageReportWithNoInvoices.id == it.externalReference }

        assertThat(damageFileWithDeductibleAndClaimTowardsEmployee.deductibles)
            .contains(MonetaryAmount(BigDecimal(500)))
        assertThat(damageFileWithDeductibleAndClaimTowardsEmployee.claimsTowardsEmployee)
            .contains(MonetaryAmount(BigDecimal(5432)))

        assertThat(damageFileWithOnlyDeductible.deductibles).contains(MonetaryAmount(BigDecimal(512)))
        assertThat(damageFileWithOnlyDeductible.claimsTowardsEmployee).isEmpty()

        assertThat(damageFileWithClaimTowardsEmployee.deductibles).isEmpty()
        assertThat(damageFileWithClaimTowardsEmployee.claimsTowardsEmployee).contains(MonetaryAmount(BigDecimal(123)))

        assertThat(damageFileWithRepair.deductibles).isEmpty()
        assertThat(damageFileWithRepair.claimsTowardsEmployee).isEmpty()

        assertThat(damageFileWithNoInvoices.deductibles).isEmpty()
        assertThat(damageFileWithNoInvoices.claimsTowardsEmployee).isEmpty()
    }

    @Test
    fun `should not fail migration if a single invoice request fails`() {
        val unprocessableDamageReport = DamageReportListItemBuilder().build()
        val successfulDamageReport = DamageReportListItemBuilder().build()
        val resultPages =
            listOf(
                DamageReportsListData(
                    damageReports = listOf(unprocessableDamageReport, successfulDamageReport),
                    total = BigDecimal.valueOf(2),
                )
            )
        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(2))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains(Regex("/damage-reports/${successfulDamageReport.id}/invoices$")) ->
                            MockResponse.Builder()
                                .body(invoiceWithOnlyDeductible)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains(Regex("/damage-reports/${unprocessableDamageReport.id}/invoices$")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(422)
                                .build()
                        request.url.toString().contains(Regex("/cars/")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carWithCostCenter))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/[^/]")) ->
                            MockResponse.Builder()
                                .body(interiorDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val damageFilesFromAdapter: List<DamageFileMigration> =
            repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten()

        assertThat(damageFilesFromAdapter.size).isEqualTo(1)

        val successDamageFileFromAdapter =
            damageFilesFromAdapter.single { it.externalReference == successfulDamageReport.id }
        assertThat(successDamageFileFromAdapter.deductibles).contains(MonetaryAmount(BigDecimal(512)))
        assertThat(successDamageFileFromAdapter.claimsTowardsEmployee).isEmpty()
    }

    @Test
    fun `should map insurance coverage, damage responsibility and damageDate for damage reports`() {

        val (
            exteriorDamageReportWithFullInsuranceAndDriverCausedAccident,
            exteriorDamageReportWithPartialInsuranceAndOtherPartyCausedAccident,
            exteriorDamageReportWithThirdPartyInsuranceAndBothCausedAccident,
            exteriorDamageReportWithUnknownInsuranceAndRespIsNotClear,
            exteriorDamageReportWithNoInsuranceAndNoResp) =
            DamageReportListItemBuilder.buildMultiple(5).map { it }

        val (exteriorDamageReportWithNotCoveredInsuranceCoverage, interiorDamageReportItem) =
            DamageReportListItemBuilder.buildMultiple(2).map { it }

        val resultPages =
            listOf(
                DamageReportsListData(
                    damageReports =
                        listOf(
                            exteriorDamageReportWithFullInsuranceAndDriverCausedAccident,
                            exteriorDamageReportWithPartialInsuranceAndOtherPartyCausedAccident,
                            exteriorDamageReportWithNoInsuranceAndNoResp,
                            exteriorDamageReportWithThirdPartyInsuranceAndBothCausedAccident,
                            exteriorDamageReportWithUnknownInsuranceAndRespIsNotClear,
                            exteriorDamageReportWithNotCoveredInsuranceCoverage,
                            interiorDamageReportItem,
                        ),
                    total = BigDecimal.valueOf(7),
                )
            )

        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(7))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/[^/]+/invoices$")) ->
                            MockResponse.Builder()
                                .body(invoiceWithNoRecords)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains(
                                "/damage-reports/${exteriorDamageReportWithFullInsuranceAndDriverCausedAccident.id}"
                            ) ->
                            MockResponse.Builder()
                                .body(fullInsuranceAndDriverCausedAccidentExteriorDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains(
                                "/damage-reports/${exteriorDamageReportWithPartialInsuranceAndOtherPartyCausedAccident.id}"
                            ) ->
                            MockResponse.Builder()
                                .body(partialInsuranceAndOtherPartyCausedAccidentDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains(
                                "/damage-reports/${exteriorDamageReportWithThirdPartyInsuranceAndBothCausedAccident.id}"
                            ) ->
                            MockResponse.Builder()
                                .body(thirdPartyInsuranceAndBothCausedAccidentDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains(
                                "/damage-reports/${exteriorDamageReportWithUnknownInsuranceAndRespIsNotClear.id}"
                            ) ->
                            MockResponse.Builder()
                                .body(unknownInsuranceAndRespNotClearDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains("/damage-reports/${exteriorDamageReportWithNoInsuranceAndNoResp.id}") ->
                            MockResponse.Builder()
                                .body(noInsuranceAndNoDamageResponsibilityDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url
                            .toString()
                            .contains("/damage-reports/${exteriorDamageReportWithNotCoveredInsuranceCoverage.id}") ->
                            MockResponse.Builder()
                                .body(notCoveredInsuranceDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains("/damage-reports/${interiorDamageReportItem.id}") ->
                            MockResponse.Builder()
                                .body(interiorDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/cars/")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carWithCostCenter))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val damageFilesFromAdapter: List<DamageFileMigration> =
            repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten()

        val damageFileForFullInsuranceAndDriverCausedExteriorDamageType =
            damageFilesFromAdapter.single {
                exteriorDamageReportWithFullInsuranceAndDriverCausedAccident.id == it.externalReference
            }

        val damageFileForPartialInsuranceAndOtherPartyCausedExteriorDamageType =
            damageFilesFromAdapter.single {
                exteriorDamageReportWithPartialInsuranceAndOtherPartyCausedAccident.id == it.externalReference
            }

        val damageFileForUnknownInsuranceAndRespNotClearExteriorDamageType =
            damageFilesFromAdapter.single {
                exteriorDamageReportWithUnknownInsuranceAndRespIsNotClear.id == it.externalReference
            }

        val damageFileForThirdPartyInsuranceAndBothCausedAccidentExteriorDamageType =
            damageFilesFromAdapter.single {
                exteriorDamageReportWithThirdPartyInsuranceAndBothCausedAccident.id == it.externalReference
            }

        val damageFileForNotCoveredInsuranceExteriorDamageType =
            damageFilesFromAdapter.single {
                exteriorDamageReportWithNotCoveredInsuranceCoverage.id == it.externalReference
            }

        val interiorDamageFile = damageFilesFromAdapter.single { interiorDamageReportItem.id == it.externalReference }

        val damageFileWithNoInsuranceAndResponsibility =
            damageFilesFromAdapter.single { exteriorDamageReportWithNoInsuranceAndNoResp.id == it.externalReference }

        assertThat(damageFileForFullInsuranceAndDriverCausedExteriorDamageType.insuranceCoverage)
            .isEqualTo(InsuranceCoverage.FULL)
        assertThat(damageFileForFullInsuranceAndDriverCausedExteriorDamageType.damageResponsibility)
            .isEqualTo(DamageResponsibility.DRIVER_CAUSED_ACCIDENT)
        assertThat(damageFileForFullInsuranceAndDriverCausedExteriorDamageType.damageDate)
            .isEqualTo("2023-09-11T05:39:00.000Z")

        assertThat(damageFileForPartialInsuranceAndOtherPartyCausedExteriorDamageType.insuranceCoverage)
            .isEqualTo(InsuranceCoverage.PARTIAL)
        assertThat(damageFileForPartialInsuranceAndOtherPartyCausedExteriorDamageType.damageResponsibility)
            .isEqualTo(DamageResponsibility.OTHER_PARTY_CAUSED_ACCIDENT)
        assertThat(damageFileForUnknownInsuranceAndRespNotClearExteriorDamageType.insuranceCoverage)
            .isEqualTo(InsuranceCoverage.UNKNOWN)
        assertThat(damageFileForUnknownInsuranceAndRespNotClearExteriorDamageType.damageResponsibility)
            .isEqualTo(DamageResponsibility.RESPONSIBILITY_IS_NOT_CLEAR)

        assertThat(damageFileForThirdPartyInsuranceAndBothCausedAccidentExteriorDamageType.insuranceCoverage)
            .isEqualTo(InsuranceCoverage.THIRD_PARTY)
        assertThat(damageFileForThirdPartyInsuranceAndBothCausedAccidentExteriorDamageType.damageResponsibility)
            .isEqualTo(DamageResponsibility.BOTH_CAUSED_ACCIDENT)
        assertThat(damageFileForNotCoveredInsuranceExteriorDamageType.insuranceCoverage)
            .isEqualTo(InsuranceCoverage.NOT_COVERED)

        assertThat(interiorDamageFile)
            .satisfies({
                assertThat(it.insuranceCoverage).isEqualTo(InsuranceCoverage.NOT_SPECIFIED)
                assertThat(it.damageResponsibility).isEqualTo(DamageResponsibility.DRIVER_CAUSED_ACCIDENT)
            })
        assertThat(damageFileWithNoInsuranceAndResponsibility)
            .satisfies({
                assertThat(it.insuranceCoverage).isEqualTo(InsuranceCoverage.NOT_SPECIFIED)
                assertThat(it.damageResponsibility).isEqualTo(DamageResponsibility.DRIVER_CAUSED_ACCIDENT)
            })

        assertThat(interiorDamageFile.damageDate).isEqualTo(interiorDamageReportItem.createdAt)
    }

    @Test
    fun `should not fail migration if a single damage report request fails`() {
        val unprocessableDamageReport = DamageReportListItemBuilder().build()
        val successfulDamageReport = DamageReportListItemBuilder().build()
        val resultPages =
            listOf(
                DamageReportsListData(
                    damageReports = listOf(unprocessableDamageReport, successfulDamageReport),
                    total = BigDecimal.valueOf(2),
                )
            )
        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(2))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/[^/]+/invoices$")) ->
                            MockResponse.Builder()
                                .body(invoiceWithNoRecords)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/${unprocessableDamageReport.id}")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(422)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/${successfulDamageReport.id}")) ->
                            MockResponse.Builder()
                                .body(fullInsuranceAndDriverCausedAccidentExteriorDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/cars/")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carWithCostCenter))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val damageFilesFromAdapter: List<DamageFileMigration> =
            repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten()

        assertThat(damageFilesFromAdapter.size).isEqualTo(1)

        val successDamageFileFromAdapter =
            damageFilesFromAdapter.single { it.externalReference == successfulDamageReport.id }
        assertThat(successDamageFileFromAdapter.insuranceCoverage).isEqualTo(InsuranceCoverage.FULL)
    }

    @Test
    fun `should not fail migration if conversion of a single damage report to ExteriorDamageReport fails `() {
        val malformedDamageReportItem = DamageReportListItemBuilder().build()
        val successfulDamageReportItem = DamageReportListItemBuilder().build()
        val resultPages =
            listOf(
                DamageReportsListData(
                    damageReports = listOf(malformedDamageReportItem, successfulDamageReportItem),
                    total = BigDecimal.valueOf(2),
                )
            )
        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(2))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/[^/]+/invoices$")) ->
                            MockResponse.Builder()
                                .body(invoiceWithNoRecords)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/${successfulDamageReportItem.id}")) ->
                            MockResponse.Builder()
                                .body(partialInsuranceAndOtherPartyCausedAccidentDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/damage-reports/${malformedDamageReportItem.id}")) ->
                            MockResponse.Builder()
                                .body(malformedDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        request.url.toString().contains(Regex("/cars/")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carWithCostCenter))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val damageFilesFromAdapter: List<DamageFileMigration> =
            repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten()

        assertThat(damageFilesFromAdapter.size).isEqualTo(1)

        val successDamageFileFromAdapter =
            damageFilesFromAdapter.single { it.externalReference == successfulDamageReportItem.id }
        assertThat(successDamageFileFromAdapter.insuranceCoverage).isEqualTo(InsuranceCoverage.PARTIAL)
    }

    @Test
    fun `should return update result`() {
        val damageReportId = UUID.randomUUID().toString()
        val externalId = UUID.randomUUID().toString()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains(Regex("/damage-reports/[a-zA-Z0-9-]+$")) ->
                            MockResponse.Builder()
                                .body(updateDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }
        val operationResult = repairfixDamageReportAdapter.updateInternalOrderNumber(damageReportId, externalId)

        assertThat(operationResult.isSuccess).isTrue()
        assertThat(operationResult.externalId).isEqualTo(damageReportId)
    }

    companion object {
        @DynamicPropertySource
        @JvmStatic
        fun properties(registry: DynamicPropertyRegistry) {
            registry.add(
                "repairfix.damage-file-migration.from-date",
                Supplier { LocalDate.now().minusDays(1).toString() },
            )
        }
    }
}

@IntegrationTest
@TestPropertySource(properties = ["repairfix.base-url=http://localhost:8181"])
class RepairfixDamageReportAdapterThresholdTest {
    companion object {
        @DynamicPropertySource
        @JvmStatic
        fun properties(registry: DynamicPropertyRegistry) {
            registry.add(
                "repairfix.damage-file-migration.from-date",
                Supplier { LocalDate.now().minusDays(35).toString() },
            )
        }
    }

    @Autowired private lateinit var repairfixDamageReportAdapter: RepairfixDamageReportAdapter

    @Autowired private lateinit var objectMapper: ObjectMapper
    private lateinit var mockWebServer: MockWebServer
    private val carWithCostCenter = CarDetails(car = CarBuilder.buildSingle())
    private val deductibleAndClaimTowardsEmployeeInvoice =
        ClassPathResource("invoices/deductible-claim-towards-employee-invoice.json")
    private val invoiceWithDeductibleAndClaimTowardsEmployee =
        deductibleAndClaimTowardsEmployeeInvoice.inputStream.bufferedReader().use(BufferedReader::readText)
    private val interiorDamageReportPath = ClassPathResource("damage-reports/interior-damage-report.json")
    private val interiorDamageReport =
        interiorDamageReportPath.inputStream.bufferedReader().use(BufferedReader::readText)
    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8181)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    @Test
    fun `should return all damage reports within threshold`() {
        val resultPages =
            DamageReportsListData(
                damageReports = DamageReportListItemBuilder.buildMultiple(10).toList(),
                total = BigDecimal.valueOf(10),
            )

        val totalDamageReportsResultPage =
            DamageReportsListData(damageReports = emptyList(), total = BigDecimal.valueOf(10))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalDamageReportsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        request.url.queryParameterNames.contains("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        request.url.toString().contains(Regex("/cars/")) ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carWithCostCenter))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        request.url.toString().contains(Regex("/damage-reports/[^/]+/invoices$")) ->
                            MockResponse.Builder()
                                .body(invoiceWithDeductibleAndClaimTowardsEmployee)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        request.url.toString().contains(Regex("/damage-reports/[^/]")) ->
                            MockResponse.Builder()
                                .body(interiorDamageReport)
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixDamageReportAdapter.getDamageFileUpdates().collectList().block()!!.flatten().sortedBy {
            it.externalReference!!
        }

        val requestForTotalNumberOfDamageReports: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val requestForPage1: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val expectedFromDate = LocalDate.now().minusDays(2).toString()
        assertThat(requestForTotalNumberOfDamageReports.url.queryParameter("fromDate")).isEqualTo(expectedFromDate)
        assertThat(requestForPage1.url.queryParameter("fromDate")).isEqualTo(expectedFromDate)
        assertThat(requestForTotalNumberOfDamageReports.url.queryParameter("toDate"))
            .isEqualTo(LocalDate.now().toString())
        assertThat(requestForPage1.url.queryParameter("toDate")).isEqualTo(LocalDate.now().toString())
        assertThat(requestForTotalNumberOfDamageReports.url.queryParameter("dateFilterField")).isEqualTo("updatedAt")
        assertThat(requestForPage1.url.queryParameter("dateFilterField")).isEqualTo("updatedAt")
    }
}
