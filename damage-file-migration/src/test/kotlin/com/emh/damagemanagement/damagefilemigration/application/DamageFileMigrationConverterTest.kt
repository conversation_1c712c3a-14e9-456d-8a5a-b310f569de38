/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.damagefilemigration.application

import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.service.DamageFileNewOrUpdate
import com.emh.damagemanagement.damagefilemigration.DamageFileMigrationBuilder
import java.time.OffsetDateTime
import java.util.function.Consumer
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class DamageFileMigrationConverterTest {

    @ParameterizedTest
    @MethodSource("damageFileMigrationProvider")
    fun `should convert DamageFileMigration to DamageFileNewOrUpdate`(damageFileMigration: DamageFileMigration) {
        val damageFileNewOrUpdate = damageFileMigration.toDamageFileNewOrUpdate()

        assertThat(damageFileNewOrUpdate).satisfies(damageFileMigrationRequirements(damageFileMigration))
    }

    companion object {
        fun damageFileMigrationRequirements(damageFileMigration: DamageFileMigration) =
            Consumer<DamageFileNewOrUpdate> { damageFileNewOrUpdate: DamageFileNewOrUpdate ->
                // currently nothing here, do not forget to update once we have proper mapping in
                // place
                assertThat(damageFileNewOrUpdate.damageReport).isNotNull
                assertThat(damageFileNewOrUpdate.vehicle.vin).isEqualTo(damageFileMigration.vehicle.vin)
                assertThat(damageFileNewOrUpdate.vehicle.vGuid).isEqualTo(damageFileMigration.vehicle.vGuid)
                assertThat(damageFileNewOrUpdate.vehicle.licensePlate)
                    .isEqualTo(damageFileMigration.vehicle.licensePlate)
                assertThat(damageFileNewOrUpdate.vehicle.leasingArt).isEqualTo(damageFileMigration.vehicle.leasingArt)
                assertThat(damageFileNewOrUpdate.vehicle.fleetId).isEqualTo(damageFileMigration.vehicle.fleetId)
                assertThat(damageFileNewOrUpdate.vehicle.costCenter).isEqualTo(damageFileMigration.vehicle.costCenter)
                assertThat(damageFileNewOrUpdate.vehicle.usingCostCenter)
                    .isEqualTo(damageFileMigration.vehicle.usingCostCenter)
                assertThat(damageFileNewOrUpdate.vehicle.externalReference)
                    .isEqualTo(damageFileMigration.vehicle.externalReference)
                assertThat(damageFileNewOrUpdate.deductible)
                    .isEqualTo(damageFileMigration.deductibles.sumOf { it.value }.let { MonetaryAmount.of(it) })
                assertThat(damageFileNewOrUpdate.claimTowardsEmployee)
                    .isEqualTo(
                        damageFileMigration.claimsTowardsEmployee.sumOf { it.value }.let { MonetaryAmount.of(it) }
                    )
                assertThat(damageFileNewOrUpdate.externalReference).isEqualTo(damageFileMigration.externalReference)
                assertThat(damageFileNewOrUpdate.employeeToBeCharged?.value)
                    .isEqualTo(damageFileMigration.employeeToBeCharged!!.value)
                assertThat(damageFileNewOrUpdate.externalCreationDate)
                    .usingComparator(OffsetDateTime::compareTo)
                    .isEqualTo(damageFileMigration.externalCreationDate)
                assertThat(damageFileNewOrUpdate.insuranceCoverage).isEqualTo(damageFileMigration.insuranceCoverage)
                assertThat(damageFileNewOrUpdate.isClosed).isEqualTo(damageFileMigration.isClosed)
                assertThat(damageFileNewOrUpdate.damageResponsibility)
                    .isEqualTo(damageFileMigration.damageResponsibility)
                assertThat(damageFileNewOrUpdate.damageDate).isEqualTo(damageFileMigration.damageDate)
            }

        @JvmStatic
        fun damageFileMigrationProvider(): Set<DamageFileMigration> =
            DamageFileMigrationBuilder.buildMultiple(10).toSet()
    }
}
