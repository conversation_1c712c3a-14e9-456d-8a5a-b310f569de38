/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application

import com.emh.damagemanagement.damagefile.TestDamageFileRepository
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.service.DamageFileNewOrUpdate
import com.emh.damagemanagement.damagefilemigration.DamageFileMigrationBuilder
import com.emh.damagemanagement.damagefilemigration.EmployeeBuilder
import com.emh.damagemanagement.damagefilemigration.IntegrationTest
import com.emh.damagemanagement.damagefilemigration.application.port.DamageFileOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.EmployeeOutPort
import com.emh.damagemanagement.shared.createRandomString
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@IntegrationTest
class DamageFileMigrationServiceTest {

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    @Qualifier("damage-file-cleanup")
    private lateinit var cleanupDamageFiles: () -> Unit

    @Autowired private lateinit var damageFileMigrationService: DamageFileMigrationService
    @Autowired private lateinit var damageFileOutPort: DamageFileOutPort
    @Autowired private lateinit var employeeOutPort: EmployeeOutPort
    @MockitoSpyBean private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase
    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private lateinit var testDamageFileRepository: TestDamageFileRepository

    /**
     * We are integrating actual damage-file module with persistence for our tests. So manual cleanup of persistence is
     * required (we are testing async methods with their own transactions)
     */
    @AfterEach
    fun cleanup() {
        cleanupDamageFiles()
        reset(employeeOutPort)
        reset(damageFileOutPort)
    }

    @Test
    fun `should migrate pool and leasing vehicles damage files`() {
        val (poolVehicleDamageFiles, leasingVehicleDamageFiles) =
            DamageFileMigrationBuilder.buildMultiple(100).partition { it.isPool }
        val poolVehicleDrivers: List<Employee> =
            poolVehicleDamageFiles
                .map {
                    EmployeeBuilder()
                        .companyEmail(it.driverEmail!!)
                        .employeeNumber(createRandomString(Random.nextInt(4, 23)))
                        .build()
                }
                .toList()

        val damageFileUpdates = Flux.fromIterable((poolVehicleDamageFiles + leasingVehicleDamageFiles).chunked(20))

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.just(poolVehicleDrivers))

        val emailsArgumentCaptor = argumentCaptor<List<String>>()
        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()

        damageFileMigrationService.migrateAllDamageReports()

        verify(employeeOutPort, times(5)).getEmployeesByEmail(emailsArgumentCaptor.capture())
        verify(createOrUpdateDamageFileUseCase, times(5))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        val damageFilesUpdate = (poolVehicleDamageFiles + leasingVehicleDamageFiles).sortedBy { it.externalReference }

        assertThat(emailsArgumentCaptor.allValues.flatten())
            .containsExactlyInAnyOrderElementsOf(poolVehicleDamageFiles.map { it.driverEmail })

        assertThat(damageFileNewOrUpdateArgumentCaptor.allValues.flatten().sortedBy { it.externalReference })
            .zipSatisfy(damageFilesUpdate) {
                damageFileNewOrUpdate: DamageFileNewOrUpdate,
                damageFileMigration: DamageFileMigration ->
                assertThat(damageFileNewOrUpdate.externalReference).isEqualTo(damageFileMigration.externalReference)
                assertThat(damageFileNewOrUpdate.employeeToBeCharged?.value)
                    .isEqualTo(
                        if (damageFileMigration.isPool)
                            poolVehicleDrivers
                                .single { it.companyEmail == damageFileMigration.driverEmail }
                                .employeeNumber
                        else damageFileMigration.employeeToBeCharged!!.value
                    )
            }
    }

    @Test
    fun `should migrate pool vehicle damage files with employee email`() {
        val poolVehicleDamageFiles =
            (1..20).map { DamageFileMigrationBuilder().isPool(true).employeeToBeCharged(null).build() }
        val (poolVehicleDamageFilesWithEmployeeEmail, _) = poolVehicleDamageFiles.partition { Random.nextBoolean() }

        val poolVehicleDrivers: List<Employee> =
            poolVehicleDamageFilesWithEmployeeEmail
                .map {
                    EmployeeBuilder()
                        .companyEmail(it.driverEmail!!)
                        .employeeNumber(createRandomString(Random.nextInt(4, 23)))
                        .build()
                }
                .toList()

        val damageFileUpdates = Flux.fromIterable(poolVehicleDamageFiles.chunked(4))

        val emailsArgumentCaptor = argumentCaptor<List<String>>()
        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.just(poolVehicleDrivers))

        damageFileMigrationService.migrateAllDamageReports()

        verify(employeeOutPort, times(5)).getEmployeesByEmail(emailsArgumentCaptor.capture())
        verify(createOrUpdateDamageFileUseCase, times(5))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        val poolVehicleDamageFileUpdate = poolVehicleDamageFilesWithEmployeeEmail.sortedBy { it.externalReference }

        assertThat(emailsArgumentCaptor.allValues.flatten())
            .containsExactlyInAnyOrderElementsOf(poolVehicleDamageFiles.map { it.driverEmail })

        assertThat(damageFileNewOrUpdateArgumentCaptor.allValues.flatten().sortedBy { it.externalReference })
            .zipSatisfy(poolVehicleDamageFileUpdate) {
                damageFileNewOrUpdate: DamageFileNewOrUpdate,
                damageFileMigration: DamageFileMigration ->
                assertThat(damageFileNewOrUpdate.externalReference).isEqualTo(damageFileMigration.externalReference)
                assertThat(damageFileNewOrUpdate.employeeToBeCharged?.value)
                    .isEqualTo(
                        poolVehicleDrivers.single { it.companyEmail == damageFileMigration.driverEmail }.employeeNumber
                    )
            }
    }

    @Test
    fun `should migrate pool vehicle damage files with case mismatching employee email`() {
        val poolVehicleDamageFile =
            DamageFileMigrationBuilder()
                .isPool(true)
                .driverEmail("<EMAIL>")
                .employeeToBeCharged(null)
                .build()

        val poolVehicleDriver: Employee =
            poolVehicleDamageFile.let {
                EmployeeBuilder()
                    .companyEmail("<EMAIL>")
                    .employeeNumber(createRandomString(Random.nextInt(4, 23)))
                    .build()
            }

        val damageFileUpdates = Flux.fromIterable(listOf(poolVehicleDamageFile).chunked(4))

        val emailsArgumentCaptor = argumentCaptor<List<String>>()
        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.just(listOf(poolVehicleDriver)))

        damageFileMigrationService.migrateAllDamageReports()

        verify(employeeOutPort, times(1)).getEmployeesByEmail(emailsArgumentCaptor.capture())
        verify(createOrUpdateDamageFileUseCase, times(1))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        assertThat(emailsArgumentCaptor.firstValue.single()).isEqualTo("<EMAIL>")

        val damageFileNewOrUpdate = damageFileNewOrUpdateArgumentCaptor.firstValue.single()
        assertThat(damageFileNewOrUpdate.externalReference).isEqualTo(poolVehicleDamageFile.externalReference)
        assertThat(damageFileNewOrUpdate.employeeToBeCharged?.value).isEqualTo(poolVehicleDriver.employeeNumber)
    }

    @Test
    fun `should not migrate pool vehicle damage files without employee email`() {
        val poolVehicleDamageFiles =
            (1..20).map { DamageFileMigrationBuilder().isPool(true).employeeToBeCharged(null).build() }

        val damageFileUpdates = Flux.fromIterable(poolVehicleDamageFiles.chunked(4))

        val emailsArgumentCaptor = argumentCaptor<List<String>>()
        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.empty())

        damageFileMigrationService.migrateAllDamageReports()

        verify(employeeOutPort, times(5)).getEmployeesByEmail(emailsArgumentCaptor.capture())
        verify(createOrUpdateDamageFileUseCase, times(5))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        assertThat(emailsArgumentCaptor.allValues.flatten())
            .containsExactlyInAnyOrderElementsOf(poolVehicleDamageFiles.map { it.driverEmail })

        assertThat(damageFileNewOrUpdateArgumentCaptor.allValues.flatten()).isEmpty()
    }

    @Test
    fun `should not verify employee information for external drivers`() {
        val poolVehicleDamageFilesWithExternalDrivers =
            (1..5).map { DamageFileMigrationBuilder().isPool(true).employeeToBeCharged(null).isExternal(true).build() }
        val damageFileUpdates = Flux.fromIterable(poolVehicleDamageFilesWithExternalDrivers.chunked(4))
        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()
        val emailsArgumentCaptor = argumentCaptor<List<String>>()

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.just(emptyList()))
        damageFileMigrationService.migrateAllDamageReports()

        verify(employeeOutPort, times(2)).getEmployeesByEmail(emailsArgumentCaptor.capture())
        verify(createOrUpdateDamageFileUseCase, times(2))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        assertThat(emailsArgumentCaptor.allValues.flatten()).isEmpty()

        assertThat(damageFileNewOrUpdateArgumentCaptor.allValues.flatten()).hasSize(5)
    }

    @Test
    fun `should treat ANUEs as external drivers`() {
        val poolVehicleDamageFile =
            DamageFileMigrationBuilder().isPool(true).employeeToBeCharged(EmployeeNumber("00123456")).build()

        val poolVehicleDrivers: List<Employee> =
            listOf(
                EmployeeBuilder()
                    .companyEmail(poolVehicleDamageFile.driverEmail!!)
                    .employeeGroup(Employee.ANUE_EMPLOYEE_GROUP)
                    .employeeNumber("00123456")
                    .build()
            )

        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.just(poolVehicleDrivers))

        val damageFileUpdates = Flux.fromIterable(listOf(poolVehicleDamageFile).chunked(4))
        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        damageFileMigrationService.migrateAllDamageReports()

        verify(createOrUpdateDamageFileUseCase, times(1))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        val damageFileToBeCreated = damageFileNewOrUpdateArgumentCaptor.allValues.flatten().single()
        assertThat(damageFileToBeCreated.isExternal).isTrue()
    }

    @Test
    fun `should not migrate pool vehicle damage files with mismatching reporter employee number`() {
        val matchingReporterEmployeeNumber = EmployeeNumber("00123456")
        val mail = "<EMAIL>"
        val employeeMatchingReporter =
            EmployeeBuilder().employeeNumber(matchingReporterEmployeeNumber.value).companyEmail(mail).build()
        val poolVehicleDamageFiles =
            (1..5).map {
                DamageFileMigrationBuilder()
                    .isPool(true)
                    .employeeToBeCharged(null)
                    .reporterEmployeeNumber(reporterEmployeeNumber = matchingReporterEmployeeNumber)
                    .driverEmail(mail)
                    .externalReference("match")
                    .build()
            }
        val mismatchingPoolVehicleDamageFiles =
            (1..2).map {
                DamageFileMigrationBuilder()
                    .isPool(true)
                    .employeeToBeCharged(null)
                    .reporterEmployeeNumber(EmployeeNumber("00111222"))
                    .build()
            }

        val damageFileUpdates =
            Flux.fromIterable((poolVehicleDamageFiles + mismatchingPoolVehicleDamageFiles).chunked(4))

        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.just(listOf(employeeMatchingReporter)))

        damageFileMigrationService.migrateAllDamageReports()

        verify(createOrUpdateDamageFileUseCase, times(2))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        val createdDamageFiles = damageFileNewOrUpdateArgumentCaptor.allValues.flatten()
        assertThat(createdDamageFiles).hasSize(5)
        assertThat(createdDamageFiles).allSatisfy { assertThat(it.externalReference).isEqualTo("match") }
    }

    @Test
    fun `should migrate leasing vehicles`() {
        val leasingVehicleDamageFiles = (1..20).map { DamageFileMigrationBuilder().isPool(false).build() }

        val damageFileUpdates = Flux.fromIterable(leasingVehicleDamageFiles.chunked(4))

        whenever(damageFileOutPort.getDamageFileUpdates()).thenReturn(damageFileUpdates)
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.empty())

        damageFileMigrationService.migrateAllDamageReports()

        val emailsArgumentCaptor = argumentCaptor<List<String>>()
        val damageFileNewOrUpdateArgumentCaptor = argumentCaptor<List<DamageFileNewOrUpdate>>()

        verify(employeeOutPort, times(5)).getEmployeesByEmail(emailsArgumentCaptor.capture())
        verify(createOrUpdateDamageFileUseCase, times(5))
            .createOrUpdateDamageFilesAsync(damageFileNewOrUpdateArgumentCaptor.capture())

        val leasingVehicleDamageFileUpdates = leasingVehicleDamageFiles.sortedBy { it.externalReference }

        assertThat(emailsArgumentCaptor.allValues.flatten()).isEmpty()

        assertThat(damageFileNewOrUpdateArgumentCaptor.allValues.flatten().sortedBy { it.externalReference })
            .zipSatisfy(leasingVehicleDamageFileUpdates) {
                damageFileNewOrUpdate: DamageFileNewOrUpdate,
                damageFileMigration: DamageFileMigration ->
                assertThat(damageFileNewOrUpdate.externalReference).isEqualTo(damageFileMigration.externalReference)
                assertThat(damageFileNewOrUpdate.employeeToBeCharged).isEqualTo(damageFileMigration.employeeToBeCharged)
            }
    }

    @Test
    fun `should migrate all damage reports including updates`() {
        val initialDamageFileUpdates = (1..20).map { DamageFileMigrationBuilder().isPool(false).build() }
        whenever(damageFileOutPort.getDamageFileUpdates())
            .thenReturn(Flux.fromIterable(initialDamageFileUpdates.chunked(10)))
        whenever(employeeOutPort.getEmployeesByEmail(any())).thenReturn(Mono.empty())

        damageFileMigrationService.migrateAllDamageReports()
        // block async creation here
        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val damageFileCount = testDamageFileRepository.count().toInt()
                assertThat(damageFileCount).isEqualTo(initialDamageFileUpdates.size)
            }
        val damageFilesNewer = (1..20).map { DamageFileMigrationBuilder().isPool(false).build() }
        val damageFilesUpdated =
            initialDamageFileUpdates
                .map { DamageFileMigrationBuilder().isPool(false).externalReference(it.externalReference!!).build() }
                .toSet()
        whenever(damageFileOutPort.getDamageFileUpdates())
            .thenReturn(Flux.fromIterable((damageFilesNewer + damageFilesUpdated).chunked(10)))

        damageFileMigrationService.migrateAllDamageReports()

        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val allDamageFiles = testDamageFileRepository.findAll()
                assertThat(allDamageFiles.size).isEqualTo(initialDamageFileUpdates.size + damageFilesNewer.size)
                assertThat(damageFilesUpdated).allSatisfy { damageFileMigration ->
                    val updatedDamageFile =
                        allDamageFiles.single { damageFile ->
                            damageFileMigration.externalReference == damageFile.externalReference
                        }
                    assertThat(updatedDamageFile.claimTowardsEmployee!!)
                        .usingComparator(MonetaryAmount::compareTo)
                        .isEqualTo(
                            damageFileMigration.claimsTowardsEmployee.sumOf { it.value }.let { MonetaryAmount(it) }
                        )
                    assertThat(updatedDamageFile.deductible!!)
                        .usingComparator(MonetaryAmount::compareTo)
                        .isEqualTo(damageFileMigration.deductibles.sumOf { it.value }.let { MonetaryAmount(it) })
                }
            }
    }
}
