package com.emh.damagemanagement.damagefilemigration.application

import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class DamageResponsibilityTest {

    @Test
    fun `should return true when damage responsibility is DRIVER_CAUSED_ACCIDENT`() {
        val result = DamageResponsibility.DRIVER_CAUSED_ACCIDENT.driverAtFault
        assertThat(result).isTrue()
    }

    @Test
    fun `should return false when damage responsibility is BOTH_CAUSED_ACCIDENT`() {
        val result = DamageResponsibility.BOTH_CAUSED_ACCIDENT.driverAtFault
        assertThat(result).isFalse()
    }

    @Test
    fun `should return false when damage responsibility is UNKNOWN`() {
        val result = DamageResponsibility.UNKNOWN.driverAtFault
        assertThat(result).isFalse()
    }

    @Test
    fun `should return false when damage responsibility is RESPONSIBILITY_IS_NOT_CLEAR`() {
        val result = DamageResponsibility.RESPONSIBILITY_IS_NOT_CLEAR.driverAtFault
        assertThat(result).isFalse()
    }

    @Test
    fun `should return false when damage responsibility is OTHER_PARTY_CAUSED_ACCIDENT`() {
        val result = DamageResponsibility.OTHER_PARTY_CAUSED_ACCIDENT.driverAtFault
        assertThat(result).isFalse()
    }
}
