package com.emh.damagemanagement.damagefilemigration.application

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.FindDamageFilesWithoutInternalOrderUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFileAlreadyClosedException
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefilemigration.IntegrationTest
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberMigrationOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPortException
import com.emh.damagemanagement.damagefilemigration.domain.service.InternalOrderNumberSyncIndicatorService
import com.emh.damagemanagement.shared.InlineClassesAnswer
import com.emh.damagemanagement.shared.createRandomString
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.reset
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@IntegrationTest
class InternalOrderNumberUpdateServiceTest {

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    @Qualifier("damage-file-cleanup")
    private lateinit var cleanupDamageFiles: () -> Unit

    @Autowired private lateinit var internalOrderNumberUpdateService: InternalOrderNumberUpdateService
    @Autowired private lateinit var internalOrderNumberOutPort: InternalOrderNumberOutPort
    @Autowired private lateinit var internalOrderNumberMigrationOutPort: InternalOrderNumberMigrationOutPort
    @Autowired private lateinit var internalOrderNumberSyncIndicatorService: InternalOrderNumberSyncIndicatorService

    @Autowired
    private lateinit var findDamageFileWithoutInternalOrderUseCase: FindDamageFilesWithoutInternalOrderUseCase
    @MockitoSpyBean private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase

    @AfterEach
    fun cleanup() {
        cleanupDamageFiles()
        reset(internalOrderNumberOutPort)
        reset(internalOrderNumberMigrationOutPort)
    }

    @Test
    fun `should update internal order number in damage files and create sync files`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        whenever(
                internalOrderNumberOutPort.requestInternalOrderNumber(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            )
            .thenReturn(InternalOrderNumberOutPort.InternalOrderNumber(createRandomString(Random.nextInt(4, 10))))

        internalOrderNumberUpdateService.updateAllEmptyInternalOrderNumbersAndCreateSyncIndicators()

        val findOpenDamageFilesWithoutInternalOrderNumber =
            findDamageFileWithoutInternalOrderUseCase.findOpenDamageFilesWithoutInternalOrderNumber()
        val forSynchronisation = internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()
        val damageFileKeys = damageFiles.map { it.key }
        val forSyncKeys = forSynchronisation.map { it.damageFileKey }

        assertThat(findOpenDamageFilesWithoutInternalOrderNumber.size).isEqualTo(0)
        assertThat(forSyncKeys).containsExactlyInAnyOrderElementsOf(damageFileKeys)
    }

    @Test
    fun `should find all internalOrderNumbers that need to be synced and update them if sync is successful`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        damageFiles.takeLast(9).forEach {
            val internalOrderNumber = InternalOrderNumber(createRandomString(Random.nextInt(4, 10)))
            createOrUpdateDamageFileUseCase.updateInternalOrderNumber(it.key, internalOrderNumber)
            internalOrderNumberSyncIndicatorService.createOrUpdate(it.key, internalOrderNumber)
        }
        val internalOrderNumberForFail = InternalOrderNumber(createRandomString(Random.nextInt(4, 10)))
        val damageFileForFail = damageFiles.first()
        createOrUpdateDamageFileUseCase.updateInternalOrderNumber(damageFileForFail.key, internalOrderNumberForFail)
        internalOrderNumberSyncIndicatorService.createOrUpdate(damageFileForFail.key, internalOrderNumberForFail)

        whenever(internalOrderNumberMigrationOutPort.updateInternalOrderNumber(any(), any())).thenAnswer { invocation ->
            val internalOrderNumberValue = invocation.arguments[1] as String

            if (internalOrderNumberValue == internalOrderNumberForFail.value) {
                DamageReportUpdate(createRandomString(Random.nextInt(4, 10)), false)
            } else {
                DamageReportUpdate(createRandomString(Random.nextInt(4, 10)), true)
            }
        }

        internalOrderNumberUpdateService.syncInternalOrderNumberToExternalSystem()

        val leftForSync = internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()

        assertThat(leftForSync.size).isEqualTo(1)
        assertThat(leftForSync.first().damageFileKey).isEqualTo(damageFileForFail.key)
    }

    @Test
    fun `should skip and continue internal order number update when encountering IllegalArgumentException for a single damage file`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        val failingDamageFile = damageFiles.random()

        whenever(
                internalOrderNumberOutPort.requestInternalOrderNumber(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            )
            .thenAnswer(
                InlineClassesAnswer {
                    val vin = it.arguments[5]
                    if (failingDamageFile.vehicle.vin == vin)
                        throw InternalOrderNumberOutPortException("Sry :(", RuntimeException())
                    else InternalOrderNumberOutPort.InternalOrderNumber(createRandomString(Random.nextInt(4, 10)))
                }
            )

        internalOrderNumberUpdateService.updateAllEmptyInternalOrderNumbersAndCreateSyncIndicators()

        val findOpenDamageFilesWithoutInternalOrderNumber =
            findDamageFileWithoutInternalOrderUseCase.findOpenDamageFilesWithoutInternalOrderNumber()
        val forSynchronisation = internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()
        val damageFileKeys = damageFiles.filterNot { failingDamageFile.key == it.key }.map { it.key }
        val forSyncKeys = forSynchronisation.map { it.damageFileKey }

        assertThat(findOpenDamageFilesWithoutInternalOrderNumber.size).isEqualTo(1)
        assertThat(forSyncKeys).containsExactlyInAnyOrderElementsOf(damageFileKeys)
    }

    @Test
    fun `should skip and continue internal order number update when encountering InternalOrderNumberUpdateException for a single damage file`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        val failingDamageFile = damageFiles.random()

        whenever(
                internalOrderNumberOutPort.requestInternalOrderNumber(
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                    any(),
                )
            )
            .thenAnswer(
                InlineClassesAnswer {
                    val vin = it.arguments[5]
                    if (failingDamageFile.vehicle.vin == vin)
                    /**
                     * strictly speaking the exception should not be thrown from this mock, but otherwise we would have
                     * to mock the useCase (instead of using the spy) which is a lot of effort for just checking
                     * exception handling one level above ;)
                     */
                    throw InternalOrderNumberUpdateException(
                            "DamageFile already closed.",
                            DamageFileAlreadyClosedException(DamageFileKey("anyKey")),
                        )
                    else InternalOrderNumberOutPort.InternalOrderNumber(createRandomString(Random.nextInt(4, 10)))
                }
            )

        internalOrderNumberUpdateService.updateAllEmptyInternalOrderNumbersAndCreateSyncIndicators()

        val findOpenDamageFilesWithoutInternalOrderNumber =
            findDamageFileWithoutInternalOrderUseCase.findOpenDamageFilesWithoutInternalOrderNumber()
        val forSynchronisation = internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()
        val damageFileKeys = damageFiles.filterNot { failingDamageFile.key == it.key }.map { it.key }
        val forSyncKeys = forSynchronisation.map { it.damageFileKey }

        assertThat(findOpenDamageFilesWithoutInternalOrderNumber.size).isEqualTo(1)
        assertThat(forSyncKeys).containsExactlyInAnyOrderElementsOf(damageFileKeys)
    }

    @Test
    fun `should skip and continue internal order number sync when encountering InternalOrderNumberUpdateException for a single sync indicator`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        damageFiles.forEach {
            val internalOrderNumber = InternalOrderNumber(createRandomString(Random.nextInt(4, 10)))
            createOrUpdateDamageFileUseCase.updateInternalOrderNumber(it.key, internalOrderNumber)
            internalOrderNumberSyncIndicatorService.createOrUpdate(it.key, internalOrderNumber)
        }
        val failingDamageFile = damageFiles.random()

        whenever(internalOrderNumberMigrationOutPort.updateInternalOrderNumber(any(), any())).thenAnswer {
            val externalId = it.arguments[0] as String
            if (failingDamageFile.externalReference == externalId)
                throw InternalOrderNumberMigrationUpdateException("update failed miserably.", RuntimeException())
            else DamageReportUpdate(externalId = externalId, isSuccess = true)
        }

        internalOrderNumberUpdateService.syncInternalOrderNumberToExternalSystem()

        val leftForSync = internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()

        assertThat(leftForSync.size).isEqualTo(1)
        assertThat(leftForSync.first().damageFileKey).isEqualTo(failingDamageFile.key)
    }

    @Test
    fun `should skip and continue internal order number sync when encountering IllegalArgumentException for a single sync indicator`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(9).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        val damageFileWithoutExternalReference =
            DamageFileNewOrUpdateBuilder().externalReference(null).build().let {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }

        damageFiles +
            setOf(damageFileWithoutExternalReference).forEach {
                val internalOrderNumber = InternalOrderNumber(createRandomString(Random.nextInt(4, 10)))
                createOrUpdateDamageFileUseCase.updateInternalOrderNumber(it.key, internalOrderNumber)
                internalOrderNumberSyncIndicatorService.createOrUpdate(it.key, internalOrderNumber)
            }

        whenever(internalOrderNumberMigrationOutPort.updateInternalOrderNumber(any(), any())).thenAnswer {
            val externalId = it.arguments[0] as String
            DamageReportUpdate(externalId = externalId, isSuccess = true)
        }

        internalOrderNumberUpdateService.syncInternalOrderNumberToExternalSystem()

        val leftForSync = internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()

        assertThat(leftForSync.size).isEqualTo(1)
        assertThat(leftForSync.first().damageFileKey).isEqualTo(damageFileWithoutExternalReference.key)
    }
}
