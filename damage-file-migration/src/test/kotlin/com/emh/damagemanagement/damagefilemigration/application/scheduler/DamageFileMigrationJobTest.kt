/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefilemigration.application.scheduler

import com.emh.damagemanagement.damagefilemigration.application.DamageFileMigrationService
import com.emh.damagemanagement.damagefilemigration.application.job.DamageFileMigrationJob
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

class DamageFileMigrationJobTest {

    private val damageFileMigrationService: DamageFileMigrationService = mock()

    private val damageFileMigrationJob: DamageFileMigrationJob =
        DamageFileMigrationJob(damageFileMigrationService = damageFileMigrationService)

    @Test
    fun `should migrate damage files when scheduler is called`() {
        damageFileMigrationJob.execute(null)

        verify(damageFileMigrationService, times(1)).migrateAllDamageReports()
    }
}
