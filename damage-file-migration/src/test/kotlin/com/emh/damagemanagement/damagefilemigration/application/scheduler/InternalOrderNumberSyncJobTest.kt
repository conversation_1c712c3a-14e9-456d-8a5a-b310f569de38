package com.emh.damagemanagement.damagefilemigration.application.scheduler

import com.emh.damagemanagement.damagefilemigration.application.InternalOrderNumberUpdateService
import com.emh.damagemanagement.damagefilemigration.application.job.InternalOrderNumberSyncJob
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.kotlin.verify

class InternalOrderNumberSyncJobTest {

    private val internalOrderNumberUpdateService: InternalOrderNumberUpdateService = mock()

    private val internalOrderNumberSyncJob: InternalOrderNumberSyncJob =
        InternalOrderNumberSyncJob(internalOrderNumberUpdateService)

    @Test
    fun `should run internal order number update job`() {
        internalOrderNumberSyncJob.execute(null)

        verify(internalOrderNumberUpdateService, times(1)).syncInternalOrderNumberToExternalSystem()
    }
}
