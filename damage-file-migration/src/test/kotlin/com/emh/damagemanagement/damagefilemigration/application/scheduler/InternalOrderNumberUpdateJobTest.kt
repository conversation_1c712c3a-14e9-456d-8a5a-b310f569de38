package com.emh.damagemanagement.damagefilemigration.application.scheduler

import com.emh.damagemanagement.damagefilemigration.application.InternalOrderNumberUpdateService
import com.emh.damagemanagement.damagefilemigration.application.job.InternalOrderNumberUpdateJob
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.times
import org.mockito.kotlin.verify

class InternalOrderNumberUpdateJobTest {

    private val internalOrderNumberUpdateService: InternalOrderNumberUpdateService = mock()

    private val internalOrderNumberUpdateJob: InternalOrderNumberUpdateJob =
        InternalOrderNumberUpdateJob(internalOrderNumberUpdateService)

    @Test
    fun `should run internal order number update job`() {
        internalOrderNumberUpdateJob.execute(null)

        verify(internalOrderNumberUpdateService, times(1)).updateAllEmptyInternalOrderNumbersAndCreateSyncIndicators()
    }
}
