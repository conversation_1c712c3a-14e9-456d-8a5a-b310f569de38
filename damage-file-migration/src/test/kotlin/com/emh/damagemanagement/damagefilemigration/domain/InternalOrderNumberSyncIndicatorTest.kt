package com.emh.damagemanagement.damagefilemigration.domain

import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.shared.createRandomString
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class InternalOrderNumberSyncIndicatorTest {

    @Test
    fun `newly created internalOrderNumberSyncIndicator should have repairfixSyncIsDone false`() {
        val syncIndicator =
            InternalOrderNumberSyncIndicator(
                DamageFileKey(createRandomString(5)),
                InternalOrderNumber(createRandomString(5)),
            )

        assertThat(syncIndicator.externalSystemSyncIsDone).isFalse()
    }

    @Test
    fun `resetting InternalOrderNumberSyncIndicator should update internalOrderNumber and set synced to false`() {
        val syncIndicator =
            InternalOrderNumberSyncIndicator(
                DamageFileKey(createRandomString(5)),
                InternalOrderNumber(createRandomString(5)),
            )

        val internalOrderNumberUpdate = InternalOrderNumber("updated")
        syncIndicator.updateAndReset(internalOrderNumber = internalOrderNumberUpdate)

        assertThat(syncIndicator.externalSystemSyncIsDone).isFalse()
        assertThat(syncIndicator.internalOrderNumber).isEqualTo(internalOrderNumberUpdate)
    }
}
