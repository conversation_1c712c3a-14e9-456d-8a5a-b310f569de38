package com.emh.damagemanagement.damagefilemigration.domain.service

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefilemigration.IntegrationTest
import com.emh.damagemanagement.damagefilemigration.domain.InternalOrderNumberSyncIndicator
import com.emh.damagemanagement.damagefilemigration.domain.InternalOrderNumberSyncIndicatorRepository
import com.emh.damagemanagement.shared.createRandomString
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@IntegrationTest
class InternalOrderNumberSyncIndicatorServiceTest {

    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    @Qualifier("damage-file-cleanup")
    private lateinit var cleanupDamageFiles: () -> Unit

    @Autowired private lateinit var internalOrderNumberSyncIndicatorService: InternalOrderNumberSyncIndicatorService
    @Autowired private lateinit var repository: InternalOrderNumberSyncIndicatorRepository
    @Autowired private lateinit var entityManager: EntityManager
    @Autowired private lateinit var damageFileCreateOrUpdateUseCase: CreateOrUpdateDamageFileUseCase
    @MockitoSpyBean private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase

    @AfterEach
    fun cleanup() {
        cleanupDamageFiles()
    }

    @Test
    fun `should create internalOrderNumberSyncIndicator for damageFileKey`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()
        val internalOrderNumber = InternalOrderNumber(createRandomString(5))

        internalOrderNumberSyncIndicatorService.createOrUpdate(damageFile.key, internalOrderNumber)

        val createdSyncIndicator = repository.findByDamageFileKey(damageFile.key)
        assertThat(createdSyncIndicator?.damageFileKey).isEqualTo(damageFile.key)
        assertThat(createdSyncIndicator?.internalOrderNumber).isEqualTo(internalOrderNumber)
        assertThat(createdSyncIndicator?.externalSystemSyncIsDone).isFalse()
    }

    @Test
    fun `should update internalOrderNumberSyncIndicator`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()
        val internalOrderNumber = InternalOrderNumber(createRandomString(5))
        internalOrderNumberSyncIndicatorService.createOrUpdate(damageFile.key, internalOrderNumber)

        internalOrderNumberSyncIndicatorService.setSynchronizeIndicatorToDone(damageFile.key)

        val updatedSyncIndicator = repository.findByDamageFileKey(damageFile.key)

        assertThat(updatedSyncIndicator?.damageFileKey).isEqualTo(damageFile.key)
        assertThat(updatedSyncIndicator?.internalOrderNumber).isEqualTo(internalOrderNumber)
        assertThat(updatedSyncIndicator?.externalSystemSyncIsDone).isTrue()
    }

    @Test
    fun `should reset sync flag if internalOrderNumberSyncIndicator already exists for given damage file key`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateOrUpdateUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()
        val syncIndicator =
            InternalOrderNumberSyncIndicator(
                damageFileKey = damageFile.key,
                internalOrderNumber = InternalOrderNumber("some old number"),
            )
        repository.save(syncIndicator)
        entityManager.flush()

        val internalOrderNumber = InternalOrderNumber("some new number")
        internalOrderNumberSyncIndicatorService.createOrUpdate(damageFile.key, internalOrderNumber)
        entityManager.flush()

        val updatedSyncIndicator = repository.findByDamageFileKey(damageFile.key)

        assertThat(updatedSyncIndicator?.damageFileKey).isEqualTo(damageFile.key)
        assertThat(updatedSyncIndicator?.internalOrderNumber).isEqualTo(internalOrderNumber)
        assertThat(updatedSyncIndicator?.externalSystemSyncIsDone).isFalse()
    }

    @Test
    fun `should find internalOrderNumberSyncIndicators for synchronization`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        damageFiles.forEach {
            internalOrderNumberSyncIndicatorService.createOrUpdate(it.key, InternalOrderNumber(createRandomString(5)))
        }
        damageFiles.takeLast(5).forEach {
            internalOrderNumberSyncIndicatorService.setSynchronizeIndicatorToDone(it.key)
        }

        val forSynchronization = internalOrderNumberSyncIndicatorService.findInternalOrderNumbersForSynchronization()

        val forSyncKeys = forSynchronization.map { it.damageFileKey }
        val expectedKeys = damageFiles.take(5).map { it.key }
        assertThat(forSyncKeys).containsExactlyInAnyOrderElementsOf(expectedKeys)
    }
}
