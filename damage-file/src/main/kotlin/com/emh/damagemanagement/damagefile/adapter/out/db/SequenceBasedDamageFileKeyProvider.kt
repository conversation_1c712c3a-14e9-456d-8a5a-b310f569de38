/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.adapter.out.db

import com.emh.damagemanagement.damagefile.application.port.DamageFileKeyProvider
import com.emh.damagemanagement.damagefile.application.port.DamageFileKeyProvisioningException
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceException
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/** A database sequence based provider of DamageFileKeys. */
@Component
@Transactional
class SequenceBasedDamageFileKeyProvider(private val entityManager: EntityManager) : DamageFileKeyProvider {
    override fun provideNewDamageFileKey(): DamageFileKey {
        val nextValueFromSequence: Long =
            try {
                // NOSONAR
                entityManager.createNativeQuery(NEXT_SEQ_DAMAGE_FILE_KEY_VALUE, Long::class.java).singleResult as Long
            } catch (exception: PersistenceException) {
                log.error("Error while reading damage file key", exception)
                throw DamageFileKeyProvisioningException(exception)
            }
        return DamageFileKey(nextValueFromSequence.toString())
    }

    companion object {
        private val log = LoggerFactory.getLogger(SequenceBasedDamageFileKeyProvider::class.java)
        private const val SEQ_DAMAGE_FILE_KEY = "emh_damage_management.seq_damage_file_key"
        private const val NEXT_SEQ_DAMAGE_FILE_KEY_VALUE = "SELECT nextval('${SEQ_DAMAGE_FILE_KEY}')"
    }
}
