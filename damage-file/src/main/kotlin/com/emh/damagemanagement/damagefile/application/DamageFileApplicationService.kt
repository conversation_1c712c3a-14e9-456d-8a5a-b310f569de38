/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.application.port.AnonymizeDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.DamageFileKeyProvider
import com.emh.damagemanagement.damagefile.application.port.DeleteDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageFileNotClosedException
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefile.domain.service.DamageFileAnonymizeService
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCloseService
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateService
import com.emh.damagemanagement.damagefile.domain.service.DamageFileDeleteService
import com.emh.damagemanagement.damagefile.domain.service.DamageFileNewOrUpdate
import com.emh.damagemanagement.damagefile.domain.service.DamageFileReopenService
import com.emh.damagemanagement.damagefile.domain.service.DamageFileUpdate
import com.emh.damagemanagement.damagefile.domain.service.DamageFileUpdateService
import com.emh.damagemanagement.legal.hold.domain.LegalHoldInPlaceException
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional
class DamageFileApplicationService(
    private val damageFileCreateService: DamageFileCreateService,
    private val damageFileUpdateService: DamageFileUpdateService,
    private val damageFileCloseService: DamageFileCloseService,
    private val damageFileDeleteService: DamageFileDeleteService,
    private val damageFileReopenService: DamageFileReopenService,
    private val damageFileFinder: DamageFileFinder,
    private val damageFileAnonymizeService: DamageFileAnonymizeService,
    private val damageFileKeyProvider: DamageFileKeyProvider,
) : CreateOrUpdateDamageFileUseCase, DeleteDamageFileUseCase, AnonymizeDamageFileUseCase {

    override fun createOrUpdateDamageFile(damageFileNewOrUpdate: DamageFileNewOrUpdate): DamageFile {
        // we are using external reference as exists-check (if set)
        val existingDamageFile =
            damageFileNewOrUpdate.externalReference?.let { damageFileFinder.findByExternalReference(it) }

        val createdOrUpdatedDamageFile =
            if (null == existingDamageFile) {
                createDamageFile(damageFileNewOrUpdate)
            } else {
                // reopen damage file if necessary
                reopenDamageFileIfNecessary(
                    existingDamageFile = existingDamageFile,
                    damageFileNewOrUpdate = damageFileNewOrUpdate,
                )

                // perform update
                updateDamageFile(damageFile = existingDamageFile, damageFileNewOrUpdate = damageFileNewOrUpdate)
                existingDamageFile
            }

        // check if damage file needs to be closed
        if (damageFileNewOrUpdate.isClosed) {
            damageFileCloseService.closeDamageFile(createdOrUpdatedDamageFile)
        }
        return createdOrUpdatedDamageFile
    }

    private fun reopenDamageFileIfNecessary(
        existingDamageFile: DamageFile,
        damageFileNewOrUpdate: DamageFileNewOrUpdate,
    ) {
        // check if damage file needs to be reopened
        if (damageFileReopenService.needsToBeReopened(existingDamageFile, damageFileNewOrUpdate)) {
            // reopen damage file
            damageFileReopenService.reopenDamageFile(existingDamageFile)
        }
    }

    /**
     * Will create or update all [DamageFile]s provided to this method. This is done asynchronously using a NEW
     * transaction!
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun createOrUpdateDamageFilesAsync(damageFileNewOrUpdates: Collection<DamageFileNewOrUpdate>) {
        damageFileNewOrUpdates.forEach {
            try {
                createOrUpdateDamageFile(it)
            } catch (exception: ConcurrentModificationException) {
                log.warn(
                    "Skipping update of damage file with with external reference: ${it.externalReference} due to concurrent modification error."
                )
            }
        }
    }

    override fun updateInternalOrderNumber(damageFileKey: DamageFileKey, internalOrderNumber: InternalOrderNumber) {
        damageFileFinder.getDamageFile(damageFileKey).let { damageFile ->
            damageFileUpdateService.updateInternalOrderNumber(damageFile, internalOrderNumber)
        }
    }

    private fun createDamageFile(damageFileNewOrUpdate: DamageFileNewOrUpdate): DamageFile {
        return damageFileCreateService.createDamageFile(
            damageFileKey = damageFileKeyProvider.provideNewDamageFileKey(),
            damageFileNewOrUpdate = damageFileNewOrUpdate,
        )
    }

    private fun updateDamageFile(damageFile: DamageFile, damageFileNewOrUpdate: DamageFileNewOrUpdate) {
        /** 2025-04-10 added damageDate and employeeToBeCharged to update */
        damageFileUpdateService.updateDamageFile(
            damageFile,
            DamageFileUpdate(
                deductible = damageFileNewOrUpdate.deductible,
                claimTowardsEmployee = damageFileNewOrUpdate.claimTowardsEmployee,
                insuranceCoverage = damageFileNewOrUpdate.insuranceCoverage,
                damageResponsibility = damageFileNewOrUpdate.damageResponsibility,
                damageDate = damageFileNewOrUpdate.damageDate,
                employeeToBeCharged = damageFileNewOrUpdate.employeeToBeCharged,
                isExternal = damageFileNewOrUpdate.isExternal,
            ),
        )
    }

    override fun deleteDamageFiles(damageFileKeys: List<DamageFileKey>) {
        val existingDamageFiles = findExistingDamageFiles(damageFileKeys)
        existingDamageFiles.forEach {
            try {
                damageFileDeleteService.deleteDamageFile(it)
            } catch (exception: DamageFileNotClosedException) {
                log.error("Damage file deletion failed: ${exception.message}")
            } catch (exception: LegalHoldInPlaceException) {
                log.warn("Damage file deletion failed: ${exception.message}")
            }
        }
    }

    override fun anonymizeDamageFiles(damageFileKeys: List<DamageFileKey>) {
        val existingDamageFiles = findExistingDamageFiles(damageFileKeys)
        existingDamageFiles.forEach {
            try {
                damageFileAnonymizeService.anonymizeDamageFile(it)
            } catch (exception: DamageFileNotClosedException) {
                log.error("Damage file anonymization failed: ${exception.message}")
            } catch (exception: LegalHoldInPlaceException) {
                log.warn("Damage file anonymization failed: ${exception.message}")
            }
        }
    }

    private fun findExistingDamageFiles(damageFileKeys: List<DamageFileKey>): List<DamageFile> {
        val existingDamageFiles = damageFileFinder.findAllByDamageKeys(damageFileKeys)
        val missingDamageFileKeys = damageFileKeys - existingDamageFiles.map { it.key }.toSet()
        if (missingDamageFileKeys.isNotEmpty()) {
            log.error("Damage File not present in repository, ${missingDamageFileKeys.map { it }}")
        }
        return existingDamageFiles
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileApplicationService::class.java)
    }
}
