/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.application.port.FindDamageFileWithClosedUseCase
import com.emh.damagemanagement.damagefile.application.port.FindDamageFilesWithDeductibleUpdateUseCase
import com.emh.damagemanagement.damagefile.application.port.FindDamageFilesWithoutInternalOrderUseCase
import com.emh.damagemanagement.damagefile.application.port.NumberOfDamagesUseCase
import com.emh.damagemanagement.damagefile.application.port.ReadDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageFileNotFoundException
import com.emh.damagemanagement.damagefile.domain.DamageFileRepository
import com.emh.damagemanagement.damagefile.domain.DamageFileState
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import java.time.OffsetDateTime
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional(readOnly = true)
class DamageFileFinder(private val damageFileRepository: DamageFileRepository) :
    FindDamageFilesWithDeductibleUpdateUseCase,
    FindDamageFileWithClosedUseCase,
    FindDamageFilesWithoutInternalOrderUseCase,
    NumberOfDamagesUseCase,
    ReadDamageFileUseCase {

    fun exists(damageFileKey: DamageFileKey): Boolean = damageFileRepository.existsByKey(key = damageFileKey)

    override fun getDamageFile(damageFileKey: DamageFileKey): DamageFile =
        damageFileRepository.findByKey(damageFileKey)
            ?: throw DamageFileNotFoundException(
                damageFileKey = damageFileKey,
                message = "Damage File with key: ${damageFileKey.value} could not be found.",
            )

    fun findByExternalReference(externalReference: String): DamageFile? =
        damageFileRepository.findByExternalReference(externalReference)

    fun findByVehicleVin(vin: String): DamageFile? = damageFileRepository.findByVehicle_vin(vin)

    fun findByVehicleVGuid(vGuid: String): DamageFile? = damageFileRepository.findByVehicle_vGuid(vGuid)

    fun findAllByDamageKeys(keys: Collection<DamageFileKey>) = damageFileRepository.findAllByKeyIn(keys)

    override fun countByVehicleVin(vin: String): Int = damageFileRepository.countByVehicle_vin(vin)

    override fun countByVehicleVinAndState(vin: String, state: DamageFileState): Int =
        damageFileRepository.countByVehicle_vinAndState(vin, state)

    override fun findAllNonExternalDamageFilesWithDeductibleUpdateBetween(
        from: OffsetDateTime,
        to: OffsetDateTime,
    ): List<DamageFile> =
        damageFileRepository.findAll(specificationNonExternalDeductibleUpdatedDateBetween(from = from, to = to))

    override fun findClosedDamageFilesWithClosedDateBefore(closedDate: OffsetDateTime): List<DamageFile> =
        damageFileRepository.findAll(specificationClosedDamageFilesBefore(closedDate))

    override fun findOpenDamageFilesWithoutInternalOrderNumber(): List<DamageFile> =
        damageFileRepository.findAll(specificationOpenDamageFileAndInternalOrderNumberIsNull())

    companion object {
        private const val PROPERTY_DAMAGE_FILE_DEDUCTIBLE_UPDATE = "deductibleUpdated"
        private const val PROPERTY_DAMAGE_FILE_IS_EXTERNAL = "isExternal"
        private const val CLOSED_DATE = "closedDate"
        private const val STATE = "state"
        private const val INTERNAL_ORDER_NUMBER = "internalOrderNumber"

        private fun specificationClosedDamageFilesBefore(date: OffsetDateTime) =
            Specification.where(specificationClosedDamageFiles()).and(specificationClosedDateBefore(date))

        private fun specificationClosedDamageFiles() =
            Specification<DamageFile> { root, _, criteriaBuilder ->
                criteriaBuilder.equal(root.get<DamageFileState>(STATE), DamageFileState.CLOSED)
            }

        private fun specificationClosedDateBefore(date: OffsetDateTime) =
            Specification<DamageFile> { root, _, criteriaBuilder ->
                criteriaBuilder.lessThan(root.get(CLOSED_DATE), date)
            }

        private fun specificationNonExternalDeductibleUpdatedDateBetween(from: OffsetDateTime, to: OffsetDateTime) =
            Specification<DamageFile> { root, criteriaQuery, criteriaBuilder ->
                criteriaQuery?.orderBy(
                    criteriaBuilder.asc(root.get<OffsetDateTime>(PROPERTY_DAMAGE_FILE_DEDUCTIBLE_UPDATE))
                )
                criteriaBuilder.and(
                    criteriaBuilder.between(root.get(PROPERTY_DAMAGE_FILE_DEDUCTIBLE_UPDATE), from, to),
                    criteriaBuilder.equal(root.get<OffsetDateTime>(PROPERTY_DAMAGE_FILE_IS_EXTERNAL), false),
                )
            }

        private fun specificationOpenDamageFileAndInternalOrderNumberIsNull() =
            Specification.where(specificationOpenDamageFiles()).and(specificationInternalOrderNumberIsNull())

        private fun specificationOpenDamageFiles() =
            Specification<DamageFile> { root, _, criteriaBuilder ->
                criteriaBuilder.equal(root.get<DamageFileState>(STATE), DamageFileState.OPEN)
            }

        private fun specificationInternalOrderNumberIsNull() =
            Specification<DamageFile> { root, _, criteriaBuilder ->
                criteriaBuilder.isNull(root.get<InternalOrderNumber>(INTERNAL_ORDER_NUMBER))
            }
    }
}
