/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.legal.hold.application.LegalHoldEventListener
import com.emh.damagemanagement.legal.hold.application.LegallyHoldableDomainEntityFinder
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldDomainEntityService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

/** DamageFile specific implementation for the [LegalHoldEventListener] */
@Component
@Transactional
class DamageFileLegalHoldEventListener(
    override val legalHoldDomainEntityService: LegalHoldDomainEntityService<DamageFile>,
    override val legallyHoldableDomainEntityFinder: LegallyHoldableDomainEntityFinder<DamageFile>,
) : LegalHoldEventListener<DamageFile>
