/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageFileRepository
import com.emh.damagemanagement.legal.hold.application.LegallyHoldableDomainEntityFinder
import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional(readOnly = true)
class DamageFileLegalHoldFinder(private val damageFileRepository: DamageFileRepository) :
    LegallyHoldableDomainEntityFinder<DamageFile> {

    override fun findDomainEntitiesFor(legalHoldKey: LegalHoldKey): Set<DamageFile> {
        return damageFileRepository.findAll(specificationLegalHoldKey(legalHoldKey)).toSet()
    }

    /** Will find [DamageFile]s by [DamageFileKey] as we are using these as [DomainEntityReference]s. */
    override fun findDomainEntitiesFor(domainEntityReferences: Collection<DomainEntityReference>): Set<DamageFile> {
        return damageFileRepository
            .findAllByKeyIn(domainEntityReferences.map { DamageFileKey(it.entityIdentifier) })
            .toSet()
    }

    companion object {
        private const val PROPERTY_DAMAGE_FILE_LEGAL_HOLD_KEYS = "_legalHolds"

        private fun specificationLegalHoldKey(legalHoldKey: LegalHoldKey) =
            Specification<DamageFile> { root, _, criteriaBuilder ->
                criteriaBuilder.isMember(legalHoldKey, root.get(PROPERTY_DAMAGE_FILE_LEGAL_HOLD_KEYS))
            }
    }
}
