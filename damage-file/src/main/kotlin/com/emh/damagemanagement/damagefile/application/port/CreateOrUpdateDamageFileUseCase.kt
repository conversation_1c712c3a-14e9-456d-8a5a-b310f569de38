/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application.port

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefile.domain.service.DamageFileNewOrUpdate

interface CreateOrUpdateDamageFileUseCase {
    fun createOrUpdateDamageFile(damageFileNewOrUpdate: DamageFileNewOrUpdate): DamageFile

    fun createOrUpdateDamageFilesAsync(damageFileNewOrUpdates: Collection<DamageFileNewOrUpdate>)

    fun updateInternalOrderNumber(damageFileKey: DamageFileKey, internalOrderNumber: InternalOrderNumber)
}
