/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application.port

import com.emh.damagemanagement.damagefile.domain.DamageFileKey

fun interface DamageFileKeyProvider {
    /**
     * Will provide a new damage file key.
     *
     * @throws DamageFileKeyProvisioningException in case of an error.
     */
    fun provideNewDamageFileKey(): DamageFileKey
}

class DamageFileKeyProvisioningException(override val cause: Throwable?) :
    RuntimeException("Error while trying to provision new damage file key.")
