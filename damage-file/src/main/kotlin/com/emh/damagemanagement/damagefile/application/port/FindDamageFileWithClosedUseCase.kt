/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application.port

import com.emh.damagemanagement.damagefile.domain.DamageFile
import java.time.OffsetDateTime

fun interface FindDamageFileWithClosedUseCase {
    fun findClosedDamageFilesWithClosedDateBefore(closedDate: OffsetDateTime): List<DamageFile>
}
