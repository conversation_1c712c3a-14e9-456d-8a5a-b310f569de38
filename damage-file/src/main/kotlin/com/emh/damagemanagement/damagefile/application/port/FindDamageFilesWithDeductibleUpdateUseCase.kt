/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application.port

import com.emh.damagemanagement.damagefile.domain.DamageFile
import java.time.OffsetDateTime

fun interface FindDamageFilesWithDeductibleUpdateUseCase {
    fun findAllNonExternalDamageFilesWithDeductibleUpdateBetween(
        from: OffsetDateTime,
        to: OffsetDateTime,
    ): List<DamageFile>
}
