/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.LegallyHoldable
import com.emh.damagemanagement.shared.domain.Auditable
import com.emh.damagemanagement.shared.domain.DomainEvent
import com.emh.damagemanagement.shared.domain.Key
import com.emh.damagemanagement.shared.domain.WithVersion
import com.emh.damagemanagement.shared.domain.createCRC32CForEntity
import com.fasterxml.jackson.annotation.JsonIgnore
import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Basic
import jakarta.persistence.CascadeType
import jakarta.persistence.CollectionTable
import jakarta.persistence.Column
import jakarta.persistence.ElementCollection
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.JoinColumn
import jakarta.persistence.OneToOne
import jakarta.persistence.Table
import jakarta.persistence.Version
import java.io.Serializable
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit
import java.util.*

/**
 * AGGREGATE ROOT A DamageFile (also known as 'Schadensakte'). This class wraps all information related to a single
 * damage, like damage report and vehicle information.
 */
@Suppress("CanBePrimaryConstructorProperty")
@Entity
@Table(name = "damage_file")
class DamageFile(
    damageReport: DamageReport,
    vehicle: Vehicle,
    employeeToBeCharged: EmployeeNumber?,
    deductible: MonetaryAmount? = null,
    claimTowardsEmployee: MonetaryAmount? = null,
    internalOrderNumber: InternalOrderNumber? = null,
    externalReference: String? = null,
    isExternal: Boolean,
    @Embedded @AttributeOverride(name = "value", column = Column(name = "damage_file_key")) val key: DamageFileKey,

    /**
     * The date this damage file was created. May be set externally if damage file is received from an external source.
     */
    @Column(name = "creation_date") val creationDate: OffsetDateTime = OffsetDateTime.now(),
    insuranceCoverage: InsuranceCoverage = InsuranceCoverage.NOT_SPECIFIED,
    damageResponsibility: DamageResponsibility = DamageResponsibility.UNKNOWN,
    damageDate: OffsetDateTime,
) : Auditable(), WithVersion, LegallyHoldable {

    /** Internal PK. Use 'key' when accessing / finding a damage file. */
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: DamageFileId = DamageFileId()

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "employee_to_be_charged"))
    lateinit var employeeToBeCharged: EmployeeNumber
        private set

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "vin", column = Column(name = "vehicle_vin")),
        AttributeOverride(name = "vGuid", column = Column(name = "vehicle_vguid")),
        AttributeOverride(name = "licensePlate", column = Column(name = "license_plate")),
        AttributeOverride(name = "leasingArt", column = Column(name = "leasing_art")),
        AttributeOverride(name = "costCenter", column = Column(name = "cost_center")),
        AttributeOverride(name = "usingCostCenter", column = Column(name = "using_cost_center")),
        AttributeOverride(name = "fleetId", column = Column(name = "fleet_id")),
    )
    val vehicle: Vehicle = vehicle

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "value", column = Column(name = "deductible_amount")),
        AttributeOverride(name = "currencyUnit", column = Column(name = "deductible_currency_unit")),
    )
    var deductible: MonetaryAmount? = null
        private set

    /**
     * 'Anspruch gegenüber Mitarbeiter'
     *
     * A monetary amount calculated using the damage fees minus deductible (simplified)
     */
    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "value", column = Column(name = "claim_towards_employee_amount")),
        AttributeOverride(name = "currencyUnit", column = Column(name = "claim_towards_employee_currency_unit")),
    )
    var claimTowardsEmployee: MonetaryAmount? = null
        private set

    @Enumerated(EnumType.STRING)
    @Column(name = "state")
    var state: DamageFileState = DamageFileState.OPEN
        private set

    /** The unique internal order number provided by PACE system for each damage (aka 'Innenauftragsnummer'). */
    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "internal_order_number"))
    var internalOrderNumber: InternalOrderNumber? = internalOrderNumber
        private set

    @Enumerated(EnumType.STRING)
    @Column(name = "insurance_coverage")
    var insuranceCoverage: InsuranceCoverage = insuranceCoverage
        private set

    @Enumerated(EnumType.STRING)
    @Column(name = "damage_responsibility")
    var damageResponsibility: DamageResponsibility = damageResponsibility
        private set

    /** The damage report associated with this damage file */
    @OneToOne(cascade = [CascadeType.ALL], orphanRemoval = true)
    @JoinColumn(name = "damage_report_id")
    val damageReport: DamageReport = damageReport

    /**
     * A reference to an external system. Used when damage file was originally provided by another system and not
     * created by us.
     */
    @Column(name = "external_reference") val externalReference: String? = externalReference

    /**
     * A timestamp dedicated to indicate that deductible and/or claimTowardsEmployee got set or changed. This is used
     * for hr-p08 to indicate if a damage file (update) has to be provided
     */
    @Column(name = "deductible_update")
    var deductibleUpdated: OffsetDateTime? = null
        private set

    /** The date this damage file was closed. Will be set during closeDamageFile(). */
    @Column(name = "closed_date")
    var closedDate: OffsetDateTime? = null
        private set

    @Column(name = "damage_date")
    var damageDate: OffsetDateTime = damageDate
        private set

    /**
     * A flag indicating, if this damage was caused by a driver considered 'external'. External damage will not be sent
     * to PVCC nor is it of interest form HCM-P08 (will be filtered out in both cases)
     */
    @Column(name = "external")
    var isExternal: Boolean = isExternal
        private set

    // legal hold
    @ElementCollection
    @CollectionTable(name = "damage_file_legal_hold", joinColumns = [JoinColumn(name = "damage_file_id")])
    @AttributeOverride(name = "value", column = Column(name = "legal_hold_key"))
    private val _legalHolds: MutableSet<LegalHoldKey> = mutableSetOf()

    @Version override val version: Int = 0
    override val etag: String
        @JsonIgnore get() = createCRC32CForEntity(id.value, version)

    init {
        updateEmployeeToBeCharged(employeeToBeCharged)
        // use update domain logic here to ensure proper timestamp handling
        update(
            deductible = deductible,
            claimTowardsEmployee = claimTowardsEmployee,
            insuranceCoverage = insuranceCoverage,
            damageResponsibility = damageResponsibility,
            damageDate = damageDate,
            employeeToBeCharged = employeeToBeCharged,
            isExternal = isExternal,
        )
    }

    val damageFileCreatedEvent: DamageFileCreatedEvent
        @JsonIgnore
        get() =
            DamageFileCreatedEvent(
                damageFileKey = this.key,
                occurredOn = OffsetDateTime.now(),
                externalReference = this.externalReference,
            )

    internal fun anonymize(): DamageFileAnonymizedEvent {
        requireDamageFileClosedWithNoLegalHold()
        this.employeeToBeCharged = EmployeeNumber(ANONYMIZE)
        return DamageFileAnonymizedEvent(
            damageFileKey = this.key,
            occurredOn = OffsetDateTime.now(),
            externalReference = this.externalReference,
        )
    }

    fun updateInternalOrderNumber(
        newInternalOrderNumber: InternalOrderNumber
    ): DamageFileInternalOrderNumberUpdatedEvent {
        requireDamageFileNotClosed()
        requireNoLegalHolds()

        this.internalOrderNumber = newInternalOrderNumber
        return DamageFileInternalOrderNumberUpdatedEvent(
            damageFileKey = this.key,
            occurredOn = OffsetDateTime.now(),
            internalOrderNumber = newInternalOrderNumber,
        )
    }

    /** Updates the properties of a damage file where we either expect or allow updates. */
    internal fun update(
        deductible: MonetaryAmount?,
        claimTowardsEmployee: MonetaryAmount?,
        insuranceCoverage: InsuranceCoverage,
        damageResponsibility: DamageResponsibility,
        damageDate: OffsetDateTime,
        employeeToBeCharged: EmployeeNumber?,
        isExternal: Boolean,
    ): DamageFileUpdatedEvent? {
        requireDamageFileNotClosed()
        requireNoLegalHolds()

        var hasBeenUpdated = false

        /** handle montary benefit updated * */
        if (monetaryAmountHasChanged(this.deductible, deductible)) {
            this.deductible = deductible
            this.deductibleUpdated = OffsetDateTime.now()
            hasBeenUpdated = true
        }

        /** handle monetary benefit deleted * */
        if (monetaryAmountHasBeenDeleted(this.deductible, deductible)) {
            this.deductible = ZERO_MONETARY_AMOUNT
            this.deductibleUpdated = OffsetDateTime.now()
            hasBeenUpdated = true
        }

        /** handle claimTowardsEmployee updated * */
        if (monetaryAmountHasChanged(this.claimTowardsEmployee, claimTowardsEmployee)) {
            this.claimTowardsEmployee = claimTowardsEmployee
            hasBeenUpdated = true
        }

        /** handle claimTowardsEmployee deleted * */
        if (monetaryAmountHasBeenDeleted(this.claimTowardsEmployee, claimTowardsEmployee)) {
            this.claimTowardsEmployee = ZERO_MONETARY_AMOUNT
            hasBeenUpdated = true
        }

        /** handle insuranceCoverage updated */
        if (this.insuranceCoverage != insuranceCoverage) {
            this.insuranceCoverage = insuranceCoverage
            hasBeenUpdated = true
        }

        /** handle damageResponsibility updated */
        if (this.damageResponsibility != damageResponsibility) {

            /**
             * FPT1-482 is damageResponsibility (more specifically 'driverAtFault') changed we also need to issue a new
             * internalOrderNumber.
             *
             * To do this, we reset the current one and let the sync job take care of issuing and synchronizing a new
             * one.
             */
            if (this.damageResponsibility.driverAtFault != damageResponsibility.driverAtFault) {
                this.internalOrderNumber = null
            }

            this.damageResponsibility = damageResponsibility
            hasBeenUpdated = true
        }

        /**
         * handle isExternal updated - call employee update to be sure it gets triggerred, even if no updated
         * employeeNumber is provided
         */
        if (this.isExternal != isExternal) {
            this.isExternal = isExternal
            if (this.isExternal) {
                // force update to external, if the "wrong" employee is provided for some reason
                updateEmployeeToBeCharged(EmployeeNumber.external)
            }
            hasBeenUpdated = true
        }

        /** Handle employeeToBeCharged updated. Has to be done after isExternal update. */
        if (this.employeeToBeCharged != employeeToBeCharged) {
            updateEmployeeToBeCharged(employeeToBeCharged)
            hasBeenUpdated = true
        }

        /** handle damageDateUpdated ignoring nanos during comparison (compensate for postgres precision) */
        if (this.damageDate.truncatedTo(ChronoUnit.MICROS) != damageDate.truncatedTo(ChronoUnit.MICROS)) {
            this.damageDate = damageDate
            hasBeenUpdated = true
        }

        return if (hasBeenUpdated)
            DamageFileUpdatedEvent(
                damageFileKey = this.key,
                occurredOn = OffsetDateTime.now(),
                externalReference = this.externalReference,
            )
        else null
    }

    /** FPT1-482 if this damage is caused by an external set EmployeeToBeCharged accordingly */
    private fun updateEmployeeToBeCharged(employeeToBeCharged: EmployeeNumber?) {
        this.employeeToBeCharged =
            if (this.isExternal) {
                EmployeeNumber.external
            } else {
                requireNotNull(employeeToBeCharged) {
                    "EmployeeToBeCharged has to be set for damage cause by internal employee."
                }
            }
    }

    /** checks if the amount has been changed in repair-fix */
    private fun monetaryAmountHasChanged(oldAmount: MonetaryAmount?, newAmount: MonetaryAmount?): Boolean =
        null != newAmount && 0 != oldAmount?.compareTo(newAmount)

    /**
     * checks if the amount has been deleted in repair-fix. 2024-07-26 Luisa: In DMS, we should treat deleted amount
     * same as putting 0 in repair-fix
     */
    private fun monetaryAmountHasBeenDeleted(oldAmount: MonetaryAmount?, newAmount: MonetaryAmount?): Boolean =
        oldAmount != null && 0 != oldAmount.compareTo(ZERO_MONETARY_AMOUNT) && newAmount == null

    private fun monetaryAmountHasBeenUpdated(oldAmount: MonetaryAmount?, newAmount: MonetaryAmount?) =
        monetaryAmountHasChanged(oldAmount, newAmount) || monetaryAmountHasBeenDeleted(oldAmount, newAmount)

    internal fun hasUpdates(
        deductible: MonetaryAmount?,
        claimTowardsEmployee: MonetaryAmount?,
        insuranceCoverage: InsuranceCoverage,
        damageResponsibility: DamageResponsibility,
        employeeToBeCharged: EmployeeNumber?,
        damageDate: OffsetDateTime,
        isExternal: Boolean,
    ): Boolean {
        val employeeToBeChargedChanged =
            if (isExternal) {
                // if isExternal is true, there is an employee change, only when isExternal changed
                this.isExternal != isExternal
            } else {
                this.employeeToBeCharged != employeeToBeCharged
            }
        return monetaryAmountHasBeenUpdated(this.deductible, deductible) ||
            monetaryAmountHasBeenUpdated(this.claimTowardsEmployee, claimTowardsEmployee) ||
            this.insuranceCoverage != insuranceCoverage ||
            this.damageResponsibility != damageResponsibility ||
            this.damageDate.truncatedTo(ChronoUnit.MICROS) != damageDate.truncatedTo(ChronoUnit.MICROS) ||
            employeeToBeChargedChanged
    }

    // ######### LOCK ################
    /** Will close this damage file preventing any further manipulation. */
    internal fun close(): DamageFileClosedEvent? {
        if (DamageFileState.CLOSED == this.state) return null
        /**
         * Check if current status is REOPEN. Save a flag in this case, as this information is relevant for PVCC
         * adapter.
         */
        val hasBeenReopened = DamageFileState.REOPENED == state
        if (null == this.closedDate) {
            /**
             * FPT1-985 only set closed date once, do not update, or PVCC will break If this causes issues with any
             * other downstream system or consumer of DamageFileClosedEvent at any time a dedicated damageFileClosedPvcc
             * property has to be maintained separately.
             */
            this.closedDate = OffsetDateTime.now()
        }
        this.state = DamageFileState.CLOSED
        return DamageFileClosedEvent(
            damageFileKey = this.key,
            occurredOn = OffsetDateTime.now(),
            externalReference = this.externalReference,
            hasBeenReopened = hasBeenReopened,
        )
    }

    /**
     * Will reopen a closed damage file.
     *
     * A reopened damage file behaves like an open one, accepting updates and such.
     */
    internal fun reopen(): DamageFileReopenedEvent? {
        if (DamageFileState.REOPENED == this.state) return null
        requireDamageFileClosedWithNoLegalHold()

        if (requireNotNull(closedDate).plusYears(REOPEN_LIMIT_IN_YEARS).isBefore(OffsetDateTime.now())) {
            throw DamageFileReopenException(
                "DamageFile with key [${key.value}] can not be reopened as it has been closed for more than $REOPEN_LIMIT_IN_YEARS years."
            )
        }

        this.state = DamageFileState.REOPENED
        return DamageFileReopenedEvent(
            damageFileKey = this.key,
            occurredOn = OffsetDateTime.now(),
            externalReference = this.externalReference,
        )
    }

    private fun requireDamageFileClosedWithNoLegalHold() {
        requireDamageFileClosed()
        requireNoLegalHolds()
    }

    /**
     * Will throw [DamageFileAlreadyClosedException] if this DamageFile is already closed and can no longer be modified.
     */
    private fun requireDamageFileNotClosed() {
        if (DamageFileState.CLOSED == this.state) throw DamageFileAlreadyClosedException(this.key)
    }

    private fun requireDamageFileClosed() {
        if (DamageFileState.CLOSED != this.state) throw DamageFileNotClosedException(this.key)
    }

    val isClosed: Boolean
        @JsonIgnore get() = DamageFileState.CLOSED == this.state

    // ######### LEGAL HOLD ################
    override val domainEntityReference: DomainEntityReference
        @JsonIgnore get() = DomainEntityReference(this.key.value)

    override val legalHolds: Set<LegalHoldKey>
        get() = _legalHolds.toSet()

    override fun addLegalHold(legalHoldKey: LegalHoldKey) {
        this._legalHolds.add(legalHoldKey)
    }

    override fun releaseLegalHold(legalHoldKey: LegalHoldKey) {
        this._legalHolds.remove(legalHoldKey)
    }

    companion object {
        private const val ANONYMIZE = "00000000"
        private const val REOPEN_LIMIT_IN_YEARS = 3L
        private val ZERO_MONETARY_AMOUNT = MonetaryAmount(value = BigDecimal(0))
    }
}

/**
 * The internal Id for a [DamageFile]. This Id is not supposed to be used outside this module. Also, as DamageFile has a
 * proper 'key' you should not touch this Id at all ;)
 */
@Embeddable data class DamageFileId(@Basic val value: UUID = UUID.randomUUID()) : Serializable

@Embeddable
data class DamageFileKey(@Basic override val value: String) : Key {
    init {
        require(value.isNotBlank()) { "DamageFileKey may not be blank." }
        /**
         * This is a new constraint enforced with FPT1-448 and the switch to use sequence generated (SAP compatible)
         * values for DamageFileKey.
         */
        require(10 >= value.length) { "DamageFileKey [$value] length may not exceed 10 characters." }
    }
}

enum class DamageFileState {
    OPEN,
    CLOSED,
    REOPENED,
}

enum class InsuranceCoverage {
    UNKNOWN,
    FULL,
    PARTIAL,
    THIRD_PARTY,
    NOT_COVERED,
    NOT_SPECIFIED,
}

enum class DamageResponsibility {
    DRIVER_CAUSED_ACCIDENT,
    OTHER_PARTY_CAUSED_ACCIDENT,
    BOTH_CAUSED_ACCIDENT,
    RESPONSIBILITY_IS_NOT_CLEAR,
    UNKNOWN;

    val driverAtFault: Boolean
        get() =
            when (this) {
                DRIVER_CAUSED_ACCIDENT -> true
                BOTH_CAUSED_ACCIDENT -> false
                UNKNOWN -> false
                RESPONSIBILITY_IS_NOT_CLEAR -> false
                OTHER_PARTY_CAUSED_ACCIDENT -> false
            }
}

interface DamageFileEvent : DomainEvent {
    val damageFileKey: DamageFileKey
}

data class DamageFileCreatedEvent(
    override val damageFileKey: DamageFileKey,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val externalReference: String?,
) : DamageFileEvent

data class DamageFileClosedEvent(
    override val damageFileKey: DamageFileKey,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val externalReference: String?,
    val hasBeenReopened: Boolean,
) : DamageFileEvent

data class DamageFileReopenedEvent(
    override val damageFileKey: DamageFileKey,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val externalReference: String?,
) : DamageFileEvent

data class DamageFileUpdatedEvent(
    override val damageFileKey: DamageFileKey,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val externalReference: String?,
) : DamageFileEvent

data class DamageFileInternalOrderNumberUpdatedEvent(
    override val damageFileKey: DamageFileKey,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val internalOrderNumber: InternalOrderNumber,
) : DamageFileEvent

data class DamageFileDeleteEvent(
    override val damageFileKey: DamageFileKey,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val externalReference: String?,
) : DamageFileEvent

data class DamageFileAnonymizedEvent(
    override val damageFileKey: DamageFileKey,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
    val externalReference: String?,
) : DamageFileEvent
