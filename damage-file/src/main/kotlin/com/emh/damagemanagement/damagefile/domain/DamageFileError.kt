/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

class DamageFileNotFoundException(val damageFileKey: DamageFileKey, message: String? = null) :
    NoSuchElementException(message)

class DamageFileAlreadyClosedException(damageFileKey: DamageFileKey) :
    NoSuchElementException("DamageFile with key: $damageFileKey is already closed and can not longer be updated.")

class DamageFileNotClosedException(damageFileKey: DamageFileKey) :
    NoSuchElementException("Damage file with key [${damageFileKey}] is not closed")

class DamageFileReopenException(override val message: String) : RuntimeException()
