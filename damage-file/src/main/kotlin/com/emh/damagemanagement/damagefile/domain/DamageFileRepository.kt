/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.repository.Repository

interface DamageFileRepository : Repository<DamageFile, DamageFileId>, JpaSpecificationExecutor<DamageFile> {

    fun save(damageFile: DamageFile): DamageFile

    fun findByKey(key: DamageFileKey): DamageFile?

    fun deleteByKey(key: DamageFileKey): Int

    @Suppress("FunctionName") fun findByVehicle_vin(vin: String): DamageFile?

    @Suppress("FunctionName") fun findByVehicle_vGuid(vGuid: String): DamageFile?

    fun findByExternalReference(externalReference: String): DamageFile?

    fun existsByKey(key: DamageFileKey): Boolean

    fun findAllByKeyIn(keys: Collection<DamageFileKey>): List<DamageFile>

    fun countByVehicle_vin(vin: String): Int

    fun countByVehicle_vinAndState(vin: String, state: DamageFileState): Int
}
