/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import java.io.Serializable
import java.util.*

/** A single damage report attached to a damage file. This is NOT an aggregate root. */
@Entity
@Table(name = "damage_report")
class DamageReport {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: DamageReportId = DamageReportId()
}

@Embeddable data class DamageReportId(@Basic val value: UUID = UUID.randomUUID()) : Serializable
