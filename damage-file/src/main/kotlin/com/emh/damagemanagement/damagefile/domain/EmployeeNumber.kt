/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import jakarta.persistence.Embeddable

/** Value class for the employee number. Currently used to identify employees. */
@Embeddable
data class EmployeeNumber(val value: String) {

    init {
        require(value.isNotBlank()) { "Employee number may not be empty." }
    }

    companion object {
        val external: EmployeeNumber
            get() = EmployeeNumber("extern")

        // create a sanitized representation of an employee number. Used for easier comparison
        fun sanitized(employeeNumber: String): String {
            // strip any single leading P and pad start number with 0s until 8 digits are reached
            return employeeNumber
                .let { if (it.startsWith("p", true)) it.lowercase().replaceFirst("p", "", true) else it }
                .padStart(8, '0')
        }
    }
}
