/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import jakarta.persistence.Embeddable

/** The unique internal order number provided by PACE system for each damage (aka 'Innenauftragsnummer'). */
@Embeddable
data class InternalOrderNumber(val value: String) {
    init {
        require(value.isNotBlank()) { "Internal order number may not be blank." }
    }
}
