/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import jakarta.persistence.Embeddable
import java.math.BigDecimal

/** Value class representing a deductible value (also known as 'Selbstbehalt') */
@Embeddable
@Suppress("unused")
data class MonetaryAmount(val value: BigDecimal) : Comparable<MonetaryAmount> {
    @Suppress("MemberVisibilityCanBePrivate") val currencyUnit: String = DEFAULT_CURRENCY_UNIT

    companion object {
        fun of(value: BigDecimal): MonetaryAmount = MonetaryAmount(value)

        fun of(value: Double): MonetaryAmount = MonetaryAmount(BigDecimal.valueOf(value))

        const val DEFAULT_CURRENCY_UNIT = "EUR"
    }

    override fun compareTo(other: MonetaryAmount): Int {
        require(this.currencyUnit == other.currencyUnit) { "CurrencyUnit mismatch." }
        return this.value.compareTo(other.value)
    }
}
