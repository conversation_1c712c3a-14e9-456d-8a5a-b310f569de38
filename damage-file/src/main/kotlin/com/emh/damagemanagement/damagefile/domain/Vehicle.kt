/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import jakarta.persistence.Column
import jakarta.persistence.Embeddable

/**
 * Vehicle reference for a damage file.
 *
 * VIN and VGUID may be used to filter for damage files.
 * VGUID is optional as manually created vehicles do not have a VGUID.
 */
@Embeddable
data class Vehicle(
    val vin: String,
    val vGuid: String?,
    val licensePlate: String?,
    val leasingArt: String,
    val fleetId: String,
    val costCenter: String?,
    val usingCostCenter: String?,
    @Column(name = "vehicle_external_reference") val externalReference: String,
) {
    init {
        require(vin.isNotBlank()) { "VIN may not be blank." }
        require(externalReference.isNotBlank()) { "ExternalReference may not be blank." }
    }
}
