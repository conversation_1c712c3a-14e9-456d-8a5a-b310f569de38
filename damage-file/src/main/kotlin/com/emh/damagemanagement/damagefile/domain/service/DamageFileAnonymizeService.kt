/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileNotClosedException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldInPlaceException
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class DamageFileAnonymizeService(private val applicationEventPublisher: ApplicationEventPublisher) {

    /**
     * Will anonymize given damage file.
     *
     * Enforcing no-rollback in case of LegalHoldInPlaceException,DamageFileNotClosedException to not throw
     * UnexpectedRollbackException when transaction ends in the caller method leading to rollback of the entire
     * transaction (all damage files)
     *
     * @param damageFile damage-file to be anonymized
     */
    @Transactional(
        propagation = Propagation.MANDATORY,
        noRollbackFor = [LegalHoldInPlaceException::class, DamageFileNotClosedException::class],
    )
    fun anonymizeDamageFile(damageFile: DamageFile) {
        damageFile.anonymize().also { anonymizeEvent ->
            log.info("Anonymized damage file with key [${damageFile.key}].")
            applicationEventPublisher.publishEvent(anonymizeEvent)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileAnonymizeService::class.java)
    }
}
