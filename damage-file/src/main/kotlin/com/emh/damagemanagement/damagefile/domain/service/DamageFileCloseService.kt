/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFile
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class DamageFileCloseService(private val applicationEventPublisher: ApplicationEventPublisher) {

    /** Will close given damage file. */
    fun closeDamageFile(damageFile: DamageFile) {
        damageFile.close()?.also { updateEvent ->
            applicationEventPublisher.publishEvent(updateEvent)
            log.info("Closed damage file with key [${damageFile.key}].")
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileCloseService::class.java)
    }
}
