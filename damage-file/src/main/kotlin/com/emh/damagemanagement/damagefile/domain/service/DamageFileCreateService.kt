/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageFileRepository
import com.emh.damagemanagement.damagefile.domain.DamageReport
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.Vehicle
import java.time.OffsetDateTime
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class DamageFileCreateService(
    private val damageFileRepository: DamageFileRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    fun createDamageFile(damageFileNewOrUpdate: DamageFileNewOrUpdate, damageFileKey: DamageFileKey): DamageFile {
        val damageFile = damageFileRepository.save(damageFileNewOrUpdate.toDamageFile(damageFileKey))

        if (null == damageFileNewOrUpdate.externalReference) {
            log.info("Created new damage file with key ${damageFile.key.value}.")
        } else {
            log.info(
                "Created new damage file with key ${damageFile.key.value} and external reference: ${damageFileNewOrUpdate.externalReference}."
            )
        }
        applicationEventPublisher.publishEvent(damageFile.damageFileCreatedEvent)

        return damageFile
    }

    private fun DamageFileNewOrUpdate.toDamageFile(damageFileKey: DamageFileKey) =
        DamageFile(
            damageReport = this.damageReport,
            vehicle = this.vehicle,
            employeeToBeCharged = this.employeeToBeCharged,
            deductible = this.deductible,
            claimTowardsEmployee = this.claimTowardsEmployee,
            externalReference = this.externalReference,
            key = damageFileKey,
            creationDate = this.externalCreationDate ?: OffsetDateTime.now(),
            insuranceCoverage = this.insuranceCoverage,
            damageResponsibility = this.damageResponsibility,
            damageDate = this.damageDate,
            isExternal = this.isExternal,
        )

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileCreateService::class.java)
    }
}

data class DamageFileNewOrUpdate(
    val damageReport: DamageReport,
    val vehicle: Vehicle,
    val externalCreationDate: OffsetDateTime? = null,
    val employeeToBeCharged: EmployeeNumber?,
    val deductible: MonetaryAmount? = null,
    val claimTowardsEmployee: MonetaryAmount? = null,
    val externalReference: String? = null,
    val insuranceCoverage: InsuranceCoverage,
    val isClosed: Boolean,
    val damageResponsibility: DamageResponsibility,
    val damageDate: OffsetDateTime,
    val isExternal: Boolean,
)
