/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileDeleteEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileNotClosedException
import com.emh.damagemanagement.damagefile.domain.DamageFileRepository
import com.emh.damagemanagement.legal.hold.domain.LegalHoldInPlaceException
import java.time.OffsetDateTime
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class DamageFileDeleteService(
    private val damageFileRepository: DamageFileRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    /**
     * Will delete given damage file.
     *
     * Enforcing no-rollback in case of LegalHoldInPlaceException,DamageFileNotClosedException to not throw
     * UnexpectedRollbackException when transaction ends in the caller method leading to rollback of the entire
     * transaction (all damage files)
     *
     * @param damageFile damage-file to be deleted
     */
    @Transactional(
        propagation = Propagation.MANDATORY,
        noRollbackFor = [LegalHoldInPlaceException::class, DamageFileNotClosedException::class],
    )
    fun deleteDamageFile(damageFile: DamageFile) {
        if (!damageFile.isClosed) throw DamageFileNotClosedException(damageFile.key)
        else if (damageFile.legalHolds.isNotEmpty())
            throw LegalHoldInPlaceException(damageFile.domainEntityReference, damageFile.legalHolds)
        val noOfRecords = damageFileRepository.deleteByKey(damageFile.key)
        if (noOfRecords == 1) {
            applicationEventPublisher.publishEvent(
                DamageFileDeleteEvent(
                    damageFileKey = damageFile.key,
                    occurredOn = OffsetDateTime.now(),
                    externalReference = damageFile.externalReference,
                )
            )
            log.info("Deleted damage file with key [${damageFile.key.value}].")
        } else log.warn("Damage File with key [${damageFile.key.value}] does not exist")
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileDeleteService::class.java)
    }
}
