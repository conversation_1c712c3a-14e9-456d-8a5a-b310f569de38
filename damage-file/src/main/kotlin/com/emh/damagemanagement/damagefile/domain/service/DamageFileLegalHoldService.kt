/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldDocuments
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldDomainEntityService
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import org.springframework.context.ApplicationEventPublisher
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Service

/** DamageFile implementation of the [LegalHoldDomainEntityService] */
@Service
class DamageFileLegalHoldService(override val applicationEventPublisher: ApplicationEventPublisher) :
    LegalHoldDomainEntityService<DamageFile> {
    /** Provides a json representation of a damage file. */
    override fun getLegalHoldRelevantDocuments(entity: DamageFile): LegalHoldDocuments {
        val damageFileJson = entity.toJsonNode().toPrettyString()
        return mapOf("${entity.key.value}.json" to ByteArrayResource(damageFileJson.toByteArray(Charsets.UTF_8)))
    }
}
