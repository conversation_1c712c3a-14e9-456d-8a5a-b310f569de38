/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFile
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class DamageFileReopenService(private val applicationEventPublisher: ApplicationEventPublisher) {
    fun reopenDamageFile(damageFile: DamageFile) {
        damageFile.reopen()?.also { reopenedEvent ->
            applicationEventPublisher.publishEvent(reopenedEvent)
            log.info("Reopened damage file with key [${damageFile.key.value}].")
        }
    }

    /** Will check if a damageFile needs to be reopened. */
    fun needsToBeReopened(damageFile: DamageFile, damageFileNewOrUpdate: DamageFileNewOrUpdate): Boolean {
        // do not try to reopen if damage file is not closed
        if (!damageFile.isClosed) return false

        // if incoming update is not closed, but damage file says it is we assume a reopen scenario
        if (damageFile.isClosed && !damageFileNewOrUpdate.isClosed) return true

        /**
         * If incoming update is closed and damage file is also closed, check if the damage file has updates...
         *
         * Then we assume that an incoming update has been reopened AND closed within our update cycle, so we mirror
         * these state transition here as well.
         */
        return damageFile.hasUpdates(
            deductible = damageFileNewOrUpdate.deductible,
            claimTowardsEmployee = damageFileNewOrUpdate.claimTowardsEmployee,
            insuranceCoverage = damageFileNewOrUpdate.insuranceCoverage,
            damageResponsibility = damageFileNewOrUpdate.damageResponsibility,
            damageDate = damageFileNewOrUpdate.damageDate,
            employeeToBeCharged = damageFileNewOrUpdate.employeeToBeCharged,
            isExternal = damageFileNewOrUpdate.isExternal,
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileReopenService::class.java)
    }
}
