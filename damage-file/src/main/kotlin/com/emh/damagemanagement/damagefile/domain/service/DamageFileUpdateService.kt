/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import java.time.OffsetDateTime
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class DamageFileUpdateService(private val applicationEventPublisher: ApplicationEventPublisher) {

    fun updateDamageFile(damageFile: DamageFile, damageFileUpdate: DamageFileUpdate) {
        if (damageFile.isClosed) {
            log.debug("Damage file with key [${damageFile.key.value}] is already closed. Skipping update.")
            return
        }

        damageFile
            .update(
                deductible = damageFileUpdate.deductible,
                claimTowardsEmployee = damageFileUpdate.claimTowardsEmployee,
                insuranceCoverage = damageFileUpdate.insuranceCoverage,
                damageResponsibility = damageFileUpdate.damageResponsibility,
                damageDate = damageFileUpdate.damageDate,
                employeeToBeCharged = damageFileUpdate.employeeToBeCharged,
                isExternal = damageFileUpdate.isExternal,
            )
            ?.also { updateEvent ->
                log.info("Updated damage file with key [${damageFile.key}].")
                applicationEventPublisher.publishEvent(updateEvent)
            }
    }

    fun updateInternalOrderNumber(damageFile: DamageFile, internalOrderNumber: InternalOrderNumber): DamageFile {
        damageFile.updateInternalOrderNumber(internalOrderNumber).also { updateEvent ->
            applicationEventPublisher.publishEvent(updateEvent)
        }
        return damageFile
    }

    companion object {
        private val log = LoggerFactory.getLogger(DamageFileUpdateService::class.java)
    }
}

data class DamageFileUpdate(
    val deductible: MonetaryAmount? = null,
    val claimTowardsEmployee: MonetaryAmount? = null,
    val insuranceCoverage: InsuranceCoverage,
    val damageResponsibility: DamageResponsibility,
    // FPT1-482 can be null for externals
    val employeeToBeCharged: EmployeeNumber?,
    val damageDate: OffsetDateTime,
    val isExternal: Boolean,
)
