package com.emh.damagemanagement.damagefile

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileId
import jakarta.persistence.EntityManager
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.data.jpa.repository.support.JpaRepositoryFactory
import org.springframework.test.context.transaction.TestTransaction

@SpringBootApplication(scanBasePackages = ["com.emh.damagemanagement.*"])
@ConfigurationPropertiesScan("com.emh.damagemanagement.*")
@EnableJpaRepositories("com.emh.damagemanagement.*")
@EntityScan("com.emh.damagemanagement.*")
class TestDamageFileApplication {

    fun main(args: Array<String>) {
        runApplication<TestDamageFileApplication>(*args)
    }
}

interface TestDamageFileRepository : JpaRepository<DamageFile, DamageFileId>

@TestConfiguration
class TestCleanupProvider {

    /**
     * Done manually here as we are currently having trouble instantiating Test-JpaRepositories in tests of other
     * modules referencing this configuration
     */
    @Bean
    @Primary
    fun testDamageFileRepository(entityManager: EntityManager): TestDamageFileRepository {
        return JpaRepositoryFactory(entityManager).getRepository(TestDamageFileRepository::class.java)
    }

    @Bean("damage-file-cleanup")
    fun cleanup(testDamageFileRepository: TestDamageFileRepository): () -> Unit = {
        if (TestTransaction.isActive()) TestTransaction.end()
        TestTransaction.start()

        testDamageFileRepository.deleteAll()

        TestTransaction.flagForCommit()
    }
}
