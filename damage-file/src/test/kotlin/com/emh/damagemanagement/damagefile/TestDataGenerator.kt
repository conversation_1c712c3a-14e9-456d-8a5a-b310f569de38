/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageReport
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.InternalOrderNumber
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.Vehicle
import com.emh.damagemanagement.damagefile.domain.service.DamageFileNewOrUpdate
import com.emh.damagemanagement.shared.createRandomString
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.OffsetDateTime
import kotlin.random.Random
import net.datafaker.Faker
import org.springframework.util.ReflectionUtils

val validDamageFileKey: DamageFileKey
    get() = DamageFileKey(Random.nextLong(1, Int.MAX_VALUE.toLong()).toString())

class DamageFileBuilder {

    private val closeFunction = requireNotNull(ReflectionUtils.findMethod(DamageFile::class.java, "close\$damage_file"))
    private val reopenFunction =
        requireNotNull(ReflectionUtils.findMethod(DamageFile::class.java, "reopen\$damage_file"))

    private var damageReport: DamageReport = DamageReport()
    private var employeeToBeCharged: EmployeeNumber = EmployeeNumber(createRandomString(Random.nextInt(4, 23)))
    private var deductible: MonetaryAmount? =
        MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP))
    private var claimTowardsEmployee: MonetaryAmount? =
        MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP))
    private var internalOrderNumber: InternalOrderNumber =
        InternalOrderNumber(createRandomString(Random.nextInt(4, 23)))
    private var externalReference: String = createRandomString(Random.nextInt(4, 23))

    private var insuranceCoverage: InsuranceCoverage = InsuranceCoverage.NOT_SPECIFIED

    private var damageDate: OffsetDateTime = OffsetDateTime.now()

    private var isExternal: Boolean = false

    private var damageResponsibility: DamageResponsibility = DamageResponsibility.entries.random()

    private var vehicle: Vehicle = VehicleBuilder.buildSingle()

    private var creationDate: OffsetDateTime = OffsetDateTime.now()

    private var closed: Boolean = false
    private var reopened: Boolean = false

    fun vehicle(vehicle: Vehicle) = apply { this.vehicle = vehicle }

    fun damageReport(damageReport: DamageReport) = apply { this.damageReport = damageReport }

    fun isExternal(isExternal: Boolean) = apply { this.isExternal = isExternal }

    fun employeeToBeCharged(employeeToBeCharged: EmployeeNumber) = apply {
        this.employeeToBeCharged = employeeToBeCharged
    }

    fun deductible(deductible: MonetaryAmount?) = apply { this.deductible = deductible }

    fun claimTowardsEmployee(claimTowardsEmployee: MonetaryAmount?) = apply {
        this.claimTowardsEmployee = claimTowardsEmployee
    }

    fun internalOrderNumber(internalOrderNumber: InternalOrderNumber) = apply {
        this.internalOrderNumber = internalOrderNumber
    }

    fun externalReference(externalReference: String) = apply { this.externalReference = externalReference }

    fun creationDate(creationDate: OffsetDateTime) = apply { this.creationDate = creationDate }

    fun damageResponsibility(damageResponsibility: DamageResponsibility) = apply {
        this.damageResponsibility = damageResponsibility
    }

    fun damageDate(damageDate: OffsetDateTime) = apply { this.damageDate = damageDate }

    fun closed() = apply { this.closed = true }

    fun reopened() = apply { this.reopened = true }

    fun insuranceCoverage(insuranceCoverage: InsuranceCoverage) = apply { this.insuranceCoverage = insuranceCoverage }

    fun build(): DamageFile =
        DamageFile(
                damageReport = damageReport,
                vehicle = vehicle,
                employeeToBeCharged = employeeToBeCharged,
                deductible = deductible,
                claimTowardsEmployee = claimTowardsEmployee,
                internalOrderNumber = internalOrderNumber,
                externalReference = externalReference,
                key = validDamageFileKey,
                creationDate = creationDate,
                insuranceCoverage = insuranceCoverage,
                damageDate = damageDate,
                isExternal = isExternal,
                damageResponsibility = damageResponsibility,
            )
            .apply {
                if (<EMAIL>) {
                    ReflectionUtils.makeAccessible(closeFunction)
                    ReflectionUtils.invokeMethod(closeFunction, this@apply)
                }
                if (<EMAIL>) {
                    ReflectionUtils.makeAccessible(closeFunction)
                    ReflectionUtils.makeAccessible(reopenFunction)
                    ReflectionUtils.invokeMethod(closeFunction, this@apply)
                    ReflectionUtils.invokeMethod(reopenFunction, this@apply)
                }
            }

    companion object {
        fun buildSingle() = DamageFileBuilder().build()

        fun buildMultiple(count: Int): Set<DamageFile> = (1..count).map { DamageFileBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

val ANONYMIZED_EMPLOYEE_NUMBER = EmployeeNumber("00000000")

class DamageFileNewOrUpdateBuilder {
    private var damageReport: DamageReport = DamageReport()
    private var employeeToBeCharged: EmployeeNumber = EmployeeNumber(createRandomString(Random.nextInt(4, 23)))
    private var deductible: MonetaryAmount? =
        MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP))
    private var claimTowardsEmployee: MonetaryAmount? =
        MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP))
    private var externalReference: String? = createRandomString(Random.nextInt(4, 23))
    private var vehicle: Vehicle =
        Vehicle(
            vin = createRandomString(22),
            vGuid = createRandomString(23),
            licensePlate = createRandomString(12),
            leasingArt = createRandomString(5),
            costCenter = createRandomString(9),
            usingCostCenter = createRandomString(9),
            fleetId = createRandomString(23),
            externalReference = createRandomString(36),
        )
    private var creationDate: OffsetDateTime = OffsetDateTime.now()
    private var closed: Boolean = false
    private var isExternal: Boolean = false
    private var insuranceCoverage: InsuranceCoverage = InsuranceCoverage.entries.random()
    private var damageResponsibility: DamageResponsibility = DamageResponsibility.entries.random()
    private var damageDate: OffsetDateTime = OffsetDateTime.now()

    fun externalReference(externalReference: String?) = apply { this.externalReference = externalReference }

    fun employeeToBeCharged(employeeToBeCharged: EmployeeNumber) = apply {
        this.employeeToBeCharged = employeeToBeCharged
    }

    fun creationDate(creationDate: OffsetDateTime) = apply { this.creationDate = creationDate }

    fun isExternal(isExternal: Boolean) = apply { this.isExternal = isExternal }

    fun deductible(deductible: MonetaryAmount?) = apply { this.deductible = deductible }

    fun claimTowardsEmployee(claimTowardsEmployee: MonetaryAmount?) = apply {
        this.claimTowardsEmployee = claimTowardsEmployee
    }

    fun insuranceCoverage(insuranceCoverage: InsuranceCoverage) = apply { this.insuranceCoverage = insuranceCoverage }

    fun damageResponsibility(damageResponsibility: DamageResponsibility) = apply {
        this.damageResponsibility = damageResponsibility
    }

    fun vehicle(vehicle: Vehicle) = apply { this.vehicle = vehicle }

    fun closed() = apply { this.closed = true }

    fun damageDate(damageDate: OffsetDateTime) = apply { this.damageDate = damageDate }

    fun build(): DamageFileNewOrUpdate {
        return DamageFileNewOrUpdate(
            damageReport = damageReport,
            vehicle = vehicle,
            externalCreationDate = creationDate,
            employeeToBeCharged = employeeToBeCharged,
            deductible = deductible,
            claimTowardsEmployee = claimTowardsEmployee,
            externalReference = externalReference,
            insuranceCoverage = insuranceCoverage,
            isClosed = closed,
            damageResponsibility = damageResponsibility,
            damageDate = damageDate,
            isExternal = isExternal,
        )
    }

    companion object {

        fun buildSingle(): DamageFileNewOrUpdate = DamageFileNewOrUpdateBuilder().build()

        fun buildMultiple(count: Int): Set<DamageFileNewOrUpdate> =
            (1..count).map { DamageFileNewOrUpdateBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class VehicleBuilder {

    val faker = Faker()

    private var vin: String = createRandomString(22)
    private var vGuid: String = createRandomString(23)
    private var licensePlate: String? = faker.regexify("[A-Z]{1}-[A-Z]{2} [1-9]{4}")
    private var leasingArt = createRandomString(5)
    private var fleetId = createRandomString(23)
    private var costCenter: String? = createRandomString(9)
    private var usingCostCenter: String? = createRandomString(9)
    private var vehicleExternalReference: String = createRandomString(36)

    fun vin(vin: String) = apply { this.vin = vin }

    fun vGuid(vGuid: String) = apply { this.vGuid = vGuid }

    fun costCenter(costCenter: String?) = apply { this.costCenter = costCenter }

    fun licensePlate(licensePlate: String?) = apply { this.licensePlate = licensePlate }

    fun build(): Vehicle {
        return Vehicle(
            vin = this.vin,
            vGuid = this.vGuid,
            licensePlate = this.licensePlate,
            leasingArt = this.leasingArt,
            costCenter = costCenter,
            usingCostCenter = usingCostCenter,
            fleetId = fleetId,
            externalReference = vehicleExternalReference,
        )
    }

    companion object {

        fun buildSingle(): Vehicle = VehicleBuilder().build()

        fun buildMultiple(count: Int): Set<Vehicle> = (1..count).map { VehicleBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
