/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.adapter.db

import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileRepository
import com.emh.damagemanagement.damagefile.domain.DamageFileState
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.Vehicle
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.shared.TEST_USER
import jakarta.persistence.EntityManager
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.data.jpa.domain.Specification

@IntegrationTest
class DamageFileRepositoryTest {

    @Autowired private lateinit var damageFileRepository: DamageFileRepository

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should set auditing field when saving damage file entity`() {
        val damageFile = DamageFileBuilder().build()
        damageFileRepository.save(damageFile)
        entityManager.flush()

        val damageFileFromRepository = damageFileRepository.findByKey(damageFile.key)!!

        assertThat(damageFileFromRepository.createdBy).isEqualTo(TEST_USER)
        assertThat(damageFileFromRepository.created).isNotNull()
    }

    @Test
    fun `should set auditing field when updating damage file entity`() {
        val damageFile = DamageFileBuilder().build()
        damageFileRepository.save(damageFile)
        entityManager.flush()
        val damageFileFromRepositoryBeforeUpdate = damageFileRepository.findByKey(damageFile.key)!!
        damageFileFromRepositoryBeforeUpdate.update(
            deductible = MonetaryAmount.of(23.00),
            claimTowardsEmployee = MonetaryAmount.of(42.5),
            insuranceCoverage = InsuranceCoverage.NOT_SPECIFIED,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )
        entityManager.flush()

        val damageFileFromRepositoryAfterUpdate = damageFileRepository.findByKey(damageFile.key)!!

        assertThat(damageFileFromRepositoryAfterUpdate.lastModifiedBy).isEqualTo(TEST_USER)
        assertThat(damageFileFromRepositoryAfterUpdate.lastModified).isNotNull()
        assertThat(damageFileFromRepositoryAfterUpdate.version).isGreaterThan(0)
    }

    @Test
    fun `should find damage file by creationDate range using specification`() {
        (1..10)
            .map {
                DamageFileBuilder()
                    .creationDate(OffsetDateTime.parse("2024-03-${it.toString().padStart(2, '0')}T10:15:30+01:00"))
                    .build()
            }
            .forEach { damageFileRepository.save(it) }
        entityManager.flush()

        val from = OffsetDateTime.of(2024, 3, 2, 0, 0, 0, 0, ZoneOffset.UTC)
        val to = OffsetDateTime.of(2024, 3, 8, 11, 0, 0, 0, ZoneOffset.UTC)
        val createDateRange: Specification<DamageFile> = Specification { root, cq, cb ->
            cq?.orderBy(cb.desc(root.get<OffsetDateTime>("creationDate")))
            cb.between(root.get("creationDate"), from, to)
        }
        val damageFilesBySpecification = damageFileRepository.findAll(createDateRange)

        assertThat(damageFilesBySpecification.size).isEqualTo(7)
    }

    @Test
    fun `should find all damage files by legal hold key`() {
        val legalHoldKey1 = LegalHoldKey("key1")
        val legalHoldKey2 = LegalHoldKey("key2")
        val matches =
            (1..10)
                .map { DamageFileBuilder().build() }
                .map {
                    it.addLegalHold(legalHoldKey1)
                    damageFileRepository.save(it)
                }
        (1..10)
            .map { DamageFileBuilder().build() }
            .forEach {
                it.addLegalHold(legalHoldKey2)
                damageFileRepository.save(it)
            }
        entityManager.flush()

        val specification =
            Specification<DamageFile> { root, _, cb -> cb.isMember(legalHoldKey1, root.get("_legalHolds")) }
        val damageFilesBySpecification = damageFileRepository.findAll(specification)

        assertThat(damageFilesBySpecification)
            .extracting("key")
            .containsExactlyInAnyOrderElementsOf(matches.map { it.key })
    }

    @Test
    fun `should find all damage files by keys`() {
        val matches = (1..10).map { DamageFileBuilder().build() }.map { damageFileRepository.save(it) }
        (1..10).map { DamageFileBuilder().build() }.forEach { damageFileRepository.save(it) }
        entityManager.flush()

        val damageFiles = damageFileRepository.findAllByKeyIn(matches.map { it.key })

        assertThat(damageFiles).extracting("key").containsExactlyInAnyOrderElementsOf(matches.map { it.key })
    }

    @Test
    fun `should count damages per vin`() {
        val vin = "vin"
        val damagesPerVin = 10
        val vehicle =
            Vehicle(
                vin = vin,
                vGuid = "vGuid",
                licensePlate = "licensePlate",
                leasingArt = "leasingArt",
                fleetId = "fleetId",
                costCenter = "costCenter",
                usingCostCenter = "usingCostCenter",
                externalReference = UUID.randomUUID().toString(),
            )
        (1..damagesPerVin).map { DamageFileBuilder().vehicle(vehicle).build() }.map { damageFileRepository.save(it) }
        (1..10).map { DamageFileBuilder().build() }.map { damageFileRepository.save(it) }

        entityManager.flush()

        val damagedFiles = damageFileRepository.countByVehicle_vin(vin)

        assertThat(damagedFiles).isEqualTo(damagesPerVin)
    }

    @Test
    fun `should count damages per vin and given state`() {
        val vin = "vin"
        val openDamagesPerVin = 5
        val closedDamagesPerVin = 4
        val reopenDamagesPerVin = 3
        val vehicle =
            Vehicle(
                vin = vin,
                vGuid = "vGuid",
                licensePlate = "licensePlate",
                leasingArt = "leasingArt",
                fleetId = "fleetId",
                costCenter = "costCenter",
                usingCostCenter = "usingCostCenter",
                externalReference = UUID.randomUUID().toString(),
            )
        (1..openDamagesPerVin)
            .map { DamageFileBuilder().vehicle(vehicle).build() }
            .map { damageFileRepository.save(it) }
        (1..closedDamagesPerVin)
            .map { DamageFileBuilder().vehicle(vehicle).closed().build() }
            .map { damageFileRepository.save(it) }
        (1..reopenDamagesPerVin)
            .map { DamageFileBuilder().vehicle(vehicle).reopened().build() }
            .map { damageFileRepository.save(it) }
        (1..2).map { DamageFileBuilder().build() }.map { damageFileRepository.save(it) }

        entityManager.flush()

        val openDamagesPerVinFromRepo = damageFileRepository.countByVehicle_vinAndState(vin, DamageFileState.OPEN)
        val closedDamagesPerVinFromRepo = damageFileRepository.countByVehicle_vinAndState(vin, DamageFileState.CLOSED)
        val reopenDamagesPerVinFromRepo = damageFileRepository.countByVehicle_vinAndState(vin, DamageFileState.REOPENED)

        assertThat(openDamagesPerVinFromRepo).isEqualTo(openDamagesPerVin)
        assertThat(closedDamagesPerVinFromRepo).isEqualTo(closedDamagesPerVin)
        assertThat(reopenDamagesPerVinFromRepo).isEqualTo(reopenDamagesPerVin)
    }
}
