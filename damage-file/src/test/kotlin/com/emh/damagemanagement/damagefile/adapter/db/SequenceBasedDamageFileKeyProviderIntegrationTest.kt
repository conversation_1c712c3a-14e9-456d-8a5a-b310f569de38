/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.adapter.db

import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.adapter.out.db.SequenceBasedDamageFileKeyProvider
import com.emh.damagemanagement.damagefile.application.port.DamageFileKeyProvisioningException
import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceException
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class SequenceBasedDamageFileKeyProviderIntegrationTest {

    @Autowired private lateinit var damageFileKeyProvider: SequenceBasedDamageFileKeyProvider

    @Test
    fun `should get new damage file key based on next value from sequence`() {
        val newDamageFileKey = damageFileKeyProvider.provideNewDamageFileKey()

        assertThat(newDamageFileKey).isNotNull()
    }
}

class SequenceBasedDamageFileKeyProviderTest {
    private val entityManager: EntityManager = mock()
    private val damageFileKeyProvider = SequenceBasedDamageFileKeyProvider(entityManager = entityManager)

    @Test
    fun `should throw DamageFileKeyProvisioningException in case of PersistenceException`() {
        whenever(entityManager.createNativeQuery(any(), eq(Long::class.java)))
            .thenThrow(PersistenceException("something went horribly wrong!"))
        assertThatExceptionOfType(DamageFileKeyProvisioningException::class.java).isThrownBy {
            damageFileKeyProvider.provideNewDamageFileKey()
        }
    }
}
