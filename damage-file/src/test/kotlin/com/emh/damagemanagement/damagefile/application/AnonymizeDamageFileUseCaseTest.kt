package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.ANONYMIZED_EMPLOYEE_NUMBER
import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.port.AnonymizeDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.annotation.Rollback
import org.springframework.test.context.transaction.TestTransaction

@IntegrationTest
class AnonymizeDamageFileUseCaseTest {
    @Autowired private lateinit var anonymizeDamageFileUseCase: AnonymizeDamageFileUseCase
    @Autowired private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase
    @Autowired private lateinit var damageFileFinder: DamageFileFinder
    @Autowired @Qualifier("damage-file-cleanup") private lateinit var cleanupDamageFiles: () -> Unit

    @Test
    fun `should anonymize multiple damage files`() {
        val listOfDamageFiles = (1..2).map { DamageFileNewOrUpdateBuilder().closed().build() }
        val damageFileKeys = listOfDamageFiles.map { createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it).key }

        anonymizeDamageFileUseCase.anonymizeDamageFiles(damageFileKeys)

        val damageFilesFromFinder = damageFileFinder.findAllByDamageKeys(damageFileKeys)
        assertThat(damageFilesFromFinder).allSatisfy {
            assertThat(it.employeeToBeCharged).isEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
        }
    }

    @Test
    fun `should not fail if a single damage file is not found in repository`() {
        val aDamageFile = DamageFileNewOrUpdateBuilder().closed().build()
        val anotherDamageFile = DamageFileBuilder().build()
        val closedDamageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(aDamageFile)

        assertThatNoException().isThrownBy {
            anonymizeDamageFileUseCase.anonymizeDamageFiles(listOf(closedDamageFile.key, anotherDamageFile.key))
        }

        val damageFileFromFinder = damageFileFinder.getDamageFile(closedDamageFile.key)
        assertThat(damageFileFromFinder.employeeToBeCharged).isEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
    }

    @Test
    @Rollback(false)
    fun `should not rollback the entire transaction if a single damage file throws DamageFileNotClosedException`() {
        val aDamageFile = DamageFileNewOrUpdateBuilder().closed().build()
        val anotherDamageFile = DamageFileNewOrUpdateBuilder().build()
        val closedDamageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(aDamageFile)
        val openDamageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(anotherDamageFile)
        TestTransaction.end()

        TestTransaction.start()
        anonymizeDamageFileUseCase.anonymizeDamageFiles(listOf(closedDamageFile.key, openDamageFile.key))

        assertThatNoException().isThrownBy { TestTransaction.end() }

        val closedDamageFileFromFinder = damageFileFinder.getDamageFile(closedDamageFile.key)
        val openDamageFileFromFinder = damageFileFinder.getDamageFile(openDamageFile.key)
        assertThat(closedDamageFileFromFinder.employeeToBeCharged).isEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
        assertThat(openDamageFileFromFinder.employeeToBeCharged).isNotEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)

        // clean up db explicitly as the TestTransaction rollback is disabled
        cleanupDamageFiles()
    }

    @Test
    @Rollback(false)
    fun `should not rollback the entire transaction if a single damage file throws LegalHoldInPlaceException`() {
        val listOfDamageFiles = (1..2).map { DamageFileNewOrUpdateBuilder().closed().build() }
        val damageFiles = listOfDamageFiles.map { createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it) }
        damageFiles.last().addLegalHold(LegalHoldKey("anyKey"))
        TestTransaction.end()

        TestTransaction.start()
        anonymizeDamageFileUseCase.anonymizeDamageFiles(damageFiles.map { it.key })

        assertThatNoException().isThrownBy { TestTransaction.end() }

        val damageFileWithoutLegalHold = damageFileFinder.getDamageFile(damageFiles.first().key)
        val damageFileWithLegalHold = damageFileFinder.getDamageFile(damageFiles.last().key)
        assertThat(damageFileWithoutLegalHold.employeeToBeCharged).isEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
        assertThat(damageFileWithLegalHold.employeeToBeCharged).isNotEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)

        // clean up db explicitly as the TestTransaction rollback is disabled
        cleanupDamageFiles()
    }
}
