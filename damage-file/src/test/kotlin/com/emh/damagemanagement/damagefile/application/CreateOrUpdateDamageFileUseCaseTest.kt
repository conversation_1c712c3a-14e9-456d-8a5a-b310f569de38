/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.TestDamageFileRepository
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.DamageFileKeyProvider
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageFileState
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateService
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateServiceTest.Companion.damageFileNewRequirements
import com.emh.damagemanagement.damagefile.domain.service.DamageFileNewOrUpdate
import com.emh.damagemanagement.damagefile.validDamageFileKey
import jakarta.persistence.EntityManager
import java.math.BigDecimal
import java.time.temporal.ChronoUnit
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.within
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier

@IntegrationTest
class CreateOrUpdateDamageFileUseCaseTest {
    @Autowired private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase

    @Autowired private lateinit var damageFileFinder: DamageFileFinder

    @Autowired private lateinit var entityManager: EntityManager

    @Autowired @Qualifier("damage-file-cleanup") private lateinit var cleanupDamageFiles: () -> Unit

    @Autowired private lateinit var testDamageFileRepository: TestDamageFileRepository

    /**
     * As we now have some async functions with their own transactions, global rollback won`t work anymore. So we are
     * doing it manually.
     */
    @AfterEach
    fun cleanup() {
        cleanupDamageFiles()
    }

    @Test
    fun `should create new damage file`() {

        val damageFileNew = DamageFileNewOrUpdateBuilder().build()

        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(damageFileFromFinder.id).isEqualTo(damageFile.id)
    }

    @Test
    fun `should update new damage file if one already exist by external reference`() {

        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        val damageFileUpdate = DamageFileNewOrUpdateBuilder().externalReference(damageFile.externalReference!!).build()

        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileUpdate)

        val updatedDamageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(updatedDamageFileFromFinder.deductible).isEqualTo(damageFileUpdate.deductible)
        assertThat(updatedDamageFileFromFinder.claimTowardsEmployee).isEqualTo(damageFileUpdate.claimTowardsEmployee)
        assertThat(updatedDamageFileFromFinder.insuranceCoverage).isEqualTo(damageFileUpdate.insuranceCoverage)
    }

    @Test
    fun `should create damage file batches asynchronously`() {

        val damageFilesNew = DamageFileNewOrUpdateBuilder.buildMultiple(50)

        createOrUpdateDamageFileUseCase.createOrUpdateDamageFilesAsync(damageFilesNew)

        await atMost
            Durations.FIVE_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val damageFileCount = testDamageFileRepository.count().toInt()
                assertThat(damageFileCount).isEqualTo(damageFilesNew.size)
            }
    }

    @Test
    fun `should create and update damage file batches asynchronously`() {
        val damageFilesNew = DamageFileNewOrUpdateBuilder.buildMultiple(20)
        createOrUpdateDamageFileUseCase.createOrUpdateDamageFilesAsync(damageFilesNew)
        // block async creation here
        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val damageFileCount = testDamageFileRepository.count().toInt()
                assertThat(damageFileCount).isEqualTo(damageFilesNew.size)
            }
        val damageFilesNewer = DamageFileNewOrUpdateBuilder.buildMultiple(20)
        val damageFilesUpdated =
            damageFilesNew
                .map { DamageFileNewOrUpdateBuilder().externalReference(it.externalReference!!).build() }
                .toSet()

        createOrUpdateDamageFileUseCase.createOrUpdateDamageFilesAsync(damageFilesNewer + damageFilesUpdated)

        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val allDamageFiles = testDamageFileRepository.findAll()
                assertThat(allDamageFiles.size).isEqualTo(damageFilesNew.size + damageFilesNewer.size)
                assertThat(damageFilesUpdated).allSatisfy {
                    val updatedDamageFile =
                        allDamageFiles.single { damageFile -> it.externalReference == damageFile.externalReference }
                    assertThat(updatedDamageFile.claimTowardsEmployee!!)
                        .usingComparator(MonetaryAmount::compareTo)
                        .isEqualTo(it.claimTowardsEmployee!!)
                    assertThat(updatedDamageFile.deductible!!)
                        .usingComparator(MonetaryAmount::compareTo)
                        .isEqualTo(it.deductible!!)
                    assertThat(updatedDamageFile.insuranceCoverage)
                        .usingComparator(InsuranceCoverage::compareTo)
                        .isEqualTo(it.insuranceCoverage)
                }
            }
    }

    @Test
    fun `should create new already closed damage file`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().closed().build()
        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(damageFileFromFinder)
            .satisfies(damageFileNewRequirements(damageFileNew = damageFileNew, isClosed = true))
        assertThat(damageFileFromFinder.key).isEqualTo(damageFile.key)
        assertThat(damageFileFromFinder.state).isEqualTo(DamageFileState.CLOSED)
    }

    @Test
    fun `should update and close damage file`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()

        val damageFileUpdate =
            DamageFileNewOrUpdateBuilder()
                .externalReference(damageFileNew.externalReference!!)
                .deductible(MonetaryAmount.of(23.00))
                .claimTowardsEmployee(MonetaryAmount.of(42.5))
                .insuranceCoverage(InsuranceCoverage.PARTIAL)
                .damageResponsibility(DamageResponsibility.RESPONSIBILITY_IS_NOT_CLEAR)
                .closed()
                .build()
        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileUpdate)
        entityManager.flush()

        val updatedDamageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)

        assertThat(updatedDamageFileFromFinder.deductible).isEqualTo(damageFileUpdate.deductible)
        assertThat(updatedDamageFileFromFinder.claimTowardsEmployee).isEqualTo(damageFileUpdate.claimTowardsEmployee)
        assertThat(updatedDamageFileFromFinder.insuranceCoverage).isEqualTo(damageFileUpdate.insuranceCoverage)
        assertThat(updatedDamageFileFromFinder.damageResponsibility).isEqualTo(damageFileUpdate.damageResponsibility)
        assertThat(updatedDamageFileFromFinder.isClosed).isTrue()
        assertThat(updatedDamageFileFromFinder.closedDate).isNotNull()
    }

    @Test
    fun `should reopen and update a closed damage file`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().closed().build()
        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()

        val damageFileUpdate =
            DamageFileNewOrUpdateBuilder()
                .externalReference(damageFileNew.externalReference!!)
                .deductible(MonetaryAmount.of(23.00))
                .claimTowardsEmployee(MonetaryAmount.of(42.5))
                .insuranceCoverage(InsuranceCoverage.FULL)
                .build()

        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileUpdate)
        entityManager.flush()

        val updatedDamageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(updatedDamageFileFromFinder.deductible).isEqualTo(damageFileUpdate.deductible)
        assertThat(updatedDamageFileFromFinder.claimTowardsEmployee).isEqualTo(damageFileUpdate.claimTowardsEmployee)
        assertThat(updatedDamageFileFromFinder.insuranceCoverage).isEqualTo(damageFileUpdate.insuranceCoverage)
        assertThat(updatedDamageFileFromFinder.damageResponsibility).isEqualTo(damageFileUpdate.damageResponsibility)
        assertThat(updatedDamageFileFromFinder.isClosed).isFalse()
    }

    @Test
    fun `should reopen,update and re-close a closed damage file`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().closed().build()
        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        val originalClosedDate = damageFileFromFinder.closedDate
        val damageFileUpdate =
            DamageFileNewOrUpdateBuilder()
                .externalReference(damageFileNew.externalReference!!)
                .deductible(MonetaryAmount.of(23.00))
                .claimTowardsEmployee(MonetaryAmount.of(42.5))
                .insuranceCoverage(InsuranceCoverage.THIRD_PARTY)
                .closed()
                .build()

        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileUpdate)
        entityManager.flush()

        val updatedDamageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(updatedDamageFileFromFinder.deductible).isEqualTo(damageFileUpdate.deductible)
        assertThat(updatedDamageFileFromFinder.claimTowardsEmployee).isEqualTo(damageFileUpdate.claimTowardsEmployee)
        assertThat(updatedDamageFileFromFinder.insuranceCoverage).isEqualTo(damageFileUpdate.insuranceCoverage)
        assertThat(updatedDamageFileFromFinder.damageResponsibility).isEqualTo(damageFileUpdate.damageResponsibility)
        assertThat(updatedDamageFileFromFinder.isClosed).isTrue()
        assertThat(updatedDamageFileFromFinder.closedDate).isEqualTo(originalClosedDate)
    }

    @Test
    fun `should not reopen closed damage file if no changes are detected`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().closed().build()
        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        val originalDeductible = damageFileFromFinder.deductible
        val originalClaimTowardsEmployee = damageFileFromFinder.claimTowardsEmployee
        val originalInsuranceCoverage = damageFileFromFinder.insuranceCoverage
        val originalDamageResponsibility = damageFileFromFinder.damageResponsibility
        val originalClosedDate = damageFileFromFinder.closedDate
        val damageFileUpdate =
            DamageFileNewOrUpdateBuilder()
                .externalReference(damageFileNew.externalReference!!)
                .deductible(originalDeductible)
                .claimTowardsEmployee(originalClaimTowardsEmployee)
                .insuranceCoverage(originalInsuranceCoverage)
                .damageResponsibility(originalDamageResponsibility)
                .closed()
                .build()

        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileUpdate)
        entityManager.flush()

        val updatedDamageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(updatedDamageFileFromFinder.deductible).isEqualTo(originalDeductible)
        assertThat(updatedDamageFileFromFinder.claimTowardsEmployee).isEqualTo(originalClaimTowardsEmployee)
        assertThat(updatedDamageFileFromFinder.insuranceCoverage).isEqualTo(originalInsuranceCoverage)
        assertThat(updatedDamageFileFromFinder.damageResponsibility).isEqualTo(originalDamageResponsibility)
        assertThat(updatedDamageFileFromFinder.isClosed).isTrue()
        assertThat(updatedDamageFileFromFinder.closedDate).isCloseTo(originalClosedDate, within(1, ChronoUnit.MILLIS))
    }

    @Test
    fun `should not reopen closed damage file due to precision difference`() {
        val damageFileNew =
            DamageFileNewOrUpdateBuilder()
                .deductible(MonetaryAmount.of(BigDecimal.valueOf(0).setScale(9)))
                .claimTowardsEmployee(MonetaryAmount.of(BigDecimal.valueOf(200).setScale(9)))
                .closed()
                .build()
        val damageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        val originalDeductible = damageFileFromFinder.deductible
        val originalClaimTowardsEmployee = damageFileFromFinder.claimTowardsEmployee
        val originalInsuranceCoverage = damageFileFromFinder.insuranceCoverage
        val originalDamageResponsibility = damageFileFromFinder.damageResponsibility
        val originalClosedDate = damageFileFromFinder.closedDate
        val damageFileUpdate =
            DamageFileNewOrUpdateBuilder()
                .externalReference(damageFileNew.externalReference!!)
                .deductible(MonetaryAmount.of(BigDecimal.valueOf(0)))
                .claimTowardsEmployee(MonetaryAmount.of(BigDecimal.valueOf(200)))
                .insuranceCoverage(originalInsuranceCoverage)
                .damageResponsibility(originalDamageResponsibility)
                .closed()
                .build()

        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileUpdate)
        entityManager.flush()

        val updatedDamageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(updatedDamageFileFromFinder.deductible).isEqualTo(originalDeductible)
        assertThat(updatedDamageFileFromFinder.claimTowardsEmployee).isEqualTo(originalClaimTowardsEmployee)
        assertThat(updatedDamageFileFromFinder.insuranceCoverage).isEqualTo(originalInsuranceCoverage)
        assertThat(updatedDamageFileFromFinder.isClosed).isTrue()
        assertThat(updatedDamageFileFromFinder.closedDate).isEqualTo(originalClosedDate)
    }
}

class DamageFileApplicationServiceTest {
    @Test
    fun `should not fail if a single element fails during batch update`() {
        val damageFileCreateService = mock<DamageFileCreateService>()
        val damageFileKeyProvider =
            mock<DamageFileKeyProvider>().apply { whenever(provideNewDamageFileKey()).thenReturn(validDamageFileKey) }
        val damageFileApplicationService =
            DamageFileApplicationService(
                damageFileCreateService = damageFileCreateService,
                damageFileFinder = mock(),
                damageFileUpdateService = mock(),
                damageFileCloseService = mock(),
                damageFileDeleteService = mock(),
                damageFileAnonymizeService = mock(),
                damageFileReopenService = mock(),
                damageFileKeyProvider = damageFileKeyProvider,
            )
        val damageFilesNew = DamageFileNewOrUpdateBuilder.buildMultiple(20)
        whenever(damageFileCreateService.createDamageFile(any<DamageFileNewOrUpdate>(), any<DamageFileKey>()))
            .thenReturn(DamageFileBuilder.buildSingle())
            .thenThrow(ConcurrentModificationException("you shall not persist."))
            .thenReturn(DamageFileBuilder.buildSingle())

        damageFileApplicationService.createOrUpdateDamageFilesAsync(damageFilesNew)

        verify(damageFileCreateService, times(damageFilesNew.size)).createDamageFile(any(), any())
    }
}
