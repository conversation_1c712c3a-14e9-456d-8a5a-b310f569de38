/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileNotFoundException
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCloseService
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateService
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import com.emh.damagemanagement.shared.createRandomString
import jakarta.persistence.EntityManager
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.ReflectionUtils

@IntegrationTest
class DamageFileFinderTest {
    @Autowired private lateinit var entityManager: EntityManager

    @Autowired lateinit var damageFileFinder: DamageFileFinder

    @Autowired lateinit var damageFileCreateService: DamageFileCreateService
    @Autowired lateinit var damageFileCloseService: DamageFileCloseService

    @Test
    fun `should return true if damage file exists`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()

        val exists = damageFileFinder.exists(damageFile.key)

        assertThat(exists).isTrue()
    }

    @Test
    fun `should return false if damage file does not exist`() {
        val exists = damageFileFinder.exists(validDamageFileKey)

        assertThat(exists).isFalse()
    }

    @Test
    fun `should get damage file by key`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)

        assertThat(damageFileFromFinder)
            .usingRecursiveComparison()
            .ignoringFieldsMatchingRegexes("^(_|mutable)\\S*")
            .ignoringFields(*TestJpaAuditingConfiguration.AUDIT_FIELDS)
            .isEqualTo(damageFile)
    }

    @Test
    fun `should throw DamageFileNotFoundException if damage file does not exist`() {

        assertThatExceptionOfType(DamageFileNotFoundException::class.java).isThrownBy {
            damageFileFinder.getDamageFile(validDamageFileKey)
        }
    }

    @Test
    fun `should find damage file by external reference`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.findByExternalReference(damageFile.externalReference!!)!!

        assertThat(damageFileFromFinder.id).isEqualTo(damageFile.id)
    }

    @Test
    fun `should return null if damage file could not be found by external reference`() {
        val damageFileFromFinder = damageFileFinder.findByExternalReference(createRandomString(13))

        assertThat(damageFileFromFinder).isNull()
    }

    @Test
    fun `should find damage file by vehicle vin`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()

        val damageFileFromFinder = requireNotNull(damageFileFinder.findByVehicleVin(damageFile.vehicle.vin))

        assertThat(damageFileFromFinder.id).isEqualTo(damageFile.id)
    }

    @Test
    fun `should find damage file by vehicle vguid`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()

        val damageFileFromFinder = requireNotNull(damageFileFinder.findByVehicleVGuid(damageFile.vehicle.vGuid))

        assertThat(damageFileFromFinder.id).isEqualTo(damageFile.id)
    }

    @Test
    fun `should find all damage file by deductible update timestamp`() {
        val damageFilesNewWithoutUpdateTimestamp =
            (1..10).map { DamageFileNewOrUpdateBuilder().claimTowardsEmployee(null).deductible(null).build() }
        val damageFilesNew = DamageFileNewOrUpdateBuilder.buildMultiple(10)
        (damageFilesNew + damageFilesNewWithoutUpdateTimestamp).forEach {
            damageFileCreateService.createDamageFile(it, validDamageFileKey)
        }
        entityManager.flush()

        val referenceTimestamp = OffsetDateTime.of(2024, 2, 1, 0, 0, 0, 0, ZoneOffset.UTC)

        val deductibleUpdatedField =
            requireNotNull(ReflectionUtils.findField(DamageFile::class.java, "deductibleUpdated"))
        ReflectionUtils.makeAccessible(deductibleUpdatedField)
        val damageFilesToBeUpdated =
            damageFilesNew
                .filter { Random.nextBoolean() }
                .map { damageFileFinder.findByExternalReference(it.externalReference!!)!! }
                .onEachIndexed { index, damageFile ->
                    damageFile.update(
                        deductible =
                            MonetaryAmount.of(
                                BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP)
                            ),
                        claimTowardsEmployee = null,
                        insuranceCoverage = InsuranceCoverage.NOT_SPECIFIED,
                        damageResponsibility = DamageResponsibility.UNKNOWN,
                        damageDate = damageFile.damageDate,
                        employeeToBeCharged = damageFile.employeeToBeCharged,
                        isExternal = false,
                    )
                    // override deductibleOrClaimTowardsEmployeeUpdated for testability
                    ReflectionUtils.setField(
                        deductibleUpdatedField,
                        damageFile,
                        referenceTimestamp.plusDays(index.toLong()),
                    )
                }
        entityManager.flush()

        val updatedDamageFilesFromFinder =
            damageFileFinder.findAllNonExternalDamageFilesWithDeductibleUpdateBetween(
                from = referenceTimestamp,
                to = referenceTimestamp.plusDays(damageFilesNew.size.toLong()),
            )

        assertThat(updatedDamageFilesFromFinder.map { it.key.value })
            .containsExactlyElementsOf(damageFilesToBeUpdated.sortedBy { it.deductibleUpdated }.map { it.key.value })
    }

    @Test
    fun `should find all closed damage files before specified date`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                damageFileCreateService.createDamageFile(it, validDamageFileKey)
            }
        damageFiles.takeLast(5).forEach { damageFileCloseService.closeDamageFile(it) }
        entityManager.flush()

        val closedFilesBeforeNow = damageFileFinder.findClosedDamageFilesWithClosedDateBefore(OffsetDateTime.now())

        assertThat(closedFilesBeforeNow.size).isEqualTo(5)
    }

    @Test
    fun `should not find any closed damage files`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                damageFileCreateService.createDamageFile(it, validDamageFileKey)
            }
        damageFiles.takeLast(5).forEach { damageFileCloseService.closeDamageFile(it) }
        entityManager.flush()

        val closedFilesBeforeNow =
            damageFileFinder.findClosedDamageFilesWithClosedDateBefore(OffsetDateTime.now().minusYears(1))

        assertThat(closedFilesBeforeNow.isEmpty())
    }

    @Test
    fun `should find open damage file without internal order number`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(5).map {
                damageFileCreateService.createDamageFile(it, validDamageFileKey)
            }
        val unchanged = damageFiles.first()
        damageFiles.takeLast(4).forEach { damageFileCloseService.closeDamageFile(it) }

        val openAndInternalOrderNumberIsNull = damageFileFinder.findOpenDamageFilesWithoutInternalOrderNumber()

        assertThat(openAndInternalOrderNumberIsNull.size).isEqualTo(1)
        assertThat(openAndInternalOrderNumberIsNull.first().key).isEqualTo(unchanged.key)
    }

    @Test
    fun `should not find any open damage files without internalOrderNumber`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(5).map {
                damageFileCreateService.createDamageFile(it, validDamageFileKey)
            }
        damageFiles.forEach { damageFileCloseService.closeDamageFile(it) }
        entityManager.flush()

        val openAndInternalOrderNumberIsNull = damageFileFinder.findOpenDamageFilesWithoutInternalOrderNumber()

        assertThat(openAndInternalOrderNumberIsNull).isEmpty()
    }

    @Test
    fun `should find damage files by keys`() {
        val damageFiles =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                damageFileCreateService.createDamageFile(it, validDamageFileKey)
            }
        entityManager.flush()

        val damageFilesInRepo = damageFileFinder.findAllByDamageKeys(damageFiles.map { it.key })

        assertThat(damageFilesInRepo).extracting("key").containsExactlyInAnyOrderElementsOf(damageFiles.map { it.key })
    }
}
