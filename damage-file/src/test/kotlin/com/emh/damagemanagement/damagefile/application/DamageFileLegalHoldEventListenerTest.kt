/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldCreatedEvent
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.LegalHoldReleasedEvent
import jakarta.persistence.EntityManager
import java.time.OffsetDateTime
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher

@IntegrationTest
class DamageFileLegalHoldEventListenerTest {

    @Autowired private lateinit var applicationEventPublisher: ApplicationEventPublisher

    @Autowired private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase

    @Autowired private lateinit var entityManager: EntityManager

    @Autowired private lateinit var damageFileLegalHoldFinder: DamageFileLegalHoldFinder

    @Test
    fun `should handle LegalHoldCreatedEvent and add legal hold to damage files`() {
        val damageFilesWithExpectedLegalHold =
            (0..1).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(DamageFileNewOrUpdateBuilder().build())
            }
        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(DamageFileNewOrUpdateBuilder().build())
        entityManager.flush()

        val legalHoldKey = LegalHoldKey("anyKey")
        val legalHoldCreatedEvent =
            LegalHoldCreatedEvent(
                legalHoldKey = legalHoldKey,
                domainEntityReferences =
                    damageFilesWithExpectedLegalHold.map { DomainEntityReference(it.key.value) }.toSet(),
                occurredOn = OffsetDateTime.now(),
            )

        applicationEventPublisher.publishEvent(legalHoldCreatedEvent)
        entityManager.flush()

        val damageFilesWithLegalHold = damageFileLegalHoldFinder.findDomainEntitiesFor(legalHoldKey)
        assertThat(damageFilesWithLegalHold)
            .extracting("key")
            .containsExactlyInAnyOrderElementsOf(damageFilesWithExpectedLegalHold.map { it.key })
        assertThat(damageFilesWithLegalHold).allSatisfy { assertThat(it.legalHolds).containsExactly(legalHoldKey) }
    }

    @Test
    fun `should handle LegalHoldReleasedEvent release legal hold from damage files`() {
        val damageFileWithOneLegalHold =
            createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(DamageFileNewOrUpdateBuilder().build())
        val damageFileWithTwoLegalHolds =
            createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(DamageFileNewOrUpdateBuilder().build())
        val damageFileWithoutLegalHold =
            createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(DamageFileNewOrUpdateBuilder().build())
        entityManager.flush()
        val legalHoldKey = LegalHoldKey("anyKey")
        val anotherLegalHoldKey = LegalHoldKey("anotherKey")
        val legalHoldCreatedEvent =
            LegalHoldCreatedEvent(
                legalHoldKey = legalHoldKey,
                domainEntityReferences =
                    setOf(damageFileWithOneLegalHold, damageFileWithTwoLegalHolds)
                        .map { DomainEntityReference(it.key.value) }
                        .toSet(),
                occurredOn = OffsetDateTime.now(),
            )
        val anotherLegalHoldCreatedEvent =
            LegalHoldCreatedEvent(
                legalHoldKey = anotherLegalHoldKey,
                domainEntityReferences =
                    setOf(damageFileWithTwoLegalHolds).map { DomainEntityReference(it.key.value) }.toSet(),
                occurredOn = OffsetDateTime.now(),
            )
        applicationEventPublisher.publishEvent(legalHoldCreatedEvent)
        entityManager.flush()
        applicationEventPublisher.publishEvent(anotherLegalHoldCreatedEvent)
        entityManager.flush()

        val legalHoldReleasedEvent =
            LegalHoldReleasedEvent(
                legalHoldKey = legalHoldKey,
                domainEntityReferences = emptySet(),
                occurredOn = OffsetDateTime.now(),
            )
        applicationEventPublisher.publishEvent(legalHoldReleasedEvent)
        entityManager.flush()

        val damageFilesWithLegalHold = damageFileLegalHoldFinder.findDomainEntitiesFor(anotherLegalHoldKey)
        val damageFilesWithoutLegalHold =
            damageFileLegalHoldFinder.findDomainEntitiesFor(
                setOf(damageFileWithOneLegalHold, damageFileWithoutLegalHold).map {
                    DomainEntityReference(it.key.value)
                }
            )
        assertThat(damageFilesWithLegalHold)
            .extracting("key")
            .containsExactlyInAnyOrder(damageFileWithTwoLegalHolds.key)
        assertThat(damageFilesWithLegalHold).allSatisfy {
            assertThat(it.legalHolds).containsExactly(anotherLegalHoldKey)
        }
        assertThat(damageFilesWithoutLegalHold)
            .extracting("key")
            .containsExactlyInAnyOrderElementsOf(
                setOf(damageFileWithOneLegalHold, damageFileWithoutLegalHold).map { damageFile -> damageFile.key }
            )
        assertThat(damageFilesWithoutLegalHold).allSatisfy { assertThat(it.legalHolds).isEmpty() }
    }
}
