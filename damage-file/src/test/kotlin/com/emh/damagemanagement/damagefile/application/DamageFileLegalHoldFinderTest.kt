/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateService
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileLegalHoldFinderTest {
    @Autowired private lateinit var entityManager: EntityManager

    @Autowired lateinit var damageFileLegalHoldFinder: DamageFileLegalHoldFinder

    @Autowired lateinit var damageFileCreateService: DamageFileCreateService

    @Test
    fun `should find all damage files for domain entity references`() {
        val keyMatches =
            (0..10)
                .map { DamageFileNewOrUpdateBuilder.buildSingle() }
                .map {
                    val damageFile = damageFileCreateService.createDamageFile(it, validDamageFileKey)
                    damageFile.key
                }
        (0..10)
            .map { DamageFileNewOrUpdateBuilder.buildSingle() }
            .forEach { damageFileCreateService.createDamageFile(it, validDamageFileKey) }
        entityManager.flush()

        val damageFilesFromFinder =
            damageFileLegalHoldFinder.findDomainEntitiesFor(keyMatches.map { DomainEntityReference(it.value) })

        assertThat(damageFilesFromFinder).extracting("key").containsExactlyInAnyOrderElementsOf(keyMatches)
    }

    @Test
    fun `should find all damage files for legal hold key`() {
        val legalHoldKey1 = LegalHoldKey("key1")
        val legalHoldKey2 = LegalHoldKey("key2")
        val keyMatches =
            (0..10)
                .map { DamageFileNewOrUpdateBuilder.buildSingle() }
                .map {
                    val damageFile = damageFileCreateService.createDamageFile(it, validDamageFileKey)
                    damageFile.addLegalHold(legalHoldKey1)
                    damageFile.key
                }
        (0..10)
            .map { DamageFileNewOrUpdateBuilder.buildSingle() }
            .forEach {
                val damageFile = damageFileCreateService.createDamageFile(it, validDamageFileKey)
                damageFile.addLegalHold(legalHoldKey2)
            }
        entityManager.flush()

        val damageFilesFromFinder = damageFileLegalHoldFinder.findDomainEntitiesFor(legalHoldKey1)

        assertThat(damageFilesFromFinder).extracting("key").containsExactlyInAnyOrderElementsOf(keyMatches)
    }
}
