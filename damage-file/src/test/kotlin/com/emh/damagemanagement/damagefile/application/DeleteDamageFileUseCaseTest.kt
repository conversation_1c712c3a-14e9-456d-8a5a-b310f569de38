/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.application

import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.TestDamageFileRepository
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.damagefile.application.port.DeleteDamageFileUseCase
import com.emh.damagemanagement.damagefile.domain.DamageFileNotFoundException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.assertj.core.api.Assertions.assertThatThrownBy
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.annotation.Rollback
import org.springframework.test.context.transaction.TestTransaction

@IntegrationTest
class DeleteDamageFileUseCaseTest {
    @Autowired private lateinit var deleteDamageFileUseCase: DeleteDamageFileUseCase
    @Autowired private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase
    @Autowired private lateinit var damageFileFinder: DamageFileFinder
    @Autowired private lateinit var testDamageFileRepository: TestDamageFileRepository
    @Autowired @Qualifier("damage-file-cleanup") private lateinit var cleanupDamageFiles: () -> Unit

    @Test
    fun `should delete multiple damage files synchronously`() {
        val listOfDamageFiles = (1..5).map { DamageFileNewOrUpdateBuilder().closed().build() }
        val damageFiles = listOfDamageFiles.map { createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it) }

        val damageFileCountBeforeDelete = testDamageFileRepository.count()

        deleteDamageFileUseCase.deleteDamageFiles(damageFiles.map { it.key })
        assertThat(damageFileCountBeforeDelete).isEqualTo(5)
        assertThat(testDamageFileRepository.count().toInt()).isEqualTo(0)
    }

    @Test
    fun `should not fail if a single damage file is not found in repository`() {
        val aDamageFile = DamageFileNewOrUpdateBuilder().closed().build()
        val anotherDamageFile = DamageFileBuilder().build()
        val closedDamageFile = createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(aDamageFile)

        val damageFileCountBeforeDelete = testDamageFileRepository.count()

        assertThatNoException().isThrownBy {
            deleteDamageFileUseCase.deleteDamageFiles(listOf(closedDamageFile.key, anotherDamageFile.key))
        }
        assertThat(damageFileCountBeforeDelete).isEqualTo(1)
        assertThat(testDamageFileRepository.count().toInt()).isEqualTo(0)
    }

    @Test
    @Rollback(false)
    fun `should not rollback the entire transaction if some damage files throw DamageFileNotClosedException`() {
        val listOfOpenDamageFiles = (1..3).map { DamageFileNewOrUpdateBuilder().build() }
        val listOfClosedDamageFiles = (1..3).map { DamageFileNewOrUpdateBuilder().closed().build() }
        val damageFiles =
            (listOfOpenDamageFiles + listOfClosedDamageFiles).map {
                createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it)
            }
        TestTransaction.end()

        val damageFileCountBeforeDelete = testDamageFileRepository.count().toInt()

        TestTransaction.start()
        deleteDamageFileUseCase.deleteDamageFiles(damageFiles.map { it.key })
        assertThatNoException().isThrownBy { TestTransaction.end() }

        assertThat(damageFileCountBeforeDelete).isEqualTo((listOfOpenDamageFiles + listOfClosedDamageFiles).size)
        assertThat(testDamageFileRepository.count().toInt()).isEqualTo(listOfOpenDamageFiles.size)

        // clean up db explicitly as the TestTransaction rollback is disabled
        cleanupDamageFiles()
    }

    @Test
    @Rollback(false)
    fun `should not rollback the entire transaction if a single damage file throws LegalHoldInPlaceException`() {
        val listOfDamageFiles = (1..2).map { DamageFileNewOrUpdateBuilder().closed().build() }
        val damageFiles = listOfDamageFiles.map { createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(it) }
        val damageFileWithLegalHold = damageFiles.last()
        damageFileWithLegalHold.addLegalHold(LegalHoldKey("anyKey"))
        TestTransaction.end()

        val damageFileCountBeforeDelete = testDamageFileRepository.count().toInt()

        TestTransaction.start()
        deleteDamageFileUseCase.deleteDamageFiles(damageFiles.map { it.key })
        assertThatNoException().isThrownBy { TestTransaction.end() }

        assertThat(damageFileCountBeforeDelete).isEqualTo(damageFiles.size)
        assertThat(testDamageFileRepository.count().toInt()).isEqualTo(1)
        assertThatThrownBy { damageFileFinder.getDamageFile(damageFiles.first().key) }
            .isInstanceOf(DamageFileNotFoundException::class.java)
        assertThat(damageFileFinder.getDamageFile(damageFileWithLegalHold.key)).isNotNull

        // clean up db explicitly as the TestTransaction rollback is disabled
        cleanupDamageFiles()
    }
}
