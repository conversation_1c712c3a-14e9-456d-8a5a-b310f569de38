/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import com.emh.damagemanagement.damagefile.ANONYMIZED_EMPLOYEE_NUMBER
import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.legal.hold.domain.LegalHoldInPlaceException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.shared.createRandomString
import java.lang.Thread.sleep
import java.math.BigDecimal
import java.math.RoundingMode
import java.time.Duration
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.stream.Stream
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.util.ReflectionUtils

class DamageFileTest {

    @Test
    fun `newly create damage files should have state OPEN`() {
        val damageFile =
            DamageFile(
                damageReport = DamageReport(),
                vehicle =
                    Vehicle(
                        vin = createRandomString(Random.nextInt(4, 23)),
                        vGuid = createRandomString(Random.nextInt(4, 23)),
                        licensePlate = createRandomString(Random.nextInt(4, 23)),
                        leasingArt = createRandomString(5),
                        costCenter = createRandomString(9),
                        usingCostCenter = createRandomString(9),
                        fleetId = createRandomString(Random.nextInt(4, 23)),
                        externalReference = createRandomString(36),
                    ),
                employeeToBeCharged = EmployeeNumber(createRandomString(Random.nextInt(4, 23))),
                deductible = MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble())),
                claimTowardsEmployee = MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble())),
                internalOrderNumber = null,
                externalReference = createRandomString(36),
                key = validDamageFileKey,
                insuranceCoverage = InsuranceCoverage.entries.random(),
                damageDate = OffsetDateTime.now(),
                isExternal = false,
            )

        assertThat(damageFile.state).isEqualTo(DamageFileState.OPEN)
    }

    @Test
    fun `newly create damage files without deductible and claim towards employee should have update timestamp null`() {
        val damageFile = DamageFileBuilder().claimTowardsEmployee(null).deductible(null).build()

        assertThat(damageFile.deductibleUpdated).isNull()
    }

    @Test
    fun `newly create damage files with deductible or claim towards employee should have update timestamp set`() {
        val damageFile =
            DamageFileBuilder()
                .claimTowardsEmployee(null)
                .deductible(
                    MonetaryAmount.of(BigDecimal.valueOf(Random.nextDouble()).setScale(2, RoundingMode.HALF_UP))
                )
                .build()

        assertThat(damageFile.deductibleUpdated).isNotNull()
    }

    @ParameterizedTest
    @MethodSource("damageFileProvider")
    fun `should update deductible,claimTowardsEmployee and insuranceCoverage on an open damage file`(
        damageFile: DamageFile
    ) {
        `should update damage file`(damageFile)
    }

    @ParameterizedTest
    @MethodSource("damageFileProvider")
    fun `should update deductible and claimTowardsEmployee on an reopenend damage file`(damageFile: DamageFile) {
        damageFile.close()
        damageFile.reopen()
        `should update damage file`(damageFile)
    }

    private fun `should update damage file`(damageFile: DamageFile) {
        val deductible = MonetaryAmount.of(value = BigDecimal(10))
        val claimTowardsEmployee = MonetaryAmount.of(value = BigDecimal(10))

        damageFile.update(
            deductible = deductible,
            claimTowardsEmployee = claimTowardsEmployee,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            employeeToBeCharged = EmployeeNumber("00123444"),
            damageDate = OffsetDateTime.of(2024, 2, 5, 5, 0, 0, 0, ZoneOffset.UTC),
            isExternal = false,
        )

        assertThat(damageFile.deductible).isEqualTo(deductible)
        assertThat(damageFile.claimTowardsEmployee).isEqualTo(claimTowardsEmployee)
        assertThat(damageFile.insuranceCoverage).isEqualTo(InsuranceCoverage.FULL)
        assertThat(damageFile.deductibleUpdated).isNotNull()
        assertThat(damageFile.damageResponsibility).isEqualTo(DamageResponsibility.UNKNOWN)
        assertThat(damageFile.employeeToBeCharged).isEqualTo(EmployeeNumber("00123444"))
        assertThat(damageFile.damageDate).isEqualTo(OffsetDateTime.of(2024, 2, 5, 5, 0, 0, 0, ZoneOffset.UTC))
        assertThat(damageFile.isExternal).isFalse()
    }

    @Test
    fun `should update deductible and deductibleUpdated if original value is null and provided value is not null`() {
        val damageFile = DamageFileBuilder().deductible(null).build()
        val originalDeductible = damageFile.deductible
        val originalUpdateTimestamp = damageFile.deductibleUpdated
        val newDeductible = MonetaryAmount(value = BigDecimal(10))

        damageFile.update(
            deductible = newDeductible,
            claimTowardsEmployee = null,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(originalDeductible).isNull()
        assertThat(damageFile.deductible).isEqualTo(newDeductible)
        assertThat(damageFile.deductibleUpdated).isNotEqualTo(originalUpdateTimestamp)
        assertThat(originalUpdateTimestamp).isNull()
    }

    @Test
    fun `should update claimTowardsEmployee if original value is null and provided value is not null`() {
        val amount = MonetaryAmount(value = BigDecimal(10))
        val damageFile = DamageFileBuilder().deductible(amount).claimTowardsEmployee(null).build()
        val originalClaimTowardsEmployee = damageFile.claimTowardsEmployee
        val originalUpdateTimestamp = damageFile.deductibleUpdated

        damageFile.update(
            deductible = amount,
            claimTowardsEmployee = amount,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(originalClaimTowardsEmployee).isNull()
        assertThat(damageFile.claimTowardsEmployee).isEqualTo(amount)
        assertThat(damageFile.deductibleUpdated).isEqualTo(originalUpdateTimestamp)
    }

    @Test
    fun `should reset internalOrderNumber if damageResponsibility driverAtFault changes`() {
        val damageFile =
            DamageFileBuilder()
                .internalOrderNumber(InternalOrderNumber("some number"))
                .damageResponsibility(DamageResponsibility.DRIVER_CAUSED_ACCIDENT)
                .build()

        damageFile.update(
            deductible = damageFile.deductible,
            claimTowardsEmployee = damageFile.claimTowardsEmployee,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.BOTH_CAUSED_ACCIDENT,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.damageResponsibility).isEqualTo(DamageResponsibility.BOTH_CAUSED_ACCIDENT)
        assertThat(damageFile.internalOrderNumber).isNull()
    }

    @Test
    fun `should reset internalOrderNumber if damageResponsibility changes but driverAtFault stays the same`() {
        val damageFile =
            DamageFileBuilder()
                .internalOrderNumber(InternalOrderNumber("some number"))
                .damageResponsibility(DamageResponsibility.UNKNOWN)
                .build()

        damageFile.update(
            deductible = damageFile.deductible,
            claimTowardsEmployee = damageFile.claimTowardsEmployee,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.BOTH_CAUSED_ACCIDENT,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.damageResponsibility).isEqualTo(DamageResponsibility.BOTH_CAUSED_ACCIDENT)
        assertThat(damageFile.internalOrderNumber).isNotNull()
    }

    @Test
    fun `should set employeeToBeCharged to external if isExtern is set to true`() {
        val damageFile =
            DamageFileBuilder()
                .internalOrderNumber(InternalOrderNumber("some number"))
                .damageResponsibility(DamageResponsibility.DRIVER_CAUSED_ACCIDENT)
                .isExternal(false)
                .employeeToBeCharged(EmployeeNumber("somebody"))
                .build()

        damageFile.update(
            deductible = damageFile.deductible,
            claimTowardsEmployee = damageFile.claimTowardsEmployee,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.BOTH_CAUSED_ACCIDENT,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = true,
        )

        assertThat(damageFile.employeeToBeCharged).isEqualTo(EmployeeNumber.external)
        assertThat(damageFile.isExternal).isTrue()
    }

    @Test
    fun `should update deductible to 0 if original value is neither null nor 0 and provided value is null`() {
        val amount = MonetaryAmount(value = BigDecimal(10))
        val damageFile = DamageFileBuilder().deductible(amount).build()
        val originalUpdateTimestamp = damageFile.deductibleUpdated
        sleep(Duration.ofMillis(10L))

        damageFile.update(
            deductible = null,
            claimTowardsEmployee = null,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = true,
        )

        assertThat(damageFile.deductible!!.value).isEqualTo(BigDecimal.valueOf(0))
        assertThat(damageFile.deductibleUpdated)
            .usingComparator(OffsetDateTime::compareTo)
            .isAfter(originalUpdateTimestamp)
    }

    @Test
    fun `should update claimTowardsEmployee to 0 if original value is neither null nor 0 and provided value is null`() {
        val amount = MonetaryAmount(value = BigDecimal(10))
        val damageFile = DamageFileBuilder().deductible(amount).claimTowardsEmployee(amount).build()
        val originalUpdateTimestamp = damageFile.deductibleUpdated
        sleep(Duration.ofMillis(10L))

        damageFile.update(
            deductible = amount,
            claimTowardsEmployee = null,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.claimTowardsEmployee!!.value).isEqualTo(BigDecimal.valueOf(0))
        assertThat(damageFile.deductibleUpdated).isEqualTo(originalUpdateTimestamp)
    }

    @Test
    fun `should not update deductible and claimTowardsEmployee if provided values are identical`() {
        val damageFile = DamageFileBuilder.buildSingle()
        val originalDeductible = damageFile.deductible
        val originalClaimTowardsEmployee = damageFile.claimTowardsEmployee
        val originalDeductibleUpdatedTimestamp = damageFile.deductibleUpdated

        damageFile.update(
            deductible = originalDeductible,
            claimTowardsEmployee = originalClaimTowardsEmployee,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.deductible).isEqualTo(originalDeductible)
        assertThat(damageFile.claimTowardsEmployee).isEqualTo(originalClaimTowardsEmployee)
        assertThat(damageFile.deductibleUpdated)
            .usingComparator(OffsetDateTime::compareTo)
            .isEqualTo(originalDeductibleUpdatedTimestamp)
    }

    @Test
    fun `should not update deductible and claimTowardsEmployee if original value is 0 and provided values is null`() {
        val amount = MonetaryAmount(value = BigDecimal(0))
        val damageFile = DamageFileBuilder().deductible(amount).claimTowardsEmployee(amount).build()
        val originalDeductible = damageFile.deductible
        val originalClaimTowardsEmployee = damageFile.claimTowardsEmployee
        val originalDeductibleUpdatedTimestamp = damageFile.deductibleUpdated

        damageFile.update(
            deductible = null,
            claimTowardsEmployee = null,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.deductible).isEqualTo(originalDeductible)
        assertThat(damageFile.claimTowardsEmployee).isEqualTo(originalClaimTowardsEmployee)
        assertThat(damageFile.deductibleUpdated)
            .usingComparator(OffsetDateTime::compareTo)
            .isEqualTo(originalDeductibleUpdatedTimestamp)
    }

    @Test
    fun `should not update deductible and claimTowardsEmployee if original and provided values are null`() {
        val damageFile = DamageFileBuilder().deductible(null).claimTowardsEmployee(null).build()
        val originalDeductible = damageFile.deductible
        val originalClaimTowardsEmployee = damageFile.claimTowardsEmployee
        val originalDeductibleUpdatedTimestamp = damageFile.deductibleUpdated

        damageFile.update(
            deductible = null,
            claimTowardsEmployee = null,
            insuranceCoverage = InsuranceCoverage.FULL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.deductible).isEqualTo(originalDeductible)
        assertThat(damageFile.claimTowardsEmployee).isEqualTo(originalClaimTowardsEmployee)
        assertThat(damageFile.deductibleUpdated).isEqualTo(originalDeductible)
        assertThat(originalDeductibleUpdatedTimestamp).isNull()
    }

    @Test
    fun `should not update insuranceCoverage if values are same`() {
        val damageFile = DamageFileBuilder().insuranceCoverage(InsuranceCoverage.PARTIAL).build()
        val originalInsuranceCoverage = damageFile.insuranceCoverage

        damageFile.update(
            deductible = null,
            claimTowardsEmployee = null,
            insuranceCoverage = InsuranceCoverage.PARTIAL,
            damageResponsibility = DamageResponsibility.UNKNOWN,
            damageDate = damageFile.damageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.insuranceCoverage).isEqualTo(originalInsuranceCoverage)
    }

    @Test
    fun `should not update damageDate if values differ only in nanos`() {
        val originalDamageDate = OffsetDateTime.now().withNano(111111456)
        val damageFile = DamageFileBuilder().damageDate(originalDamageDate).build()
        val newDamageDate = damageFile.damageDate.withNano(111111999)

        damageFile.update(
            deductible = null,
            claimTowardsEmployee = null,
            insuranceCoverage = damageFile.insuranceCoverage,
            damageResponsibility = damageFile.damageResponsibility,
            damageDate = newDamageDate,
            employeeToBeCharged = damageFile.employeeToBeCharged,
            isExternal = false,
        )

        assertThat(damageFile.damageDate).isEqualTo(originalDamageDate)
    }

    @Test
    fun `should throw DamageFileAlreadyClosedException when updating a closed damage file`() {
        val damageFile = DamageFileBuilder.buildSingle()
        val stateField = requireNotNull(ReflectionUtils.findField(DamageFile::class.java, "state"))
        ReflectionUtils.makeAccessible(stateField)
        ReflectionUtils.setField(stateField, damageFile, DamageFileState.CLOSED)

        assertThatExceptionOfType(DamageFileAlreadyClosedException::class.java).isThrownBy {
            damageFile.update(
                null,
                null,
                InsuranceCoverage.FULL,
                DamageResponsibility.UNKNOWN,
                damageFile.damageDate,
                damageFile.employeeToBeCharged,
                isExternal = false,
            )
        }
    }

    @Test
    fun `should throw LegalHoldInPlaceException when updating a damage file with legal hold in place`() {
        val damageFile = DamageFileBuilder.buildSingle()
        damageFile.addLegalHold(LegalHoldKey("anyKey"))

        assertThatExceptionOfType(LegalHoldInPlaceException::class.java).isThrownBy {
            damageFile.update(
                null,
                null,
                InsuranceCoverage.FULL,
                DamageResponsibility.UNKNOWN,
                damageFile.damageDate,
                damageFile.employeeToBeCharged,
                isExternal = false,
            )
        }
    }

    @Test
    fun `should anonymize a closed damageFile`() {
        val closeDamageFile = DamageFileBuilder().closed().build()
        closeDamageFile.anonymize()
        assertThat(closeDamageFile.employeeToBeCharged).isEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
    }

    @Test
    fun `should throw DamageFileNotClosedException when trying to anonymize an open damage file`() {
        val damageFile = DamageFileBuilder().build()
        assertThatExceptionOfType(DamageFileNotClosedException::class.java).isThrownBy { damageFile.anonymize() }
        assertThat(damageFile.employeeToBeCharged).isNotEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
    }

    @Test
    fun `should throw LegalHoldInPlaceException when anonymizing a closed damage file with legal hold in place`() {
        val damageFile = DamageFileBuilder().closed().build()
        damageFile.addLegalHold(LegalHoldKey("anyKey"))
        assertThatExceptionOfType(LegalHoldInPlaceException::class.java).isThrownBy { damageFile.anonymize() }
    }

    @Test
    fun `should throw DamageFileNotClosedException when trying to reopen an open damage file`() {
        val damageFile = DamageFileBuilder().build()
        assertThatExceptionOfType(DamageFileNotClosedException::class.java).isThrownBy { damageFile.reopen() }
    }

    @Test
    fun `should throw LegalHoldInPlaceException when trying to reopen an open damage file with legal hold in place`() {
        val damageFile = DamageFileBuilder().closed().build()
        damageFile.addLegalHold(LegalHoldKey("anyKey"))
        assertThatExceptionOfType(LegalHoldInPlaceException::class.java).isThrownBy { damageFile.reopen() }
    }

    @Test
    fun `should throw IllegalArgumentException when damage file key exceeds length of 10 characters`() {
        assertThatExceptionOfType(IllegalArgumentException::class.java).isThrownBy { DamageFileKey("12345678900") }
    }

    @Test
    fun `should close a reopened damage file`() {
        val damageFile = DamageFileBuilder().reopened().build()

        val reopenEvent = requireNotNull(damageFile.close())
        assertThat(reopenEvent.hasBeenReopened).isTrue()
    }

    @Test
    fun `should close a damage file`() {
        val damageFile = DamageFileBuilder().build()

        val closedEvent = requireNotNull(damageFile.close())
        assertThat(closedEvent.hasBeenReopened).isFalse()
    }

    @Test
    fun `should not update closed date on reopen and closing again`() {
        val damageFile = DamageFileBuilder().build()
        damageFile.close()
        val initialClosedDate = damageFile.closedDate

        damageFile.reopen()
        damageFile.close()

        assertThat(damageFile.closedDate).isEqualTo(initialClosedDate)
    }

    @Test
    fun `should throw DamageFileReopenException when trying to reopened a damage file that has been closed for more than 3 years`() {
        val damageFile = DamageFileBuilder().closed().build()
        val closedDateField = requireNotNull(ReflectionUtils.findField(DamageFile::class.java, "closedDate"))
        ReflectionUtils.makeAccessible(closedDateField)
        ReflectionUtils.setField(closedDateField, damageFile, OffsetDateTime.now().minusYears(4))

        assertThatExceptionOfType(DamageFileReopenException::class.java).isThrownBy { damageFile.reopen() }
    }

    companion object {

        @JvmStatic
        fun damageFileProvider(): Stream<Arguments> =
            DamageFileBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
