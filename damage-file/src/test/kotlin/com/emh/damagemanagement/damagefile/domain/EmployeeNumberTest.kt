/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class EmployeeNumberTest {

    @Test
    fun `should match sanitized employee numbers`() {
        val pNumber = EmployeeNumber.sanitized("P379696")
        val leadingZeroesNumber = EmployeeNumber.sanitized("00379696")

        val employeeNumberMatch = pNumber == leadingZeroesNumber

        assertThat(employeeNumberMatch).isTrue()
    }
}
