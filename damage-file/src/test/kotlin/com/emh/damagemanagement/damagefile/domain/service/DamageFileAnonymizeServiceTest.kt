package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.ANONYMIZED_EMPLOYEE_NUMBER
import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageFileNotClosedException
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.legal.hold.domain.LegalHoldInPlaceException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileAnonymizeServiceTest {

    @Autowired private lateinit var damageFileAnonymizeService: DamageFileAnonymizeService
    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService
    @Autowired private lateinit var damageFileFinder: DamageFileFinder

    @Test
    fun `should anonymize damage file`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFile.close()

        damageFileAnonymizeService.anonymizeDamageFile(damageFile)

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(damageFileFromFinder.employeeToBeCharged).isEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
    }

    @Test
    fun `should throw DamageFileNotClosedException as damage file is open`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)

        assertThatExceptionOfType(DamageFileNotClosedException::class.java).isThrownBy {
            damageFileAnonymizeService.anonymizeDamageFile(damageFile)
        }

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(damageFileFromFinder.employeeToBeCharged).isNotEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
    }

    @Test
    fun `should throw LegalHoldInPlaceException as damage file has legal hold in place`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFile.close()
        damageFile.addLegalHold(LegalHoldKey("anyKey"))

        assertThatExceptionOfType(LegalHoldInPlaceException::class.java).isThrownBy {
            damageFileAnonymizeService.anonymizeDamageFile(damageFile)
        }

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(damageFileFromFinder.employeeToBeCharged).isNotEqualTo(ANONYMIZED_EMPLOYEE_NUMBER)
    }
}
