/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageFileState
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateServiceTest.Companion.damageFileNewRequirements
import com.emh.damagemanagement.damagefile.validDamageFileKey
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileCloseServiceTest {

    @Autowired private lateinit var entityManager: EntityManager

    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService

    @Autowired private lateinit var damageFileCloseService: DamageFileCloseService

    @Autowired private lateinit var damageFileFinder: DamageFileFinder

    @Test
    fun `should close damage file`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder.buildSingle()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        damageFileCloseService.closeDamageFile(damageFileFromFinder)

        assertThat(damageFileFromFinder)
            .satisfies(damageFileNewRequirements(damageFileNew = damageFileNew, isClosed = true))
        assertThat(damageFileFromFinder.key).isEqualTo(damageFile.key)
        assertThat(damageFileFromFinder.state).isEqualTo(DamageFileState.CLOSED)
    }
}
