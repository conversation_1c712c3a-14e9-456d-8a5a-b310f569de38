/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.DamageFileState
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.shared.TEST_USER
import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import jakarta.persistence.EntityManager
import java.time.OffsetDateTime
import java.time.temporal.ChronoUnit
import java.util.function.Consumer
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.within
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileCreateServiceTest {
    @Autowired private lateinit var entityManager: EntityManager

    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService

    @Autowired private lateinit var damageFileFinder: DamageFileFinder

    @Test
    fun `should create new damage file`() {
        val externalCreationDate = OffsetDateTime.parse("2023-12-03T10:15:23+01:00")
        val damageFileNew = DamageFileNewOrUpdateBuilder().creationDate(externalCreationDate).build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)

        assertThat(damageFileFromFinder).satisfies(damageFileNewRequirements(damageFileNew = damageFileNew))
        assertThat(damageFileFromFinder.key).isEqualTo(damageFile.key)
        assertThat(damageFileFromFinder.state).isEqualTo(DamageFileState.OPEN)
    }

    companion object {
        fun damageFileNewRequirements(
            damageFileNew: DamageFileNewOrUpdate,
            isClosed: Boolean = false,
            isReopened: Boolean = false,
        ) =
            Consumer<DamageFile> { damageFile: DamageFile ->
                assertThat(damageFile)
                    .usingRecursiveComparison()
                    .ignoringFieldsMatchingRegexes("^(_|mutable)\\S*")
                    .ignoringCollectionOrder()
                    .ignoringFields(
                        *TestJpaAuditingConfiguration.AUDIT_FIELDS,
                        "internalOrderNumber",
                        "id",
                        "state",
                        "key",
                        "creationDate",
                        "deductibleUpdated",
                        "closedDate",
                        "employeeToBeCharged",
                    )
                    .isEqualTo(damageFileNew)
                assertThat(damageFile.createdBy).isEqualTo(TEST_USER)
                assertThat(damageFile.created).isNotNull()
                assertThat(damageFile.creationDate)
                    .isCloseTo(damageFileNew.externalCreationDate, within(1, ChronoUnit.MILLIS))
                if (null != damageFileNew.deductible || null != damageFileNew.claimTowardsEmployee) {
                    assertThat(damageFile.deductibleUpdated).isNotNull()
                }
                when {
                    isClosed -> {
                        assertThat(damageFile.isClosed).isTrue()
                        assertThat(damageFile.closedDate).isNotNull()
                    }
                    isReopened -> {
                        assertThat(damageFile.isClosed).isFalse()
                        assertThat(damageFile.closedDate).isNotNull()
                        assertThat(damageFile.state).isEqualTo(DamageFileState.REOPENED)
                    }
                    else -> {
                        assertThat(damageFile.closedDate).isNull()
                        assertThat(damageFile.isClosed).isFalse()
                    }
                }
                assertThat(damageFile.employeeToBeCharged).isEqualTo(damageFileNew.employeeToBeCharged)
                assertThat(damageFile.insuranceCoverage).isEqualTo(damageFileNew.insuranceCoverage)
                assertThat(damageFile.damageResponsibility).isEqualTo(damageFileNew.damageResponsibility)
                assertThat(damageFile.damageDate).isEqualTo(damageFileNew.damageDate)
                assertThat(damageFile.vehicle).usingRecursiveComparison().isEqualTo(damageFileNew.vehicle)
            }
    }
}
