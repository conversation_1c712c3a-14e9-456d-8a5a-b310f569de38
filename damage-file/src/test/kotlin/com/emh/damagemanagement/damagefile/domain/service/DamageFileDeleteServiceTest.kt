/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageFileNotClosedException
import com.emh.damagemanagement.damagefile.domain.DamageFileNotFoundException
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.legal.hold.domain.LegalHoldInPlaceException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import org.assertj.core.api.Assertions
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileDeleteServiceTest {

    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService

    @Autowired private lateinit var damageFileDeleteService: DamageFileDeleteService

    @Autowired private lateinit var damageFileCloseService: DamageFileCloseService

    @Autowired private lateinit var damageFileFinder: DamageFileFinder

    @Test
    fun `should delete damage file`() {
        val damageFile =
            damageFileCreateService.createDamageFile(DamageFileNewOrUpdateBuilder().build(), validDamageFileKey)
        damageFileCloseService.closeDamageFile(damageFile)
        damageFileDeleteService.deleteDamageFile(damageFile)
        Assertions.assertThatThrownBy { damageFileFinder.getDamageFile(damageFile.key) }
            .isInstanceOf(DamageFileNotFoundException::class.java)
    }

    @Test
    fun `should throw DamageFileNotClosedException when damage file is not closed`() {
        val damageFile =
            damageFileCreateService.createDamageFile(DamageFileNewOrUpdateBuilder().build(), validDamageFileKey)
        Assertions.assertThatThrownBy { damageFileDeleteService.deleteDamageFile(damageFile) }
            .isInstanceOf(DamageFileNotClosedException::class.java)
    }

    @Test
    fun `should throw LegalHoldInPlaceException when a closed damage file has legal hold in place`() {
        val damageFile =
            damageFileCreateService.createDamageFile(DamageFileNewOrUpdateBuilder().build(), validDamageFileKey)
        damageFile.close()
        damageFile.addLegalHold(LegalHoldKey("anyKey"))

        Assertions.assertThatThrownBy { damageFileDeleteService.deleteDamageFile(damageFile) }
            .isInstanceOf(LegalHoldInPlaceException::class.java)
    }
}
