/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldAppliedToDomainEntityEvent
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldReleasedFromDomainEntityEvent
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.reset
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [DamageFileLegalHoldServiceTest.TestConfiguration::class])
class DamageFileLegalHoldServiceTest {

    @Autowired private lateinit var damageFileLegalHoldService: DamageFileLegalHoldService
    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService
    @Autowired private lateinit var entityManager: EntityManager
    @Autowired private lateinit var damageFileFinder: DamageFileFinder
    @Autowired private lateinit var applicationEventPublisher: ApplicationEventPublisher

    @BeforeEach
    fun resetCounter() {
        reset(applicationEventPublisher)
    }

    @Test
    fun `should apply legal hold`() {
        val damageFile =
            damageFileCreateService.createDamageFile(DamageFileNewOrUpdateBuilder.buildSingle(), validDamageFileKey)
        entityManager.flush()
        val legalHoldAppliedEventArgumentCaptor = argumentCaptor<LegalHoldAppliedToDomainEntityEvent>()
        val legalHoldKey = LegalHoldKey("hold_me")

        damageFileLegalHoldService.applyLegalHold(legalHoldKey = legalHoldKey, entity = damageFile)
        entityManager.flush()

        verify(applicationEventPublisher).publishEvent(legalHoldAppliedEventArgumentCaptor.capture())
        assertThat(legalHoldAppliedEventArgumentCaptor.firstValue)
            .satisfies({
                assertThat(it.legalHoldKey).isEqualTo(legalHoldKey)
                assertThat(it.domainEntityReference).isEqualTo(DomainEntityReference(damageFile.key.value))
            })
        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(damageFileFromFinder.legalHolds).containsExactly(legalHoldKey)
    }

    @Test
    fun `should release legal hold`() {
        val damageFile =
            damageFileCreateService.createDamageFile(DamageFileNewOrUpdateBuilder.buildSingle(), validDamageFileKey)
        entityManager.flush()
        val legalHoldKey = LegalHoldKey("hold_me")
        damageFileLegalHoldService.applyLegalHold(legalHoldKey = legalHoldKey, entity = damageFile)
        entityManager.flush()
        verify(applicationEventPublisher).publishEvent(any<LegalHoldAppliedToDomainEntityEvent>())
        val legalHoldReleasedEventArgumentCaptor = argumentCaptor<LegalHoldReleasedFromDomainEntityEvent>()
        val damageFileWithLegalHold = damageFileFinder.getDamageFile(damageFile.key)

        damageFileLegalHoldService.releaseLegalHold(legalHoldKey = legalHoldKey, entity = damageFileWithLegalHold)
        entityManager.flush()

        verify(applicationEventPublisher).publishEvent(legalHoldReleasedEventArgumentCaptor.capture())
        assertThat(legalHoldReleasedEventArgumentCaptor.firstValue)
            .satisfies({
                assertThat(it.legalHoldKey).isEqualTo(legalHoldKey)
                assertThat(it.domainEntityReference).isEqualTo(DomainEntityReference(damageFile.key.value))
            })
        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(damageFileFromFinder.legalHolds).isEmpty()
    }

    internal class TestConfiguration {

        @Bean @Primary fun applicationEventPublisher(): ApplicationEventPublisher = mock()
    }
}
