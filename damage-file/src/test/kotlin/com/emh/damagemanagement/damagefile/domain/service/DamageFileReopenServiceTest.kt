/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageFileState
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateServiceTest.Companion.damageFileNewRequirements
import com.emh.damagemanagement.damagefile.validDamageFileKey
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileReopenServiceTest {
    @Autowired private lateinit var entityManager: EntityManager

    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService

    @Autowired private lateinit var damageFileCloseService: DamageFileCloseService

    @Autowired private lateinit var damageFileReopenService: DamageFileReopenService

    @Autowired private lateinit var damageFileFinder: DamageFileFinder

    @Test
    fun `should reopen damage file`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder.buildSingle()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFileCloseService.closeDamageFile(damageFile)
        entityManager.flush()

        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        damageFileReopenService.reopenDamageFile(damageFileFromFinder)

        assertThat(damageFileFromFinder)
            .satisfies(damageFileNewRequirements(damageFileNew = damageFileNew, isReopened = true))
        assertThat(damageFileFromFinder.key).isEqualTo(damageFile.key)
        assertThat(damageFileFromFinder.state).isEqualTo(DamageFileState.REOPENED)
    }

    @Test
    fun `needsToBeReponed should return false if nothing changed for employees`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().isExternal(false).closed().build()

        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFileCloseService.closeDamageFile(damageFile)
        entityManager.flush()

        val damageFileUpdate = damageFileNew
        val needsToBeReopened = damageFileReopenService.needsToBeReopened(damageFile, damageFileUpdate)
        assertThat(needsToBeReopened).isFalse()
    }

    @Test
    fun `needsToBeReponed should return false if nothing changed for externals`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().isExternal(true).closed().build()

        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFileCloseService.closeDamageFile(damageFile)
        entityManager.flush()

        val damageFileUpdate = damageFileNew
        val needsToBeReopened = damageFileReopenService.needsToBeReopened(damageFile, damageFileUpdate)
        assertThat(needsToBeReopened).isFalse()
    }

    @Test
    fun `needsToBeReponed should return false for externals, if employeeToBeCharged changes`() {
        val damageFileBuilder = DamageFileNewOrUpdateBuilder().isExternal(true).closed()

        val damageFileNew = damageFileBuilder.employeeToBeCharged(EmployeeNumber("P00000001")).build()
        val damageFileUpdate = damageFileBuilder.employeeToBeCharged(EmployeeNumber("P00000002")).build()

        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFileCloseService.closeDamageFile(damageFile)
        entityManager.flush()

        val needsToBeReopened = damageFileReopenService.needsToBeReopened(damageFile, damageFileUpdate)
        assertThat(needsToBeReopened).isFalse()
    }

    @Test
    fun `needsToBeReponed should return true for externals, if was employee before`() {
        val damageFileBuilder = DamageFileNewOrUpdateBuilder().closed()

        val damageFileNew = damageFileBuilder.employeeToBeCharged(EmployeeNumber("P00000001")).isExternal(false).build()
        val damageFileUpdate =
            damageFileBuilder.employeeToBeCharged(EmployeeNumber("EX0000002")).isExternal(true).build()

        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFileCloseService.closeDamageFile(damageFile)
        entityManager.flush()

        val needsToBeReopened = damageFileReopenService.needsToBeReopened(damageFile, damageFileUpdate)
        assertThat(needsToBeReopened).isTrue()
    }

    @Test
    fun `needsToBeReponed should return true for employees, if employeeToBeCharged changes`() {
        val damageFileBuilder = DamageFileNewOrUpdateBuilder().isExternal(false).closed()

        val damageFileNew = damageFileBuilder.employeeToBeCharged(EmployeeNumber("EX0000001")).build()
        val damageFileUpdate = damageFileBuilder.employeeToBeCharged(EmployeeNumber("EX0000002")).build()

        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        damageFileCloseService.closeDamageFile(damageFile)
        entityManager.flush()

        val needsToBeReopened = damageFileReopenService.needsToBeReopened(damageFile, damageFileUpdate)
        assertThat(needsToBeReopened).isTrue()
    }
}
