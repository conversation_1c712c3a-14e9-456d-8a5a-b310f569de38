/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.damagefile.domain.service

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.IntegrationTest
import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageResponsibility
import com.emh.damagemanagement.damagefile.domain.EmployeeNumber
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.damagefile.domain.MonetaryAmount
import com.emh.damagemanagement.damagefile.validDamageFileKey
import jakarta.persistence.EntityManager
import java.time.OffsetDateTime
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageFileUpdateServiceTest {
    @Autowired private lateinit var entityManager: EntityManager

    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService
    @Autowired private lateinit var damageFileUpdateService: DamageFileUpdateService

    @Autowired private lateinit var damageFileFinder: DamageFileFinder

    @Test
    fun `should update damage file`() {
        val externalCreationDate = OffsetDateTime.parse("2023-12-03T10:15:23+01:00")
        val damageFileNew = DamageFileNewOrUpdateBuilder().creationDate(externalCreationDate).build()
        val damageFile = damageFileCreateService.createDamageFile(damageFileNew, validDamageFileKey)
        entityManager.flush()
        val damageFileUpdate =
            DamageFileUpdate(
                deductible = MonetaryAmount.of(23.00),
                claimTowardsEmployee = MonetaryAmount.of(42.5),
                insuranceCoverage = InsuranceCoverage.PARTIAL,
                damageResponsibility = DamageResponsibility.DRIVER_CAUSED_ACCIDENT,
                damageDate = OffsetDateTime.now(),
                employeeToBeCharged = EmployeeNumber("00123456"),
                isExternal = false,
            )
        val damageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)

        damageFileUpdateService.updateDamageFile(damageFileFromFinder, damageFileUpdate)
        entityManager.flush()

        val updatedDamageFileFromFinder = damageFileFinder.getDamageFile(damageFile.key)
        assertThat(updatedDamageFileFromFinder.deductible).isEqualTo(damageFileUpdate.deductible)
        assertThat(updatedDamageFileFromFinder.claimTowardsEmployee).isEqualTo(damageFileUpdate.claimTowardsEmployee)
        assertThat(updatedDamageFileFromFinder.insuranceCoverage).isEqualTo(damageFileUpdate.insuranceCoverage)
        assertThat(updatedDamageFileFromFinder.damageResponsibility).isEqualTo(damageFileUpdate.damageResponsibility)
        assertThat(updatedDamageFileFromFinder.creationDate)
            .usingComparator(OffsetDateTime::compareTo)
            .isEqualTo(externalCreationDate)
    }
}
