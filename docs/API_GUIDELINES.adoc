= API Guidelines

== Why do we need this?

Consistency, Structure and Organization will help us create a maintainable system. The aim of these guidelines are to provide standards that guide us towards a common goal.

== Goals of the API

* Provide a consistent mechanism for Porsche Fuhrpark services to consume vehicle-related data

== Design Principles

. We prefer a *REST based API with JSON* payloads
. Follow *Postel's law*: "Be conservate in what you send, but liberal in what you accept"

== Guidelines

=== Representational Guidelines

|===
| Guidance | Description

| MUST
| provide API specification through OpenAPI

| MUST
| provide API in US English

| MUST
| use standard https://opensource.zalando.com/restful-api-guidelines/#data-formats[data-formats]

| MUST
| use standard formats for date and time https://opensource.zalando.com/restful-api-guidelines/#169[More information]

| MUST
| use standard formats for country, language and currency representations https://opensource.zalando.com/restful-api-guidelines/#170[More information]

| MUST
| use plural forms for REST resources

| MUST
| use camelCase (not snake-case or kebab_case) for path segments

| MUST
| use only official https://en.wikipedia.org/wiki/List_of_HTTP_status_codes[HTTP Status Codes]
|===

=== Compliance and Security
|===
| Guidance | Description

| MUST     | secure endpoints with authentication
| MUST     | not include PII data in URL paths or query string (prefer Request-Headers)
|===

=== Error Handling

|===
| Guidance | Description

| MUST
| all errors originating from the API backend supports Problem RFC 7807

| MUST
| not expose stack-traces or any other form of data pertaining to the backend
|===

=== API Versioning and Compatibility

|===
| Guidance | Description

| SHOULD
| not break backwards compatibility (see section below)

| MUST
| not use API versioning (see section below)
|===

https://martinfowler.com/articles/enterpriseREST.html[API versioning is not recommended]

For breaking changes in the API, prefer

* creating new resources and deprecating older resources (say, when an optional field becomes required)
* keeping in mind Postel's law, new fields can be added to an API response. Deprecate fields that will be removed.
* progressively remove deprecated resources and fields with tooling such as PACT (for contract-testing) to ensure dependent teams are not impacted
