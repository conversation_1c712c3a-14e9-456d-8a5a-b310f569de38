= Commit Message Format

In order to encourage smaller commits that encapsulate logical chunks of work, we willuse the following format for commit-messages.

== Commit Heading

[,shell]
----
(feat|fix|docs|ci|test|refactor): (Short commit message < 70 chars) | (Ticket-Number)
----

|===
| Label | Reason

| feat
| Any change in business logic

| fix
| Bug fixes

| docs
| Documentation, No change in business logic

| ci
| Changes to infrastructure tools, No change in business logic

| test
| Addition of tests, No change in business logic

| refactor
| Rework of code, No change in business logic
|===

== Commit Body

*For bigger changes ensure the why of a certain code change is documented.*

This will help future maintainers understand the thought-process, considerations and environment that had been there when the code change was made.

== Commit Authors

When pairing, use `Co-Authored-By` to indicate who paired with you on the commit.

https://gitlab.com/gitlab-org/gitlab-foss/-/issues/31640
