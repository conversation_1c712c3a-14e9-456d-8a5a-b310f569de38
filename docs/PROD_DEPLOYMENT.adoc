=  Prod deployment
:doctype: book

== Requirements

* 4-Eyes principle: 2 people like 2 developers has to confirm the deployment to the production environment.
* The approval has to be ensured and documented from GitLab.

== Solution

* We tag our prod-deployment pipeline step with "environment: name: production" on .gitlab-ci-yml
* Setup on GitLab
 ** Select the repository
 ** Go to Settings > CI/CD > https://cicd.skyway.porsche.com/FP20/damage-management/damage-management-service/-/settings/ci_cd[Protected environments]
 ** Set up an approval rule for the production environment

== How to approve deployment?

* Go to pipeline
* Click on the stage "deploy"
* Click on the step terraform-prod
* Click on the button "View environment details page"
* Click on the button "Approval options"
* Click on the button "Approve"

= How to deploy to production?

* Get all needed approvals on GitLab
* Click on the button "Deploy to..."
