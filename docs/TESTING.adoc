= Testing Strategy

== General Principles

* If the tests pass, then the functionality should not break in production
* Don't test for the sake of a coverage metricfootnote:1[https://itnext.io/code-coverage-the-metric-that-makes-your-tests-worse-c1dddcc0831]
* Test-Driven-Development is not only encouraged, but required
* Prefer Classicist styles of testing, over the Mockist style - within a single architectural layerfootnote:2[https://martinfowler.com/articles/mocksArentStubs.html#ClassicalAndMockistTesting]

== Levels of testing

The project at the moment only has two levels of testing

* *Controller Tests* to test exposed API
* *API Client Tests* to test web-clients making calls to remote systems

=== Testing the Controllers

Controllers are tested using an `@WebMVCTest`.

We should NOT make calls to the API endpoint under test using plain JSON strings, never objects. This ensures that the `ObjectMapper` settings in the project are as expected.

=== Testing API Clients

Since mocking the framework is considered a bad ideafootnote:3[https://testing.googleblog.com/2020/07/testing-on-toilet-dont-mock-types-you.html] we chose to use Wiremock to test all of our API clients.
This allows us flexibility to change the API client library in the future and the tests can serve as a safety net.

Wiremock was chosed over MockServer due to it's style of expectation matching, and it's descriptive error-message when expectations are not met.

== Next Steps

=== Introducing PACT Contract Testing

This should be done when our API is consumed by a third party

== References
