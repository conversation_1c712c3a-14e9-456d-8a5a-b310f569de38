:imagesdir: ../images
:jbake-menu: -
// header file for arc42-template,
// including all help texts
//
// ====================================

// configure EN settings for asciidoc
include::chapters/config.adoc[]

= image:arc42-logo.png[arc42] ARC42 Damage Management Service
:revnumber: 8.2 EN
:revdate: January 2023
:revremark: (based upon AsciiDoc version)
// toc-title definition MUST follow document title without blank line!
:toc-title: Table of Contents

//additional style for arc42 help callouts
ifdef::backend-html5[]
++++
<style>
.arc42help {font-size:small; width: 14px; height: 16px; overflow: hidden; position: absolute; right: 0; padding: 2px 0 3px 2px;}
.arc42help::before {content: "?";}
.arc42help:hover {width:auto; height: auto; z-index: 100; padding: 10px;}
.arc42help:hover::before {content: "";}
@media print {
	.arc42help {display:none;}
}
</style>
++++
endif::backend-html5[]


include::chapters/about-arc42.adoc[]

// horizontal line
***

[role="arc42help"]
****
[NOTE]
====
This version of the template contains some help and explanations.
It is used for familiarization with arc42 and the understanding of the concepts.
For documentation of your own system you use better the _plain_ version.
====
****


// numbering from here on
:numbered:

<<<<
// 1. Introduction and Goals
include::chapters/01_introduction_and_goals.adoc[]

<<<<
// 2. Architecture Constraints
include::chapters/02_architecture_constraints.adoc[]

<<<<
// 3. System Scope and Context
include::chapters/03_system_scope_and_context.adoc[]

<<<<
// 4. Solution Strategy
include::chapters/04_solution_strategy.adoc[]

<<<<
// 5. Building Block View
include::chapters/05_building_block_view.adoc[]

<<<<
// 6. Runtime View
include::chapters/06_runtime_view.adoc[]

<<<<
// 7. Deployment View
include::chapters/07_deployment_view.adoc[]

<<<<
// 8. Concepts
include::chapters/08_concepts.adoc[]

<<<<
// 9. Architecture Decisions
include::chapters/09_architecture_decisions.adoc[]

<<<<
// 10. Quality Requirements
include::chapters/10_quality_requirements.adoc[]

<<<<
// 11. Technical Risks
include::chapters/11_technical_risks.adoc[]

<<<<
// 12. Glossary
include::chapters/12_glossary.adoc[]


