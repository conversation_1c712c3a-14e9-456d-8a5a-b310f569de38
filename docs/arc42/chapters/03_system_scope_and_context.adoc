:jbake-title: System Scope and Context
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 3
:filename: /chapters/03_system_scope_and_context.adoc
ifndef::imagesdir[:imagesdir: ../../images]

:toc:



[[section-system-scope-and-context]]
== System Scope and Context


[role="arc42help"]
****
.Contents
System scope and context - as the name suggests - delimits your system (i.e. your scope) from all its communication partners
(neighboring systems and users, i.e. the context of your system). It thereby specifies the external interfaces.

If necessary, differentiate the business context (domain specific inputs and outputs) from the technical context (channels, protocols, hardware).

.Motivation
The domain interfaces and technical interfaces to communication partners are among your system's most critical aspects. Make sure that you completely understand them.

.Form
Various options:

* Context diagrams
* Lists of communication partners and their interfaces.


.Further Information

See https://docs.arc42.org/section-3/[Context and Scope] in the arc42 documentation.

****


=== Business Context

[role="arc42help"]
****
.Contents
Specification of *all* communication partners (users, IT-systems, ...) with explanations of domain specific inputs and outputs or interfaces.
Optionally you can add domain specific formats or communication protocols.

.Motivation
All stakeholders should understand which data are exchanged with the environment of the system.

.Form
All kinds of diagrams that show the system as a black box and specify the domain interfaces to communication partners.

Alternatively (or additionally) you can use a table.
The title of the table is the name of your system, the three columns contain the name of the communication partner, the inputs, and the outputs.

****

**<Diagram or Table>**

**<optionally: Explanation of external domain interfaces>**

=== Technical Context

.Context diagramm showing the current state of the DMS and its connection to surrounding systems.
image::drawio/context-diagram.png[]

[cols="1,4,1"]
|===
| Name | Description | Type of Interface

| Repairfix
| SaaS Provider for managing damages, reports and repairs for personal and non-personal fleet vehicles.
| http, REST

| PVCC P40
| PAG internal system for calculating the monetary benefit, related to damages. The system consumes MonetaryBenefitAccountableEvent
via Kafka messages.
| tcp (Streamzilla; Kafka)

| PACE P40
| PAG internal system that provides an internal order number. The order number is required for the causal recording and
posting of all costs/payments incurred in connection with the damage claim (e.g. expert opinion, repair, opposing insurance).
| http

| Fleet Vehicle Manager (FVM)
| ART internal service. The FVM consumes damage reports via REST Api and DB view, to provide overview and detailed
information about a vehicles damages.

The DMS consumes scraped and sold date from FVM via REST Api.
| http (REST)

| User Service
| ART internal service, that provides PAG employee and subsidiary employee data via REST Api. The data is used for
migration of fleet managers and responsible person.
| http (REST)

| HR P08
| HR P08 consumes the emplyee to be charged and the deductible vie REST Api. These are then deducted from the employees
payroll.
| http (REST)


|===
