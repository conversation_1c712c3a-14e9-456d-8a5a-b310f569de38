:jbake-title: Building Block View
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 5
:filename: /chapters/05_building_block_view.adoc
ifndef::imagesdir[:imagesdir: ../../images]

:toc:



[[section-building-block-view]]


== Building Block View

=== Whitebox Overall System

.Level-1 overview of the DMS.
image::drawio/building-block-view-level-1.png[]

Motivation::

The different interfaces to the surrounding systems are seperated as their own building blocks. This allows independent
development of the components and adheres to the clean architecture, that is aimed for. The clean architecture with ports
and adapters allows an easy switch of interfaces if necessary.

.Level-1 building blocks and their responsibilities.
[cols="1,2" options="header"]
|===
| **Name** | **Responsibility**

| Vehicle Migration
| This building block is responsible for the migration of PAG data, like vehicles, fleets, fleet managers and
responsible persons to repairfix.

| DamageFile Migration
|  This building block is responsible for the migration of damage reports from repairfix to PAG internal damage files.

| DamageFile
| This building block is responsible for persisting and insuring integrity of damage files.

| Deductible
| This building block is responsible for providing an interface for the HR P40 PAG system, to request information about
employees to be charged and the deductible amount.

| Monetary Benefit
| This building block is responsible for providing information for PVCC P40 about closed damage files, so that the
monetary benefit for employess can be calculated.

| InternalOrderNumber
| This building block is responsible for requesting an internal order number that is necessary for all costs/payments
incurred in connection with the damage claim.

| GoBD
| This building block is responsible for compliance with the GoBD requirements. It enables to put legal hold on damage
files and is responsible for archiving closed damage files.

|===

=== Level 2 / Use Cases

==== Vehicle Migration [[use-case-vehicle-migration]]

.Migration of vehicles, fleets and responsible persons.
image::drawio/vehicle-migration/vehicle-migration.png[]

[start=0]
. The Product Owner uploads an Excel file with vehicles, fleets and responsible persons to a dedicated AWS S3 bucket.
. The vehicle-migration module loads and parses the Excel file.
. (feature-flag; optional) Using a feature flag the vehicle-migration module can utilize the employee-client.
. (feature-flag; optional) The employee-client fetches PAG employee and subsidiary employee data by employee number. The data is then
used instead of the data provided by the Excel file.
. The vehicle-migration utilizes the CarApi adapter provided by the repairfix-client module.
. Vehicles, responsible person and fleets are created or updated if they already exist.

==== Fleet Migration [[use-case-fleet-migration]]

**What is a fleet?**

A **fleet** (or fleet-identifier) is a repairfix concept that is used to provide grouping and access control on a set of vehicles, that are assigned to the respective fleet. During vehicle creation or update a **car** (repairfix term for a vehicle) will get a fleet assigned to it. A **fleet manager** is a (repairfix) user that has access (called _visibilityScope_) to a set of fleets. With this repairfix manages the access, a user has to multiple vehicles by assigning fleets and respective visibilityScopes to cars and users. Fleets work hierarchically, so a user that has access to a higher level fleet implicitly has access to all seublevels as well.

Fleet migration differs slightly, depending on the type of migration module that executes the migration. Excel-based vehicle-miration (**evm**) will do a fleet migration on each new file, the new single vehicle vehicle-migration (**vm**) will execute fleet migration only once on service startup.
Fleet migration itself is split into two parts: Initial migration and pool-migration based on pool managers.

===== Initial (Managed) Fleet Migration
Initial migration will ensure that top-level (managed) fleets are migrated to repairfix. Those include a fleet per _leasingArt_, as well as an overarching _pool_-Fleet.

[mermaid]
....
sequenceDiagram
    FleetMigration->>+Leasing Fleet Migration: start fleet migration
    Leasing Fleet Migration->>+LeasingArt: fetch leasing fleets
    LeasingArt->>+Leasing Fleet Migration: leasing fleet IDs
    Leasing Fleet Migration->>+Repairfix: migrate leasing fleets (fleetGroup = REPAIRFIX_FLEET_GROUP_DIENST_LEASING)
    FleetMigration->>+Pool Fleet Migration: start fleet migration
    Pool Fleet Migration->>+Repairfix: migrate pre-, postUsage fleets (fleetGroup = REPAIRFIX_FLEET_GROUP_POOL)
....

===== Pool Manager Fleet Migration

During the migration of a pool vehicle (or all pool vehicles in case of **evm**) a pool/fleet-manager will have its own pool created within repairfix. This is done by combining first name, last name and employee number and creating a _sub-fleet_ below the previously created pool-fleet (see <<Initial (Managed) Fleet Migration>>).

E.g.: Harald Meier with employee number 00123456 will have a fleet name **Meier_Harald_00123456** created for himself. All pool vehicles, that he is responsible for will also get this sub-fleet assigned during vehicle migration.

[mermaid]
....
classDiagram
    Root Fleet --> Dienstleasing Fleet
    Root Fleet --> Pool Fleet
    Dienstleasing Fleet --> `L1.1`
    Dienstleasing Fleet --> `L1.2`
    Dienstleasing Fleet --> `L1.3`
    Dienstleasing Fleet --> `L1.4`
    Dienstleasing Fleet --> L2
    Dienstleasing Fleet --> `L9.1`
    Pool Fleet --> Meier_Harald_00123456
    class RootFleet{
      REPAIRFIX_FLEET_GROUP_ROOT
    }
    class Dienstleasing Fleet{
     REPAIRFIX_FLEET_GROUP_DIENST_LEASING
    }
    class Pool Fleet{
     REPAIRFIX_FLEET_GROUP_POOL
    }
....


==== DamageFile Migration

.Migration of damage files: Repairfix damage  reports are migrated to the PAG damageFiles.
image::drawio/damage-file-migration/damage-file-migration.png[]

[start=0]
. A user creates a new or updates an existing damage report.
. A cron in teh damage-file-migration module periodically fetches all damage reports from repairfix.
. If a damage report is new or was updated, a respective damage file is created or updated.

==== Deductible

.Employee to be charged and deductible requested by HR P08.
image::drawio/damage-file-migration/request-deductible.png[]

[start=1]
. HR P08 uses requests the deductibles from a REST Api provided by hcm-p08-damage-report-adapter.
. The deductible information are retrieved from the respective damage files from the damage-file module.
. HR P08 then uses the data to charge employees based on the retrieved information.

==== Monetary Benefit

.Calculation of the monetary benefit by PVCC P40
image::drawio/damage-file-migration/close-damage-file.png[]

[start=0]
. A VPM employee closes a damage report on repairfix.
. The damage-file-migration retrieves the closed damage report (see xref:#use-case-vehicle-migration[DamageFile Migration]).
. The related damageFile is closed and a corresponding DamageFileClosedEvent is published internally.
. The DamageFileClosedEvent is consumed by the pvcc-p40-monetary-benefit-adapter
. and a corresponding MonetaryBenefitAccountableEvent is created and published to the outbox-message-relay.
. The outbox-message-relay periodically publishes events to Streamzilla.
. The MonetaryBenefitAccountableEvent is then consumed by PVCC P40.
. PVCC P40 then calculates the monetary benefit and is responsible for charging it from the employee's payroll.

