:jbake-title: Architecture Decisions
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 9
:filename: /chapters/09_architecture_decisions.adoc
ifndef::imagesdir[:imagesdir: ../../images]

:toc:



[[section-design-decisions]]
== Architecture Decisions


[role="arc42help"]
****
.Contents
Important, expensive, large scale or risky architecture decisions including rationales.
With "decisions" we mean selecting one alternative based on given criteria.

Please use your judgement to decide whether an architectural decision should be documented
here in this central section or whether you better document it locally
(e.g. within the white box template of one building block).

Avoid redundancy. 
Refer to section 4, where you already captured the most important decisions of your architecture.

.Motivation
Stakeholders of your system should be able to comprehend and retrace your decisions.

.Form
Various options:

* ADR (https://cognitect.com/blog/2011/11/15/documenting-architecture-decisions[Documenting Architecture Decisions]) for every important decision
* List or table, ordered by importance and consequences or:
* more detailed in form of separate sections per decision

.Further Information

See https://docs.arc42.org/section-9/[Architecture Decisions] in the arc42 documentation.
There you will find links and examples about ADR.

****
