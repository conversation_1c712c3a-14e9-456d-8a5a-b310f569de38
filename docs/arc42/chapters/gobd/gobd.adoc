ifndef::imagesdir[:imagesdir: ../../../images]
:toc:
:numbered:
= GoBD

The "GoBD" (**G**rundsätze zur **o**rdnungsmäßigen Führung und Aufbewahrung von **B**üchern, Aufzeichnungen und Unterlagen in
elektronischer Form, sowie zum **D**atenzugriff) are guidelines set forth by the German Federal Ministry of Finance.
They outline the principles for the proper management and storage of books, records, and documents in electronic
form, as well as regulations regarding data access.
Here you can fing the https://skyway.porsche.com/confluence/pages/viewpage.action?pageId=1315383379[GoBD Requirements].


== FA-01-01 Definition of Documents in the IT System

The necessary data for the integration of the SaaS solution for damage management are migrated to the damage management
Service. This data are the damage files (Schadensakte).

Document Responsible: <PERSON><PERSON> <EMAIL>
Assigned CSD-category: 1.2 Financial Documents (for tax purposes)

== Documentation (FA-01-02)

=== GA-03: Classification System for Documents (CSD)

==== FA-03-02: Retention

===== Anonymization

When cars are still in use by the Porsche AG, the damage files are still valuable and need to be provided by the DMS.
The related user data however, is not of interest anymore and can be anonymized.

.Constraint
When a closed damage file is retained for more than 15 years, but the car is not scraped or sold for more than 3 years,
the user data attached to the damage file is anonymized.


===== Deletion

When a car is not in use by the Porsche AG, the damage file can be irretrievable deleted.

.Constraint
When a closed damage file is retained for more than 15 years, AND the car is scraped or sold for more than 3 years,
the damage file is irretrievable.


=== GA-04: Implementation of a Legal Hold

==== FA-04-01: Identification of Documents

Documents are identified by their _DamageFileKey_. Currently, the _DamageFileKey_ can be obtained by querying the database
from an EC2 Bastion with an SQL tool. This will be unnecessary in the future, when the legal hold is put via the Fleet
Vehicle Manager (FVM).

==== FA-04-02: Assignment of Documents to Legal Holds


.Legal hold is assigned by the document responsible. Then the a copy of the document is stored in an S3 Bucket and a Object Lock Legal Hold is put on the document to prevent deletion or modification.
image::drawio/GoBD/assign-legal-hold.png[]


When the __DamageFileKey__s are identified, there is an API provided by the DMS.
The current openApi Specification can be found https://cicd.skyway.porsche.com/apim/api-providers/space-dms/-/blob/dev/api/legal-hold.yaml[here].

==== FA-04-03: Protection Against Modification and Deletion

.When legal hold is assigned to a document no updates are applied from Repairfix. The document is locked and cannot be changed or deleted. Even when the retention time is reached the document is neither deleted nor anonymized (see FA-03-03: Locking)
image::drawio/GoBD/lock-legal-hold.png[]



Documents got an assigned legal hold on them, are stored in an AWS S3 Bucket and an Object Lock legal hold is put on that
file. That prevents the file from being deleted or overwritten until the hold is explicitly removed. Since the bucket has versioning
enabled, one can delete the object, however they cannot permanently delete. Deleting a version enabled bucket object will
make the object as non current and create a new version with a delete marker. However if someone tries to delete the non
current object (action: permanently delete) , it will not be possible.

.The Damage Management Service stores the damage file in an AWS S3 bucket an puts an Object Lock Legal Hold on it. Only the service it self can release the Object Lock to delete the file.
image::drawio/GoBD/export-legal-hold.png[]

AWS S3 Bucket name:
`damage-management-service-legal-hold-{accountid}`


==== FA-04-04: Export

Damage files that are stored in the AWS S3 bucket for legal hold can be downloaded as Files from the bucket.

AWS S3 Bucket name:
`damage-management-service-legal-hold-{accountid}`

=== GA-07 Data integrity and data access

:leveloffset: +2
include::z1-z2-z3-access.adoc[]
:leveloffset: -2

=== GA-08: Archiving

NOTE: The IT system must ensure audit-proof archiving of tax-relevant data and documents for the retention period in
accordance with P10 and support the export of documents in accordance with FA-04-04 Export. Measures against loss,
modification and deletion must be taken (esp. Legal Hold GA-04).

.Damage Mangement Service stores the closed damage files in an AWS S3 Bucket with Object Lock Mode Compliance.
image::drawio/GoBD/archive.png[]

When a damage file is closed, it is said to be "final" and it has to be retained for 15 years. To ensure this the damage
file is archived in an AWS S3 bucket with Object Lock Mode Compliance. This mode prevents the modification or deletion
of a document by anybody, before the retention time is reached.

AWS S3 Bucket name:
`damage-management-service-archive-{accountId}`

==== Object Lock retention
Prevent objects from being deleted or overwritten for a fixed amount of time. Objects in damage-management-service-gobd-{accountId} are locked in GOVERNANCE mode as soon as they are uploaded for the specified retention time. However, You can perform a permanent DELETE operation on an object that's protected by GOVERNANCE retention mode even before the retention period is over. However, you must have the permissions to bypass the GOVERNANCE mode (s3:BypassGovernanceRetention). (https://repost.aws/knowledge-center/s3-object-lock-delete)

==== Expiration and Deletion
Expiration: The object with current version will be made noncurrent and generates a delete marker which becomes the current version. Delete marker is just another version of the object.

Deletion: Object will be permanently deleted asynchronously after expired.

====  Transition
Moves object to Glacier Flexible Retrieval (formerly Glacier) asynchronously.

image::GoBD/s3-lifecycle-policy.png[]

image::GoBD/s3-lifecycle-policy-deletion.png[]

NOTE: If there is a requirement where object can have multiple versions being uploaded to GoBD bucket eg. a damage file
is re-opened and then closed (not a business usecase currently), we would need to align what is the deletion time for
2 2the old versions. For now it is 1 day for all environments.

