/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
SELECT rdsdf.id                            as "id",
       rdsdf.employee_to_be_charged        as "employee_to_be_charged",
       rdsdf.deductible_amount             as "deductible_amount",
       rdsdf.claim_towards_employee_amount as "claim_towards_employee_amount",
       rdsdf.deductible_update             as "deductible_update",
       rdsdf.vehicle_vin                   as "vehicle_vin",
       rdsdf.vehicle_vguid                 as "vehicle_vguid",
       rdsdf.license_plate                 as "license_plate",
       rdsdf.creation_date                 as "creation_date",
       rdsdf.state                         as "state"
FROM "damage-management-service-athena-postgres-data-catalog"."emh_damage_management"."damage_file" as rdsdf
WHERE rdsdf.creation_date >= TIMESTAMP '2024-01-01'
  AND rdsdf.creation_date <= TIMESTAMP '2024-12-31'
  AND rdsdf.vehicle_vin = 'WP0ZZZ992PS241320'
  AND rdsdf.state = 'CLOSED'