/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

SELECT distinct union_table.*
FROM (SELECT rdsdf.id                            as "id",
             rdsdf.employee_to_be_charged        as "employee_to_be_charged",
             rdsdf.deductible_amount             as "deductible_amount",
             rdsdf.claim_towards_employee_amount as "claim_towards_employee_amount",
             rdsdf.deductible_update             as "deductible_update",
             rdsdf.vehicle_vin                   as "vehicle_vin",
             rdsdf.vehicle_vguid                 as "vehicle_vguid",
             rdsdf.license_plate                 as "license_plate",
             rdsdf.creation_date                 as "creation_date",
             rdsdf.state                         as "state"
      FROM "damage-management-service-athena-postgres-data-catalog"."emh_damage_management"."damage_file" as rdsdf

      UNION ALL

      SELECT s3df.id.value                                  as "id",
             s3df.employeetobecharged.value                 as "employee_to_be_charged",
             s3df.claimtowardsemployee.value                as "deductible_amount",
             s3df.deductible.value                          as "claim_towards_employee_amount",
             from_iso8601_timestamp(s3df.deductibleupdated) as "deductible_update",
             s3df.vehicle.vin                               as "vehicle_vin",
             s3df.vehicle.vguid                             as "vehicle_vguid",
             s3df.vehicle.licenseplate                      as "license_plate",
             from_iso8601_timestamp(s3df.creationdate)      as "creation_date",
             s3df.state                                     as "state"
      FROM "damage-management-service-archive"."damage_management_service_archive_938255451121" as s3df) as union_table
WHERE union_table.creation_date >= TIMESTAMP '2024-01-01'
  AND union_table.creation_date <= TIMESTAMP '2024-12-31'
  AND union_table.vehicle_vin = 'WP0ZZZ992PS241320'