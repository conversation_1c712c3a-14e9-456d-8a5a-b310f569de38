ifndef::imagesdir[:imagesdir: ../../../images]
:toc:
:numbered:

The GoBD requires that a tax auditor is provided with read access of three different types.

* Z1 - direct access
* Z2 - indirect access
* Z3 - data carrier transfer

All three types of access rely on the same approach to fetch the data.

.Data access types for the tax auditor.
image::drawio/GoBD/data-access.png[]

As shown in the diagram above, the data of the Damage Management Service is stored in the RDS database, and additionally
it is archived in an AWS S3 bucket. To access the data https://aws.amazon.com/de/athena/[AWS Athena] can be used to query
both sources.

== Getting Access

To get access a specific role needs to be granted:

* *dev*: AWSReservedSSO_Auditor_b3402812de67f777
* *staging*: AWSReservedSSO_Auditor_9c44eefaa2e5c0e0
* *prod*: AWSReservedSSO_Auditor_3a2ce69107b0b0ae

*_Z1 direct access_*

If the tax auditor requires direct access, he needs to be registered through the B2B portal of the Porsche AG. Then a
Porsche-AD-User accounts needs to be created and the mentioned AWS Auditor role needs to be assigned.

*_Z2 and Z3 indirect access and data carrier transfer_*

For the indirect access the mentioned AWS Auditor role needs to be assigned to a developer who already has a Porsche-AD-User.
The developer then assists the auditor querying the data or queries the required data, exports it and burns a CD/DVD-Rom
with the data.

== AWS Athena

*_How to get to Athena?_*

Logged in to the AWS console, Athena can either be found via the search bar or via _Services > Analytics > Athena_.

*_The Interface_*

.Screenshot of the AWS Athena interface.
image::GoBD/athena.png[]

(1) As first step it is necessary to select a Workgroup.

(2) Multiple Data sources are available. The data sources are the crawled catalogs (S3 archive bucket and the database
of the Damage Management Service).

(3) Database are the available schemas that can be queried for a given data source.

(4) Tables for the selected database are listed and can be further inspected.

(5) In the editor custom queries can be written.

(6) The queries are executed by pressing "Run".

(7) The query results are shown here and can be downloaded.

== Example Queries

=== Query the RDS Database

All relevant data should be contained in the RDS database. ince the data is relevant for the running processes it is
kept there, for the specified retention time.

.Example Query to query the RDS Database.
[source, sql, num]
....
include::snippets/query-rds.sql[]
....

The query above shows an example where it is possible to filter for a specific year (e.g. 2024):
[source, sql]
....
include::snippets/query-rds.sql[lines=17..18]
....

and for a specified vin (e.g. WP0ZZZ992PS241320):
[source, sql]
....
include::snippets/query-rds.sql[lines=19]
....

and for a specified state (e.g. CLOSED):
[source, sql]
....
include::snippets/query-rds.sql[lines=20]
....

=== Query the S3 Archive Bucket

The S3 bucket keeps the archived data. The only reason this data may differ from the data in the RDS database is, when
a damage file is reopened and not yet closed again. In that case the reopened damage file is present in the RDS database.
When the damage file is closed again, the archived version is updated.

.Example Query to query the archived data in the S3 bucket.
[source, sql, num]
....
include::snippets/query-s3.sql[]
....

The query above shows an example where it is possible to filter for a specific year (e.g. 2024):
[source, sql]
....
include::snippets/query-s3.sql[lines=17..18]
....

and for a specified vin (e.g. WP0ZZZ992PS241320):
[source, sql]
....
include::snippets/query-s3.sql[lines=19]
....

and for a specified state (e.g. CLOSED):
[source, sql]
....
include::snippets/query-s3.sql[lines=20]
....


=== Query RDS and S3 at the Same Time

It is possible to query both sources at the same time. The provided example makes a distinct select. In this case, rows
that have the same values for all columns, will be deduplicated. Without a filter on "state", you would get reopened
damage files and the archived versions at the same time.

.Example Query to query the union of S3 and RDS data.[[union]-query-example]
[source, sql]
....
include::snippets/query-union.sql[]
....
