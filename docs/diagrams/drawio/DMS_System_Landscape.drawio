<mxfile host="Electron" modified="2024-07-02T07:01:10.890Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="fRFSJdFOepWx6OEaGsX1" version="24.4.13" type="device" pages="9">
  <diagram id="C5RBs43oDa-KdzZeNtuy" name="PI2024.11.S2">
    <mxGraphModel dx="3218" dy="3520" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="WIyWlLk6GJQsqaUBKTNV-1" parent="WIyWlLk6GJQsqaUBKTNV-0" />
        <mxCell id="xJO2Gman_QYZqxzgfFD9-1" value="emh_damage_management" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#fff2cc;strokeColor=#d6b656;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-117.5" y="80" width="165" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#00CC00;strokeColor=#00CC00;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-2" target="xJO2Gman_QYZqxzgfFD9-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-12" value="Damage-Reports" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFD966;" parent="xJO2Gman_QYZqxzgfFD9-11" vertex="1" connectable="0">
          <mxGeometry x="-0.4296" y="-1" relative="1" as="geometry">
            <mxPoint x="43" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-2" value="Repairfix" style="rounded=0;whiteSpace=wrap;html=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-540" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-3" value="HCM P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="190" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#FF6666;entryX=0.733;entryY=0.99;entryDx=0;entryDy=0;entryPerimeter=0;exitX=-0.022;exitY=0.489;exitDx=0;exitDy=0;exitPerimeter=0;fontColor=#FF6666;startArrow=none;startFill=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-4" target="xJO2Gman_QYZqxzgfFD9-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="19.74000000000001" y="361.30000000000007" as="targetPoint" />
            <Array as="points">
              <mxPoint x="225" y="399" />
              <mxPoint x="90" y="400" />
              <mxPoint x="19" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-20" value="internalOrderNumber" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFD966;" parent="xJO2Gman_QYZqxzgfFD9-19" vertex="1" connectable="0">
          <mxGeometry x="-0.7861" y="2" relative="1" as="geometry">
            <mxPoint x="-85" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-4" value="PACE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="270" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-7" value="PVCC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-95" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-8" target="xJO2Gman_QYZqxzgfFD9-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-16" value="DamageFiles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xJO2Gman_QYZqxzgfFD9-15" vertex="1" connectable="0">
          <mxGeometry x="-0.2424" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-8" target="xJO2Gman_QYZqxzgfFD9-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-18" value="DamageReport(Deductible)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xJO2Gman_QYZqxzgfFD9-17" vertex="1" connectable="0">
          <mxGeometry x="-0.497" relative="1" as="geometry">
            <mxPoint x="97" y="-100" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF6666;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-8" target="xJO2Gman_QYZqxzgfFD9-7" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-39" y="350" as="sourcePoint" />
            <mxPoint x="20" y="440" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="400" />
              <mxPoint x="-35" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-22" value="monetaryBenefit" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFD966;" parent="xJO2Gman_QYZqxzgfFD9-21" vertex="1" connectable="0">
          <mxGeometry x="-0.3912" y="-2" relative="1" as="geometry">
            <mxPoint x="1" y="41" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-8" value="Damage-Management-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-150" y="250" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-29" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fillColor=#f8cecc;strokeColor=#FF6666;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-9" target="xJO2Gman_QYZqxzgfFD9-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-315" y="440" />
              <mxPoint x="-93" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-30" value="(Subsidiary)Employees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" parent="xJO2Gman_QYZqxzgfFD9-29" vertex="1" connectable="0">
          <mxGeometry x="-0.0753" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-9" value="User-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=3;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-430" y="470" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-10" value="Vehicle-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;dashed=1;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-480" y="-30" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-8" target="xJO2Gman_QYZqxzgfFD9-2" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-380" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="380" />
              <mxPoint x="-480" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-14" value="Vehicles, Fleets, VehicleResponsiblePersons" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xJO2Gman_QYZqxzgfFD9-13" vertex="1" connectable="0">
          <mxGeometry x="0.0842" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-25" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FF6666;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-23" target="xJO2Gman_QYZqxzgfFD9-9" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-26" value="HR-MiniStamm" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" parent="xJO2Gman_QYZqxzgfFD9-25" vertex="1" connectable="0">
          <mxGeometry x="0.2573" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-23" value="HR P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-375" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#FF6666;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-10" target="xJO2Gman_QYZqxzgfFD9-8" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-370" y="-5" as="sourcePoint" />
            <mxPoint x="-212.5" y="225" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-365" y="220" />
              <mxPoint x="-93" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-28" value="Vehicles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" parent="xJO2Gman_QYZqxzgfFD9-27" vertex="1" connectable="0">
          <mxGeometry x="-0.6906" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-31" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#00CC00;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="120" as="sourcePoint" />
            <mxPoint x="-800" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-32" value="Ready for PROD" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-756" y="106" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-33" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FFD966;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="160" as="sourcePoint" />
            <mxPoint x="-800" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-34" value="(partially) specified but not implemented / ready for staging" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-756" y="146" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-35" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FF6666;" parent="WIyWlLk6GJQsqaUBKTNV-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="212" as="sourcePoint" />
            <mxPoint x="-800" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xJO2Gman_QYZqxzgfFD9-36" value="Unspecified / Unavailable" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-756" y="198" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-0" value="emh_user" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#fff2cc;strokeColor=#d6b656;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-720" y="480" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-1" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00CC00;" parent="WIyWlLk6GJQsqaUBKTNV-1" source="xJO2Gman_QYZqxzgfFD9-9" target="4P0ZYr6HXUUKbqMsPtxx-0" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-2" value="Employees, SubsidiaryEmployees,&lt;br&gt;&lt;font color=&quot;#ffd966&quot;&gt;BusinessPartner&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="4P0ZYr6HXUUKbqMsPtxx-1" vertex="1" connectable="0">
          <mxGeometry x="-0.121" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-4" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-800" y="240" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-5" value="PI 2024.11 - S2" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-756" y="240" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-7" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-390" y="280" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-8" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-413" y="480" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-9" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-610" y="520" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-10" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="-680" y="470" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="4P0ZYr6HXUUKbqMsPtxx-11" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="WIyWlLk6GJQsqaUBKTNV-1" vertex="1">
          <mxGeometry x="170" y="280" width="40" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="PI2024.11.S3" id="1UXbb5CxC_SgvboBCpu-">
    <mxGraphModel dx="2901" dy="2267" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-0" />
        <mxCell id="xbvl31wPPXYIFcGB-fIj-1" parent="xbvl31wPPXYIFcGB-fIj-0" />
        <mxCell id="xbvl31wPPXYIFcGB-fIj-2" value="emh_damage_management" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-117.5" y="80" width="165" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#00CC00;strokeColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-5" target="xbvl31wPPXYIFcGB-fIj-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-4" value="&lt;font color=&quot;#00cc00&quot;&gt;Damage-Reports&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#97D077;" parent="xbvl31wPPXYIFcGB-fIj-3" vertex="1" connectable="0">
          <mxGeometry x="-0.4296" y="-1" relative="1" as="geometry">
            <mxPoint x="43" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-5" value="Repairfix" style="rounded=0;whiteSpace=wrap;html=1;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-540" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-6" value="HCM P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="190" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#FFD966;entryX=0.733;entryY=0.99;entryDx=0;entryDy=0;entryPerimeter=0;exitX=-0.022;exitY=0.489;exitDx=0;exitDy=0;exitPerimeter=0;fontColor=#FF6666;startArrow=none;startFill=0;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-9" target="xbvl31wPPXYIFcGB-fIj-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="19.74000000000001" y="361.30000000000007" as="targetPoint" />
            <Array as="points">
              <mxPoint x="225" y="399" />
              <mxPoint x="90" y="400" />
              <mxPoint x="19" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-8" value="internalOrderNumber" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFD966;" parent="xbvl31wPPXYIFcGB-fIj-7" vertex="1" connectable="0">
          <mxGeometry x="-0.7861" y="2" relative="1" as="geometry">
            <mxPoint x="-85" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-9" value="PACE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="270" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-10" value="PVCC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-95" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-17" target="xbvl31wPPXYIFcGB-fIj-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-12" value="DamageFiles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-11" vertex="1" connectable="0">
          <mxGeometry x="-0.2424" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-17" target="xbvl31wPPXYIFcGB-fIj-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-14" value="DamageReport(Deductible)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-13" vertex="1" connectable="0">
          <mxGeometry x="-0.497" relative="1" as="geometry">
            <mxPoint x="97" y="-100" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-17" target="xbvl31wPPXYIFcGB-fIj-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-39" y="350" as="sourcePoint" />
            <mxPoint x="20" y="440" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="400" />
              <mxPoint x="-35" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-16" value="monetaryBenefit" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-15" vertex="1" connectable="0">
          <mxGeometry x="-0.3912" y="-2" relative="1" as="geometry">
            <mxPoint x="1" y="41" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-17" value="Damage-Management-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-150" y="250" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fillColor=#f8cecc;strokeColor=#FF6666;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-20" target="xbvl31wPPXYIFcGB-fIj-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-315" y="440" />
              <mxPoint x="-93" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-19" value="(Subsidiary)Employees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" parent="xbvl31wPPXYIFcGB-fIj-18" vertex="1" connectable="0">
          <mxGeometry x="-0.0753" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-20" value="User-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;strokeWidth=3;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-430" y="470" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-21" value="Vehicle-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;dashed=1;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-480" y="-30" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-17" target="xbvl31wPPXYIFcGB-fIj-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-380" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="380" />
              <mxPoint x="-480" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-23" value="Vehicles, Fleets, VehicleResponsiblePersons(-updates)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-22" vertex="1" connectable="0">
          <mxGeometry x="0.0842" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FFD966;fontColor=#FFD966;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-26" target="xbvl31wPPXYIFcGB-fIj-20" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-25" value="HR-MiniStamm" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFD966;" parent="xbvl31wPPXYIFcGB-fIj-24" vertex="1" connectable="0">
          <mxGeometry x="0.2573" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-26" value="HR P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-375" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#FF6666;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-21" target="xbvl31wPPXYIFcGB-fIj-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-370" y="-5" as="sourcePoint" />
            <mxPoint x="-212.5" y="225" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-365" y="220" />
              <mxPoint x="-93" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-28" value="Vehicles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" parent="xbvl31wPPXYIFcGB-fIj-27" vertex="1" connectable="0">
          <mxGeometry x="-0.6906" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-29" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="120" as="sourcePoint" />
            <mxPoint x="-800" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-30" value="Ready for PROD" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-756" y="106" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-31" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FFD966;" parent="xbvl31wPPXYIFcGB-fIj-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="160" as="sourcePoint" />
            <mxPoint x="-800" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-32" value="(partially) specified but not implemented / ready for staging" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-756" y="146" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-33" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FF6666;" parent="xbvl31wPPXYIFcGB-fIj-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="212" as="sourcePoint" />
            <mxPoint x="-800" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-34" value="Unspecified / Unavailable" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-756" y="198" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-35" value="emh_user" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#fff2cc;strokeColor=#d6b656;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-720" y="480" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-1" source="xbvl31wPPXYIFcGB-fIj-20" target="xbvl31wPPXYIFcGB-fIj-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-37" value="Employees, SubsidiaryEmployees,&lt;br&gt;&lt;font color=&quot;#ffd966&quot;&gt;BusinessPartner&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="xbvl31wPPXYIFcGB-fIj-36" vertex="1" connectable="0">
          <mxGeometry x="-0.121" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-38" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-800" y="240" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-39" value="PI 2024.11 - S3" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-756" y="240" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-41" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-55" y="446" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-42" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-350" y="650" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="xbvl31wPPXYIFcGB-fIj-44" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="25" y="120" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="BYaGeYFQ7XRuQ0c6VkzI-0" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-255" y="270" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="BYaGeYFQ7XRuQ0c6VkzI-1" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="140" y="350" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="BYaGeYFQ7XRuQ0c6VkzI-2" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="xbvl31wPPXYIFcGB-fIj-1" vertex="1">
          <mxGeometry x="-215" y="330" width="40" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="PI2024.17.S1" id="8SXJhhUqhXv678ifVWrk">
    <mxGraphModel dx="2901" dy="2365" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-0" />
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-1" parent="CgkhPnYeLcVbqW4DfFzM-0" />
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-2" value="emh_damage_management" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=3;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-117.5" y="80" width="165" height="100" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#00CC00;strokeColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-5" target="CgkhPnYeLcVbqW4DfFzM-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-4" value="&lt;font color=&quot;#00cc00&quot;&gt;Damage-Reports&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#97D077;" parent="CgkhPnYeLcVbqW4DfFzM-3" vertex="1" connectable="0">
          <mxGeometry x="-0.4296" y="-1" relative="1" as="geometry">
            <mxPoint x="43" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-5" value="Repairfix" style="rounded=0;whiteSpace=wrap;html=1;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-540" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-6" value="HCM P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="190" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#FFD966;entryX=0.733;entryY=0.99;entryDx=0;entryDy=0;entryPerimeter=0;exitX=-0.022;exitY=0.489;exitDx=0;exitDy=0;exitPerimeter=0;fontColor=#FF6666;startArrow=none;startFill=0;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-9" target="CgkhPnYeLcVbqW4DfFzM-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="19.74000000000001" y="361.30000000000007" as="targetPoint" />
            <Array as="points">
              <mxPoint x="225" y="399" />
              <mxPoint x="90" y="400" />
              <mxPoint x="19" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-8" value="internalOrderNumber" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-7" vertex="1" connectable="0">
          <mxGeometry x="-0.7861" y="2" relative="1" as="geometry">
            <mxPoint x="-85" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-9" value="PACE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="270" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-10" value="PVCC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-95" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-17" target="CgkhPnYeLcVbqW4DfFzM-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-12" value="DamageFiles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-11" vertex="1" connectable="0">
          <mxGeometry x="-0.2424" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-17" target="CgkhPnYeLcVbqW4DfFzM-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-14" value="DamageReport(Deductible)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-13" vertex="1" connectable="0">
          <mxGeometry x="-0.497" relative="1" as="geometry">
            <mxPoint x="97" y="-100" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-17" target="CgkhPnYeLcVbqW4DfFzM-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-39" y="350" as="sourcePoint" />
            <mxPoint x="20" y="440" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="400" />
              <mxPoint x="-35" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-16" value="monetaryBenefit" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-15" vertex="1" connectable="0">
          <mxGeometry x="-0.3912" y="-2" relative="1" as="geometry">
            <mxPoint x="1" y="41" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-17" value="Damage-Management-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-150" y="250" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fillColor=#f8cecc;strokeColor=#FF6666;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-20" target="CgkhPnYeLcVbqW4DfFzM-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-315" y="440" />
              <mxPoint x="-93" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-19" value="Pag|Subsidiary-Employees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-18" vertex="1" connectable="0">
          <mxGeometry x="-0.0753" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-20" value="User-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-430" y="470" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-21" value="Vehicle-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;dashed=1;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-480" y="-30" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-17" target="CgkhPnYeLcVbqW4DfFzM-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-380" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="380" />
              <mxPoint x="-480" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-23" value="Vehicles, Fleets, VehicleResponsiblePersons(-updates)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-22" vertex="1" connectable="0">
          <mxGeometry x="0.0842" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FFD966;fontColor=#FFD966;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-26" target="CgkhPnYeLcVbqW4DfFzM-20" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-25" value="HR-MiniStamm" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-24" vertex="1" connectable="0">
          <mxGeometry x="0.2573" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-26" value="HR P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-375" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#FF6666;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-21" target="CgkhPnYeLcVbqW4DfFzM-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-370" y="-5" as="sourcePoint" />
            <mxPoint x="-212.5" y="225" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-365" y="220" />
              <mxPoint x="-93" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-28" value="Vehicles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" parent="CgkhPnYeLcVbqW4DfFzM-27" vertex="1" connectable="0">
          <mxGeometry x="-0.6906" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-29" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="120" as="sourcePoint" />
            <mxPoint x="-800" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-30" value="Ready for PROD" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-756" y="106" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-31" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FFD966;" parent="CgkhPnYeLcVbqW4DfFzM-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="160" as="sourcePoint" />
            <mxPoint x="-800" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-32" value="(partially) specified but not implemented / ready for staging" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-756" y="146" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-33" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FF6666;" parent="CgkhPnYeLcVbqW4DfFzM-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="212" as="sourcePoint" />
            <mxPoint x="-800" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-34" value="Unspecified / Unavailable" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-756" y="198" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-35" value="emh_user" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#f8cecc;strokeColor=#b85450;strokeWidth=3;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-720" y="480" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-1" source="CgkhPnYeLcVbqW4DfFzM-20" target="CgkhPnYeLcVbqW4DfFzM-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-37" value="Pag|SubsidiaryEmployees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="CgkhPnYeLcVbqW4DfFzM-36" vertex="1" connectable="0">
          <mxGeometry x="-0.121" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-38" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-800" y="240" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-39" value="PI 2024.17 - S1" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-756" y="240" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-40" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-170" y="450" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="CgkhPnYeLcVbqW4DfFzM-41" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-270" y="610" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="E6WTHu-NjivJzBHtO2h9-1" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-440" y="460" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="E6WTHu-NjivJzBHtO2h9-2" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="-560" y="530" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="E6WTHu-NjivJzBHtO2h9-3" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="CgkhPnYeLcVbqW4DfFzM-1" vertex="1">
          <mxGeometry x="130" y="410" width="40" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="PI2024.17.S2" id="Gu5V42nsdhk2ZXQS_3sD">
    <mxGraphModel dx="3781" dy="2365" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-0" />
        <mxCell id="k_xTlPYL07HUzxWQzTmC-1" parent="k_xTlPYL07HUzxWQzTmC-0" />
        <mxCell id="k_xTlPYL07HUzxWQzTmC-2" value="emh_damage_management" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-117.5" y="80" width="165" height="100" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#00CC00;strokeColor=#00CC00;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-5" target="k_xTlPYL07HUzxWQzTmC-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-4" value="&lt;font color=&quot;#00cc00&quot;&gt;Damage-Reports&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#97D077;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-3">
          <mxGeometry x="-0.4296" y="-1" relative="1" as="geometry">
            <mxPoint x="43" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-5" value="Repairfix" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-540" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-6" value="HCM P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="190" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#FF6666;entryX=0.733;entryY=0.99;entryDx=0;entryDy=0;entryPerimeter=0;exitX=-0.022;exitY=0.489;exitDx=0;exitDy=0;exitPerimeter=0;fontColor=#FF6666;startArrow=none;startFill=0;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-9" target="k_xTlPYL07HUzxWQzTmC-17">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="19.74000000000001" y="361.30000000000007" as="targetPoint" />
            <Array as="points">
              <mxPoint x="225" y="399" />
              <mxPoint x="90" y="400" />
              <mxPoint x="19" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-8" value="internalOrderNumber" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFB570;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-7">
          <mxGeometry x="-0.7861" y="2" relative="1" as="geometry">
            <mxPoint x="-85" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-9" value="PACE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="270" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-10" value="PVCC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-95" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-17" target="k_xTlPYL07HUzxWQzTmC-2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-12" value="DamageFiles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-11">
          <mxGeometry x="-0.2424" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-17" target="k_xTlPYL07HUzxWQzTmC-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-14" value="DamageReport(Deductible)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-13">
          <mxGeometry x="-0.497" relative="1" as="geometry">
            <mxPoint x="97" y="-100" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-17" target="k_xTlPYL07HUzxWQzTmC-10">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-39" y="350" as="sourcePoint" />
            <mxPoint x="20" y="440" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="400" />
              <mxPoint x="-35" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-16" value="monetaryBenefit" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-15">
          <mxGeometry x="-0.3912" y="-2" relative="1" as="geometry">
            <mxPoint x="1" y="41" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-17" value="Damage-Management-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-150" y="250" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fillColor=#f8cecc;strokeColor=#FFB570;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-20" target="k_xTlPYL07HUzxWQzTmC-17">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-315" y="440" />
              <mxPoint x="-93" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-19" value="Pag|Subsidiary-Employees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-18">
          <mxGeometry x="-0.0753" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-20" value="User-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-430" y="470" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-21" value="Vehicle-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;dashed=1;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-480" y="-30" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-17" target="k_xTlPYL07HUzxWQzTmC-5">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-380" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="380" />
              <mxPoint x="-480" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-23" value="Vehicles, Fleets, VehicleResponsiblePersons(-updates)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-22">
          <mxGeometry x="0.0842" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#FFD966;fontColor=#FFD966;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-26" target="k_xTlPYL07HUzxWQzTmC-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-25" value="HR-MiniStamm" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-24">
          <mxGeometry x="0.2573" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-26" value="HR P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-375" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#FF6666;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-21" target="k_xTlPYL07HUzxWQzTmC-17">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-370" y="-5" as="sourcePoint" />
            <mxPoint x="-212.5" y="225" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-365" y="220" />
              <mxPoint x="-93" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-28" value="Vehicles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-27">
          <mxGeometry x="-0.6906" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-29" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#00CC00;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="120" as="sourcePoint" />
            <mxPoint x="-800" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-30" value="Ready for PROD" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-756" y="106" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-31" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FFD966;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="160" as="sourcePoint" />
            <mxPoint x="-800" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-32" value="(partially) specified but not implemented / ready for staging" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-756" y="146" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-33" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FF6666;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="212" as="sourcePoint" />
            <mxPoint x="-800" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-34" value="Unspecified / Unavailable" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-756" y="198" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-35" value="emh_user" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-720" y="480" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00CC00;" edge="1" parent="k_xTlPYL07HUzxWQzTmC-1" source="k_xTlPYL07HUzxWQzTmC-20" target="k_xTlPYL07HUzxWQzTmC-35">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-37" value="Pag|SubsidiaryEmployees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="k_xTlPYL07HUzxWQzTmC-36">
          <mxGeometry x="-0.121" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-38" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-800" y="240" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-39" value="PI 2024.17 - S2" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-756" y="240" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-40" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-170" y="450" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-41" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-230" y="545" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-42" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="60" y="320" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-43" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="-750" y="530" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="k_xTlPYL07HUzxWQzTmC-44" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="k_xTlPYL07HUzxWQzTmC-1">
          <mxGeometry x="25" y="80" width="40" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="PI2024.17.S3" id="tkbH_Inv2nDZLzpTjcP9">
    <mxGraphModel dx="2901" dy="2365" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-0" />
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-1" parent="P-fBOY_n_NZP3ixBTLzd-0" />
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-2" value="emh_damage_management" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-117.5" y="80" width="165" height="100" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#00CC00;strokeColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-5" target="P-fBOY_n_NZP3ixBTLzd-17" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-4" value="&lt;font color=&quot;#00cc00&quot;&gt;Damage-Reports,&lt;br&gt;Deductible,&lt;br&gt;ClaimTowardsEmployee&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#97D077;" parent="P-fBOY_n_NZP3ixBTLzd-3" vertex="1" connectable="0">
          <mxGeometry x="-0.4296" y="-1" relative="1" as="geometry">
            <mxPoint x="43" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-5" value="Repairfix" style="rounded=0;whiteSpace=wrap;html=1;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-540" y="270" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-6" value="HCM P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="190" y="80" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#FF6666;entryX=0.733;entryY=0.99;entryDx=0;entryDy=0;entryPerimeter=0;exitX=-0.022;exitY=0.489;exitDx=0;exitDy=0;exitPerimeter=0;fontColor=#FF6666;startArrow=none;startFill=0;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-9" target="P-fBOY_n_NZP3ixBTLzd-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="19.74000000000001" y="361.30000000000007" as="targetPoint" />
            <Array as="points">
              <mxPoint x="225" y="399" />
              <mxPoint x="90" y="400" />
              <mxPoint x="19" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-8" value="internalOrderNumber" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFB570;" parent="P-fBOY_n_NZP3ixBTLzd-7" vertex="1" connectable="0">
          <mxGeometry x="-0.7861" y="2" relative="1" as="geometry">
            <mxPoint x="-85" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-9" value="PACE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="270" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-10" value="PVCC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-95" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-17" target="P-fBOY_n_NZP3ixBTLzd-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-12" value="DamageFiles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-11" vertex="1" connectable="0">
          <mxGeometry x="-0.2424" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-17" target="P-fBOY_n_NZP3ixBTLzd-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-14" value="DamageReport(Deductible)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-13" vertex="1" connectable="0">
          <mxGeometry x="-0.497" relative="1" as="geometry">
            <mxPoint x="97" y="-100" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-17" target="P-fBOY_n_NZP3ixBTLzd-10" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-39" y="350" as="sourcePoint" />
            <mxPoint x="20" y="440" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="400" />
              <mxPoint x="-35" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-16" value="monetaryBenefit" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-15" vertex="1" connectable="0">
          <mxGeometry x="-0.3912" y="-2" relative="1" as="geometry">
            <mxPoint x="1" y="41" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-17" value="Damage-Management-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-150" y="250" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=1;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fillColor=#f8cecc;strokeColor=#97D077;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-20" target="P-fBOY_n_NZP3ixBTLzd-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-315" y="440" />
              <mxPoint x="-93" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-19" value="Pag|Subsidiary-Employees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-18" vertex="1" connectable="0">
          <mxGeometry x="-0.0753" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-20" value="User-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-430" y="470" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-21" value="Vehicle-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;dashed=1;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-480" y="-30" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-17" target="P-fBOY_n_NZP3ixBTLzd-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-380" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="380" />
              <mxPoint x="-480" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-23" value="Vehicles, Fleets, VehicleResponsiblePersons(-updates)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-22" vertex="1" connectable="0">
          <mxGeometry x="0.0842" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#82b366;fontColor=#FFD966;fillColor=#d5e8d4;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-26" target="P-fBOY_n_NZP3ixBTLzd-20" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-25" value="HR-MiniStamm" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-24" vertex="1" connectable="0">
          <mxGeometry x="0.2573" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-26" value="HR P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-375" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#FF6666;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-21" target="P-fBOY_n_NZP3ixBTLzd-17" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-370" y="-5" as="sourcePoint" />
            <mxPoint x="-212.5" y="225" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-365" y="220" />
              <mxPoint x="-93" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-28" value="Vehicles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" parent="P-fBOY_n_NZP3ixBTLzd-27" vertex="1" connectable="0">
          <mxGeometry x="-0.6906" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-29" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="120" as="sourcePoint" />
            <mxPoint x="-800" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-30" value="Ready for PROD" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-756" y="106" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-31" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FFD966;" parent="P-fBOY_n_NZP3ixBTLzd-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="160" as="sourcePoint" />
            <mxPoint x="-800" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-32" value="(partially) specified but not implemented / ready for staging" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-756" y="146" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-33" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FF6666;" parent="P-fBOY_n_NZP3ixBTLzd-1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="212" as="sourcePoint" />
            <mxPoint x="-800" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-34" value="Unspecified / Unavailable" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-756" y="198" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-35" value="emh_user" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-720" y="480" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-1" source="P-fBOY_n_NZP3ixBTLzd-20" target="P-fBOY_n_NZP3ixBTLzd-35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-37" value="Pag|SubsidiaryEmployees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" parent="P-fBOY_n_NZP3ixBTLzd-36" vertex="1" connectable="0">
          <mxGeometry x="-0.121" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-38" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-800" y="240" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-39" value="PI 2024.17 - S3" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-756" y="240" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-40" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-170" y="450" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-41" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-230" y="545" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="P-fBOY_n_NZP3ixBTLzd-42" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="200" y="250" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="fmalyIjgKdfeAhu571Jq-0" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-260" y="260" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="GTn3R4DU3Rpfithp_v_b-0" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" parent="P-fBOY_n_NZP3ixBTLzd-1" vertex="1">
          <mxGeometry x="-350" y="640" width="40" height="40" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram name="PI2024.23.S1" id="pahOcGrhatDMemDjGrYL">
    <mxGraphModel dx="2555" dy="2166" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="rQIXcqdT13QJ9u93eAk--0" />
        <mxCell id="rQIXcqdT13QJ9u93eAk--1" parent="rQIXcqdT13QJ9u93eAk--0" />
        <mxCell id="rQIXcqdT13QJ9u93eAk--2" value="emh_damage_management" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-27.5" y="-110" width="165" height="100" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fontColor=#00CC00;strokeColor=#00CC00;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--5" target="rQIXcqdT13QJ9u93eAk--17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--4" value="&lt;font color=&quot;#00cc00&quot;&gt;Damage-Reports,&lt;br&gt;Deductible,&lt;br&gt;ClaimTowardsEmployee&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#97D077;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--3">
          <mxGeometry x="-0.4296" y="-1" relative="1" as="geometry">
            <mxPoint x="43" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--5" value="Repairfix" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-540" y="215" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--6" value="HCM P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="510" y="215" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;fillColor=#f8cecc;strokeColor=#FF6666;entryX=0.733;entryY=0.99;entryDx=0;entryDy=0;entryPerimeter=0;exitX=-0.022;exitY=0.489;exitDx=0;exitDy=0;exitPerimeter=0;fontColor=#FF6666;startArrow=none;startFill=0;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--9" target="rQIXcqdT13QJ9u93eAk--17">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="400" y="330" as="sourcePoint" />
            <mxPoint x="19.74000000000001" y="361.30000000000007" as="targetPoint" />
            <Array as="points">
              <mxPoint x="225" y="399" />
              <mxPoint x="90" y="400" />
              <mxPoint x="19" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--8" value="internalOrderNumber" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FFB570;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--7">
          <mxGeometry x="-0.7861" y="2" relative="1" as="geometry">
            <mxPoint x="-85" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--9" value="PACE" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="270" y="370" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--10" value="PVCC" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-95" y="500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--17" target="rQIXcqdT13QJ9u93eAk--2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--12" value="DamageFiles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--11">
          <mxGeometry x="-0.2424" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;strokeColor=#00CC00;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--17" target="rQIXcqdT13QJ9u93eAk--6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--14" value="DamageReport(Deductible)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--13">
          <mxGeometry x="-0.497" relative="1" as="geometry">
            <mxPoint x="48" y="-17" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#00CC00;entryX=0.5;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--17" target="rQIXcqdT13QJ9u93eAk--10">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-39" y="350" as="sourcePoint" />
            <mxPoint x="20" y="440" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="400" />
              <mxPoint x="-35" y="400" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--16" value="monetaryBenefit" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--15">
          <mxGeometry x="-0.3912" y="-2" relative="1" as="geometry">
            <mxPoint x="1" y="41" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--17" value="Damage-Management-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-150" y="140" width="410" height="210" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.175;entryY=0.991;entryDx=0;entryDy=0;exitX=0.5;exitY=0;exitDx=0;exitDy=0;fillColor=#f8cecc;strokeColor=#97D077;entryPerimeter=0;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--20" target="rQIXcqdT13QJ9u93eAk--17">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="-315" y="440" />
              <mxPoint x="-78" y="440" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--19" value="Pag|Subsidiary-Employees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--18">
          <mxGeometry x="-0.0753" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--20" value="User-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-430" y="470" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="AlyFDCPkM4aLVFwV7V9y-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;strokeColor=#FF6666;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--21" target="rQIXcqdT13QJ9u93eAk--17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AlyFDCPkM4aLVFwV7V9y-6" value="&lt;font color=&quot;#ff6666&quot;&gt;scrapped/sold&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="AlyFDCPkM4aLVFwV7V9y-5">
          <mxGeometry x="-0.5063" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--21" value="Vehicle-Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;dashed=1;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-540" y="-119.25" width="230" height="100" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;strokeColor=#00CC00;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--17" target="rQIXcqdT13QJ9u93eAk--5">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-380" y="340" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-35" y="380" />
              <mxPoint x="-480" y="380" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--23" value="Vehicles, Fleets, VehicleResponsiblePersons(-updates)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--22">
          <mxGeometry x="0.0842" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeColor=#82b366;fontColor=#FFD966;fillColor=#d5e8d4;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--26" target="rQIXcqdT13QJ9u93eAk--20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--25" value="HR-MiniStamm" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--24">
          <mxGeometry x="0.2573" y="-1" relative="1" as="geometry">
            <mxPoint x="-1" y="5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--26" value="HR P08" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-375" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;strokeColor=#FF6666;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--21" target="rQIXcqdT13QJ9u93eAk--17">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="-370" y="-5" as="sourcePoint" />
            <mxPoint x="-212.5" y="225" as="targetPoint" />
            <Array as="points">
              <mxPoint x="-425" y="80" />
              <mxPoint x="-47" y="80" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--28" value="Vehicles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#FF6666;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--27">
          <mxGeometry x="-0.6906" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--29" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#00CC00;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="120" as="sourcePoint" />
            <mxPoint x="-800" y="120" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--30" value="Ready for PROD" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-756" y="106" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--31" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FFD966;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="160" as="sourcePoint" />
            <mxPoint x="-800" y="160" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--32" value="(partially) specified but not implemented / ready for staging" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-756" y="146" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--33" value="" style="endArrow=none;html=1;rounded=0;strokeColor=#FF6666;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-760" y="212" as="sourcePoint" />
            <mxPoint x="-800" y="212" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--34" value="Unspecified / Unavailable" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-756" y="198" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--35" value="emh_user" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;fillColor=#d5e8d4;strokeColor=#82b366;strokeWidth=3;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-720" y="480" width="60" height="80" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00CC00;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--20" target="rQIXcqdT13QJ9u93eAk--35">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--37" value="Pag|SubsidiaryEmployees" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontColor=#00CC00;" vertex="1" connectable="0" parent="rQIXcqdT13QJ9u93eAk--36">
          <mxGeometry x="-0.121" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--38" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-800" y="240" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--39" value="PI 2024.23 - S1" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-756" y="240" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--42" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="440" y="200" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--43" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-260" y="260" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="rQIXcqdT13QJ9u93eAk--44" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="-350" y="640" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="hSjRsVrRz0K4ncZQmoNW-1" value="LegalHold" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="110" y="155" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="hSjRsVrRz0K4ncZQmoNW-2" value="Legal Hold" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.bucket_with_objects;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="390" y="-100" width="60" height="61.5" as="geometry" />
        </mxCell>
        <mxCell id="hSjRsVrRz0K4ncZQmoNW-3" value="Archiving" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.bucket_with_objects;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="370" y="268.5" width="60" height="61.5" as="geometry" />
        </mxCell>
        <mxCell id="hSjRsVrRz0K4ncZQmoNW-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.06;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#fff2cc;strokeColor=#FFB570;exitX=0.75;exitY=0;exitDx=0;exitDy=0;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="rQIXcqdT13QJ9u93eAk--17" target="hSjRsVrRz0K4ncZQmoNW-2">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="158" y="-70" />
              <mxPoint x="160" y="-70" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="AlyFDCPkM4aLVFwV7V9y-1" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="414" y="290" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AlyFDCPkM4aLVFwV7V9y-2" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="430" y="-89.25" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AlyFDCPkM4aLVFwV7V9y-3" value="" style="verticalLabelPosition=bottom;verticalAlign=top;html=1;shape=mxgraph.basic.smiley;fillColor=#1ba1e2;strokeColor=#006EAF;fontColor=#ffffff;" vertex="1" parent="rQIXcqdT13QJ9u93eAk--1">
          <mxGeometry x="200" y="193" width="40" height="40" as="geometry" />
        </mxCell>
        <mxCell id="AlyFDCPkM4aLVFwV7V9y-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;entryPerimeter=0;strokeColor=#00CC00;" edge="1" parent="rQIXcqdT13QJ9u93eAk--1" source="hSjRsVrRz0K4ncZQmoNW-1" target="rQIXcqdT13QJ9u93eAk--2">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="AlyFDCPkM4aLVFwV7V9y-9" value="&lt;font color=&quot;#00cc00&quot;&gt;LegalHold&lt;/font&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="AlyFDCPkM4aLVFwV7V9y-8">
          <mxGeometry x="-0.1148" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="chSzUeResifcAXKf0iBz" name="DMS - LegalHold">
    <mxGraphModel dx="2954" dy="1196" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="3MEumQARUUYYfkFbGQGP-1" value="DMS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontColor=#333333;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="300" y="60" width="1290" height="1130" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-4" target="3MEumQARUUYYfkFbGQGP-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-9" value="applyLeagalHold()" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-8">
          <mxGeometry x="-0.2756" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-4" value="Alon" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="110" y="255" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-5" value="/legal-hold/damage-files" style="fontStyle=0;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=middle;spacingLeft=2;" vertex="1" parent="1">
          <mxGeometry x="290" y="270" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=classic;endFill=1;exitX=0.051;exitY=0.488;exitDx=0;exitDy=0;exitPerimeter=0;dashed=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-14" target="3MEumQARUUYYfkFbGQGP-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-12" value="&amp;lt;&amp;lt;implements&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-11">
          <mxGeometry x="-0.1533" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-17" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-14" target="3MEumQARUUYYfkFbGQGP-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-18" value="calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-17">
          <mxGeometry x="-0.0533" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-14" target="3MEumQARUUYYfkFbGQGP-16">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="890" y="205" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-36" value="&amp;lt;&amp;lt;dependency&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-35">
          <mxGeometry x="0.1731" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-14" value="&lt;b&gt;legal-hold-in-adapter&lt;/b&gt;" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="610" y="180" width="140" height="50" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-16" value="&lt;b&gt;legal-hold&lt;/b&gt;" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="350" width="500" height="310" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-19" value="ApplyLegalHoldUseCase" style="fontStyle=0;labelPosition=right;verticalLabelPosition=middle;align=left;verticalAlign=top;spacingLeft=2;rotation=0;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="665" y="330" width="30" height="30" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-21" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;endArrow=block;endFill=0;dashed=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-20" target="3MEumQARUUYYfkFbGQGP-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-22" value="&amp;lt;&amp;lt;implements&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-21">
          <mxGeometry x="-0.2367" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-20" target="3MEumQARUUYYfkFbGQGP-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-25" value="calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-24">
          <mxGeometry x="-0.4182" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-20" value="LegalHoldApplicationService" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="595" y="400" width="170" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-27" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-23" target="3MEumQARUUYYfkFbGQGP-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-28" value="persists new LegalHold entity" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-27">
          <mxGeometry x="-0.2284" y="1" relative="1" as="geometry">
            <mxPoint x="-16" y="-1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-23" target="3MEumQARUUYYfkFbGQGP-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-31" value="publishes LegalHoldCreatedEvent" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-30">
          <mxGeometry x="-0.6158" y="1" relative="1" as="geometry">
            <mxPoint x="122" y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-23" value="LegalHoldCreateService" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="595" y="510" width="170" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-26" value="" style="strokeWidth=2;html=1;shape=mxgraph.flowchart.database;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="330" y="510" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-29" value="ApplicationEventPublisher" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1200" y="455" width="280" height="170" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-32" target="3MEumQARUUYYfkFbGQGP-16">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-34" value="&amp;lt;&amp;lt;dependency&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-33">
          <mxGeometry x="-0.0361" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-32" value="&lt;b&gt;damage-file&lt;/b&gt;" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="480" y="780" width="500" height="380" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=block;endFill=0;dashed=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-37" target="3MEumQARUUYYfkFbGQGP-38">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-40" value="&amp;lt;&amp;lt;implements&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-39">
          <mxGeometry x="0.4117" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-37" target="3MEumQARUUYYfkFbGQGP-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-42" value="listens for LegalHoldCreatedEvent(s)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-41">
          <mxGeometry x="-0.3448" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-37" target="3MEumQARUUYYfkFbGQGP-43">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="870" y="970" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-48" value="calls" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-47">
          <mxGeometry x="-0.013" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-37" value="DamageFileLegalHoldEventListener" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="760" y="860" width="205" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-38" value="LegalHoldEventListener" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="760" y="580" width="205" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-45" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-43" target="3MEumQARUUYYfkFbGQGP-44">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-46" value="&amp;lt;&amp;lt;implements" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-45">
          <mxGeometry x="0.7242" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-50" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-43" target="3MEumQARUUYYfkFbGQGP-49">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-51" value="appliesLegalHold" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-50">
          <mxGeometry x="-0.3444" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-52" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.75;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-43" target="3MEumQARUUYYfkFbGQGP-29">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1410" y="980" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-53" value="publishes LegalHoldAppliedToDomainEntityEvent&amp;nbsp;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-52">
          <mxGeometry x="-0.1641" y="-4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-43" value="DamageFileLegalHoldService" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="510" y="940" width="205" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-44" value="LegalHoldDomainEntityService&amp;lt;T&amp;gt;" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="510" y="580" width="205" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-49" value="DamageFile" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="552.5" y="1060" width="120" height="80" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-55" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;dashPattern=1 1;" edge="1" parent="1" source="3MEumQARUUYYfkFbGQGP-54" target="3MEumQARUUYYfkFbGQGP-29">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-57" value="listens for LegalHoldAppliedToDomainEntityEvent&amp;nbsp;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="3MEumQARUUYYfkFbGQGP-55">
          <mxGeometry x="-0.3215" y="4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3MEumQARUUYYfkFbGQGP-54" value="&lt;b&gt;legal-hold-out-adapter&lt;/b&gt;" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="1270" y="180" width="140" height="50" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="59FyLzHA4CACb-fqNkp5" name="JPA Entities vs. Domain Entities">
    <mxGraphModel dx="2819" dy="797" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="PzxTuapBAIiZLRhT0GHD-1" value="Old World&#xa;(aka LegacyProjects)" style="rounded=1;whiteSpace=wrap;verticalAlign=top;dashed=1;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="-240" y="100" width="1020" height="680" as="geometry" />
        </mxCell>
        <mxCell id="PzxTuapBAIiZLRhT0GHD-2" value="Application" style="rounded=0;whiteSpace=wrap;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="40" y="200" width="450" height="520" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-8" value="&lt;&lt;service&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="90" y="260" width="330" height="240" as="geometry" />
        </mxCell>
        <mxCell id="PzxTuapBAIiZLRhT0GHD-3" value="&#xa;Uncle Bob on Clean Architecture &#xa;https://www.youtube.com/watch?v=Nsjsiz2A9mg" style="text;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="1150" y="1320" width="360" height="70" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-7" value="&lt;&lt;model&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="90" y="530" width="330" height="140" as="geometry" />
        </mxCell>
        <mxCell id="PzxTuapBAIiZLRhT0GHD-5" value="(JPA)Entity" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="260" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-70" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="6ghQyVzOMPhyWPAVSb8O-1" target="TWc_-wkGDkE3uQKccxnZ-69">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-72" value="persist()&#xa;update()" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-70">
          <mxGeometry x="-0.0444" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="6ghQyVzOMPhyWPAVSb8O-1" value="ApplicationService" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="260" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;endArrow=none;endFill=0;dashed=1;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-5" target="PzxTuapBAIiZLRhT0GHD-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-5" value="Model / Anmeic Domain ModelEntity Identifier is coupled to the persistence" style="text;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="450" y="567.5" width="190" height="65" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-1" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="6ghQyVzOMPhyWPAVSb8O-1" target="PzxTuapBAIiZLRhT0GHD-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-2" value="setStatus()" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="LQqQNVTx3Zo5zQ7ZsP2F-1">
          <mxGeometry x="-0.0747" y="2" relative="1" as="geometry">
            <mxPoint x="-2" y="-16" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-14" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=1;entryY=0.5;entryDx=0;entryDy=0;dashed=1;endArrow=none;endFill=0;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-9" target="6ghQyVzOMPhyWPAVSb8O-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-9" value="Business Logic UseCaseA" style="text;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="500" y="423.75" width="190" height="32.5" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-15" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=0;entryY=0.5;entryDx=0;entryDy=0;dashed=1;endArrow=none;endFill=0;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-10" target="LQqQNVTx3Zo5zQ7ZsP2F-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-10" value="Business Logic UseCaseB&#xa;most likely very imperative" style="text;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="-200" y="423.75" width="190" height="32.5" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-11" target="PzxTuapBAIiZLRhT0GHD-5">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="160" y="600" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-13" value="setProperty()" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="LQqQNVTx3Zo5zQ7ZsP2F-12">
          <mxGeometry x="-0.6327" y="2" relative="1" as="geometry">
            <mxPoint y="-12" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-11" value="ApplicationService" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="100" y="410" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-16" value="DomainDrivenDesign" style="rounded=1;whiteSpace=wrap;verticalAlign=top;dashed=1;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="750" y="790" width="1110" height="370" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-23" value="&lt;&lt;domain&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1030" y="905" width="480" height="140" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-18" value="Holy DB" style="shape=datastore;whiteSpace=wrap;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="530" y="260" width="130" height="120" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-68" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=0;entryY=0.5;entryDx=0;entryDy=0;endArrow=none;endFill=0;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-20" target="TWc_-wkGDkE3uQKccxnZ-66">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-20" value="DomainEntity&#xa;(may be an Aggregate)" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1060" y="950" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-21" value="Repository" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1350" y="950" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-30" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;dashed=1;endArrow=none;endFill=0;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-29" target="LQqQNVTx3Zo5zQ7ZsP2F-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-29" value="Entity has all the domain/business logicEntity maintains and guarantees its own integrityEntity identifier is related to the domainoffers only domain functionality (e.g. close() instead of setStatus(CLOSED))" style="text;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="780" y="886.88" width="190" height="186.25" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-32" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=1;entryY=0.5;entryDx=0;entryDy=0;endArrow=none;endFill=0;dashed=1;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-31" target="LQqQNVTx3Zo5zQ7ZsP2F-21">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-31" value="independent of the actual persistencedeals only with domain entities" style="text;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1620" y="947.5" width="190" height="65" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-33" value="Implementing DomainDrivenDesign the proper way" style="rounded=1;whiteSpace=wrap;verticalAlign=top;dashed=1;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="-190" y="1275" width="950" height="920" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-34" value="Service" style="rounded=0;whiteSpace=wrap;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="-80" y="1320" width="550" height="820" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-35" value="&lt;&lt;domain&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="-45" y="1950" width="480" height="170" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-36" value="DomainEntity" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="135" y="2000" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-37" value="Repository" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="275" y="2000" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-38" value="&lt;&lt;application&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="-45" y="1620" width="480" height="310" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-39" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-43" target="TWc_-wkGDkE3uQKccxnZ-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-41" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-13" target="LQqQNVTx3Zo5zQ7ZsP2F-37">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="50" y="2080" />
              <mxPoint x="335" y="2080" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-49" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;endArrow=block;endFill=0;dashed=1;" edge="1" parent="1" source="LQqQNVTx3Zo5zQ7ZsP2F-43" target="LQqQNVTx3Zo5zQ7ZsP2F-48">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-50" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="LQqQNVTx3Zo5zQ7ZsP2F-49">
          <mxGeometry x="0.175" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-43" value="ApplicationService" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="-10" y="1820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="LQqQNVTx3Zo5zQ7ZsP2F-48" value="UseCase&#xa;Incoming Port" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="-10" y="1670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-1" value="&lt;&lt;adapter&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="-45" y="1360" width="480" height="240" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-3" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=0.75;entryY=0;entryDx=0;entryDy=0;dashed=1;endArrow=block;endFill=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-2" target="LQqQNVTx3Zo5zQ7ZsP2F-37">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-4" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-3">
          <mxGeometry x="-0.8964" y="2" relative="1" as="geometry">
            <mxPoint x="-2" y="36" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-6" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-2" target="TWc_-wkGDkE3uQKccxnZ-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-11" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-2" target="TWc_-wkGDkE3uQKccxnZ-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-73" value="create()/update()" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-11">
          <mxGeometry x="-0.2867" y="-1" relative="1" as="geometry">
            <mxPoint y="-5" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-2" value="JpaRepository" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="305" y="1500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-5" value="JpaEntity" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="140" y="1500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-8" value="DB" style="shape=datastore;whiteSpace=wrap;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="540" y="1360" width="130" height="120" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-12" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-10" target="TWc_-wkGDkE3uQKccxnZ-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-10" value="EntityManager" style="rounded=1;whiteSpace=wrap;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="305" y="1390" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-17" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-13" target="LQqQNVTx3Zo5zQ7ZsP2F-36">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-13" value="(optional)&#xa;DomainService" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="-10" y="2000" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-16" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-15" target="LQqQNVTx3Zo5zQ7ZsP2F-48">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-15" value="Adapter/Controller" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="-10" y="1500" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-20" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;dashed=1;endArrow=block;endFill=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-18" target="TWc_-wkGDkE3uQKccxnZ-19">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-22" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-20">
          <mxGeometry x="0.0259" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-21" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-18" target="TWc_-wkGDkE3uQKccxnZ-13">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="1910" />
              <mxPoint x="50" y="1910" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-18" value="ApplicationService" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="140" y="1820" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-19" value="UseCase" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="140" y="1670" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-23" value="Implementing DomainDrivenDesign the lazy way" style="rounded=1;whiteSpace=wrap;verticalAlign=top;dashed=1;fillColor=none;" vertex="1" parent="1">
          <mxGeometry x="1010" y="1800" width="950" height="980" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-24" value="Service" style="rounded=0;whiteSpace=wrap;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="1120" y="1840" width="550" height="745" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-25" value="&lt;&lt;domain&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1155" y="2395" width="480" height="170" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-26" value="DomainEntity" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=#E1D5E7;" vertex="1" parent="1">
          <mxGeometry x="1335" y="2445" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-27" value="Repository" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1475" y="2445" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-28" value="&lt;&lt;application&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="1155" y="2065" width="480" height="310" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-29" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-33" target="TWc_-wkGDkE3uQKccxnZ-47">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-30" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-47" target="TWc_-wkGDkE3uQKccxnZ-27">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="1250" y="2525" />
              <mxPoint x="1535" y="2525" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-31" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;endArrow=block;endFill=0;dashed=1;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-33" target="TWc_-wkGDkE3uQKccxnZ-34">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-32" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-31">
          <mxGeometry x="0.175" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-33" value="ApplicationService" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="1190" y="2265" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-34" value="UseCase" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="1190" y="2115" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-35" value="&lt;&lt;adapter&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="1155" y="1880" width="480" height="165" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-43" value="DB" style="shape=datastore;whiteSpace=wrap;fillColor=#f8cecc;strokeColor=#b85450;" vertex="1" parent="1">
          <mxGeometry x="1740" y="1895" width="130" height="120" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-44" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-55" target="TWc_-wkGDkE3uQKccxnZ-43">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1625" y="1865" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-46" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-47" target="TWc_-wkGDkE3uQKccxnZ-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-47" value="DomainService" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=none;" vertex="1" parent="1">
          <mxGeometry x="1190" y="2445" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-48" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-49" target="TWc_-wkGDkE3uQKccxnZ-34">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-49" value="Adapter" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="1190" y="1925" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-50" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;dashed=1;endArrow=block;endFill=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-33" target="TWc_-wkGDkE3uQKccxnZ-54">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1400" y="2265" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-51" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-50">
          <mxGeometry x="0.0259" y="-2" relative="1" as="geometry">
            <mxPoint x="-2" y="-57" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-54" value="UseCase" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="1340" y="2115" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-56" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;dashed=1;endArrow=block;endFill=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-55" target="TWc_-wkGDkE3uQKccxnZ-27">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-57" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-56">
          <mxGeometry x="-0.7587" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-55" value="SpringData JPA Repository" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="1475" y="1925" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-59" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-58" target="TWc_-wkGDkE3uQKccxnZ-54">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-58" value="Adapter" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="1340" y="1925" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-64" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;dashed=1;endArrow=none;endFill=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-63" target="TWc_-wkGDkE3uQKccxnZ-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-63" value="combines domain entric approach of DD with JPA annotationshas both &quot;identifiers&quot; (JPA and Entity)" style="text;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="1300" y="2658" width="190" height="92.5" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-66" value="Value Object" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="1205" y="950" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-71" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-69" target="LQqQNVTx3Zo5zQ7ZsP2F-18">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-69" value="JPA Repository" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="260" y="290" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-75" value="Domain-Driven Design: Tackling Complexity in the Heart of Software&#xa;by Eric Evans (2003)&#xa;&#xa;Implementing Domain-Driven Design&#xa;by Vaughn Vernon (2013)&#xa;&#xa;Domain-Driven Design Distilled&#xa;by Vaughn Vernon (2016)" style="text;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" vertex="1" parent="1">
          <mxGeometry x="1470" y="600" width="380" height="160" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-77" value="&lt;&lt;domain&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="-550" y="1960" width="480" height="170" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-76" value="Repository" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="-240" y="2000" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-78" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-13" target="TWc_-wkGDkE3uQKccxnZ-76">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-81" value="Service" style="rounded=0;whiteSpace=wrap;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="-350" y="2505" width="750" height="905" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-82" value="&lt;&lt;domain&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="-280" y="3060" width="645" height="280" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-84" value="Repository" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="205" y="3110" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-85" value="&lt;&lt;application&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="-115" y="2730" width="480" height="310" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-86" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-90" target="TWc_-wkGDkE3uQKccxnZ-107">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-87" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" target="TWc_-wkGDkE3uQKccxnZ-84">
          <mxGeometry relative="1" as="geometry">
            <Array as="points" />
            <mxPoint x="40" y="3140" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-106" value="delete(damageFile: DamageFile)" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-87">
          <mxGeometry x="-0.039" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-88" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;endArrow=block;endFill=0;dashed=1;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-90" target="TWc_-wkGDkE3uQKccxnZ-91">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-89" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-88">
          <mxGeometry x="0.175" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-112" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-90" target="TWc_-wkGDkE3uQKccxnZ-111">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-90" value="ApplicationService" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="-80" y="2927.5" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-91" value="UseCase" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="-80" y="2780" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-92" value="&lt;&lt;adapter&gt;&gt;" style="rounded=0;whiteSpace=wrap;dashed=1;verticalAlign=top;align=center;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="-115" y="2545" width="480" height="165" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-93" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-103">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="355" y="2530" as="sourcePoint" />
            <mxPoint x="470" y="2620" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-96" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-97" target="TWc_-wkGDkE3uQKccxnZ-91">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-97" value="Adapter" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="-80" y="2590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-98" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;dashed=1;endArrow=block;endFill=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-90" target="TWc_-wkGDkE3uQKccxnZ-100">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="130" y="2930" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-99" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-98">
          <mxGeometry x="0.0259" y="-2" relative="1" as="geometry">
            <mxPoint x="-2" y="-57" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-100" value="UseCase" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="70" y="2780" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-101" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;dashed=1;endArrow=block;endFill=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-103" target="TWc_-wkGDkE3uQKccxnZ-84">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-102" value="&lt;&lt;implements&gt;&gt;" style="edgeLabel;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="TWc_-wkGDkE3uQKccxnZ-101">
          <mxGeometry x="-0.7587" y="-3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-103" value="SpringData JPA Repository" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;dashed=1;" vertex="1" parent="1">
          <mxGeometry x="205" y="2590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-104" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-105" target="TWc_-wkGDkE3uQKccxnZ-100">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-105" value="Adapter" style="rounded=1;whiteSpace=wrap;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="70" y="2590" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-80" value="DamageFile" style="ellipse;whiteSpace=wrap;html=1;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="-140" y="3230" width="240" height="80" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-108" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-107" target="TWc_-wkGDkE3uQKccxnZ-80">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-113" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="TWc_-wkGDkE3uQKccxnZ-107" target="TWc_-wkGDkE3uQKccxnZ-84">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-107" value="DamageFileGobdDeleteService&#xa;- deleteDamageFile()" style="rounded=1;whiteSpace=wrap;fillColor=#d5e8d4;strokeColor=#82b366;gradientColor=none;" vertex="1" parent="1">
          <mxGeometry x="-270" y="3110" width="180" height="60" as="geometry" />
        </mxCell>
        <mxCell id="TWc_-wkGDkE3uQKccxnZ-111" value="VehiclePort" style="rounded=1;whiteSpace=wrap;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="-270" y="2780" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
  <diagram id="LlYnXk5CrxxQlf53wYV3" name="Modules - CleanArchitecture">
    <mxGraphModel dx="7414" dy="3758" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-4" value="" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;rotation=-180;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="-310" y="190" width="480" height="230" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-8" value="(Out-)Adapter-Module" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="-155" y="140" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-9" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;adapter&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-220" y="215" width="310" height="180" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-14" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;config&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-200" y="247.5" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-15" value="*Adapter.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-55" y="317.5" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-16" value="*AdapterConvert.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-55" y="247.5" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-18" value="" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;rotation=-180;fillColor=#ffe6cc;strokeColor=#d79b00;" vertex="1" parent="1">
          <mxGeometry x="470" y="470" width="560" height="410" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-19" value="Application/Business-Module" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="625" y="420" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-20" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;application&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="560" y="495" width="390" height="325" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-21" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;port&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="580" y="527.5" width="140" height="192.5" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-33" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;dashed=1;endArrow=block;endFill=0;" edge="1" parent="1" source="VQ4r5UwVihBFYhrRx8EI-22" target="VQ4r5UwVihBFYhrRx8EI-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-34" value="&amp;lt;&amp;lt;implements&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VQ4r5UwVihBFYhrRx8EI-33">
          <mxGeometry x="-0.8195" y="1" relative="1" as="geometry">
            <mxPoint x="-22" y="24" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-56" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=-0.025;exitY=0.569;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitPerimeter=0;" edge="1" parent="1" source="VQ4r5UwVihBFYhrRx8EI-22" target="VQ4r5UwVihBFYhrRx8EI-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-57" value="&amp;lt;&amp;lt;uses&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VQ4r5UwVihBFYhrRx8EI-56">
          <mxGeometry x="0.298" relative="1" as="geometry">
            <mxPoint x="15" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-22" value="*ApplicationService.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="790" y="570" width="125" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-23" value="*Finder.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="790" y="750" width="125" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-25" value="*OutPort.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="595" y="570" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-26" value="*UseCase.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="595" y="632.5" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-27" value="" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;rotation=-180;fillColor=#e1d5e7;strokeColor=#9673a6;" vertex="1" parent="1">
          <mxGeometry x="-260" y="505" width="390" height="247.5" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-28" value="(In-)Adapter-Module" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="-155" y="450" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-29" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;adapter&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-220" y="525" width="310" height="180" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-30" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;config&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-200" y="557.5" width="120" height="70" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-53" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="VQ4r5UwVihBFYhrRx8EI-31" target="VQ4r5UwVihBFYhrRx8EI-26">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-55" value="&amp;lt;&amp;lt;uses&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VQ4r5UwVihBFYhrRx8EI-53">
          <mxGeometry x="0.0897" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-31" value="*Adapter.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-55" y="627.5" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-32" value="*AdapterConvert.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="-55" y="557.5" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-35" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.005;entryY=0.627;entryDx=0;entryDy=0;entryPerimeter=0;endArrow=block;endFill=0;dashed=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="VQ4r5UwVihBFYhrRx8EI-15" target="VQ4r5UwVihBFYhrRx8EI-25">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="55" y="340" />
              <mxPoint x="410" y="340" />
              <mxPoint x="410" y="601" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-36" value="&amp;lt;&amp;lt;implements&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VQ4r5UwVihBFYhrRx8EI-35">
          <mxGeometry x="0.0728" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-37" value="" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;rotation=-180;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="1">
          <mxGeometry x="415" y="1050" width="600" height="410" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-38" value="Domain-Module" style="text;html=1;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontSize=20;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="630" y="1000" width="170" height="30" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-39" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;domain&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="475" y="1075" width="480" height="325" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-40" value="&lt;p style=&quot;margin:0px;margin-top:4px;margin-left:10px;text-align:left;&quot;&gt;&lt;b&gt;service&lt;/b&gt;&lt;/p&gt;" style="html=1;shape=mxgraph.sysml.package;html=1;overflow=fill;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="515" y="1107.5" width="215" height="142.5" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-43" value="{AggregateRoot}Repository" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="745" y="1290" width="170" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-44" value="DomainEntity.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="745" y="1107.5" width="170" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="VQ4r5UwVihBFYhrRx8EI-45" target="VQ4r5UwVihBFYhrRx8EI-43">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-49" value="&amp;lt;&amp;lt;uses&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VQ4r5UwVihBFYhrRx8EI-48">
          <mxGeometry x="-0.2085" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-45" value="DomainEntity{action}Service.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="535" y="1150" width="180" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-47" value="AggregateRoot.kt" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
          <mxGeometry x="745" y="1180" width="170" height="50" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-50" value="&amp;lt;&amp;lt;uses&amp;gt;&amp;gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="VQ4r5UwVihBFYhrRx8EI-22" target="VQ4r5UwVihBFYhrRx8EI-45">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="853" y="740" />
              <mxPoint x="625" y="740" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-51" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="VQ4r5UwVihBFYhrRx8EI-23" target="VQ4r5UwVihBFYhrRx8EI-43">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="1190" y="870" as="sourcePoint" />
            <Array as="points">
              <mxPoint x="970" y="775" />
              <mxPoint x="970" y="1315" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-52" value="&amp;lt;&amp;lt;uses&amp;gt;&amp;gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VQ4r5UwVihBFYhrRx8EI-51">
          <mxGeometry x="0.0721" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-58" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="465" y="-507" width="500" height="507.41" as="geometry" />
        </mxCell>
        <mxCell id="ObddP--EdoiUdJNwWvql-3" value="Adapter" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#e1d5e7;strokeColor=#9673a6;verticalAlign=top;" vertex="1" parent="VQ4r5UwVihBFYhrRx8EI-58">
          <mxGeometry width="500" height="500" as="geometry" />
        </mxCell>
        <mxCell id="ObddP--EdoiUdJNwWvql-2" value="Application" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#ffe6cc;strokeColor=#d79b00;verticalAlign=top;" vertex="1" parent="VQ4r5UwVihBFYhrRx8EI-58">
          <mxGeometry x="95.45454545454545" y="95.45534348840194" width="309.09090909090907" height="309.09090909090907" as="geometry" />
        </mxCell>
        <mxCell id="ObddP--EdoiUdJNwWvql-1" value="Domain" style="ellipse;whiteSpace=wrap;html=1;aspect=fixed;fillColor=#d5e8d4;strokeColor=#82b366;" vertex="1" parent="VQ4r5UwVihBFYhrRx8EI-58">
          <mxGeometry x="177.27272727272728" y="177.2742093356036" width="145.45454545454544" height="145.45454545454544" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-2" value="Module" style="triangle;whiteSpace=wrap;html=1;rotation=-135;" vertex="1" parent="VQ4r5UwVihBFYhrRx8EI-58">
          <mxGeometry x="220.0727272727272" y="283.7114628177189" width="236.36363636363635" height="145.4557615061363" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-5" value="Module" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;rotation=30;" vertex="1" parent="VQ4r5UwVihBFYhrRx8EI-58">
          <mxGeometry x="85.76363636363628" y="383.34865944942226" width="140" height="95.45534348840194" as="geometry" />
        </mxCell>
        <mxCell id="VQ4r5UwVihBFYhrRx8EI-24" value="Module" style="shape=trapezoid;perimeter=trapezoidPerimeter;whiteSpace=wrap;html=1;fixedSize=1;rotation=100;" vertex="1" parent="VQ4r5UwVihBFYhrRx8EI-58">
          <mxGeometry x="77.83636363636353" y="179.14695226499506" width="140" height="90.90985094133518" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
