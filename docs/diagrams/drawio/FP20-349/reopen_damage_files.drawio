<mxfile host="Electron" modified="2024-07-10T09:11:49.340Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="4fKp8j0vBVaVjQ1F5JHA" version="24.4.13" type="device">
  <diagram name="Page-1" id="cUIJTEAvMhr1Rlgv1zSP">
    <mxGraphModel dx="1456" dy="1431" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="WaF1Rw50EiIV9lVManH7-3" value="Repairfix" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" parent="1" vertex="1">
          <mxGeometry x="425" y="230" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.5;exitY=0.5;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="WaF1Rw50EiIV9lVManH7-5" target="WaF1Rw50EiIV9lVManH7-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-7" value="1. reopens Damage File" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="WaF1Rw50EiIV9lVManH7-6" vertex="1" connectable="0">
          <mxGeometry x="0.1538" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-5" value="Admin" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="490" y="60" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-8" value="DMS" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;horizontal=1;verticalAlign=top;" parent="1" vertex="1">
          <mxGeometry x="45" y="400" width="670" height="480" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-22" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="WaF1Rw50EiIV9lVManH7-9" target="WaF1Rw50EiIV9lVManH7-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-9" value="damage-file-migration" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;fillColor=#FFFFFF;strokeColor=#FFD966;strokeWidth=2;" parent="1" vertex="1">
          <mxGeometry x="310" y="435" width="360" height="175" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="WaF1Rw50EiIV9lVManH7-10" target="WaF1Rw50EiIV9lVManH7-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-17" value="3. Update Damage file" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="WaF1Rw50EiIV9lVManH7-16" vertex="1" connectable="0">
          <mxGeometry x="0.1824" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-10" value="DamageFileMigrationService" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#fff2cc;strokeColor=#d6b656;" parent="1" vertex="1">
          <mxGeometry x="395" y="480" width="220" height="70" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="WaF1Rw50EiIV9lVManH7-3" target="WaF1Rw50EiIV9lVManH7-10" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-12" value="2. checks (closed) damage files for updates" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="WaF1Rw50EiIV9lVManH7-11" vertex="1" connectable="0">
          <mxGeometry x="-0.3053" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-14" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;dashed=1;" parent="1" source="WaF1Rw50EiIV9lVManH7-13" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="610" y="360" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-13" value="Check damage files that are&amp;nbsp;&amp;nbsp;&lt;div&gt;- NOT closed&lt;/div&gt;&lt;div&gt;- OR closed for less than X years&lt;/div&gt;" style="shape=note;size=20;whiteSpace=wrap;html=1;align=left;" parent="1" vertex="1">
          <mxGeometry x="690" y="230" width="200" height="70" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="WaF1Rw50EiIV9lVManH7-15" target="WaF1Rw50EiIV9lVManH7-19" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-21" value="4. Archive damage file when closed&lt;div&gt;OR re-archive when closed damage file gets updated&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="WaF1Rw50EiIV9lVManH7-20" vertex="1" connectable="0">
          <mxGeometry x="-0.2541" relative="1" as="geometry">
            <mxPoint y="26" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="WaF1Rw50EiIV9lVManH7-15" target="WWzX2Uj_O8JyCuzjIl1e-2">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="180" y="670" />
              <mxPoint x="390" y="670" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-5" value="4. DamageFileClosedEvent" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="WWzX2Uj_O8JyCuzjIl1e-4">
          <mxGeometry x="0.0148" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.25;entryY=0;entryDx=0;entryDy=0;endArrow=none;endFill=0;startArrow=classic;startFill=1;" edge="1" parent="1" source="WaF1Rw50EiIV9lVManH7-15" target="WWzX2Uj_O8JyCuzjIl1e-3">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="200" y="640" />
              <mxPoint x="540" y="640" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-15" value="6. get updated deductibles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="WWzX2Uj_O8JyCuzjIl1e-10">
          <mxGeometry x="0.0073" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-15" value="damage-file" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="60" y="475" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="WaF1Rw50EiIV9lVManH7-19" value="GoBD Archived&lt;div&gt;damage files&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3_object_lock;" parent="1" vertex="1">
          <mxGeometry x="104.5" y="898" width="71" height="78" as="geometry" />
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-7" value="5. Send MonetaryBenefitAccountableEvent&lt;div&gt;with action: CREATED | UPDATED&lt;/div&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="WWzX2Uj_O8JyCuzjIl1e-2" target="WWzX2Uj_O8JyCuzjIl1e-6">
          <mxGeometry x="-0.4118" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-2" value="pvcc-p40-monetary-benefit-adapter" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="310" y="730" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-3" value="hcm-p08-damage-report-adapterdamage-file" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="500" y="730" width="160" height="80" as="geometry" />
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-6" value="Streamzilla" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;rotation=90;horizontal=0;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="360" y="920" width="60" height="180" as="geometry" />
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-9" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;dashed=1;" edge="1" parent="1" source="WWzX2Uj_O8JyCuzjIl1e-8">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="480" y="870" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-8" value="CREATED when prev. state == OPEN&lt;div&gt;UPDATED when prev. state == REOPENED&lt;/div&gt;" style="shape=note;size=20;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="1">
          <mxGeometry x="525" y="898" width="250" height="70" as="geometry" />
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="WWzX2Uj_O8JyCuzjIl1e-12" target="WWzX2Uj_O8JyCuzjIl1e-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-14" value="5. get deductibles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="WWzX2Uj_O8JyCuzjIl1e-13">
          <mxGeometry x="0.0178" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="WWzX2Uj_O8JyCuzjIl1e-12" value="HCM P08" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="870" y="730" width="160" height="80" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
