<mxfile host="Electron" modified="2024-07-10T12:49:48.224Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="Rg-1C5pXTgmVEP-onfok" version="24.4.13" type="device">
  <diagram name="assign" id="7UvbjHWKqGwS673DN4pY">
    <mxGraphModel dx="6666" dy="4118" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-54" value="&lt;font style=&quot;font-size: 31px;&quot;&gt;Assign Legal Hold&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;verticalAlign=top;fontSize=31;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="-1990" y="62.5" width="1370" height="590" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-90" value="AWS Cloud" style="whiteSpace=wrap;html=1;aspect=fixed;verticalAlign=top;align=left;fontSize=18;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="-1030" y="250" width="316.25" height="316.25" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-55" value="&lt;font style=&quot;font-size: 18px;&quot;&gt;&amp;nbsp; &amp;nbsp; &amp;nbsp; Damage Management Service&lt;/font&gt;" style="rounded=1;whiteSpace=wrap;html=1;align=left;verticalAlign=top;fontSize=18;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="-1650" y="252.5" width="560" height="310" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-57" value="Damage File" style="shape=table;startSize=30;container=1;collapsible=0;childLayout=tableLayout;strokeColor=default;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="-1580" y="312.5" width="380" height="150" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-58" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-57" vertex="1">
          <mxGeometry y="30" width="380" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-59" value="id" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-58" vertex="1">
          <mxGeometry width="127" height="40" as="geometry">
            <mxRectangle width="127" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-60" value="data" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-58" vertex="1">
          <mxGeometry x="127" width="126" height="40" as="geometry">
            <mxRectangle width="126" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-61" value="legalHold" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-58" vertex="1">
          <mxGeometry x="253" width="127" height="40" as="geometry">
            <mxRectangle width="127" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-62" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-57" vertex="1">
          <mxGeometry y="70" width="380" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-63" value="1" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-62" vertex="1">
          <mxGeometry width="127" height="40" as="geometry">
            <mxRectangle width="127" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-64" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-62" vertex="1">
          <mxGeometry x="127" width="126" height="40" as="geometry">
            <mxRectangle width="126" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-65" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-62" vertex="1">
          <mxGeometry x="253" width="127" height="40" as="geometry">
            <mxRectangle width="127" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-66" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-57" vertex="1">
          <mxGeometry y="110" width="380" height="40" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-67" value="2" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-66" vertex="1">
          <mxGeometry width="127" height="40" as="geometry">
            <mxRectangle width="127" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-68" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-66" vertex="1">
          <mxGeometry x="127" width="126" height="40" as="geometry">
            <mxRectangle width="126" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-69" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="Qm0hk6vXsL_TJ2h12PxG-66" vertex="1">
          <mxGeometry x="253" width="127" height="40" as="geometry">
            <mxRectangle width="127" height="40" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-70" value="" style="sketch=0;aspect=fixed;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=none;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;fillColor=#00188D;shape=mxgraph.mscae.enterprise.lock_unlocked" parent="1" vertex="1">
          <mxGeometry x="-1274" y="389.12" width="21" height="28.38" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-71" value="" style="sketch=0;aspect=fixed;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=none;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;fillColor=#00188D;shape=mxgraph.mscae.enterprise.lock" parent="1" vertex="1">
          <mxGeometry x="-1276.5" y="424.17" width="26" height="33.33" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-72" value="" style="html=1;verticalLabelPosition=bottom;align=center;labelBackgroundColor=#ffffff;verticalAlign=top;strokeWidth=2;strokeColor=#82b366;shadow=0;dashed=0;shape=mxgraph.ios7.icons.documents;fillColor=#d5e8d4;" parent="1" vertex="1">
          <mxGeometry x="-1401.25" y="387.5" width="22.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-73" value="" style="html=1;verticalLabelPosition=bottom;align=center;labelBackgroundColor=#ffffff;verticalAlign=top;strokeWidth=2;strokeColor=#6c8ebf;shadow=0;dashed=0;shape=mxgraph.ios7.icons.documents;fillColor=#dae8fc;" parent="1" vertex="1">
          <mxGeometry x="-1401.25" y="425.84" width="22.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-81" value="" style="endArrow=classic;html=1;rounded=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;fillColor=#f8cecc;strokeColor=#FF3333;strokeWidth=4;" parent="1" target="Qm0hk6vXsL_TJ2h12PxG-66" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-1840" y="440.9305454545456" as="sourcePoint" />
            <mxPoint x="-1320" y="462.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-82" value="1. puts Legal Hold" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];fontSize=14;" parent="Qm0hk6vXsL_TJ2h12PxG-81" vertex="1" connectable="0">
          <mxGeometry x="-0.342" y="6" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-83" value="&lt;font style=&quot;font-size: 16px;&quot;&gt;Storage with ObjectLock LegalHold&lt;/font&gt;" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.bucket;fillColor=#E05243;gradientColor=none;" parent="1" vertex="1">
          <mxGeometry x="-930" y="387.5" width="109.76" height="112.5" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-84" value="2. Document with Id 2" style="html=1;verticalLabelPosition=bottom;align=center;labelBackgroundColor=#ffffff;verticalAlign=top;strokeWidth=2;strokeColor=#6c8ebf;shadow=0;dashed=0;shape=mxgraph.ios7.icons.documents;fillColor=#dae8fc;fontSize=14;" parent="1" vertex="1">
          <mxGeometry x="-1069" y="410.84" width="22.5" height="30" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-85" value="" style="endArrow=classic;html=1;rounded=0;exitX=1;exitY=0.5;exitDx=0;exitDy=0;strokeWidth=3;" parent="1" source="Qm0hk6vXsL_TJ2h12PxG-66" target="Qm0hk6vXsL_TJ2h12PxG-83" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="-1050" y="522.5" as="sourcePoint" />
            <mxPoint x="-1000" y="472.5" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-86" value="" style="sketch=0;aspect=fixed;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=none;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;fillColor=#00188D;shape=mxgraph.mscae.enterprise.lock" parent="1" vertex="1">
          <mxGeometry x="-913.15" y="306.25" width="76.06" height="97.5" as="geometry" />
        </mxCell>
        <mxCell id="Qm0hk6vXsL_TJ2h12PxG-95" value="Responsible&lt;div&gt;Person&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=16;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.user;" parent="1" vertex="1">
          <mxGeometry x="-1920" y="389.12" width="78" height="78" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
