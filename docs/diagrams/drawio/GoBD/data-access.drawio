<mxfile host="Electron" modified="2024-06-25T06:46:25.948Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="NZLtQHeWNHNoeUOEDkEP" version="24.4.13" type="device">
  <diagram name="z1" id="t-Xtz5A0IxHHtWxNSkEH">
    <mxGraphModel dx="1242" dy="831" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="09ycynSsePLyQnu1A7m4-12" value="" style="group" parent="1" vertex="1" connectable="0">
          <mxGeometry x="68.5" y="30" width="480" height="480" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-11" value="&lt;b&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;Z1 - Direct Data Access&lt;/font&gt;&lt;/b&gt;" style="whiteSpace=wrap;html=1;aspect=fixed;horizontal=1;verticalAlign=top;" parent="09ycynSsePLyQnu1A7m4-12" vertex="1">
          <mxGeometry width="480" height="480" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="09ycynSsePLyQnu1A7m4-12" source="09ycynSsePLyQnu1A7m4-1" target="09ycynSsePLyQnu1A7m4-3" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="75.5" y="150" />
              <mxPoint x="75.5" y="150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-1" value="Accountant" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="09ycynSsePLyQnu1A7m4-12" vertex="1">
          <mxGeometry x="60" y="40" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-2" value="Archive" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bucket_with_objects;" parent="09ycynSsePLyQnu1A7m4-12" vertex="1">
          <mxGeometry x="37.5" y="330" width="75" height="78" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="09ycynSsePLyQnu1A7m4-12" source="09ycynSsePLyQnu1A7m4-3" target="09ycynSsePLyQnu1A7m4-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="09ycynSsePLyQnu1A7m4-12" source="09ycynSsePLyQnu1A7m4-3" target="09ycynSsePLyQnu1A7m4-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-3" value="AWS Athena" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.athena;" parent="09ycynSsePLyQnu1A7m4-12" vertex="1">
          <mxGeometry x="36" y="180" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="09ycynSsePLyQnu1A7m4-12" source="09ycynSsePLyQnu1A7m4-4" target="09ycynSsePLyQnu1A7m4-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-4" value="Datasource Connector" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.athena_data_source_connectors;" parent="09ycynSsePLyQnu1A7m4-12" vertex="1">
          <mxGeometry x="201.5" y="180" width="77" height="78" as="geometry" />
        </mxCell>
        <mxCell id="09ycynSsePLyQnu1A7m4-8" value="DMS Database" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#C925D1;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.rds_instance;" parent="09ycynSsePLyQnu1A7m4-12" vertex="1">
          <mxGeometry x="381.5" y="180" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-1" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="570" y="30" width="480" height="480" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-2" value="&lt;b&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;Z2 - Indirect Data Access&lt;/font&gt;&lt;/b&gt;" style="whiteSpace=wrap;html=1;aspect=fixed;horizontal=1;verticalAlign=top;" vertex="1" parent="Oi8rdO5taaYzlcJfDmAC-1">
          <mxGeometry width="480" height="480" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="Oi8rdO5taaYzlcJfDmAC-1" source="Oi8rdO5taaYzlcJfDmAC-4" target="Oi8rdO5taaYzlcJfDmAC-8">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="75.5" y="150" />
              <mxPoint x="75.5" y="150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-4" value="Developer" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="Oi8rdO5taaYzlcJfDmAC-1">
          <mxGeometry x="60" y="40" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-5" value="Archive" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bucket_with_objects;" vertex="1" parent="Oi8rdO5taaYzlcJfDmAC-1">
          <mxGeometry x="37.5" y="330" width="75" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="Oi8rdO5taaYzlcJfDmAC-1" source="Oi8rdO5taaYzlcJfDmAC-8" target="Oi8rdO5taaYzlcJfDmAC-10">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="Oi8rdO5taaYzlcJfDmAC-1" source="Oi8rdO5taaYzlcJfDmAC-8" target="Oi8rdO5taaYzlcJfDmAC-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-8" value="AWS Athena" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.athena;" vertex="1" parent="Oi8rdO5taaYzlcJfDmAC-1">
          <mxGeometry x="36" y="180" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="Oi8rdO5taaYzlcJfDmAC-1" source="Oi8rdO5taaYzlcJfDmAC-10" target="Oi8rdO5taaYzlcJfDmAC-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-10" value="Datasource Connector" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.athena_data_source_connectors;" vertex="1" parent="Oi8rdO5taaYzlcJfDmAC-1">
          <mxGeometry x="201.5" y="180" width="77" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-11" value="DMS Database" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#C925D1;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.rds_instance;" vertex="1" parent="Oi8rdO5taaYzlcJfDmAC-1">
          <mxGeometry x="381.5" y="180" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-12" value="Accountant" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="Oi8rdO5taaYzlcJfDmAC-1">
          <mxGeometry x="171.5" y="40" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="Oi8rdO5taaYzlcJfDmAC-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="Oi8rdO5taaYzlcJfDmAC-1" source="Oi8rdO5taaYzlcJfDmAC-12" target="Oi8rdO5taaYzlcJfDmAC-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-1" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="1080" y="30" width="480" height="480" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-2" value="&lt;b&gt;&lt;font style=&quot;font-size: 16px;&quot;&gt;Z3 - Data Carrier Transfer&lt;/font&gt;&lt;/b&gt;" style="whiteSpace=wrap;html=1;aspect=fixed;horizontal=1;verticalAlign=top;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry width="480" height="480" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1tV1AUALF3S4_gAF22GJ-1" source="1tV1AUALF3S4_gAF22GJ-5" target="1tV1AUALF3S4_gAF22GJ-9">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="75.5" y="150" />
              <mxPoint x="75.5" y="150" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-4" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1tV1AUALF3S4_gAF22GJ-1" source="1tV1AUALF3S4_gAF22GJ-5" target="1tV1AUALF3S4_gAF22GJ-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1tV1AUALF3S4_gAF22GJ-1" source="1tV1AUALF3S4_gAF22GJ-5" target="1tV1AUALF3S4_gAF22GJ-13">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-5" value="Developer" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry x="60" y="40" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-6" value="Archive" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.bucket_with_objects;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry x="37.5" y="330" width="75" height="78" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1tV1AUALF3S4_gAF22GJ-1" source="1tV1AUALF3S4_gAF22GJ-9" target="1tV1AUALF3S4_gAF22GJ-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1tV1AUALF3S4_gAF22GJ-1" source="1tV1AUALF3S4_gAF22GJ-9" target="1tV1AUALF3S4_gAF22GJ-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-9" value="AWS Athena" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.athena;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry x="36" y="180" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1tV1AUALF3S4_gAF22GJ-1" source="1tV1AUALF3S4_gAF22GJ-11" target="1tV1AUALF3S4_gAF22GJ-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-11" value="Datasource Connector" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.athena_data_source_connectors;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry x="201.5" y="180" width="77" height="78" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-12" value="DMS Database" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#C925D1;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.rds_instance;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry x="381.5" y="180" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="xXathsTNYpnIdvJw0kc2-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1tV1AUALF3S4_gAF22GJ-1" source="1tV1AUALF3S4_gAF22GJ-13" target="xXathsTNYpnIdvJw0kc2-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="1tV1AUALF3S4_gAF22GJ-13" value="CD Rom" style="sketch=0;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=none;fillColor=#434445;aspect=fixed;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;shape=mxgraph.vvd.cd;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry x="220" y="45" width="50" height="50" as="geometry" />
        </mxCell>
        <mxCell id="xXathsTNYpnIdvJw0kc2-1" value="Accountant" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1tV1AUALF3S4_gAF22GJ-1">
          <mxGeometry x="390" y="40" width="30" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
