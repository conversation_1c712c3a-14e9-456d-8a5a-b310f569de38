<mxfile host="Electron" modified="2024-07-10T12:48:22.774Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="BMMGipaKdicNoJPf5UtS" version="24.4.13" type="device">
  <diagram name="Page-1" id="n00f2gzobxePJnk5PzsP">
    <mxGraphModel dx="1421" dy="1647" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="qOdLygQ7gk3BIrkYICpf-1" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry y="1190" width="670" height="400" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-2" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="qOdLygQ7gk3BIrkYICpf-1">
          <mxGeometry x="-140" y="-50" width="810" height="450" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-3" value="Export Documents under Legal Hold" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fontSize=22;fontStyle=1" vertex="1" parent="qOdLygQ7gk3BIrkYICpf-1">
          <mxGeometry x="-1" width="610" height="360" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-4" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qOdLygQ7gk3BIrkYICpf-1" source="qOdLygQ7gk3BIrkYICpf-6" target="qOdLygQ7gk3BIrkYICpf-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-5" value="&lt;font style=&quot;font-size: 14px;&quot;&gt;Active Push to S3&lt;/font&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qOdLygQ7gk3BIrkYICpf-1" source="qOdLygQ7gk3BIrkYICpf-6" target="qOdLygQ7gk3BIrkYICpf-10">
          <mxGeometry x="0.0033" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-6" value="Damage Management Service" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#ED7100;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=16;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.ecs;" vertex="1" parent="qOdLygQ7gk3BIrkYICpf-1">
          <mxGeometry x="100" y="210" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="qOdLygQ7gk3BIrkYICpf-1" source="qOdLygQ7gk3BIrkYICpf-8" target="qOdLygQ7gk3BIrkYICpf-9">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-8" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#DD344C;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.role;" vertex="1" parent="qOdLygQ7gk3BIrkYICpf-1">
          <mxGeometry x="100" y="90" width="78" height="44" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-9" value="&lt;span style=&quot;color: rgb(0, 0, 0); font-size: 16px;&quot;&gt;Allow:&lt;/span&gt;&lt;div style=&quot;color: rgb(0, 0, 0); font-size: 16px;&quot;&gt;s3:PutObjectLegalHold&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#DD344C;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=16;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.permissions;" vertex="1" parent="qOdLygQ7gk3BIrkYICpf-1">
          <mxGeometry x="220" y="73" width="62" height="78" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-10" value="Legal Hold S3 Bucket&lt;div&gt;w/ objectLock Legal Hold&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=16;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3_object_lock;" vertex="1" parent="qOdLygQ7gk3BIrkYICpf-1">
          <mxGeometry x="465" y="210" width="71" height="78" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-11" value="Legal Hold&lt;div&gt;assigned&lt;/div&gt;" style="shadow=0;dashed=0;html=1;strokeColor=none;fillColor=#4495D1;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;shape=mxgraph.veeam.2d.letter;" vertex="1" parent="qOdLygQ7gk3BIrkYICpf-1">
          <mxGeometry x="20" y="210" width="40" height="30" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="qOdLygQ7gk3BIrkYICpf-13" target="qOdLygQ7gk3BIrkYICpf-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="qOdLygQ7gk3BIrkYICpf-13" value="Responsible&lt;div&gt;Person&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=16;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.user;" vertex="1" parent="1">
          <mxGeometry x="-100" y="1400" width="78" height="78" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
