<mxfile host="Electron" modified="2024-06-17T06:14:35.821Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="LHL1cFKYy9UPRWFqo5Hk" version="24.4.13" type="device">
  <diagram name="Page-1" id="DHuTpF6rEpgORpootgZ6">
    <mxGraphModel dx="1674" dy="1645" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="PHhVvRRfaXW2OOooEzAu-1" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#C925D1;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.rds_instance;" parent="1" vertex="1">
          <mxGeometry x="100" y="450" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-2" value="Glue Catalog" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.glue_data_catalog;" parent="1" vertex="1">
          <mxGeometry x="378" y="280" width="72" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=none;startFill=0;endArrow=classic;endFill=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-3" target="PHhVvRRfaXW2OOooEzAu-1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="139" y="410" />
              <mxPoint x="139" y="410" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-3" target="PHhVvRRfaXW2OOooEzAu-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-3" value="Glue Crawler" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.glue_crawlers;" parent="1" vertex="1">
          <mxGeometry x="100" y="280" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;dashed=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-4" target="PHhVvRRfaXW2OOooEzAu-2" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="PHhVvRRfaXW2OOooEzAu-4" target="PHhVvRRfaXW2OOooEzAu-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-4" target="PHhVvRRfaXW2OOooEzAu-7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-4" value="" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.glue;" parent="1" vertex="1">
          <mxGeometry x="375" y="450" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-14" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="PHhVvRRfaXW2OOooEzAu-7" target="PHhVvRRfaXW2OOooEzAu-13" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-7" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3_object_lock;" parent="1" vertex="1">
          <mxGeometry x="378" y="650" width="71" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-57" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-11" target="PHhVvRRfaXW2OOooEzAu-56" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="876" y="869" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-11" value="Glue Catalog" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.glue_data_catalog;" parent="1" vertex="1">
          <mxGeometry x="840" y="650" width="72" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-12" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-13" target="PHhVvRRfaXW2OOooEzAu-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-13" value="Glue Crawler" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.glue_crawlers;" parent="1" vertex="1">
          <mxGeometry x="590" y="650" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-15" target="PHhVvRRfaXW2OOooEzAu-11" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-15" value="AWS Athena" style="sketch=0;points=[[0,0,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0,0],[0,1,0],[0.25,1,0],[0.5,1,0],[0.75,1,0],[1,1,0],[0,0.25,0],[0,0.5,0],[0,0.75,0],[1,0.25,0],[1,0.5,0],[1,0.75,0]];outlineConnect=0;fontColor=#232F3E;fillColor=#8C4FFF;strokeColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.athena;" parent="1" vertex="1">
          <mxGeometry x="1040" y="650" width="78" height="78" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-17" value="" style="ellipse;whiteSpace=wrap;html=1;dashed=1;" parent="1" vertex="1">
          <mxGeometry x="580" y="250" width="300" height="272" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-18" value="" style="endArrow=none;html=1;rounded=0;entryX=0;entryY=0;entryDx=0;entryDy=0;exitX=1;exitY=0.25;exitDx=0;exitDy=0;exitPerimeter=0;dashed=1;dashPattern=12 12;" parent="1" source="PHhVvRRfaXW2OOooEzAu-4" target="PHhVvRRfaXW2OOooEzAu-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="449" y="460" as="sourcePoint" />
            <mxPoint x="499" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-19" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=1;entryDx=0;entryDy=0;exitX=1;exitY=0.75;exitDx=0;exitDy=0;exitPerimeter=0;dashed=1;dashPattern=12 12;" parent="1" source="PHhVvRRfaXW2OOooEzAu-4" target="PHhVvRRfaXW2OOooEzAu-17" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="463" y="480" as="sourcePoint" />
            <mxPoint x="596" y="372" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-20" value="Table" style="shape=table;startSize=30;container=1;collapsible=0;childLayout=tableLayout;strokeColor=default;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="620" y="302" width="60" height="56" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-21" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-20" vertex="1">
          <mxGeometry y="30" width="60" height="9" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-22" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-21" vertex="1">
          <mxGeometry width="20" height="9" as="geometry">
            <mxRectangle width="20" height="9" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-23" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-21" vertex="1">
          <mxGeometry x="20" width="20" height="9" as="geometry">
            <mxRectangle width="20" height="9" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-24" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-21" vertex="1">
          <mxGeometry x="40" width="20" height="9" as="geometry">
            <mxRectangle width="20" height="9" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-25" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-20" vertex="1">
          <mxGeometry y="39" width="60" height="8" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-26" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-25" vertex="1">
          <mxGeometry width="20" height="8" as="geometry">
            <mxRectangle width="20" height="8" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-27" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-25" vertex="1">
          <mxGeometry x="20" width="20" height="8" as="geometry">
            <mxRectangle width="20" height="8" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-28" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-25" vertex="1">
          <mxGeometry x="40" width="20" height="8" as="geometry">
            <mxRectangle width="20" height="8" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-29" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-20" vertex="1">
          <mxGeometry y="47" width="60" height="9" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-30" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-29" vertex="1">
          <mxGeometry width="20" height="9" as="geometry">
            <mxRectangle width="20" height="9" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-31" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-29" vertex="1">
          <mxGeometry x="20" width="20" height="9" as="geometry">
            <mxRectangle width="20" height="9" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-32" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-29" vertex="1">
          <mxGeometry x="40" width="20" height="9" as="geometry">
            <mxRectangle width="20" height="9" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-33" value="Table" style="shape=table;startSize=26;container=1;collapsible=0;childLayout=tableLayout;strokeColor=default;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="760" y="302" width="70" height="56" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-34" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-33" vertex="1">
          <mxGeometry y="26" width="70" height="10" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-35" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-34" vertex="1">
          <mxGeometry width="24" height="10" as="geometry">
            <mxRectangle width="24" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-36" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-34" vertex="1">
          <mxGeometry x="24" width="22" height="10" as="geometry">
            <mxRectangle width="22" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-37" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-34" vertex="1">
          <mxGeometry x="46" width="24" height="10" as="geometry">
            <mxRectangle width="24" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-38" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-33" vertex="1">
          <mxGeometry y="36" width="70" height="10" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-39" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-38" vertex="1">
          <mxGeometry width="24" height="10" as="geometry">
            <mxRectangle width="24" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-40" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-38" vertex="1">
          <mxGeometry x="24" width="22" height="10" as="geometry">
            <mxRectangle width="22" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-41" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-38" vertex="1">
          <mxGeometry x="46" width="24" height="10" as="geometry">
            <mxRectangle width="24" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-42" value="" style="shape=tableRow;horizontal=0;startSize=0;swimlaneHead=0;swimlaneBody=0;strokeColor=inherit;top=0;left=0;bottom=0;right=0;collapsible=0;dropTarget=0;fillColor=none;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-33" vertex="1">
          <mxGeometry y="46" width="70" height="10" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-43" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-42" vertex="1">
          <mxGeometry width="24" height="10" as="geometry">
            <mxRectangle width="24" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-44" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-42" vertex="1">
          <mxGeometry x="24" width="22" height="10" as="geometry">
            <mxRectangle width="22" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-45" value="" style="shape=partialRectangle;html=1;whiteSpace=wrap;connectable=0;strokeColor=inherit;overflow=hidden;fillColor=none;top=0;left=0;bottom=0;right=0;pointerEvents=1;fontSize=16;" parent="PHhVvRRfaXW2OOooEzAu-42" vertex="1">
          <mxGeometry x="46" width="24" height="10" as="geometry">
            <mxRectangle width="24" height="10" as="alternateBounds" />
          </mxGeometry>
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-53" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-46" target="PHhVvRRfaXW2OOooEzAu-52" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-46" value="" style="sketch=0;aspect=fixed;pointerEvents=1;shadow=0;dashed=0;html=1;strokeColor=default;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;align=center;fillColor=none;shape=mxgraph.mscae.enterprise.filter" parent="1" vertex="1">
          <mxGeometry x="705" y="390" width="50" height="45" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-47" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.3;entryY=0.011;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="PHhVvRRfaXW2OOooEzAu-21" target="PHhVvRRfaXW2OOooEzAu-46" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-48" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.64;entryY=0.033;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="PHhVvRRfaXW2OOooEzAu-34" target="PHhVvRRfaXW2OOooEzAu-46" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-52" value="" style="dashed=0;outlineConnect=0;html=1;align=center;labelPosition=center;verticalLabelPosition=bottom;verticalAlign=top;shape=mxgraph.weblogos.json" parent="1" vertex="1">
          <mxGeometry x="715" y="472.76" width="30" height="32.49" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-55" value="" style="html=1;verticalLabelPosition=bottom;align=center;labelBackgroundColor=#ffffff;verticalAlign=top;strokeWidth=2;strokeColor=#000000;shadow=0;dashed=0;shape=mxgraph.ios7.icons.settings;" parent="1" vertex="1">
          <mxGeometry x="722.5" y="393" width="15" height="15" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-58" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="PHhVvRRfaXW2OOooEzAu-56" target="PHhVvRRfaXW2OOooEzAu-1" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="PHhVvRRfaXW2OOooEzAu-56" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#8C4FFF;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.athena_data_source_connectors;" parent="1" vertex="1">
          <mxGeometry x="378" y="830" width="77" height="78" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
