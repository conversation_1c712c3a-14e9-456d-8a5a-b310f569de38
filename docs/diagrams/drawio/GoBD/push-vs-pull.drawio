<mxfile host="Electron" modified="2024-06-17T07:03:01.684Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="Y3A1lIGwnIA8pViFv5PA" version="24.4.13" type="device">
  <diagram name="Page-1" id="v1z-H2Z3YY7olX0v4XPT">
    <mxGraphModel dx="2093" dy="3225" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="VqqhRh3zzkan3uNuptBZ-47" value="Legal Hold" style="rounded=0;whiteSpace=wrap;html=1;fontSize=24;fontStyle=1;verticalAlign=top;movable=0;resizable=0;rotatable=0;deletable=0;editable=0;locked=1;connectable=0;" vertex="1" parent="1">
          <mxGeometry x="10" y="-530" width="1920" height="1110" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-36" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-11" target="VqqhRh3zzkan3uNuptBZ-33">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-11" value="Damage Management Service" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="50" y="70" width="420" height="360" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-10" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="130" y="160" width="220" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-4" value="&amp;lt;&amp;lt;entity&amp;gt;&amp;gt;&lt;div&gt;&lt;b&gt;DamageFile&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;horizontal=1;verticalAlign=top;fontStyle=0" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-10">
          <mxGeometry width="220" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-1" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-10">
          <mxGeometry x="30" y="60" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-2" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-10">
          <mxGeometry x="140" y="60" width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-12" value="S3 Bucket&lt;div&gt;Legal Hold&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3_object_lock;" vertex="1" parent="1">
          <mxGeometry x="630" y="130" width="71" height="78" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-4" target="VqqhRh3zzkan3uNuptBZ-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-39" value="either" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-13">
          <mxGeometry x="0.0198" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-24" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-20" target="VqqhRh3zzkan3uNuptBZ-23">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-23" target="VqqhRh3zzkan3uNuptBZ-25">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-23" value="AWS Glue" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#ffffff;fillColor=#232F3E;dashed=0;verticalLabelPosition=middle;verticalAlign=bottom;align=center;html=1;whiteSpace=wrap;fontSize=10;fontStyle=1;spacing=3;shape=mxgraph.aws4.productIcon;prIcon=mxgraph.aws4.glue;" vertex="1" parent="1">
          <mxGeometry x="1500" y="355" width="80" height="100" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-25" value="S3 Bucket&lt;div&gt;Archive&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3_object_lock;" vertex="1" parent="1">
          <mxGeometry x="1670" y="366" width="71" height="78" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-29" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="1100" y="-170" width="430" height="370" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-14" value="Fleet Vehicle Manager" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fontSize=14;fontStyle=1" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-29">
          <mxGeometry width="430" height="370" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-19" value="" style="group" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-29">
          <mxGeometry x="319" y="154" width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-17" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-19">
          <mxGeometry width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-18" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.question;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-19">
          <mxGeometry x="17.639999999999986" y="16" width="14.72" height="28" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-28" value="" style="group" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-29">
          <mxGeometry x="30" y="104" width="180" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-15" value="&amp;lt;&amp;lt;entity&amp;gt;&amp;gt;&lt;div&gt;&lt;b&gt;VehicleObject&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-28">
          <mxGeometry width="180" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-16" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-28">
          <mxGeometry x="30" y="70" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-14" target="VqqhRh3zzkan3uNuptBZ-20">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-31" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="1235" y="310" width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-20" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-31">
          <mxGeometry width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-21" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-31">
          <mxGeometry x="20" y="70" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-22" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-31">
          <mxGeometry x="90" y="70" width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-32" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="180" y="-230" width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-33" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-32">
          <mxGeometry width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-34" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-32">
          <mxGeometry x="20" y="70" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-35" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-32">
          <mxGeometry x="90" y="70" width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-38" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0;entryDx=0;entryDy=0;endArrow=none;endFill=0;dashed=1;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-37" target="VqqhRh3zzkan3uNuptBZ-11">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-37" value="Root Aggregate knows its meta data. Pushing is straight forward.&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Either directly to S3 or via&amp;nbsp;&lt;/div&gt;&lt;div&gt;proxy Lambda Function.&lt;/div&gt;" style="shape=note;size=20;whiteSpace=wrap;html=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="520" y="-220" width="190" height="180" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-42" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-40" target="VqqhRh3zzkan3uNuptBZ-12">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="740" y="336" />
              <mxPoint x="740" y="169" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-44" value="transform and push" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-42">
          <mxGeometry x="-0.0571" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-40" value="" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.lambda_function;fillColor=#F58534;gradientColor=none;" vertex="1" parent="1">
          <mxGeometry x="631" y="300" width="69" height="72" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-41" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-4" target="VqqhRh3zzkan3uNuptBZ-40">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-43" value="or&amp;nbsp;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-41">
          <mxGeometry x="0.069" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-46" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;dashed=1;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-45" target="VqqhRh3zzkan3uNuptBZ-14">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-45" value="How complex is it to push all?" style="shape=note;size=20;whiteSpace=wrap;html=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="1610.5" y="-350" width="190" height="130" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-48" value="Archiving" style="rounded=0;whiteSpace=wrap;html=1;fontSize=24;fontStyle=1;verticalAlign=top;" vertex="1" parent="1">
          <mxGeometry x="10" y="640" width="1920" height="1110" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-87" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-88" target="VqqhRh3zzkan3uNuptBZ-114">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-88" value="Damage Management Service" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fontSize=14;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="110" y="1180" width="420" height="360" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-89" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="190" y="1270" width="330" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-90" value="&amp;lt;&amp;lt;entity&amp;gt;&amp;gt;&lt;div&gt;&lt;b&gt;DamageFile&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;horizontal=1;verticalAlign=top;fontStyle=0" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-89">
          <mxGeometry width="220" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-91" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-89">
          <mxGeometry x="30" y="60" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-92" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-89">
          <mxGeometry x="140" y="60" width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-126" value="" style="shape=mxgraph.mockup.markup.redX;fillColor=#ff0000;html=1;shadow=0;whiteSpace=wrap;strokeColor=none;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-89">
          <mxGeometry x="135" y="60" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-93" value="S3 Bucket&lt;div&gt;Legal Hold&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3_object_lock;" vertex="1" parent="1">
          <mxGeometry x="690" y="1240" width="71" height="78" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-94" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-90" target="VqqhRh3zzkan3uNuptBZ-93">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-95" value="either" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-94">
          <mxGeometry x="0.0198" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-96" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-110" target="VqqhRh3zzkan3uNuptBZ-98">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-97" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-98" target="VqqhRh3zzkan3uNuptBZ-99">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-98" value="AWS Glue" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#ffffff;fillColor=#232F3E;dashed=0;verticalLabelPosition=middle;verticalAlign=bottom;align=center;html=1;whiteSpace=wrap;fontSize=10;fontStyle=1;spacing=3;shape=mxgraph.aws4.productIcon;prIcon=mxgraph.aws4.glue;" vertex="1" parent="1">
          <mxGeometry x="1560" y="1465" width="80" height="100" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-99" value="S3 Bucket&lt;div&gt;Archive&lt;/div&gt;" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#7AA116;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.s3_object_lock;" vertex="1" parent="1">
          <mxGeometry x="1730" y="1476" width="71" height="78" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-100" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="1160" y="940" width="430" height="370" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-101" value="Fleet Vehicle Manager" style="rounded=0;whiteSpace=wrap;html=1;verticalAlign=top;fontSize=14;fontStyle=1" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-100">
          <mxGeometry width="430" height="370" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-102" value="" style="group" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-100">
          <mxGeometry x="319" y="154" width="55" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-103" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-102">
          <mxGeometry width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-104" value="" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;fillColor=#232F3D;strokeColor=none;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;pointerEvents=1;shape=mxgraph.aws4.question;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-102">
          <mxGeometry x="17.639999999999986" y="16" width="14.72" height="28" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-127" value="" style="shape=mxgraph.mockup.markup.redX;fillColor=#ff0000;html=1;shadow=0;whiteSpace=wrap;strokeColor=none;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-102">
          <mxGeometry x="-5" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-105" value="" style="group" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-100">
          <mxGeometry x="30" y="104" width="180" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-106" value="&amp;lt;&amp;lt;entity&amp;gt;&amp;gt;&lt;div&gt;&lt;b&gt;VehicleObject&lt;/b&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-105">
          <mxGeometry width="180" height="160" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-107" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-105">
          <mxGeometry x="30" y="70" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-108" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-101" target="VqqhRh3zzkan3uNuptBZ-110">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-109" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="1295" y="1420" width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-110" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-109">
          <mxGeometry width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-111" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-109">
          <mxGeometry x="20" y="70" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-112" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-109">
          <mxGeometry x="90" y="70" width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-128" value="" style="shape=mxgraph.mockup.markup.redX;fillColor=#ff0000;html=1;shadow=0;whiteSpace=wrap;strokeColor=none;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-109">
          <mxGeometry x="85" y="70" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-113" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry x="240" y="880" width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-114" value="" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-113">
          <mxGeometry width="160" height="190" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-115" value="Object Data" style="sketch=0;outlineConnect=0;fontColor=#232F3E;gradientColor=none;strokeColor=#232F3E;fillColor=#ffffff;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;fontSize=12;fontStyle=0;aspect=fixed;shape=mxgraph.aws4.resourceIcon;resIcon=mxgraph.aws4.object;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-113">
          <mxGeometry x="20" y="70" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-116" value="Meta Data" style="shape=mxgraph.bpmn.data;labelPosition=center;verticalLabelPosition=bottom;align=center;verticalAlign=top;size=15;html=1;" vertex="1" parent="VqqhRh3zzkan3uNuptBZ-113">
          <mxGeometry x="90" y="70" width="50" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-117" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0;entryDx=0;entryDy=0;endArrow=none;endFill=0;dashed=1;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-118" target="VqqhRh3zzkan3uNuptBZ-88">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-118" value="Root Aggregate knows its meta data. Pushing is straight forward.&lt;div&gt;&lt;br&gt;&lt;/div&gt;&lt;div&gt;Either directly to S3 or via&amp;nbsp;&lt;/div&gt;&lt;div&gt;proxy Lambda Function.&lt;/div&gt;" style="shape=note;size=20;whiteSpace=wrap;html=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="580" y="890" width="190" height="180" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-119" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-121" target="VqqhRh3zzkan3uNuptBZ-93">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="800" y="1446" />
              <mxPoint x="800" y="1279" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-120" value="transform and push" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-119">
          <mxGeometry x="-0.0571" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-121" value="" style="outlineConnect=0;dashed=0;verticalLabelPosition=bottom;verticalAlign=top;align=center;html=1;shape=mxgraph.aws3.lambda_function;fillColor=#F58534;gradientColor=none;" vertex="1" parent="1">
          <mxGeometry x="691" y="1410" width="69" height="72" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-122" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=3;strokeColor=#97D077;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-90" target="VqqhRh3zzkan3uNuptBZ-121">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-123" value="or&amp;nbsp;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VqqhRh3zzkan3uNuptBZ-122">
          <mxGeometry x="0.069" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-124" style="rounded=0;orthogonalLoop=1;jettySize=auto;html=1;endArrow=none;endFill=0;dashed=1;entryX=1;entryY=0;entryDx=0;entryDy=0;" edge="1" parent="1" source="VqqhRh3zzkan3uNuptBZ-125" target="VqqhRh3zzkan3uNuptBZ-101">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-125" value="How complex is it to push all?" style="shape=note;size=20;whiteSpace=wrap;html=1;fontSize=14;" vertex="1" parent="1">
          <mxGeometry x="1670.5" y="760" width="190" height="130" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-129" value="Archiving has no need for meta data" style="shape=note;size=20;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="30" y="650" width="180" height="110" as="geometry" />
        </mxCell>
        <mxCell id="VqqhRh3zzkan3uNuptBZ-130" value="Legal Hold also needs meta data to be stored immutable:&lt;div&gt;&lt;span style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;- Creation Date&lt;/span&gt;&lt;br style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;&lt;span style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;- Document Responsible&lt;/span&gt;&lt;br style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;&lt;span style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;- Assigned CSD-category&lt;/span&gt;&lt;br style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;&lt;span style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;- Assigned CSD-event and CSD-event date (when using CSD-categories with events)&lt;/span&gt;&lt;br style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;&lt;span style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;- Locking Status [Locked/Unlocked]&lt;/span&gt;&lt;br style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;&lt;span style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;- Legal Hold Status [Y/N]&lt;/span&gt;&lt;br style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;&lt;span style=&quot;color: rgb(23, 43, 77); font-family: -apple-system, &amp;quot;system-ui&amp;quot;, &amp;quot;Segoe UI&amp;quot;, Roboto, Oxygen, Ubuntu, &amp;quot;Fira Sans&amp;quot;, &amp;quot;Droid Sans&amp;quot;, &amp;quot;Helvetica Neue&amp;quot;, sans-serif; font-size: 14px; background-color: rgb(255, 255, 255);&quot;&gt;- List of active Legal Holds on the document&lt;/span&gt;&lt;br&gt;&lt;/div&gt;" style="shape=note;size=20;whiteSpace=wrap;html=1;align=left;" vertex="1" parent="1">
          <mxGeometry x="30" y="-520" width="370" height="180" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
