:toc:
:numbered:
= draw.io (diagrams.net)

To create diagrams we are using draw.io.

== Edit

To edit the `.drawio` files either the web application can be used (https://app.diagrams.net/) or the desktop application
can be downloaded (https://github.com/jgraph/drawio-desktop/releases).

== Export

To export the drawio diagrams the python script link:export.py[] can be used. However this requires the desktop app to
be installed.  Alternatively the diagram can be exported manually.

[source, shell]
----
usage: export.py [-h] [--source SOURCE] [--target TARGET] [--app APP]

This script finds all drawio files in a directory and exports them as png to a the target directory, recreating the folder structure.

options:
  -h, --help       show this help message and exit
  --source SOURCE  Directory with drawio files. (default=.)
  --target TARGET  Directory where the files are exported to.(default=../../images)
  --app APP        Drawio cli that is used to export drawio diagrams. (default=/Applications/draw.io.app/Contents/MacOS/draw.io)

----

