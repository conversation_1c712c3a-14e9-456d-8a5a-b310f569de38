<mxfile host="Electron" modified="2024-07-24T14:36:21.378Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="6ZRysu_oae12kvRbS8Cu" version="24.4.13" type="device">
  <diagram name="Page-1" id="rTGUPOdNQMCubzzQ3KbM">
    <mxGraphModel dx="1242" dy="884" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="FsRnnKahvP_MBotV-7cm-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="FsRnnKahvP_MBotV-7cm-1" target="FsRnnKahvP_MBotV-7cm-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-7" value="Requests employee details" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-6">
          <mxGeometry x="-0.0111" y="-9" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-15" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="FsRnnKahvP_MBotV-7cm-1" target="FsRnnKahvP_MBotV-7cm-11">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="487" y="630" />
              <mxPoint x="487" y="555" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-16" value="Sends&amp;nbsp;&lt;div&gt;MonetaryBenefitAccountableEvent&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-15">
          <mxGeometry x="0.0739" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-18" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="FsRnnKahvP_MBotV-7cm-1" target="FsRnnKahvP_MBotV-7cm-17">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="320" y="530" />
              <mxPoint x="320" y="530" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-19" value="Migrates fleets, vehicles&lt;div&gt;and responsible persons&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-18">
          <mxGeometry x="0.3063" y="2" relative="1" as="geometry">
            <mxPoint y="65" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-37" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.5;exitDx=0;exitDy=0;" edge="1" parent="1" source="FsRnnKahvP_MBotV-7cm-1" target="FsRnnKahvP_MBotV-7cm-12">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-38" value="Requests internal&amp;nbsp;&lt;div&gt;order number&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-37">
          <mxGeometry x="0.0548" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-39" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=1;exitY=0.75;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="FsRnnKahvP_MBotV-7cm-1" target="FsRnnKahvP_MBotV-7cm-8">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-40" value="Requests scraped&amp;nbsp;&lt;div&gt;or sold date&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-39">
          <mxGeometry x="0.0345" y="3" relative="1" as="geometry">
            <mxPoint y="-17" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-1" value="Damage Management Service" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="294" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-3" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="FsRnnKahvP_MBotV-7cm-2" target="FsRnnKahvP_MBotV-7cm-1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-4" value="Requests deductible" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-3">
          <mxGeometry x="-0.0909" y="-4" relative="1" as="geometry">
            <mxPoint y="-4" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-2" value="HR P08" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="30" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-5" value="User Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="294" y="750" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-8" value="Fleet Vehicle Manager" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="560" y="750" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-11" value="PVCC P40" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="560" y="525" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-12" value="PACE P40" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="560" y="620" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-17" value="Reparifix" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;" vertex="1" parent="1">
          <mxGeometry x="232" y="370" width="244" height="90" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-22" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;exitX=0.75;exitY=0;exitDx=0;exitDy=0;entryX=0.623;entryY=0.994;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="FsRnnKahvP_MBotV-7cm-1" target="FsRnnKahvP_MBotV-7cm-17">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-23" value="Requests damage files" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-22">
          <mxGeometry x="0.4763" y="1" relative="1" as="geometry">
            <mxPoint y="1" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-27" value="" style="group" vertex="1" connectable="0" parent="1">
          <mxGeometry y="270" width="180" height="290" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-28" value="" style="rounded=0;whiteSpace=wrap;html=1;" vertex="1" parent="FsRnnKahvP_MBotV-7cm-27">
          <mxGeometry width="180" height="290" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-29" value="External SaaS&lt;div&gt;Solution&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;verticalAlign=middle;" vertex="1" parent="FsRnnKahvP_MBotV-7cm-27">
          <mxGeometry x="30" y="10" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-30" value="External Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=default;" vertex="1" parent="FsRnnKahvP_MBotV-7cm-27">
          <mxGeometry x="30" y="90" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-31" value="Service" style="rounded=1;whiteSpace=wrap;html=1;" vertex="1" parent="FsRnnKahvP_MBotV-7cm-27">
          <mxGeometry x="30" y="170" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-32" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="FsRnnKahvP_MBotV-7cm-27">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="25" y="260" as="sourcePoint" />
            <mxPoint x="155" y="260" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="FsRnnKahvP_MBotV-7cm-33" value="Ineractions" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="FsRnnKahvP_MBotV-7cm-32">
          <mxGeometry x="-0.3385" y="-2" relative="1" as="geometry">
            <mxPoint x="17" as="offset" />
          </mxGeometry>
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
