<mxfile host="Electron" modified="2024-08-13T09:35:22.000Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="VfB2nq0HMwt2AkruLVat" version="24.4.13" type="device">
  <diagram name="Page-1" id="TJ4IiMcTszkItr4yXk9o">
    <mxGraphModel dx="594" dy="1694" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="3pRbu0QPMpibKT0392Vq-4" value="Damage Management Service" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="264" y="380" width="296" height="550" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-5" value="Repairfix" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="270" y="190" width="280" height="120" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="3pRbu0QPMpibKT0392Vq-8" target="3pRbu0QPMpibKT0392Vq-15" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-7" value="3. consumes DamageFileClosedEvent" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3pRbu0QPMpibKT0392Vq-6" vertex="1" connectable="0">
          <mxGeometry x="-0.2056" y="-2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-8" value="damage-file" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="354" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3pRbu0QPMpibKT0392Vq-13" target="3pRbu0QPMpibKT0392Vq-5" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="414" y="320" />
              <mxPoint x="414" y="320" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-10" value="1. requests damage reports" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3pRbu0QPMpibKT0392Vq-9" vertex="1" connectable="0">
          <mxGeometry x="0.4308" y="4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3pRbu0QPMpibKT0392Vq-13" target="3pRbu0QPMpibKT0392Vq-8" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-12" value="2. closes DamageFile" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3pRbu0QPMpibKT0392Vq-11" vertex="1" connectable="0">
          <mxGeometry x="-0.3143" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-13" value="damage-file-migration" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="354" y="440" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-14" value="4. publishes MonetaryBenefitAccountableEvent&lt;div&gt;to outbox&lt;/div&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3pRbu0QPMpibKT0392Vq-15" target="3pRbu0QPMpibKT0392Vq-18" edge="1">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="414" y="800" />
              <mxPoint x="414" y="800" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-15" value="pvcc-p40-monetary-benefit-adapter" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="354" y="710" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="3pRbu0QPMpibKT0392Vq-18" target="3pRbu0QPMpibKT0392Vq-21" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-17" value="5. publishes&amp;nbsp;&lt;div&gt;MonetaryBenefitAccountableEvent&lt;/div&gt;&lt;div&gt;to Streamzilla&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3pRbu0QPMpibKT0392Vq-16" vertex="1" connectable="0">
          <mxGeometry x="-0.2784" relative="1" as="geometry">
            <mxPoint x="37" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-18" value="outbox-message-relay" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="354" y="830" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-19" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="3pRbu0QPMpibKT0392Vq-21" target="3pRbu0QPMpibKT0392Vq-22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-20" value="6. consumes&lt;div&gt;MonetaryBenefitAccountableEvent&lt;br&gt;&lt;div&gt;event&lt;/div&gt;&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3pRbu0QPMpibKT0392Vq-19" vertex="1" connectable="0">
          <mxGeometry x="-0.3426" relative="1" as="geometry">
            <mxPoint y="11" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-21" value="Streamzilla (Kafka)" style="shape=cylinder3;whiteSpace=wrap;html=1;boundedLbl=1;backgroundOutline=1;size=15;rotation=90;horizontal=0;" parent="1" vertex="1">
          <mxGeometry x="750" y="790" width="40" height="140" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-22" value="PVCC P40&lt;div&gt;&lt;span style=&quot;font-weight: normal;&quot;&gt;7. calculates monetary benefit&amp;nbsp;&lt;/span&gt;&lt;span style=&quot;font-weight: normal; background-color: initial;&quot;&gt;and charges employees payroll&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontStyle=1;fontColor=#333333;" parent="1" vertex="1">
          <mxGeometry x="690" y="940" width="160" height="60" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-26" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="690" y="600" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-27" value="D1. requests deductibles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3pRbu0QPMpibKT0392Vq-26" vertex="1" connectable="0">
          <mxGeometry x="0.2415" relative="1" as="geometry">
            <mxPoint x="27" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-30" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=0;entryDx=0;entryDy=0;" parent="1" source="3pRbu0QPMpibKT0392Vq-29" target="3pRbu0QPMpibKT0392Vq-5" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-31" value="0. closes damage report" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="3pRbu0QPMpibKT0392Vq-30" vertex="1" connectable="0">
          <mxGeometry x="-0.1636" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="3pRbu0QPMpibKT0392Vq-29" value="VPM Employee" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="395" y="30" width="30" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
