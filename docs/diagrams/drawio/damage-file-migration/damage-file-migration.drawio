<mxfile host="Electron" modified="2024-08-12T10:59:19.504Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="WN0Vu7EyO4C5A1_xfL_u" version="24.4.13" type="device">
  <diagram name="Page-1" id="djAg-X5dONludCyDsfS5">
    <mxGraphModel dx="1288" dy="1303" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="VBPOry_50INS4TI24NgT-1" value="Damage Management Service" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="266" y="380" width="296" height="300" as="geometry" />
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-3" value="Repairfix" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="279" y="190" width="270" height="120" as="geometry" />
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-4" value="damage-file" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="354" y="570" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-6" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="VBPOry_50INS4TI24NgT-5" target="VBPOry_50INS4TI24NgT-3">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="414" y="320" as="targetPoint" />
            <Array as="points" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-7" value="1. request damage reports" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VBPOry_50INS4TI24NgT-6">
          <mxGeometry x="0.4308" y="4" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-8" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="VBPOry_50INS4TI24NgT-5" target="VBPOry_50INS4TI24NgT-4">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-9" value="2. creates or updates DamageFiles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VBPOry_50INS4TI24NgT-8">
          <mxGeometry x="-0.3143" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-5" value="damage-file-migration" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="354" y="440" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-31" value="" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="VBPOry_50INS4TI24NgT-30" target="VBPOry_50INS4TI24NgT-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-32" value="0. creates damage report" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="VBPOry_50INS4TI24NgT-31">
          <mxGeometry x="0.1092" y="-1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="VBPOry_50INS4TI24NgT-30" value="User" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="399" y="30" width="30" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
