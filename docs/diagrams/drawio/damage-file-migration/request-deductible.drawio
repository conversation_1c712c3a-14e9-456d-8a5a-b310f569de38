<mxfile host="Electron" modified="2024-08-12T11:00:51.897Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="6LI8JXqFgEDTxOZrQMIt" version="24.4.13" type="device">
  <diagram name="Page-1" id="JJSV9ar4MXXILX9ww4Kc">
    <mxGraphModel dx="797" dy="807" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="fN_PT01SPRmR2saSfFN8-1" value="Damage Management Service" style="rounded=1;whiteSpace=wrap;html=1;verticalAlign=top;fontStyle=1" vertex="1" parent="1">
          <mxGeometry x="264" y="380" width="446" height="160" as="geometry" />
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-5" value="damage-file" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="330" y="440" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-11" value="4. publishes MonetaryBenefitAccountableEvent" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" target="fN_PT01SPRmR2saSfFN8-15">
          <mxGeometry relative="1" as="geometry">
            <Array as="points">
              <mxPoint x="414" y="800" />
              <mxPoint x="414" y="800" />
            </Array>
            <mxPoint x="414" y="770" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-13" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" target="fN_PT01SPRmR2saSfFN8-18">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="474" y="860" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-16" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" target="fN_PT01SPRmR2saSfFN8-19">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="870" y="860" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-20" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="fN_PT01SPRmR2saSfFN8-22" target="fN_PT01SPRmR2saSfFN8-5">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-21" value="2. gets&lt;div&gt;deductibles&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="fN_PT01SPRmR2saSfFN8-20">
          <mxGeometry x="-0.0046" y="1" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-22" value="hcm-p08-damage-report-adapter" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="546" y="440" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-23" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="fN_PT01SPRmR2saSfFN8-25" target="fN_PT01SPRmR2saSfFN8-22">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-24" value="1. requests deductibles" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="fN_PT01SPRmR2saSfFN8-23">
          <mxGeometry x="0.2415" relative="1" as="geometry">
            <mxPoint x="27" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="fN_PT01SPRmR2saSfFN8-25" value="HR P08&lt;div&gt;&lt;span style=&quot;font-weight: 400;&quot;&gt;3. charges employees payroll with the deductible amount&lt;/span&gt;&lt;/div&gt;" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;strokeColor=#666666;fontStyle=1;fontColor=#333333;" vertex="1" parent="1">
          <mxGeometry x="840" y="440" width="160" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
