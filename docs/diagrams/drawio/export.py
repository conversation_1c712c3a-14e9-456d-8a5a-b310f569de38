#!/usr/bin/env python3

import os
import subprocess
import argparse

parser = argparse.ArgumentParser(description="This script finds all drawio files in a directory and exports them as png to a the target directory, recreating the folder structure.")

# Add arguments
parser.add_argument('--source', type=str, default='../', help='Directory with drawio files. (default=../)')
parser.add_argument('--target', type=str, default='../../images', help='Directory where the files are exported to.(default=../../images)')
parser.add_argument('--app', type=str, default='/Applications/draw.io.app/Contents/MacOS/draw.io', help='Drawio cli that is used to export drawio diagrams. (default=/Applications/draw.io.app/Contents/MacOS/draw.io)')


# Parse the arguments
args = parser.parse_args()

def export_drawio_to_png(source_file, target_file):
	command = [args.app, '--export', source_file, '--output', target_file, '--format', 'png']
	result = subprocess.run(command, capture_output=True, text=True)

	# Print the output
	print(result)

def create_empty_txt_files(source_dir, target_dir):
	# Walk through the source directory
	for root, dirs, files in os.walk(source_dir):
		for file in files:
			if file.endswith('.drawio'):
				# Compute the relative path of the current file
				relative_path = os.path.relpath(root, source_dir)

				source_file_path = os.path.join(source_dir, relative_path)

				# Compute the corresponding target directory
				target_path = os.path.join(target_dir, relative_path)

				# Create the target directory if it doesn't exist
				os.makedirs(target_path, exist_ok=True)

				# Change the file extension from .drawio to .txt
				new_file_name = os.path.splitext(file)[0] + '.png'
				# Compute the full target file path
				target_file_path = os.path.join(target_path, new_file_name)

				# Create an empty file at the target location
				# open(target_file_path, 'a').close()
				export_drawio_to_png(source_file_path + '/' + file, target_file_path)
				print(f'Created: {target_file_path}')


create_empty_txt_files(args.source, args.target)