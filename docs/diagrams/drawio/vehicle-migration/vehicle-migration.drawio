<mxfile host="Electron" modified="2024-08-13T09:11:18.276Z" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/24.4.13 Chrome/124.0.6367.207 Electron/30.0.6 Safari/537.36" etag="OV7EYPYfmu5asiUMph8I" version="24.4.13" type="device">
  <diagram name="Page-1" id="9zyjmcQUPHtHt0Ai-fHS">
    <mxGraphModel dx="1674" dy="1694" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-2" value="Dama Management Service" style="rounded=1;whiteSpace=wrap;html=1;horizontal=1;verticalAlign=top;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="550" y="470" width="355" height="380" as="geometry" />
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-9" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="14Zl2VrsIYQzpQjAHD5J-3" target="14Zl2VrsIYQzpQjAHD5J-6" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-14" value="4. uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="14Zl2VrsIYQzpQjAHD5J-9" vertex="1" connectable="0">
          <mxGeometry x="-0.04" y="3" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-5" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="14Zl2VrsIYQzpQjAHD5J-3" target="GispYAmgKejfD356Ghwx-3">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-8" value="2. uses" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="GispYAmgKejfD356Ghwx-5">
          <mxGeometry x="-0.075" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-3" value="vehicle-migration" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="760" y="660" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-4" value="Repairfix" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#dae8fc;strokeColor=#6c8ebf;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="550" y="250" width="350" height="120" as="geometry" />
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-10" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" parent="1" source="14Zl2VrsIYQzpQjAHD5J-6" target="14Zl2VrsIYQzpQjAHD5J-4" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-13" value="5. updates vehicles,&lt;div&gt;fleets,&lt;/div&gt;&lt;div&gt;responsible persons&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="14Zl2VrsIYQzpQjAHD5J-10" vertex="1" connectable="0">
          <mxGeometry x="0.43" relative="1" as="geometry">
            <mxPoint y="10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-6" value="repairfix-client" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="665" y="510" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-11" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;startArrow=classic;startFill=1;endArrow=none;endFill=0;" parent="1" source="14Zl2VrsIYQzpQjAHD5J-7" target="14Zl2VrsIYQzpQjAHD5J-3" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-12" value="1. loads Excel file" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" parent="14Zl2VrsIYQzpQjAHD5J-11" vertex="1" connectable="0">
          <mxGeometry x="0.2769" y="2" relative="1" as="geometry">
            <mxPoint as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="14Zl2VrsIYQzpQjAHD5J-7" value="Migration File&lt;div&gt;excel&lt;/div&gt;" style="image;aspect=fixed;html=1;points=[];align=center;fontSize=12;image=img/lib/azure2/general/File.svg;" parent="1" vertex="1">
          <mxGeometry x="792" y="855.5" width="56.00000000000001" height="69" as="geometry" />
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-2" value="0. uploads&amp;nbsp;&lt;div&gt;Excel file to S3 bucket&lt;/div&gt;" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="GispYAmgKejfD356Ghwx-1" target="14Zl2VrsIYQzpQjAHD5J-7">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-1" value="PO" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;outlineConnect=0;" vertex="1" parent="1">
          <mxGeometry x="610" y="864.5" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-7" style="edgeStyle=orthogonalEdgeStyle;rounded=0;orthogonalLoop=1;jettySize=auto;html=1;" edge="1" parent="1" source="GispYAmgKejfD356Ghwx-3" target="GispYAmgKejfD356Ghwx-6">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-9" value="3. request PAG employee&lt;div&gt;and subsidiary&amp;nbsp;&lt;/div&gt;&lt;div&gt;employee data&lt;/div&gt;" style="edgeLabel;html=1;align=center;verticalAlign=middle;resizable=0;points=[];" vertex="1" connectable="0" parent="GispYAmgKejfD356Ghwx-7">
          <mxGeometry x="0.1" y="-1" relative="1" as="geometry">
            <mxPoint x="10" as="offset" />
          </mxGeometry>
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-3" value="employee-adapter" style="shape=module;align=left;spacingLeft=20;align=center;verticalAlign=top;whiteSpace=wrap;html=1;" vertex="1" parent="1">
          <mxGeometry x="570" y="660" width="120" height="60" as="geometry" />
        </mxCell>
        <mxCell id="GispYAmgKejfD356Ghwx-6" value="User Service" style="rounded=1;whiteSpace=wrap;html=1;fillColor=#f5f5f5;fontColor=#333333;strokeColor=#666666;" vertex="1" parent="1">
          <mxGeometry x="294" y="660" width="120" height="60" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
