import com.diffplug.spotless.LineEnding
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}
val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    api(libs.api.emhfxp)

    implementation(project(":shared"))
    implementation(libs.bundles.base)
    implementation(libs.bundles.webclient)

    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(libs.bundles.tests)
    testImplementation(libs.okhttp)
    testImplementation(libs.mockwebserver) {
        // we are forcing okhttp:5.0.0-alpha, as this is required by aws.sdk.kotlin
        exclude("com.squareup.okhttp3:okhttp")
    }
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
        targetExclude("**/*generated*/**")
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

val employeeGeneratedSourcesPath = "src/generated/"

sourceSets {
    main { kotlin.srcDir("$employeeGeneratedSourcesPath/kotlin") }
    test { kotlin { kotlin.srcDir(sharedTestSrcSetsOutput) } }
    test { resources { resources.srcDir(project(":shared").sourceSets.test.get().resources) } }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }

tasks.withType<KotlinCompile> { dependsOn("setupGeneratedSources") }

tasks.register<GenerateTask>("generateEmployeeClient") {
    outputs.upToDateWhen { false }
    val sourceGeneratedFolder = "$projectDir/generated"
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/api/employees.yaml")
    outputDir.set(sourceGeneratedFolder)
    apiPackage.set("com.emh.damagemanagement.employee.generated.adapter.out.rest")
    modelPackage.set("com.emh.damagemanagement.employee.generated.adapter.out.rest.model")
    modelNameSuffix.set("Dto")
    validateSpec.set(true)
    templateDir.set(
        "${project(":shared").projectDir.absolutePath}/src/main/resources/generator-templates"
    )
    additionalProperties.set(
        mapOf(
            "useSpringBoot3" to true,
            "dateLibrary" to "java8",
            "library" to "jvm-spring-webclient",
            "apiSuffix" to "Api",
            "serializationLibrary" to "jackson",
            "enumPropertyNaming" to "UPPERCASE",
            "omitGradleWrapper" to "true"
        )
    )
    dependsOn("unpackApi")
}

tasks.register<Copy>("setupGeneratedSources") {
    doFirst { delete(employeeGeneratedSourcesPath) }
    from("generated/src/main") { exclude("resources", "**/Serializer.kt") }
    into(employeeGeneratedSourcesPath)
    includeEmptyDirs = false
    dependsOn("generateEmployeeClient")
    doLast { delete("generated/") }
}
