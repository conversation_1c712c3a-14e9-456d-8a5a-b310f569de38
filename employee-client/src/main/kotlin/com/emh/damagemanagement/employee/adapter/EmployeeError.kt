/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.employee.adapter

open class EmployeeException(message: String?, cause: Throwable?) : RuntimeException(message, cause)

class EmployeeNotFoundException(message: String?, cause: Throwable? = null) : EmployeeException(message, cause)

class DistinctEmployeeNotFoundException(message: String?, cause: Throwable? = null) : EmployeeException(message, cause)
