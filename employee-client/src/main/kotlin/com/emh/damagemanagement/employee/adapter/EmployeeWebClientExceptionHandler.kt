/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.employee.adapter

import java.net.ConnectException
import org.slf4j.LoggerFactory
import org.springframework.security.oauth2.client.ClientAuthorizationException
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFunction
import reactor.core.publisher.Mono

@Component
class EmployeeWebClientExceptionHandler {

    /** A request error processor. Deals with connection issues nad may handle automatic retry in the future. */
    fun clientErrorRequestProcessor(request: ClientRequest, next: ExchangeFunction): Mono<ClientResponse> =
        next.exchange(request).onErrorMap {
            when (it.cause) {
                is ConnectException -> {
                    log.error("Error during request. Target not reachable.", it.cause)
                    EmployeeException("Error during request. Target not reachable.", it.cause)
                }
                is ClientAuthorizationException -> {
                    log.error("Error during authorization.", it.cause)
                    EmployeeException("Error during authorization.", it.cause)
                }
                else -> it
            }
        }

    companion object {
        private val log = LoggerFactory.getLogger(EmployeeWebClientExceptionHandler::class.java)
    }
}
