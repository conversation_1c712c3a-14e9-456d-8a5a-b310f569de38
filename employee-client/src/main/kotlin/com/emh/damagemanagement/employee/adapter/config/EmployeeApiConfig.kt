/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.employee.adapter.config

import com.emh.damagemanagement.employee.generated.adapter.out.rest.DmsApi
import com.emh.damagemanagement.employee.generated.adapter.out.rest.EmployeeApi
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class EmployeeApiConfig {
    @Bean fun dmsApi(@Qualifier("employee") webClient: WebClient) = DmsApi(webClient)

    @Bean fun employeeApi(@Qualifier("employee") webClient: WebClient) = EmployeeApi(webClient)
}
