/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.employee.adapter.config

import com.emh.damagemanagement.employee.adapter.EmployeeWebClientExceptionHandler
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.codec.ClientCodecConfigurer
import org.springframework.http.codec.json.Jackson2JsonDecoder
import org.springframework.http.codec.json.Jackson2JsonEncoder
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.security.oauth2.client.AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ClientCredentialsReactiveOAuth2AuthorizedClientProvider
import org.springframework.security.oauth2.client.InMemoryReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.security.oauth2.client.web.server.AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository
import org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class EmployeeWebClientConfig {

    @Bean
    @Qualifier("employee")
    fun employeeExchangeStrategies(jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder): ExchangeStrategies =
        ExchangeStrategies.builder()
            .codecs { clientDefaultCodecsConfigurer: ClientCodecConfigurer ->
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonEncoder(
                        Jackson2JsonEncoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonDecoder(
                        Jackson2JsonDecoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
            }
            .build()

    @Bean
    @Qualifier("employee")
    fun reactiveEmployeeClientRegistrationRepository(
        clientRegistrationRepository: ClientRegistrationRepository
    ): ReactiveClientRegistrationRepository {
        return InMemoryReactiveClientRegistrationRepository(
            clientRegistrationRepository.findByRegistrationId(CLIENT_REGISTRATION_ID)
        )
    }

    @Bean
    @Qualifier("employee")
    fun reactiveEmployeeAuthorizedClientService(
        @Qualifier("employee") clientRegistrationRepository: ReactiveClientRegistrationRepository
    ): ReactiveOAuth2AuthorizedClientService {
        return InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrationRepository)
    }

    @Bean
    @Qualifier("employee")
    fun reactiveEmployeeAuthorizedClientRepository(
        @Qualifier("employee") authorizedClientService: ReactiveOAuth2AuthorizedClientService
    ): ServerOAuth2AuthorizedClientRepository {
        return AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository(authorizedClientService)
    }

    @Bean
    @Qualifier("employee")
    fun reactiveEmployeeAuthorizedClientManager(
        @Qualifier("employee") reactiveRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("employee") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ReactiveOAuth2AuthorizedClientManager {
        return AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(
                reactiveRegistrationRepository,
                authorizedClientService,
            )
            .apply { setAuthorizedClientProvider(ClientCredentialsReactiveOAuth2AuthorizedClientProvider()) }
    }

    @Bean
    @Qualifier("employee")
    fun employeeWebClient(
        @Qualifier("employee") reactiveRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("employee") authorizedClientRepository: ServerOAuth2AuthorizedClientRepository,
        @Qualifier("employee") reactiveOAuth2AuthorizedClientManager: ReactiveOAuth2AuthorizedClientManager,
        employeeWebClientProperties: EmployeeWebClientProperties,
        @Qualifier("employee") exchangeStrategies: ExchangeStrategies,
        employeeWebClientExceptionHandler: EmployeeWebClientExceptionHandler,
    ): WebClient {
        val oauth2Client = ServerOAuth2AuthorizedClientExchangeFilterFunction(reactiveOAuth2AuthorizedClientManager)
        // ExchangeFilterFunction already has an apply function, so we do it the imperative way
        oauth2Client.setDefaultClientRegistrationId(CLIENT_REGISTRATION_ID)
        return WebClient.builder()
            .baseUrl(employeeWebClientProperties.baseUrl)
            .exchangeStrategies(exchangeStrategies)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .filter(oauth2Client)
            .filter(employeeWebClientExceptionHandler::clientErrorRequestProcessor)
            .build()
    }

    companion object {
        const val CLIENT_REGISTRATION_ID = "employee"
    }
}

@ConfigurationProperties("employees") data class EmployeeWebClientProperties(val baseUrl: String)
