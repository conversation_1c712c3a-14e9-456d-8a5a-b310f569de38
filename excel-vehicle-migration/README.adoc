= Vehicle-Migration

== 1. Module description

This module handle the migration of internal vehicle data from an excel file to an external system. Currently, the external system is *Repairfix* that requires vehicle data for the creation and management of damages and damage reports.

As this is not stricly within the core domain responsibility of this service, this module does not (and will not) have any dependencies on other modules (other than _shared_).

== 2. Getting started

We integrated *Repaifix*-API within this module. For that, OpenAPI-Generator is used to create client-code from provided API-Specification. Code generation is done automatically when compiling this module but may be triggered manually (for example when importing this project for the first time) by executing

`gradlew :vehicle-migration:setupGeneratedSources`

The generated code is then placed into `/src/generated/` and incorporated into the module via respective adapter configurations.

=== Localstack

We are using https://docs.localstack.cloud/[localstack] for testing S3 related functionality. This requires you (for local tests) to have a localstack container or pod running.

==== Kubernetes

----
helm repo add localstack https://localstack.github.io/helm-charts
----

----
helm install localstack -f localstack_deployment.yaml localstack/localstack
----

----
helm upgrade localstack -f localstack_deployment.yaml localstack/localstack
----

Also requires a matching port-forwarding for port 4566 (or another, if configured differently).

==== Docker

Localstack is contained in to compose file. See link:../README.md#docker-compose-[Project-Readme]

== 3. Client code generation

We use https://openapi-generator.tech/[OpenApiGenerator] for generation, more specifically https://openapi-generator.tech/docs/generators/kotlin[kotlin-Generator] with `jvm-spring-webclient` as library, which will result in Webflux-ClientCode. For configuration, you may have a look at the relevant gradle task(s) (`generateRepairfixClient`).
We also encountered some minor dependency issues with the generated code, so we ended up using custom templates for ApiClient and Api (see `/resources/genertor-templates` for details). Original tempaltes can be found at

* https://github.com/OpenAPITools/openapi-generator/blob/e8160d8d7d86b48d07fc7d2b38c64916162edf47/modules/openapi-generator/src/main/resources/kotlin-client/libraries/jvm-spring-webclient/infrastructure/ApiClient.kt.mustache
* https://github.com/OpenAPITools/openapi-generator/blob/f31c2a4b171daaccbae1c572e5b4652b5e0d7ced/modules/openapi-generator/src/main/resources/kotlin-client/libraries/jvm-spring-webclient/api.mustache

== 4. Testing

Client testing is done using https://github.com/square/okhttp/tree/master/mockwebserver[MockWebServer].
