import com.diffplug.spotless.LineEnding

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

evaluationDependsOn(":shared")
val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    implementation(project(":shared"))
    implementation(project(":repairfix-client"))
    implementation(project(":employee-client"))
    implementation(libs.s3.client)
    implementation(libs.bundles.base)
    implementation(libs.bundles.webclient)
    implementation(libs.apache.poi) {
        // will be replaced with 1.26.0 defined in catalog to avoid CVE
        exclude(group = "org.apache.commons", module = "commons-compress")
    }
    // can be removed once apache.poi updates commons-compress to > 1.25.0
    implementation(libs.apache.commons.compress)

    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(libs.bundles.tests)
    testImplementation(libs.bundles.tests.aws)
    testImplementation(libs.okhttp)
    testImplementation(libs.mockwebserver) {
        // we are forcing okhttp:5.0.0-alpha, as this is required by aws.sdk.kotlin
        exclude(group = "com.squareup.okhttp3", module = "okhttp")
    }
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
        targetExclude("**/*generated*/**")
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

sourceSets {
    test { kotlin.srcDir(sharedTestSrcSetsOutput) }
    test { resources.srcDir(project(":shared").sourceSets.test.get().resources) }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }
