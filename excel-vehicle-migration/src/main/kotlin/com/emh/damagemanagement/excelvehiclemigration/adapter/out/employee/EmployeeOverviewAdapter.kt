/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.employee

import com.emh.damagemanagement.employee.adapter.EmployeeException
import com.emh.damagemanagement.employee.generated.adapter.out.rest.DmsApi
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeOverviewDto
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.QueryEmployeesForDmsDto
import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeOverview
import com.emh.damagemanagement.excelvehiclemigration.application.port.EmployeeOutPort
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClientException
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component
class EmployeeOverviewAdapter(private val dmsApi: DmsApi) : EmployeeOutPort {

    override fun getEmployeeOverviewsFor(employeeNumbers: Collection<String>): List<EmployeeOverview> {
        return Flux.fromIterable(employeeNumbers.chunked(DEFAULT_CHUNK_SIZE))
            .flatMap(::getEmployeeOverviewsChunked)
            .flatMapIterable { employees -> employees }
            .map(EmployeeOverviewDto::toEmployeeOverview)
            .collectList()
            .block()
            ?.toList() ?: emptyList()
    }

    private fun getEmployeeOverviewsChunked(employeeNumbers: Collection<String>): Mono<List<EmployeeOverviewDto>> {
        return dmsApi
            .queryEmployeesForDms(QueryEmployeesForDmsDto(employeeNumbers = employeeNumbers.toList()))
            .onErrorMap(WebClientException::class.java) {
                log.warn("Error while trying to get employee overviews.", it)
                EmployeeException(it.message, it)
            }
    }

    companion object {
        private const val DEFAULT_CHUNK_SIZE = 100
        private val log = LoggerFactory.getLogger(EmployeeOverviewAdapter::class.java)
    }
}
