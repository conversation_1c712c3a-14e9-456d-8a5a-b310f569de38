/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.employee

import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeOverviewDto
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeTypeDto
import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeOverview
import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeType

fun EmployeeOverviewDto.toEmployeeOverview(): EmployeeOverview =
    EmployeeOverview(
        key = this.key,
        employeeNumber = this.employeeNumber,
        firstName = this.firstName,
        lastName = this.lastName,
        companyEmail = this.companyEmail,
        accountingArea = this.accountingArea,
        type = this.type.toEmployeeType(),
    )

private fun EmployeeTypeDto.toEmployeeType(): EmployeeType =
    when (this) {
        EmployeeTypeDto.SUBSIDIARY_EMPLOYEE -> EmployeeType.SUBSIDIARY_EMPLOYEE
        EmployeeTypeDto.PAG_EMPLOYEE -> EmployeeType.PAG_EMPLOYEE
    }
