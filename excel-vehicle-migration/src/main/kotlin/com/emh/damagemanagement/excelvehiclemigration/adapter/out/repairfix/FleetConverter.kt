/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetOnboardingDto

fun Fleet.toFleetOnboardingDto(parentFleetId: String): FleetOnboardingDto =
    FleetOnboardingDto(name = this.description, fleetPartnerId = this.id, parentFleetId = parentFleetId)
