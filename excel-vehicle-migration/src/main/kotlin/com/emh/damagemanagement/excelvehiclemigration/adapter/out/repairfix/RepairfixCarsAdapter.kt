/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.adapter.config.RepairfixRequestsProperties
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.CarsApi
import java.math.BigDecimal
import java.time.Duration
import kotlin.math.ceil
import kotlin.math.floor
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component("repairfixCarsAdapterExcelMigration")
class RepairfixCarsAdapter(
    private val carsApi: CarsApi,
    private val repairfixRequestsProperties: RepairfixRequestsProperties,
) : VehicleOutPort {

    override fun addVehicles(vehicles: List<Vehicle>): AddVehiclesResult {
        log.info("Adding ${vehicles.size} vehicles to repairfix.")
        val chunkedVehicles = vehicles.chunked(DEFAULT_CHUNK_SIZE)

        val addVehiclesResult =
            Flux.fromIterable(chunkedVehicles)
                .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
                .map(::addVehiclesChunk)
                .flatMap { result -> result }
                .reduce { intermediateResult, currentResult ->
                    AddVehiclesResult(
                        successfullyAddedVehicles =
                            intermediateResult.successfullyAddedVehicles + currentResult.successfullyAddedVehicles,
                        failedVehicles = intermediateResult.failedVehicles + currentResult.failedVehicles,
                    )
                }
                // in case we did not migrate any vehicles
                .block() ?: AddVehiclesResult(successfullyAddedVehicles = emptySet(), failedVehicles = emptySet())
        return addVehiclesResult
    }

    /**
     * Adds a subset of vehicles to repairfix. Chunk size will automatically be reduced on error to determine the
     * offending vehicles.
     */
    private fun addVehiclesChunk(vehicleChunk: List<Vehicle>): Flux<AddVehiclesResult> =
        carsApi
            .porscheCarsControllerAdd(vehicleChunk.toAddCarDtos())
            .flatMapIterable { listOf(it) }
            .flatMap { operationResult ->
                if (!operationResult.success) {
                    reduceChunkSizeAndRetry(vehicleChunk)
                } else
                    Flux.fromIterable(
                        listOf(
                            AddVehiclesResult(
                                failedVehicles = emptySet(),
                                successfullyAddedVehicles = vehicleChunk.toSet(),
                            )
                        )
                    )
            }
            .onErrorResume(RepairfixException::class.java) {
                when (it.type) {
                    RepairfixException.Type.BUSINESS -> reduceChunkSizeAndRetry(chunk = vehicleChunk, exception = it)
                    RepairfixException.Type.TECHNICAL -> Mono.error(it.toVehicleMigrationException())
                }
            }

    private fun reduceChunkSizeAndRetry(
        chunk: List<Vehicle>,
        exception: RepairfixException? = null,
    ): Flux<AddVehiclesResult> {
        // if we are already at size one just return the failed vehicle as single failed result
        if (1 == chunk.size) {
            log.warn(
                """Car with VIN: ${chunk.single().vin.value} failed migration due to: 
                    |${exception?.message ?: "operation success = false."}"""
                    .trimMargin()
            )
            return Flux.fromIterable(
                listOf(AddVehiclesResult(failedVehicles = chunk.toSet(), successfullyAddedVehicles = emptySet()))
            )
        }
        // reduce chunk size and retry
        val reducedChunkSize = getReducedChunkSize(chunk.size)
        log.debug("Error with chunk size ${chunk.size}. Retrying with reduced chunk size $reducedChunkSize")
        return Flux.fromIterable(chunk.chunked(reducedChunkSize))
            .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
            .map(::addVehiclesChunk)
            .flatMap { addVehicleResults -> addVehicleResults }
    }

    private fun getReducedChunkSize(currentChunkSize: Int): Int = ceil(currentChunkSize.toDouble().div(10)).toInt()

    override fun getAllVehicles(): List<Vehicle> {
        val totalNumberOfCars = getTotalNumberOfCars()
        if (0 == totalNumberOfCars) return emptyList()

        val maxPageNumber = getMaxPageNumber(totalNumberOfCars)

        return Flux.fromIterable(START_PAGE..maxPageNumber)
            .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
            .flatMapSequential { pageNumber -> getVehiclesPage(pageNumber) }
            .flatMapIterable { vehicles -> vehicles }
            .collectList()
            .block()
            ?.toList() ?: emptyList()
    }

    override fun updateVehicles(vehicles: List<Vehicle>): UpdateVehiclesResult {
        log.info("Updating ${vehicles.size} vehicles to repairfix.")

        val updateVehicleResult =
            Flux.fromIterable(vehicles)
                .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
                .flatMapSequential { updateVehicle(it) }
                .reduce { intermediateResult, currentResult ->
                    UpdateVehiclesResult(
                        successfulVehicles = intermediateResult.successfulVehicles + currentResult.successfulVehicles,
                        failedVehicles = intermediateResult.failedVehicles + currentResult.failedVehicles,
                    )
                }
                .block() ?: UpdateVehiclesResult(successfulVehicles = emptySet(), failedVehicles = emptySet())
        return updateVehicleResult
    }

    override fun deactivateVehicles(vehicles: List<Vehicle>): UpdateVehiclesResult {
        log.info("Deactivating ${vehicles.size} vehicles in repairfix.")

        val deactivateVehicleResult =
            Flux.fromIterable(vehicles)
                .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
                .flatMapSequential { deactivateVehicle(it) }
                .reduce { intermediateResult, currentResult ->
                    UpdateVehiclesResult(
                        successfulVehicles = intermediateResult.successfulVehicles + currentResult.successfulVehicles,
                        failedVehicles = intermediateResult.failedVehicles + currentResult.failedVehicles,
                    )
                }
                .block() ?: UpdateVehiclesResult(successfulVehicles = emptySet(), failedVehicles = emptySet())
        return deactivateVehicleResult
    }

    private fun updateVehicle(vehicle: Vehicle): Mono<UpdateVehiclesResult> {
        log.info("Updating car with VIN [${vehicle.vin.value}].")
        return carsApi
            .porscheCarsControllerUpdate(
                carIdentifier = vehicle.externalId.toString(),
                updateCarDto = vehicle.toUpdateCarDto(),
            )
            .map { _ ->
                log.info("Finished updating car with VIN [${vehicle.vin.value}].")
                UpdateVehiclesResult(successfulVehicles = setOf(vehicle), failedVehicles = emptySet())
            }
            .onErrorResume(RepairfixException::class.java) {
                log.warn("Error while trying to update vehicle with VIN [${vehicle.vin.value}].")
                Mono.just(UpdateVehiclesResult(successfulVehicles = emptySet(), failedVehicles = setOf(vehicle)))
            }
    }

    private fun deactivateVehicle(vehicle: Vehicle): Mono<UpdateVehiclesResult> {
        log.info("Deactivating car with VIN [${vehicle.vin.value}].")
        return carsApi
            .porscheCarsControllerUpdate(
                carIdentifier = vehicle.externalId.toString(),
                updateCarDto = vehicle.toUpdateCarDto(deactivate = true),
            )
            .map { _ ->
                log.info("Finished deactivating car with VIN [${vehicle.vin.value}].")
                UpdateVehiclesResult(successfulVehicles = setOf(vehicle), failedVehicles = emptySet())
            }
            .onErrorResume(RepairfixException::class.java) {
                log.warn("Error while trying to deactivate vehicle with VIN [${vehicle.vin.value}].")
                Mono.just(UpdateVehiclesResult(successfulVehicles = emptySet(), failedVehicles = setOf(vehicle)))
            }
    }

    /**
     * Will return a single page of vehicles.
     *
     * There is no option to set sort, so we assume/hope that pages are returned using a consistent sort property.
     *
     * @param pageNumber the number of the page to be fetched
     */
    private fun getVehiclesPage(pageNumber: Int): Mono<List<Vehicle>> =
        carsApi
            .porscheCarsControllerGet(
                limit = BigDecimal.valueOf(DEFAULT_PAGE_SIZE.toLong()),
                page = BigDecimal.valueOf(pageNumber.toLong()),
                make = null,
                model = null,
                isPool = null,
                location = null,
                availability = null,
                fleetPartnerId = null,
            )
            /**
             * 2024-02-14 repairfix API has phoneNUmber of carDriver set as required, which does not match their domain
             * constraints when adding users (as phoneNumber is optional there). We manually changed the api spec to
             * make phoneNumber not required, the same change should be included in the next api update from repairfix.
             */
            .map { carsList ->
                carsList.cars.mapNotNull {
                    try {
                        it.toVehicle()
                    } catch (exception: IllegalArgumentException) {
                        // skip cars that fail domain validation (like drivers without
                        // partnerIds)
                        null
                    }
                }
            }
            .onErrorMap(RepairfixException::class.java) {
                log.warn("Error while fetching cars. ${it.message}")
                it.toVehicleMigrationException()
            }

    /** Fetches the total number of cars for given filters. */
    private fun getTotalNumberOfCars(): Int {
        return carsApi
            .porscheCarsControllerGet(
                // we do not care about the result and use 1 to minimize the return payload
                // (also note, that using 0 work like 'unlimited')
                limit = BigDecimal.valueOf(1L),
                page = BigDecimal.valueOf(START_PAGE.toLong()),
                make = null,
                model = null,
                isPool = null,
                location = null,
                availability = null,
                fleetPartnerId = null,
            )
            .map { carsList -> carsList.total.toInt() }
            .onErrorMap(RepairfixException::class.java) {
                log.warn("Error while trying to get total number of cars. ${it.message}")
                it.toVehicleMigrationException()
            }
            .block() ?: 0
    }

    /**
     * Returns the max page number for given count of elements, [DEFAULT_PAGE_SIZE] and [START_PAGE] Will NOT work if '1
     * !=[START_PAGE]'.
     */
    private fun getMaxPageNumber(count: Int): Int {
        val offset = if (0 == count.mod(DEFAULT_PAGE_SIZE)) 0 else START_PAGE

        return floor(count.toDouble().div(DEFAULT_PAGE_SIZE.toDouble())).toInt() + offset
    }

    companion object {
        private const val DEFAULT_PAGE_SIZE = 100
        private const val DEFAULT_CHUNK_SIZE = 100
        // be aware pages are starting with 1, changing this also requires to refactor
        // [getMaxPageNumber]
        private const val START_PAGE = 1
        private val log = LoggerFactory.getLogger(RepairfixCarsAdapter::class.java)
    }
}
