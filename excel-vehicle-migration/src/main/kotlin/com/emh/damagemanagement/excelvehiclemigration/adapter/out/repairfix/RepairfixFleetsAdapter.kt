/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.application.AddFleetFailedException
import com.emh.damagemanagement.excelvehiclemigration.application.port.FleetOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.adapter.config.FleetGroupProperties
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.FleetsApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetListItem
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetOnboardingDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.OperationResult
import java.math.BigDecimal
import kotlin.math.floor
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component("repairfixFleetAdapterExcelMigration")
class RepairfixFleetsAdapter(private val fleetsApi: FleetsApi, private val fleetGroupProperties: FleetGroupProperties) :
    FleetOutPort {

    override fun migrateLeasingFleets(fleetsToBeMigrated: Set<Fleet>) =
        migrateFleets(fleetsToBeMigrated = fleetsToBeMigrated, fleetGroup = fleetGroupProperties.dienstLeasing)

    override fun migratePoolFleets(fleetsToBeMigrated: Set<Fleet>) =
        migrateFleets(fleetsToBeMigrated = fleetsToBeMigrated, fleetGroup = fleetGroupProperties.pool)

    private fun migrateFleets(fleetsToBeMigrated: Set<Fleet>, fleetGroup: String) {
        val existingFleetPartnerIds =
            getAllFleets()
                .mapNotNull {
                    /**
                     * 2024-02-14 repairfix API has partnerId of fleets set as required, which does not match their
                     * domain constraints and their data on at least staging system. We manually changed the api spec to
                     * make partnerId not required, the same change should be included in the next api update from
                     * repairfix. This also requires us now to filter for fleets with partnerIds.
                     */
                    it.partnerId
                }
                .also { log.info("Existing fleets: ${it.joinToString(", ")}.") }

        fleetsToBeMigrated
            .filterNot { it.id in existingFleetPartnerIds }
            .forEach { fleetToBeCreated ->
                log.debug("Adding new fleet: ${fleetToBeCreated.id} - ${fleetToBeCreated.description}.")
                try {
                    onboardNewFleet(fleetToBeCreated.toFleetOnboardingDto(fleetGroup)).block()
                } catch (exception: AddFleetFailedException) {
                    log.warn("Adding fleet: ${fleetToBeCreated.id} failed. Skipping...")
                }
            }
    }

    private fun onboardNewFleet(newFleet: FleetOnboardingDto): Mono<OperationResult> =
        fleetsApi
            .porscheFleetsControllerAdd(newFleet)
            .handle { operationResult, sink ->
                if (!operationResult.success)
                    sink.error(AddFleetFailedException(fleetId = newFleet.fleetPartnerId, cause = null))
                else sink.next(operationResult)
            }
            .onErrorMap(RepairfixException::class.java) { exception ->
                log.warn("Adding fleet failed. ${exception.message}")
                if (RepairfixException.Type.BUSINESS == exception.type)
                    AddFleetFailedException(fleetId = newFleet.fleetPartnerId, cause = exception)
                else exception.toFleetMigrationException()
            }

    private fun getAllFleetsAndFleetGroups(): List<FleetListItem> {
        val totalNumberOfFleets = getTotalNumberOfFleets()
        if (0 == totalNumberOfFleets) return emptyList()

        val maxPageNumber = getMaxPageNumber(totalNumberOfFleets)

        return Flux.fromIterable(START_PAGE..maxPageNumber)
            .flatMap { pageNumber -> getFleetPage(pageNumber) }
            .flatMapIterable { fleets -> fleets }
            .collectList()
            .block()
            ?.toList() ?: emptyList()
    }

    /**
     * Will return all fleets currently registered with repairfix. Those ONLY include 'fleets' with parentFleetId (as
     * those without are classified as 'fleetGroups')
     */
    private fun getAllFleets(): List<FleetListItem> =
        getAllFleetsAndFleetGroups().filterNot {
            /**
             * 2024-03-04 repairfix API change now has fleets with parentFleetId (filtering crashes their service, so we
             * fetch all ad filter ourselves)
             */
            null == it.parentFleetId
        }

    /**
     * Returns the max page number for given count of elements, [DEFAULT_PAGE_SIZE] and [START_PAGE] Will NOT work if '1
     * !=[START_PAGE]'.
     */
    private fun getMaxPageNumber(count: Int): Int {
        val offset = if (0 == count.mod(DEFAULT_PAGE_SIZE)) 0 else START_PAGE

        return floor(count.toDouble().div(DEFAULT_PAGE_SIZE.toDouble())).toInt() + offset
    }

    private fun getFleetPage(pageNumber: Int): Mono<List<FleetListItem>> =
        fleetsApi
            .porscheFleetsControllerGetList(
                limit = BigDecimal.valueOf(DEFAULT_PAGE_SIZE.toLong()),
                page = BigDecimal.valueOf(pageNumber.toLong()),
                // set to null, as repairfix does not return fleets when empty strings are provided
                parentFleetId = null,
                partnerId = null,
                search = null,
            )
            .map { fleetsList -> fleetsList.fleets }
            .onErrorMap(RepairfixException::class.java) {
                log.warn("Error while trying to fetch fleets. ${it.message}")
                it.toFleetMigrationException()
            }

    private fun getTotalNumberOfFleets(): Int {
        return fleetsApi
            .porscheFleetsControllerGetList(
                // we do not care about the result and use 1 to minimize the return payload
                // (also note, that using 0 work like 'unlimited')
                limit = BigDecimal.valueOf(1L),
                page = BigDecimal.valueOf(START_PAGE.toLong()),
                // set to null, as repairfix does not return fleets when empty strings are provided
                parentFleetId = null,
                partnerId = null,
                search = null,
            )
            .map { carsList -> carsList.total.toInt() }
            .onErrorMap(RepairfixException::class.java) {
                log.warn("Error while trying to get total number of cars. ${it.message}")
                it.toVehicleMigrationException()
            }
            .block() ?: 0
    }

    override fun allFleetsExist(fleetsToCompare: Set<Fleet>): Boolean {
        val allRepairfixFleetPartnerIds = getAllFleets().mapNotNull { it.partnerId }
        return allRepairfixFleetPartnerIds.containsAll(fleetsToCompare.map { it.id })
    }

    companion object {
        private val log = LoggerFactory.getLogger(RepairfixFleetsAdapter::class.java)
        private const val DEFAULT_PAGE_SIZE = 100
        private const val START_PAGE = 1
    }
}
