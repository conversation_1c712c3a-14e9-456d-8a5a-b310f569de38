/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehicleResponsiblePersonsResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehicleResponsiblePersonsResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.adapter.config.FleetGroupProperties
import com.emh.damagemanagement.repairfix.adapter.config.RepairfixRequestsProperties
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.UserApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CreateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.User
import java.math.BigDecimal
import java.time.Duration
import kotlin.math.ceil
import kotlin.math.floor
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

typealias VehicleResponsiblePersonAndUser = Pair<VehicleResponsiblePerson, CreateUserDto>

typealias VehicleResponsiblePersonAndUserUpdate = Pair<VehicleResponsiblePerson, UpdateUserDto>

@Component("repairfixUsersAdapterExcelMigration")
class RepairfixUsersAdapter(
    private val userApi: UserApi,
    private val fleetGroupProperties: FleetGroupProperties,
    private val repairfixRequestsProperties: RepairfixRequestsProperties,
) : VehicleResponsiblePersonOutPort {

    override fun getAllVehicleResponsiblePersons(): List<Pair<VehicleResponsiblePerson, Set<String>>> {
        return getAllUsers().mapNotNull {
            try {
                it.toVehicleResponsiblePersonAndFleetIds()
            } catch (exception: IllegalArgumentException) {
                null
            }
        }
    }

    private fun getAllUsers(): List<User> {
        val totalNumberOfDrivers = getTotalNumberOfUsers()
        if (0 == totalNumberOfDrivers) return emptyList()

        val maxPageNumber = getMaxPageNumber(totalNumberOfDrivers)

        return Flux.fromIterable(START_PAGE..maxPageNumber)
            .flatMap { pageNumber -> getUserPage(pageNumber = pageNumber) }
            .flatMapIterable { drivers -> drivers }
            .collectList()
            .block()
            ?.toList() ?: emptyList()
    }

    /**
     * Returns the max page number for given count of elements, [DEFAULT_PAGE_SIZE] and [START_PAGE] Will NOT work if '1
     * !=[START_PAGE]'.
     */
    private fun getMaxPageNumber(count: Int): Int {
        val offset = if (0 == count.mod(DEFAULT_PAGE_SIZE)) 0 else START_PAGE

        return floor(count.toDouble().div(DEFAULT_PAGE_SIZE.toDouble())).toInt() + offset
    }

    private fun getUserPage(pageNumber: Int): Mono<List<User>> =
        userApi
            .porscheUsersControllerGetUsers(
                limit = BigDecimal.valueOf(DEFAULT_PAGE_SIZE.toLong()),
                page = BigDecimal.valueOf(pageNumber.toLong()),
                subFleet = fleetGroupProperties.root,
                role = null,
            )
            .onErrorMap(RepairfixException::class.java) {
                log.warn("Error while trying to get drivers page: $pageNumber. ${it.message}")
                it.toVehicleResponsiblePersonMigrationException()
            }
            .map { driversList -> driversList.users }

    private fun getTotalNumberOfUsers(): Int =
        userApi
            .porscheUsersControllerGetUsers(
                // we do not care about the result and use 1 to minimize the return payload
                // (also note, that using 0 work like 'unlimited')
                limit = BigDecimal.valueOf(1L),
                page = BigDecimal.valueOf(START_PAGE.toLong()),
                subFleet = fleetGroupProperties.root,
                role = null,
            )
            .map { driversList -> driversList.total.toInt() }
            .onErrorMap(RepairfixException::class.java) {
                log.warn("Error while trying to get total number of drivers. ${it.message}")
                it.toVehicleResponsiblePersonMigrationException()
            }
            .block() ?: 0

    override fun addVehicleResponsiblePersons(
        vehicleResponsiblePersons: List<VehicleResponsiblePerson>
    ): AddVehicleResponsiblePersonsResult {
        log.info("Adding ${vehicleResponsiblePersons.size} vehicle responsible persons as drivers to repairfix.")
        val chunkedVehicleResponsiblePersonsAndUser =
            vehicleResponsiblePersons.map { Pair(it, it.toCreateUserDto()) }.chunked(DEFAULT_CHUNK_SIZE)
        return addChunkedVehicleResponsiblePersonAndUsers(chunkedVehicleResponsiblePersonsAndUser)
    }

    override fun addFleetManagers(
        fleetManagerAndFleets: List<Pair<VehicleResponsiblePerson, Set<Fleet>>>
    ): AddVehicleResponsiblePersonsResult {
        log.info("Adding ${fleetManagerAndFleets.size} (local) fleet managers to repairfix.")
        val chunkedFleetManagerAndUser =
            fleetManagerAndFleets.map { Pair(it.first, it.toCreateUserDto()) }.chunked(DEFAULT_CHUNK_SIZE)

        return addChunkedVehicleResponsiblePersonAndUsers(chunkedFleetManagerAndUser)
    }

    private fun addChunkedVehicleResponsiblePersonAndUsers(
        vehicleResponsiblePersonAndUserChunk: List<List<VehicleResponsiblePersonAndUser>>
    ): AddVehicleResponsiblePersonsResult =
        Flux.fromIterable(vehicleResponsiblePersonAndUserChunk)
            .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
            .map(::addVehicleResponsiblePersonChunk)
            .flatMap { result -> result }
            .reduce { intermediateResult, currentResult ->
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons =
                        intermediateResult.successfullyAddedVehicleResponsiblePersons +
                            currentResult.successfullyAddedVehicleResponsiblePersons,
                    failedVehicleResponsiblePersons =
                        intermediateResult.failedVehicleResponsiblePersons +
                            currentResult.failedVehicleResponsiblePersons,
                )
            }
            .block()
            // in case we did not migrate any vehicle responsible persons
            ?: AddVehicleResponsiblePersonsResult(
                failedVehicleResponsiblePersons = emptySet(),
                successfullyAddedVehicleResponsiblePersons = emptySet(),
            )

    override fun updateVehicleResponsiblePersons(
        vehicleResponsiblePersons: Collection<VehicleResponsiblePerson>
    ): UpdateVehicleResponsiblePersonsResult {
        return Flux.fromIterable(vehicleResponsiblePersons)
            .delayElements(Duration.ofSeconds(repairfixRequestsProperties.pageDelaySeconds.toLong()))
            .flatMapSequential { updateVehicleResponsiblePersonAndUser(Pair(it, it.toUpdateUserDto())) }
            .reduce { intermediateResult, currentResult ->
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons =
                        intermediateResult.successfullyUpdatedVehicleResponsiblePersons +
                            currentResult.successfullyUpdatedVehicleResponsiblePersons,
                    failedVehicleResponsiblePersons =
                        intermediateResult.failedVehicleResponsiblePersons +
                            currentResult.failedVehicleResponsiblePersons,
                )
            }
            .block()
            ?: UpdateVehicleResponsiblePersonsResult(
                successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                failedVehicleResponsiblePersons = emptySet(),
            )
    }

    override fun updateFleetManagers(
        fleetManagerAndFleets: Collection<Pair<VehicleResponsiblePerson, Set<Fleet>>>
    ): UpdateVehicleResponsiblePersonsResult {
        return Flux.fromIterable(fleetManagerAndFleets)
            .flatMap { updateVehicleResponsiblePersonAndUser(Pair(it.first, it.toUpdateUserDto())) }
            .reduce { intermediateResult, currentResult ->
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons =
                        intermediateResult.successfullyUpdatedVehicleResponsiblePersons +
                            currentResult.successfullyUpdatedVehicleResponsiblePersons,
                    failedVehicleResponsiblePersons =
                        intermediateResult.failedVehicleResponsiblePersons +
                            currentResult.failedVehicleResponsiblePersons,
                )
            }
            .block()
            ?: UpdateVehicleResponsiblePersonsResult(
                successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                failedVehicleResponsiblePersons = emptySet(),
            )
    }

    private fun updateVehicleResponsiblePersonAndUser(
        vehicleResponsiblePersonAndUserUpdate: VehicleResponsiblePersonAndUserUpdate
    ): Mono<UpdateVehicleResponsiblePersonsResult> {
        log.info(
            "Updating user (vehicle responsible person or fleet manager) with employee number: ${vehicleResponsiblePersonAndUserUpdate.first.employeeNumber.value}..."
        )

        return userApi
            .porscheUsersControllerUpdate(
                userPartnerId = vehicleResponsiblePersonAndUserUpdate.first.employeeNumber.value,
                updateUserDto = vehicleResponsiblePersonAndUserUpdate.second,
            )
            .map { operationResult ->
                if (!operationResult.success) {
                    log.warn(
                        "Failed to update vehicle responsible person with employee number : ${vehicleResponsiblePersonAndUserUpdate.first.employeeNumber.value}."
                    )
                    UpdateVehicleResponsiblePersonsResult(
                        failedVehicleResponsiblePersons = setOf(vehicleResponsiblePersonAndUserUpdate.first),
                        successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                    )
                } else {
                    log.info(
                        "Finished updating vehicle responsible person with employee number: ${vehicleResponsiblePersonAndUserUpdate.first.employeeNumber.value}."
                    )
                    UpdateVehicleResponsiblePersonsResult(
                        failedVehicleResponsiblePersons = emptySet(),
                        successfullyUpdatedVehicleResponsiblePersons =
                            setOf(vehicleResponsiblePersonAndUserUpdate.first),
                    )
                }
            }
            .onErrorResume(RepairfixException::class.java) { _ ->
                log.warn(
                    "Error while trying to update vehicle responsible person with employee number : ${vehicleResponsiblePersonAndUserUpdate.first.employeeNumber.value}."
                )
                Mono.just(
                    UpdateVehicleResponsiblePersonsResult(
                        failedVehicleResponsiblePersons = setOf(vehicleResponsiblePersonAndUserUpdate.first),
                        successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                    )
                )
            }
    }

    private fun addVehicleResponsiblePersonChunk(
        chunk: List<VehicleResponsiblePersonAndUser>
    ): Flux<AddVehicleResponsiblePersonsResult> =
        userApi
            .porscheUsersControllerAdd(chunk.map(VehicleResponsiblePersonAndUser::second))
            .flatMapIterable { listOf(it) }
            .flatMap { operationResult ->
                if (!operationResult.success) {
                    reduceChunkSizeAndRetry(chunk)
                } else
                    Flux.fromIterable(
                        listOf(
                            AddVehicleResponsiblePersonsResult(
                                failedVehicleResponsiblePersons = emptySet(),
                                successfullyAddedVehicleResponsiblePersons =
                                    chunk.map(VehicleResponsiblePersonAndUser::first).toSet(),
                            )
                        )
                    )
            }
            .onErrorResume(RepairfixException::class.java) {
                when (it.type) {
                    RepairfixException.Type.BUSINESS -> reduceChunkSizeAndRetry(chunk = chunk, exception = it)
                    RepairfixException.Type.TECHNICAL -> Mono.error(it.toVehicleResponsiblePersonMigrationException())
                }
            }

    private fun reduceChunkSizeAndRetry(
        chunk: List<VehicleResponsiblePersonAndUser>,
        exception: RepairfixException? = null,
    ): Flux<AddVehicleResponsiblePersonsResult> {
        // if we are already at size one just return the failed vehicle responsible person as single
        // failed result
        if (1 == chunk.size) {
            log.warn(
                """User with employee number: ${chunk.single().first.employeeNumber.value} failed migration due to: 
                    |${exception?.message ?: "operation success = false."}"""
                    .trimMargin()
            )
            return Flux.fromIterable(
                listOf(
                    AddVehicleResponsiblePersonsResult(
                        failedVehicleResponsiblePersons = chunk.map(VehicleResponsiblePersonAndUser::first).toSet(),
                        successfullyAddedVehicleResponsiblePersons = emptySet(),
                    )
                )
            )
        }
        // reduce chunk size and retry
        val reducedChunkSize = getReducedChunkSize(chunk.size)
        log.debug("Error with chunk size ${chunk.size}. Retrying with reduced chunk size $reducedChunkSize")
        return Flux.fromIterable(chunk.chunked(reducedChunkSize).map { addVehicleResponsiblePersonChunk(it) })
            .flatMap { result -> result }
    }

    private fun getReducedChunkSize(currentChunkSize: Int): Int = ceil(currentChunkSize.toDouble().div(10)).toInt()

    companion object {
        private const val DEFAULT_PAGE_SIZE = 100
        private const val DEFAULT_CHUNK_SIZE = 10
        // be aware pages are starting with 1, changing this also requires to refactor
        // [getMaxPageNumber]
        private const val START_PAGE = 1
        private val log = LoggerFactory.getLogger(RepairfixUsersAdapter::class.java)
    }
}
