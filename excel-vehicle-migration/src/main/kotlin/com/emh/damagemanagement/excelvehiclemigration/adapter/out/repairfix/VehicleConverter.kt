/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.Vin
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AddCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarInsuranceDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarInsuranceDto

fun Vehicle.toAddCarDto(): AddCarDto =
    AddCarDto(
        vin = this.vin.value,
        carPartnerId = this.vGuid,
        make = this.make,
        model = this.model,
        isPool = this.isPoolVehicle,
        licensePlate = this.licensePlate?.ifBlank { null },
        /**
         * 2024-02-07 motum/repairfix told us, that fleetIdentifier can be either repairfix-internal fleetId or
         * fleetPartnerId. So we will use fleetPartnerId (fleetId within our domain), which saves us some calls and
         * mapping.
         */
        fleetIdentifier = this.fleetId,
        /**
         * 2024-02-07 motum/repairfix told us, that driver can be either repairfix-internal userId or userPartnerId. So
         * we will use userPartnerId (employeeNumber within our domain), which saves us some calls and mapping.
         */
        driver = this.responsiblePerson?.value,
        insurance =
            CarInsuranceDto(
                /**
                 * FPT1-514 With the changes in fleetId for pool vehicles we lost access to leasingArt for leasing
                 * vehicles. This is a problem downstream for PVCC, as this information is required there. As
                 * VehicleService is not ready yet, we have to find a place within repairfix to store and later retrieve
                 * leasingArt value. We are misusing insuranceBroker for now, as no proper field is available for this
                 * purpose.
                 */
                insuranceBroker = "$INSURANCE_BROKER_LEASING_PREFIX${this.leasingArt}",
                insuranceCompany = INSURANCE_COMPANY_DEFAULT,
                costCenter = this.costCenter,
                /**
                 * FPT1-986 PACE requires additional using cost center information. As we have no proper place for this
                 * information within repairfix we, once again, misuse and existing, unused property of the insurance to
                 * save this information in repairfix and fetch it again later, during damage file migration.
                 *
                 * All of this will no longer be necessary, once VehicleService is able to provide vehicle data (and
                 * updates) to DamageManagementService.
                 *
                 * FPT1-1151 switched to address, as phone got validation from repairfix and is no longer working
                 */
                insurerAddress = "$INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX${this.usingCostCenter}",
            ),
    )

fun Car.toVehicle(): Vehicle =
    Vehicle(
            vin = Vin(this.vin),
            vGuid = requireNotNull(this.carPartnerId) { "VGUID (carPartnerId) is missing." },
            model = this.model,
            licensePlate = this.licensePlate ?: "",
            fleetId = requireNotNull(this.fleetPartnerId) { "FleetId is missing." },
            responsiblePerson = this.driver?.partnerId?.let { EmployeeNumber(it) },
            /**
             * For some weird reason we can get a list of insurances back from repairfix, but can only provide one
             * during onboarding.
             */
            costCenter = this.insurances?.firstOrNull()?.costCenter,
            /**
             * FPT1-514 With the changes in fleetId for pool vehicles we lost access to leasingArt for leasing vehicles.
             * This is a problem downstream for PVCC, as this information is required there. As VehicleService is not
             * ready yet, we have to find a place within repairfix to store and later retrieve leasingArt value. We are
             * misusing insuranceBroker for now, as no proper field is available for this purpose.
             */
            leasingArt =
                this.insurances?.firstOrNull()?.insuranceBroker?.replace(INSURANCE_BROKER_LEASING_PREFIX, "") ?: "",
            isActive = this.isActive,
            /**
             * FPT1-986 PACE requires additional using cost center information. As we have no proper place for this
             * information within repairfix we, once again, misuse and existing, unused property of the insurance to
             * save this information in repairfix and fetch it again later, during damage file migration.
             *
             * All of this will no longer be necessary, once VehicleService is able to provide vehicle data (and
             * updates) to DamageManagementService.
             *
             * FPT1-1151 switched to address, as phone got validation from repairfix and is no longer working
             */
            usingCostCenter =
                this.insurances?.firstOrNull()?.insurerAddress?.replace(INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX, "")
                    ?: "",
        )
        .apply { this.updateExternalId(<EMAIL>) }

/**
 * @param deactivate special flag to indicate that we want to 'deactivate' a vehicle (only set in context of FTP1-530
 *   deactivation of vehicles not in Excel)
 */
fun Vehicle.toUpdateCarDto(deactivate: Boolean = false): UpdateCarDto =
    /**
     * FPT1-543 UpdateCarDto is now using a custom serializer, to ensure that we are only serializing the values that we
     * are actually using/providing and also ensuring that we are sending explicit null-values for the properties we
     * explicitly want to null (like licensePlate).
     *
     * So if you are changing UpdateDto here, or any of the values ALWAYS have an additional look into
     * 'com.emh.damagemanagement.repairfix.adapter.config.UpdateCarDtoSerializer' to match your changes.
     */
    UpdateCarDto(
        vin = this.vin.value,
        carPartnerId = this.vGuid,
        isPool = this.isPoolVehicle,
        licensePlate = this.licensePlate,
        fleetIdentifier = this.fleetId,
        driver = this.responsiblePerson?.value,
        isActive = if (deactivate) false else this.isActive,
        insurance =
            UpdateCarInsuranceDto(
                /**
                 * FPT1-514 With the changes in fleetId for pool vehicles we lost access to leasingArt for leasing
                 * vehicles. This is a problem downstream for PVCC, as this information is required there. As
                 * VehicleService is not ready yet, we have to find a place within repairfix to store and later retrieve
                 * leasingArt value. We are misusing insuranceBroker for now, as no proper field is available for this
                 * purpose.
                 */
                insuranceBroker = "$INSURANCE_BROKER_LEASING_PREFIX${this.leasingArt}",
                insuranceCompany = INSURANCE_COMPANY_DEFAULT,
                costCenter = this.costCenter,
                // FPT1-1151 phone stopped working, when repairfix implemented validation
                insurerAddress = this.usingCostCenter?.let { "$INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX${it}" },
            ),
    )

fun List<Vehicle>.toAddCarDtos(): List<AddCarDto> = this.map { it.toAddCarDto() }.toList()

/**
 * There is a difference in insurance providers for PAG and MHP Leasing, but the differentiation can (no longer) not be
 * made based on LeasingArt.
 *
 * 2024-07-22 Luisa: The default insurance name to be used until we can get something "better" or a dedicated value can
 * be provided by excel/vehicle-provider.
 *
 * 2025-01-20 | FPT1-772 proper value provided
 */
private const val INSURANCE_COMPANY_DEFAULT = "Allianz SE"

/** Custom prefix to be able to use insuranceBroker field, which requires 3 or more characters */
private const val INSURANCE_BROKER_LEASING_PREFIX = "LeasingArt:"
private const val INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX = "UsingCostCenter:"
