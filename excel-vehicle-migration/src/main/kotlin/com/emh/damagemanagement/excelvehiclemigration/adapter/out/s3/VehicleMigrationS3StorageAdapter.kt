/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.s3

import com.emh.damagemanagement.excelvehiclemigration.application.port.StorageException
import com.emh.damagemanagement.excelvehiclemigration.application.port.StorageOutPort
import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.S3StorageClientException
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Profile
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

@Component
@Profile("!test")
class VehicleMigrationS3StorageAdapter(
    @Qualifier("vehicle-migration") private val storageProperties: StorageProperties,
    private val s3StorageClient: S3StorageClient,
) : StorageOutPort {
    init {
        val bucketExists = s3StorageClient.bucketExists(storageProperties.bucket)
        if (!bucketExists) {
            throw StorageException("Bucket [${storageProperties.bucket}] does not exist.")
        } else createUploadPrefix()
    }

    /** Will fetch and return all file keys for given prefix */
    override fun findAllFileKeysForPrefix(prefix: String): List<String> =
        s3StorageClient.findAllFileKeysForPrefix(prefix = prefix, bucket = storageProperties.bucket)

    /** Will download file with given key (qualified resource path/location) */
    override fun downloadFile(key: String): Resource =
        try {
            s3StorageClient.downloadFile(key = key, bucket = storageProperties.bucket)
        } catch (exception: S3StorageClientException) {
            throw StorageException(message = exception.message, cause = exception)
        }

    /** Will move file with given key to another destination, source will be deleted */
    override fun moveFile(sourceKey: String, targetKey: String) =
        try {
            s3StorageClient.moveFile(sourceKey = sourceKey, targetKey = targetKey, bucket = storageProperties.bucket)
        } catch (exception: S3StorageClientException) {
            throw StorageException(message = exception.message, cause = exception)
        }

    /**
     * This is a helper function to create a 0 byte object with the upload-prefix as key. We call this on startup to
     * ensure that the upload "folder" is present when browsing S3 console. This is only done/necessary to provide
     * better user experience for manual file upload and can be removed once we are fully automated in that regard.
     */
    private fun createUploadPrefix() =
        s3StorageClient.createPrefixes(
            prefixes = setOf(PREFIX_VEHICLE_MIGRATION, PREFIX_UPLOAD),
            bucket = storageProperties.bucket,
        )

    companion object {
        private const val PREFIX_VEHICLE_MIGRATION = "vehicle-migration/"
        private const val PREFIX_UPLOAD = "${PREFIX_VEHICLE_MIGRATION}upload/"
    }
}

@ConfigurationProperties("storage.vehicle-migration")
@Qualifier("vehicle-migration")
data class StorageProperties(val bucket: String)
