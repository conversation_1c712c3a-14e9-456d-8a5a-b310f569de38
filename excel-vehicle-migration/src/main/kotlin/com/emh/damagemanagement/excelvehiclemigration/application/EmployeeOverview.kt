/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

data class EmployeeOverview(
    val key: String,
    val employeeNumber: String,
    val firstName: String,
    val lastName: String,
    val companyEmail: String,
    val accountingArea: String,
    val type: EmployeeType,
)

enum class EmployeeType {
    PAG_EMPLOYEE,
    SUBSIDIARY_EMPLOYEE,
}
