/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.application.port.FleetOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class ExcelFleetMigrationService(private val fleetOutPort: FleetOutPort) {

    /** Migrated all managed fleet. These include all leasing fleets, as well as pre- and post-usage fleets. */
    fun migrateManagedFleets() {
        log.info("Migrating leasing fleets.")
        fleetOutPort.migrateLeasingFleets(fleetsToBeMigrated = Fleet.leasingFleets)

        log.info("Migrating pre-/post-usage fleets.")
        fleetOutPort.migratePoolFleets(fleetsToBeMigrated = setOf(Fleet.preUsageFleet, Fleet.postUsageFleet))

        log.info("Finished migrating fleets.")
    }

    fun migratePoolFleets(poolFleets: Set<Fleet>) {
        log.info("Migrating pool fleets.")
        fleetOutPort.migratePoolFleets(fleetsToBeMigrated = poolFleets)

        log.info("Finished migrating pool fleets.")
    }

    /**
     * Checks if [Fleet.leasingFleets] are already migrate to 3rd party system
     *
     * @return return true if all fleets have been migrated successfully.
     */
    fun allManagedFleetsHaveBeenMigrated(): Boolean = fleetOutPort.allFleetsExist(Fleet.leasingFleets)

    companion object {
        private val log = LoggerFactory.getLogger(ExcelFleetMigrationService::class.java)
    }
}
