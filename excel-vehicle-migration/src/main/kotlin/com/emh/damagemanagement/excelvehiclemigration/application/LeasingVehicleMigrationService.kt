/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationService.VehicleMigrationResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component("leasingVehicleMigrationServiceExcelMigration")
class LeasingVehicleMigrationService(
    private val vehicleOutPort: VehicleOutPort,
    private val vehicleResponsiblePersonMigrationService: VehicleResponsiblePersonMigrationService,
) {
    /**
     * Will migrate all leasing vehicles and their associated vehicle responsible persons (aka drivers). Make sure, that
     * pool vehicle and fleet manager migration has happened before calling this function.
     *
     * @param leasingVehicleAndResponsiblePersons parsed rows of leasing vehicles and their respective vehicle
     *   responsible person
     * @param existingVehicles list of currently maintained repairfix vehicles
     * @param existingVehicleResponsiblePersons list of vehicle responsible persons obtained from repairfix, filtered by
     *   their current role (only drivers). This is done to ensure, that we do not migrate fleet managers (again) during
     *   this process.
     */
    fun migrateLeasingVehiclesAndVehicleResponsiblePersons(
        leasingVehicleAndResponsiblePersons: List<VehicleAndResponsiblePerson>,
        existingVehicles: List<Vehicle>,
        existingVehicleResponsiblePersons: List<VehicleResponsiblePerson>,
    ): VehicleMigrationResult {
        // 1. migrate vehicle responsible persons
        val parsedVehicleResponsiblePersons: Set<VehicleResponsiblePerson> =
            leasingVehicleAndResponsiblePersons
                .map { it.second }
                .distinctBy { it.employeeNumber }
                .filterNot { it.isAlsoFleetManager }
                .toSet()
        val employeeNumbersThatFailedAdding: List<EmployeeNumber> =
            try {
                vehicleResponsiblePersonMigrationService
                    .migrateVehicleResponsiblePersons(
                        parsedVehicleAndResponsiblePersons = parsedVehicleResponsiblePersons,
                        existingVehicleResponsiblePersons = existingVehicleResponsiblePersons,
                    )
                    .addVehicleResponsiblePersonsResult
                    .failedVehicleResponsiblePersons
                    .map(VehicleResponsiblePerson::employeeNumber)
            } catch (exception: VehicleResponsiblePersonMigrationException) {
                log.warn("Some vehicle responsible persons failed migration.")
                throw VehicleMigrationException(
                    message = exception.message,
                    cause = exception,
                    canRetry = exception.canRetry,
                    errorType = exception.errorType,
                )
            }

        // 2. migrate vehicles
        // 2.1. partition parsed vehicles based on existing vins in withPotentialUpdates and
        // toBeAdded
        val (vehiclesWithPotentialUpdates, vehiclesToBeAdded) =
            leasingVehicleAndResponsiblePersons
                .map { it.first }
                .partition { vehicle -> existingVehicles.any { it.vin == vehicle.vin } }

        // 2.2. filter vehicles with responsible persons that failed migration
        val (vehiclesWithoutResponsiblePerson, vehiclesWithMigratedOrExistingResponsiblePersons) =
            vehiclesToBeAdded.partition { employeeNumbersThatFailedAdding.contains(it.responsiblePerson) }
        log.trace(
            """Filtering leasing vehicles to only include ones with successfully migrated or existing responsible persons. 
            |Wanted to add ${vehiclesToBeAdded.size} vehicles. 
            |${vehiclesWithMigratedOrExistingResponsiblePersons.size} remained after filtering."""
                .trimMargin()
        )

        // 2.3. add new vehicles
        log.debug("Adding ${vehiclesWithMigratedOrExistingResponsiblePersons.size} new leasing vehicles.")
        log.info(
            "Total leasing vehicles parsed from excel: ${leasingVehicleAndResponsiblePersons.size}, already migrated: ${vehiclesWithPotentialUpdates.size}, new: ${vehiclesToBeAdded.size}"
        )
        val addVehiclesResult = vehicleOutPort.addVehicles(vehiclesWithMigratedOrExistingResponsiblePersons)

        val vehiclesWithActualUpdates =
            vehiclesWithPotentialUpdates
                .filter { vehicleWithPotentialUpdate ->
                    vehicleMustBeUpdated(
                        vehicleToBeMigrated = vehicleWithPotentialUpdate,
                        existingVehicle = existingVehicles.single { it.vin == vehicleWithPotentialUpdate.vin },
                    )
                }
                /**
                 * FPT1-526 map repair-fix car id to vehicle to be used for vehicle update later as "Old" vehicles (like
                 * museum cars) may have "/" in their VIN. As / is a delimiter in url paths, it can not be used as path
                 * parameter
                 */
                .map { vehicle ->
                    vehicle.apply { this.updateExternalId(existingVehicles.single { it.vin == this.vin }.externalId) }
                }

        val updateVehicleResult = vehicleOutPort.updateVehicles(vehiclesWithActualUpdates)

        // NOSONAR
        log.info(
            "New leasing vehicles in excel: ${vehiclesToBeAdded.size}, skipped due to missing vehicle responsible person: ${vehiclesWithoutResponsiblePerson.size}, vehicles to be migrated: ${vehiclesWithMigratedOrExistingResponsiblePersons.size}, " +
                "successful: ${addVehiclesResult.successfullyAddedVehicles.size}, failed: ${addVehiclesResult.failedVehicles.size}"
        )

        log.info(
            "Potential leasing vehicles in excel to be updated: ${vehiclesWithPotentialUpdates.size}, actual: ${vehiclesWithActualUpdates.size}, successful: ${updateVehicleResult.successfulVehicles.size},failed: ${updateVehicleResult.failedVehicles.size}"
        )
        return VehicleMigrationResult(
            skippedVehicleResult = vehiclesWithoutResponsiblePerson.toSet(),
            addVehiclesResult = addVehiclesResult,
            updateVehiclesResult = updateVehicleResult,
        )
    }

    private fun vehicleMustBeUpdated(vehicleToBeMigrated: Vehicle, existingVehicle: Vehicle): Boolean {
        /**
         * We are using .lower() to account for repairfix sided transformation of our inputs (which is at least done to
         * emails)
         */
        val vehicleToBeMigratedHash =
            Objects.hash(
                vehicleToBeMigrated.licensePlate?.lowercase() ?: "",
                vehicleToBeMigrated.responsiblePerson?.value ?: "",
                vehicleToBeMigrated.fleetId.lowercase(),
                vehicleToBeMigrated.costCenter?.lowercase() ?: "",
                vehicleToBeMigrated.usingCostCenter?.lowercase() ?: "",
                vehicleToBeMigrated.leasingArt?.lowercase() ?: "",
            )
        val existingVehicleHash =
            Objects.hash(
                existingVehicle.licensePlate?.lowercase() ?: "",
                existingVehicle.responsiblePerson?.value ?: "",
                existingVehicle.fleetId.lowercase(),
                existingVehicle.costCenter?.lowercase() ?: "",
                existingVehicle.usingCostCenter?.lowercase() ?: "",
                existingVehicle.leasingArt?.lowercase() ?: "",
            )
        return vehicleToBeMigratedHash != existingVehicleHash
    }

    companion object {
        private val log = LoggerFactory.getLogger(LeasingVehicleMigrationService::class.java)
    }
}
