/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationService.VehicleMigrationResult
import com.emh.damagemanagement.excelvehiclemigration.application.parser.VehicleAndResponsiblePersonAndFleet
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component("poolVehicleMigrationServiceExcelMigration")
class PoolVehicleMigrationService(
    private val vehicleOutPort: VehicleOutPort,
    private val fleetMigrationService: ExcelFleetMigrationService,
    private val vehicleResponsiblePersonMigrationService: VehicleResponsiblePersonMigrationService,
) {

    /**
     * Will migrate all Fleet Managers and Pool Vehicles.
     *
     * @param poolVehicleAndFleetManagerAndFleet parsed row containing a pool vehicle, the fleet manager and the fleet
     *   associated with the vehicle
     * @param existingVehicles a list of all vehicles currently maintained by repairfix
     * @param existingVehicleResponsiblePersonsWithFleetIds a list of vehicle responsible persons currently maintained
     *   by repairfix (users) that we determined to NOT be fleet managers (role = driver) and their (assumed empty)
     *   fleets obtained by parsing visibilityScope. This list is required as repairfix user can only maintain a single
     *   role, and we might encounter a user that is currently a driver that is now also a fleet manager
     */
    fun migratePoolVehiclesAndFleetManagers(
        poolVehicleAndFleetManagerAndFleet: List<VehicleAndResponsiblePersonAndFleet>,
        existingVehicles: List<Vehicle>,
        existingVehicleResponsiblePersonsWithFleetIds: List<Pair<VehicleResponsiblePerson, Set<String>>>,
    ): VehicleMigrationResult {
        // 1. onboard pool fleets
        fleetMigrationService.migratePoolFleets(
            poolVehicleAndFleetManagerAndFleet.map { it.third }.distinctBy { it.id }.toSet()
        )

        // 2. onboard pool vehicles
        val poolVehiclesMigrationResult =
            migratePoolVehicles(
                poolVehicles = poolVehicleAndFleetManagerAndFleet.map { it.first },
                existingVehicles = existingVehicles,
            )

        // 3. onboard fleet managers
        try {
            vehicleResponsiblePersonMigrationService.migrateFleetManagers(
                fleetManagerAndFleets = poolVehicleAndFleetManagerAndFleet.map { Pair(it.second, it.third) },
                existingVehicleResponsiblePersonsWithFleetIds = existingVehicleResponsiblePersonsWithFleetIds,
            )
        } catch (exception: VehicleResponsiblePersonMigrationException) {
            log.warn("Some fleet managers failed migration.")
            throw VehicleMigrationException(
                message = exception.message,
                cause = exception,
                canRetry = exception.canRetry,
                errorType = exception.errorType,
            )
        }

        return poolVehiclesMigrationResult
    }

    private fun migratePoolVehicles(
        poolVehicles: List<Vehicle>,
        existingVehicles: List<Vehicle>,
    ): VehicleMigrationResult {

        // 1. migrate vehicles
        // 1.1. partition parsed vehicles based on existing vins in withPotentialUpdates and
        // toBeAdded
        val (vehiclesWithPotentialUpdates, vehiclesToBeAdded) =
            poolVehicles.partition { vehicle -> existingVehicles.any { it.vin == vehicle.vin } }

        // 2.4. add new vehicles
        log.debug("Adding ${vehiclesToBeAdded.size} new pool vehicles.")
        log.info(
            "Total pool vehicles parsed from excel: ${poolVehicles.size}, already migrated: ${vehiclesWithPotentialUpdates.size}, new: ${vehiclesToBeAdded.size}"
        )
        val addVehiclesResult = vehicleOutPort.addVehicles(vehiclesToBeAdded)

        val vehiclesWithActualUpdates =
            vehiclesWithPotentialUpdates
                .filter { vehicleWithPotentialUpdate ->
                    vehicleMustBeUpdated(
                        vehicleToBeMigrated = vehicleWithPotentialUpdate,
                        existingVehicle = existingVehicles.single { it.vin == vehicleWithPotentialUpdate.vin },
                    )
                }
                /**
                 * FPT1-526 map repair-fix car id to vehicle to be used for vehicle update later as "Old" vehicles (like
                 * museum cars) may have "/" in their VIN. As / is a delimiter in url paths, it can not be used as path
                 * parameter
                 */
                .map { vehicle ->
                    vehicle.apply { this.updateExternalId(existingVehicles.single { it.vin == this.vin }.externalId) }
                }

        val updateVehicleResult = vehicleOutPort.updateVehicles(vehiclesWithActualUpdates)

        // NOSONAR
        log.info(
            "New pool vehicles in excel: ${vehiclesToBeAdded.size}, vehicles to be migrated: ${poolVehicles.size}, " +
                "successful: ${addVehiclesResult.successfullyAddedVehicles.size}, failed: ${addVehiclesResult.failedVehicles.size}"
        )

        log.info(
            "Potential pool vehicles in excel to be updated: ${vehiclesWithPotentialUpdates.size}, actual: ${vehiclesWithActualUpdates.size}, successful: ${updateVehicleResult.successfulVehicles.size},failed: ${updateVehicleResult.failedVehicles.size}"
        )

        return VehicleMigrationResult(
            skippedVehicleResult = emptySet(),
            addVehiclesResult = addVehiclesResult,
            updateVehiclesResult = updateVehicleResult,
        )
    }

    private fun vehicleMustBeUpdated(vehicleToBeMigrated: Vehicle, existingVehicle: Vehicle): Boolean {
        /**
         * We are using .lower() to account for repairfix sided transformation of our inputs (which is at least done to
         * emails)
         */
        val vehicleToBeMigratedHash =
            Objects.hash(
                vehicleToBeMigrated.licensePlate?.lowercase() ?: "",
                vehicleToBeMigrated.fleetId.lowercase(),
                vehicleToBeMigrated.costCenter?.lowercase() ?: "",
                vehicleToBeMigrated.usingCostCenter?.lowercase() ?: "",
                vehicleToBeMigrated.leasingArt?.lowercase() ?: "",
            )
        val existingVehicleHash =
            Objects.hash(
                existingVehicle.licensePlate?.lowercase() ?: "",
                existingVehicle.fleetId.lowercase(),
                existingVehicle.costCenter?.lowercase() ?: "",
                existingVehicle.usingCostCenter?.lowercase() ?: "",
                existingVehicle.leasingArt?.lowercase() ?: "",
            )
        return vehicleToBeMigratedHash != existingVehicleHash
    }

    companion object {
        private val log = LoggerFactory.getLogger(PoolVehicleMigrationService::class.java)
    }
}
