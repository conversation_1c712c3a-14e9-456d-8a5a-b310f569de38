/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationService.VehicleMigrationResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import org.springframework.stereotype.Component

@Component("vehicleDeactivationServiceExcelMigration")
class VehicleDeactivationService(private val vehicleOutPort: VehicleOutPort) {

    /**
     * Will calculate delta between provided/parsed vehicles and vehicles currently maintained in repairfix. Any vehicle
     * no longer provided by the parsed import will be deactivated.
     */
    fun deactivateVehicles(parsedVehicles: List<Vehicle>, existingVehicles: List<Vehicle>): VehicleMigrationResult {
        val vehiclesToDeactivate =
            existingVehicles
                .filter { it.isActive }
                .filterNot { existingVehicle -> parsedVehicles.any { existingVehicle.vin == it.vin } }

        val deactivatedVehiclesResult = vehicleOutPort.deactivateVehicles(vehiclesToDeactivate)

        return VehicleMigrationResult(deactivatedVehiclesResult = deactivatedVehiclesResult)
    }
}
