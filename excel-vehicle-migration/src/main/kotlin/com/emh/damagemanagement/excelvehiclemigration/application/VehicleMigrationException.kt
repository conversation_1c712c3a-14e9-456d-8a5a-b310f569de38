/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

class InvalidFMSExportFileException(cause: Throwable) :
    RuntimeException("Provided file is not a valid xlsx file.", cause)

class AddFleetFailedException(val fleetId: String, cause: Throwable?) : RuntimeException(cause)

class VehicleMigrationException(
    val canRetry: <PERSON><PERSON><PERSON>,
    @Suppress("unused") val errorType: ErrorType,
    message: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

class VehicleResponsiblePersonMigrationException(
    val canRetry: <PERSON><PERSON><PERSON>,
    val errorType: ErrorType,
    message: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

enum class ErrorType {
    BUSINESS,
    TECHNICAL,
}

class FleetMigrationException(val errorType: ErrorType, message: String? = null, cause: Throwable? = null) :
    RuntimeException(message, cause)
