/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationService.VehicleMigrationResult.Companion.combine
import com.emh.damagemanagement.excelvehiclemigration.application.parser.FMSExportParser
import com.emh.damagemanagement.excelvehiclemigration.application.parser.FMSExportParserException
import com.emh.damagemanagement.excelvehiclemigration.application.parser.FMSExportParserResult
import com.emh.damagemanagement.excelvehiclemigration.application.parser.VehicleAndResponsiblePersonAndFleet
import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.StorageException
import com.emh.damagemanagement.excelvehiclemigration.application.port.StorageOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import java.io.IOException
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import org.apache.poi.EmptyFileException
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.slf4j.LoggerFactory
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

typealias VehicleAndResponsiblePerson = Pair<Vehicle, VehicleResponsiblePerson>

/** Will handle vehicle migration aka csv -> repairfix */
@Component("vehicleMigrationServiceExcelMigration")
class VehicleMigrationService(
    private val fmsExportParser: FMSExportParser,
    private val storageOutPort: StorageOutPort,
    private val vehicleOutPort: VehicleOutPort,
    private val vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort,
    private val leasingVehicleMigrationService: LeasingVehicleMigrationService,
    private val poolVehicleMigrationService: PoolVehicleMigrationService,
    private val vehicleDeactivationService: VehicleDeactivationService,
) {
    private val datetimeFormatter = DateTimeFormatter.ofPattern(DATE_TIME_PATTERN)

    /**
     * Fetches all unprocessed excel uploads from S3 (using prefix: [PREFIX_UPLOAD]). Successfully processes files will
     * be moved to [PREFIX_SUCCESS], failed ones to [PREFIX_FAILURE].
     *
     * Any files that fil processing due to a technical error will be left in [PREFIX_UPLOAD] and retried during the
     * next job.
     */
    fun migrateVehiclesFromS3() {
        // 1. fetch all files
        val fileKeysToProcess =
            storageOutPort.findAllFileKeysForPrefix(PREFIX_UPLOAD).filter { it.matches(REGEX_EXCEL_FILENAME) }
        log.info("Found ${fileKeysToProcess.size} files for vehicle migration.")
        // 2. process each file individually
        fileKeysToProcess.forEach(this::migrateVehicle)
    }

    fun migrateVehicle(key: String) {
        log.info("Starting migration for file with key: $key.")
        val file =
            try {
                storageOutPort.downloadFile(key)
            } catch (exception: StorageException) {
                log.warn("Error while trying to fetch file with key: $key. Skipping...")
                return
            }

        try {
            val fmsExportParserResult = parseFMSExport(file)
            val migrationResult = migrateAllVehicles(fmsExportParserResult.vehicleAndResponsiblePersonAndFleets)

            if (migrationResult.deactivatedVehiclesResult.successfulVehicles.isNotEmpty()) {
                log.info(
                    "Successfully deactivated ${migrationResult.deactivatedVehiclesResult.successfulVehicles.size} vehicles."
                )
            }
            // 2024-02-12 Luisa: any failed migration has to be retried
            // 2024-02-19 any migration that failed due to a 'business' exception can not be
            // successfully retried and gets moved to 'success with warning'
            when {
                migrationResult.addVehiclesResult.failedVehicles.isNotEmpty() ||
                    migrationResult.updateVehiclesResult.failedVehicles.isNotEmpty() -> {
                    log.info("Migration finished with failed vehicles. Moving file to 'success with warning'.")
                    moveFileToSuccessWithWarning(key)
                    return
                }
                migrationResult.skippedVehicleResult.isNotEmpty() -> {
                    log.info(
                        "Migration finished with skipped vehicles due to failure during vehicle responsible person migration. Moving file to 'success with warning'."
                    )
                    moveFileToSuccessWithWarning(key)
                    return
                }
                fmsExportParserResult.hasParsingErrors -> {
                    /**
                     * If we end up here we have resolved any migration issues but some (unresolvable) parsing errors
                     * still remain. We will not move directly to success but place the file in a 'success with warning'
                     * prefix for manual inspection
                     */
                    moveFileToSuccessWithWarning(key)
                    log.info(
                        "Successfully finished migration for file with key: $key, but file still has parsing errors. Moving file to 'success with warning'."
                    )
                    return
                }
                else -> {
                    // no parsing error and no migration issues
                    moveFileToSuccess(key)
                    log.info("Successfully finished migration for file with key: $key. Moving file to success.")
                }
            }
        } catch (exception: InvalidFMSExportFileException) {
            log.warn("Error while processing file: ${getFilename(key)}. ${exception.message} Moving file to failure.")
            moveFileToFailure(key)
        } catch (exception: FMSExportParserException) {
            log.warn("Error while parsing file: ${getFilename(key)}. ${exception.message} Moving file to failure.")
            moveFileToFailure(key)
        } catch (exception: VehicleMigrationException) {
            if (exception.canRetry) {
                log.warn(
                    "Error while migrating file: ${getFilename(key)}. ${exception.message} Keeping the file in uploaded."
                )
            } else {
                log.warn(
                    "Error while processing file: ${getFilename(key)}. ${exception.message} Moving file to failure."
                )
                moveFileToFailure(key)
            }
        }
    }

    private fun prefixFilenameWithDateTime(filename: String): String =
        "${datetimeFormatter.format(OffsetDateTime.now())}_$filename"

    private fun moveFileToFailure(key: String) {
        val filenameWithDatetimePrefix = prefixFilenameWithDateTime(getFilename(key))
        log.debug("Moving file with name: $filenameWithDatetimePrefix to 'failure'.")
        try {
            storageOutPort.moveFile(sourceKey = key, targetKey = "$PREFIX_FAILURE$filenameWithDatetimePrefix")
        } catch (exception: StorageException) {
            log.warn("Error while trying to move file: $filenameWithDatetimePrefix to 'failure'.")
        }
    }

    private fun moveFileToSuccess(key: String) {
        val filenameWithDatetimePrefix = prefixFilenameWithDateTime(getFilename(key))
        log.debug("Moving file with name: $filenameWithDatetimePrefix to 'success'.")
        try {
            storageOutPort.moveFile(sourceKey = key, targetKey = "$PREFIX_SUCCESS$filenameWithDatetimePrefix")
        } catch (exception: StorageException) {
            log.warn("Error while trying to move file: $filenameWithDatetimePrefix to 'success'.")
        }
    }

    private fun moveFileToSuccessWithWarning(key: String) {
        val filenameWithDatetimePrefix = prefixFilenameWithDateTime(getFilename(key))
        log.debug("Moving file with name: $filenameWithDatetimePrefix to 'success with warning'.")
        try {
            storageOutPort.moveFile(
                sourceKey = key,
                targetKey = "$PREFIX_SUCCESS_WITH_WARNING$filenameWithDatetimePrefix",
            )
        } catch (exception: StorageException) {
            log.warn("Error while trying to move file: $filenameWithDatetimePrefix to 'success with warning'.")
        }
    }

    private fun getFilename(key: String) = key.substringAfterLast("/")

    private fun parseFMSExport(resource: Resource): FMSExportParserResult {
        val workbook =
            try {
                XSSFWorkbook(resource.inputStream)
            } catch (exception: IOException) {
                log.error("Invalid file uploaded. [${resource.filename}]")
                throw InvalidFMSExportFileException(exception)
            } catch (exception: EmptyFileException) {
                log.error("Provided file ${resource.filename} was empty.")
                throw InvalidFMSExportFileException(exception)
            }
        val fmsExportParserResult = fmsExportParser.parseFMSExport(workbook)
        log.debug(
            "Parsed ${fmsExportParserResult.vehicleAndResponsiblePersonAndFleets.size} vehicles and vehicle responsible persons form file."
        )
        return fmsExportParserResult
    }

    private fun migrateAllVehicles(
        parsedVehicleAndResponsiblePersonsAndFleet: Set<VehicleAndResponsiblePersonAndFleet>
    ): VehicleMigrationResult {
        // 1. fetch list of all existing vehicles in repairfix (we need those for comparison of pool
        // and leasing vehicles later)
        val existingVehicles = vehicleOutPort.getAllVehicles()

        // 2. deactivate all other vehicles ('not' contained in the Excel file)
        // do this first, to avoid duplicate license plate issues on repairfix side during migration
        val deactivateVehiclesResult =
            vehicleDeactivationService.deactivateVehicles(
                parsedVehicles = parsedVehicleAndResponsiblePersonsAndFleet.map { it.first },
                existingVehicles = existingVehicles,
            )

        // 3. fetch all existing VehicleResponsiblePerson
        val existingVehicleResponsiblePersons = vehicleResponsiblePersonOutPort.getAllVehicleResponsiblePersons()

        // 4. partition parsed results based on leasing and pool migration
        val (poolVehicleAndFleetManagerAndFleet, leasingVehicleAndResponsiblePersonAndFleet) =
            parsedVehicleAndResponsiblePersonsAndFleet.partition { it.first.isPoolVehicle }

        // 5. migrate pool vehicles, fleet managers and pool fleets
        val poolVehicleMigrationResult =
            poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(
                poolVehicleAndFleetManagerAndFleet = poolVehicleAndFleetManagerAndFleet,
                existingVehicles = existingVehicles,
                existingVehicleResponsiblePersonsWithFleetIds = existingVehicleResponsiblePersons,
            )

        // 6. migrate leasing vehicles and vehicle responsible persons
        val leasingVehicleMigrationResult =
            leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(
                leasingVehicleAndResponsiblePersons =
                    leasingVehicleAndResponsiblePersonAndFleet.map { Pair(it.first, it.second) },
                existingVehicles = existingVehicles,
                existingVehicleResponsiblePersons = existingVehicleResponsiblePersons.map { it.first },
            )

        return leasingVehicleMigrationResult.combine(poolVehicleMigrationResult).combine(deactivateVehiclesResult)
    }

    data class VehicleMigrationResult(
        val skippedVehicleResult: Set<Vehicle> = emptySet(),
        val addVehiclesResult: AddVehiclesResult = AddVehiclesResult.empty,
        val updateVehiclesResult: UpdateVehiclesResult = UpdateVehiclesResult.empty,
        val deactivatedVehiclesResult: UpdateVehiclesResult = UpdateVehiclesResult.empty,
    ) {
        companion object {
            fun VehicleMigrationResult.combine(result: VehicleMigrationResult): VehicleMigrationResult {
                return VehicleMigrationResult(
                    skippedVehicleResult = this.skippedVehicleResult + result.skippedVehicleResult,
                    addVehiclesResult =
                        AddVehiclesResult(
                            successfullyAddedVehicles =
                                this.addVehiclesResult.successfullyAddedVehicles +
                                    result.addVehiclesResult.successfullyAddedVehicles,
                            failedVehicles =
                                this.addVehiclesResult.failedVehicles + result.addVehiclesResult.failedVehicles,
                        ),
                    updateVehiclesResult =
                        UpdateVehiclesResult(
                            successfulVehicles =
                                this.updateVehiclesResult.successfulVehicles +
                                    result.updateVehiclesResult.successfulVehicles,
                            failedVehicles =
                                this.updateVehiclesResult.failedVehicles + result.updateVehiclesResult.failedVehicles,
                        ),
                    deactivatedVehiclesResult =
                        UpdateVehiclesResult(
                            successfulVehicles =
                                this.deactivatedVehiclesResult.successfulVehicles +
                                    result.deactivatedVehiclesResult.successfulVehicles,
                            failedVehicles =
                                this.deactivatedVehiclesResult.failedVehicles +
                                    result.deactivatedVehiclesResult.failedVehicles,
                        ),
                )
            }

            val empty: VehicleMigrationResult =
                VehicleMigrationResult(
                    addVehiclesResult = AddVehiclesResult.empty,
                    skippedVehicleResult = emptySet(),
                    updateVehiclesResult = UpdateVehiclesResult.empty,
                    deactivatedVehiclesResult = UpdateVehiclesResult.empty,
                )
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleMigrationService::class.java)
        private const val PREFIX_VEHICLE_MIGRATION = "vehicle-migration/"
        const val PREFIX_UPLOAD = "${PREFIX_VEHICLE_MIGRATION}upload/"
        const val PREFIX_SUCCESS = "${PREFIX_VEHICLE_MIGRATION}success/"
        const val PREFIX_SUCCESS_WITH_WARNING = "${PREFIX_VEHICLE_MIGRATION}successWithWarning/"
        const val PREFIX_FAILURE = "${PREFIX_VEHICLE_MIGRATION}failure/"
        private const val DATE_TIME_PATTERN = "yyyy-MM-dd_HH-mm-ss"
        val REGEX_EXCEL_FILENAME = Regex("^.+.(xlsx|XLSX)\$")
    }
}
