/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.employee.adapter.EmployeeException
import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehicleResponsiblePersonsResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.EmployeeOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonsMigrationResult
import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

typealias FleetManager = VehicleResponsiblePerson

typealias FleetManagerAndFleet = Pair<FleetManager, Fleet>

@Component("vehicleResponsiblePersonMigrationServiceExcelMigration")
class VehicleResponsiblePersonMigrationService(
    private val vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort,
    @Value("\${feature-flags.vehicle-responsible-person-migration-uses-user-service}")
    private val requestDataFromUserService: Boolean,
    private val employeeOutPort: EmployeeOutPort,
) {

    /**
     * Will migrate given set of [VehicleResponsiblePerson]. [VehicleResponsiblePerson]s that fail migration will end up
     * in the [AddVehicleResponsiblePersonsResult] as failed persons. The result may later be checked to exclude
     * vehicles from migration that are based on a failed vehicle responsible person.
     */
    fun migrateVehicleResponsiblePersons(
        parsedVehicleAndResponsiblePersons: Set<VehicleResponsiblePerson>,
        existingVehicleResponsiblePersons: List<VehicleResponsiblePerson>,
    ): VehicleResponsiblePersonsMigrationResult {

        if (parsedVehicleAndResponsiblePersons.isEmpty()) return VehicleResponsiblePersonsMigrationResult.empty

        val vehicleResponsiblePersons =
            /**
             * If flag is set we try to fetch employee information updates from user-service to keep our vehicle
             * responsible persons up-to-date.
             */
            if (requestDataFromUserService)
                getVehicleResponsiblePersonUpdatesFromEmployeeProvider(parsedVehicleAndResponsiblePersons)
            else parsedVehicleAndResponsiblePersons

        //  filter persons to only include 'new' ones
        val (vehicleResponsiblePersonsWithPotentialUpdates, vehicleResponsiblePersonsToBeAdded) =
            vehicleResponsiblePersons.partition { vehicleResponsiblePerson ->
                existingVehicleResponsiblePersons.any { it.employeeNumber == vehicleResponsiblePerson.employeeNumber }
            }

        // NOSONAR
        log.info(
            "Total unique vehicle responsible parsed from excel: ${parsedVehicleAndResponsiblePersons.size}, already migrated: ${vehicleResponsiblePersonsWithPotentialUpdates.size}, new: ${vehicleResponsiblePersonsToBeAdded.size}"
        )

        log.debug("Adding ${vehicleResponsiblePersonsToBeAdded.size} vehicle responsible persons.")
        val addVehicleResponsiblePersonsResult =
            vehicleResponsiblePersonOutPort.addVehicleResponsiblePersons(vehicleResponsiblePersonsToBeAdded)

        val parsedVehicleResponsiblePersonsWithActualUpdates =
            vehicleResponsiblePersonsWithPotentialUpdates.filter { parsedVehicleResponsiblePersonWithPotentialUpdates ->
                vehicleResponsiblePersonHasToBeUpdated(
                    vehicleResponsiblePersonToBeMigrated = parsedVehicleResponsiblePersonWithPotentialUpdates,
                    existingVehicleResponsiblePerson =
                        existingVehicleResponsiblePersons.single {
                            parsedVehicleResponsiblePersonWithPotentialUpdates.employeeNumber == it.employeeNumber
                        },
                )
            }
        val updateVehicleResponsiblePersonsResult =
            vehicleResponsiblePersonOutPort.updateVehicleResponsiblePersons(
                parsedVehicleResponsiblePersonsWithActualUpdates
            )
        // NOSONAR
        log.info(
            "New vehicle responsible person in excel to be migrated: ${vehicleResponsiblePersonsToBeAdded.size}, successful: ${addVehicleResponsiblePersonsResult.successfullyAddedVehicleResponsiblePersons.size},failed: ${addVehicleResponsiblePersonsResult.failedVehicleResponsiblePersons.size}"
        )
        // NOSONAR
        log.info(
            "Potential Vehicle responsible person in excel to be updated: ${vehicleResponsiblePersonsWithPotentialUpdates.size}, actual: ${parsedVehicleResponsiblePersonsWithActualUpdates.size}, successful: ${updateVehicleResponsiblePersonsResult.successfullyUpdatedVehicleResponsiblePersons.size},failed: ${updateVehicleResponsiblePersonsResult.failedVehicleResponsiblePersons.size}"
        )

        return VehicleResponsiblePersonsMigrationResult(
            addVehicleResponsiblePersonsResult = addVehicleResponsiblePersonsResult,
            updateVehicleResponsiblePersonsResult = updateVehicleResponsiblePersonsResult,
        )
    }

    /**
     * Will migrate given set of [VehicleResponsiblePerson] representing FleetManagers.
     *
     * @param fleetManagerAndFleets parsed excel row containing a fleet manager and a single associated fleet. A single
     *   fleet manager will most likely be contained multiple times, grouping will be done within this function
     * @param existingVehicleResponsiblePersonsWithFleetIds a list of vehicle responsible persons currently maintained
     *   by repairfix (users) that we determined to NOT be fleet managers (role = driver) and their (assumed empty)
     *   fleets obtained by parsing visibilityScope. This list is required as repairfix user can only maintain a single
     *   role, and we might encounter a user that is currently a driver that is now also a fleet manager
     */
    fun migrateFleetManagers(
        fleetManagerAndFleets: List<FleetManagerAndFleet>,
        existingVehicleResponsiblePersonsWithFleetIds: List<Pair<VehicleResponsiblePerson, Set<String>>>,
    ): VehicleResponsiblePersonsMigrationResult {
        if (fleetManagerAndFleets.isEmpty()) return VehicleResponsiblePersonsMigrationResult.empty

        /**
         * FPT1-516 pre- and post-usage fleet do not get fleet managers (or at least none that are maintained within the
         * excel). So we will ignore al person-fleet relation matching pre- or post-usage. This will exclude pre- and
         * post-usage from visibility scope and also ignore any managers that would only have visibility on pre- and/or
         * post-usage fleets.
         */
        val fleetManagerAndFleetsExcludingPreAndPostUsage =
            fleetManagerAndFleets.filterNot { setOf(Fleet.preUsageFleet, Fleet.postUsageFleet).contains(it.second) }

        val fleetManagersFromEmployeeProvider =
            /**
             * If flag is set we try to fetch employee information updates from user-service to keep our fleet managers
             * up-to-date.
             */
            if (requestDataFromUserService)
                getVehicleResponsiblePersonUpdatesFromEmployeeProvider(
                    fleetManagerAndFleetsExcludingPreAndPostUsage.map { it.first }.toSet()
                )
            else emptySet()

        val fleetManagerToFleets =
            fleetManagerAndFleetsExcludingPreAndPostUsage.groupBy({ it.first }, { it.second }).map {
                Pair(
                    // take fleet manager from employee provider, if exists
                    fleetManagersFromEmployeeProvider.singleOrNull { updatedFleetManager ->
                        it.key.employeeNumber == updatedFleetManager.employeeNumber
                    } ?: it.key,
                    it.value.distinctBy { fleet -> fleet.id }.toSet(),
                )
            }

        //  filter fleet managers to only include 'new' ones
        val (fleetManagersWithPotentialUpdates, vehicleResponsiblePersonsIncludingNonFleetManagers) =
            fleetManagerToFleets.partition { fleetManagerWithFleets ->
                existingVehicleResponsiblePersonsWithFleetIds.any {
                    /**
                     * Be aware that the partition if a fleetManager is "new" based on employee number may lead to
                     * problems during actual onboarding if the user already exists in repairfix but WITHOUT (or a
                     * different) employee number. Then repairfix will throw a 400 bad request, as the user already
                     * exists (based on email) and the entire process crashes.
                     *
                     * TODO: think about switching to email for comparison/partitioning instead.
                     */
                    it.first.employeeNumber == fleetManagerWithFleets.first.employeeNumber
                }
            }
        val fleetManagersToBeAdded =
            vehicleResponsiblePersonsIncludingNonFleetManagers
                // make sure that we do not have non-FleetManagers here by mistake
                .filter { it.first.isAlsoFleetManager }
                .map {
                    Pair(
                        it.first,
                        it.second
                            /**
                             * only use pool fleets for migration (visibility scope), as the original list may contain
                             * leasing fleets (e.g. when fleet manager is also a driver)
                             */
                            .filter { fleet -> fleet.isPoolFleet }
                            .toSet(),
                    )
                }
                // do not include fleet managers without fleets
                .filter { it.second.isNotEmpty() }

        // TODO: fix total count, looks like we get something like user * fleets instead of just
        // users which result in an incorrect total-count
        // NOSONAR
        log.info(
            "Total unique fleet managers parsed from excel: ${fleetManagerToFleets.size}, already migrated: ${fleetManagersWithPotentialUpdates.size}, new: ${vehicleResponsiblePersonsIncludingNonFleetManagers.size}"
        )

        log.debug("Adding ${fleetManagersToBeAdded.size} fleet managers.")
        val addFleetManagerResult = vehicleResponsiblePersonOutPort.addFleetManagers(fleetManagersToBeAdded)

        val parsedFleetManagersWithActualUpdates =
            fleetManagersWithPotentialUpdates.filter { parsedFleetManagersWithPotentialUpdates ->
                val existingFleetManagerAndFleets =
                    existingVehicleResponsiblePersonsWithFleetIds.single {
                        parsedFleetManagersWithPotentialUpdates.first.employeeNumber == it.first.employeeNumber
                    }
                // update if either employee information have changed,
                vehicleResponsiblePersonHasToBeUpdated(
                    vehicleResponsiblePersonToBeMigrated = parsedFleetManagersWithPotentialUpdates.first,
                    existingVehicleResponsiblePerson = existingFleetManagerAndFleets.first,
                ) ||
                    /**
                     * or the responsible fleets differ from the current ones. This will also detect users, that
                     * currently have no fleet assigned (visibilityScope=empty) and get one or more assigned during this
                     * update. This happens if a driver (also) becomes a fleet manager.
                     */
                    responsibleFleetsChanged(
                        newResponsibleFleets = parsedFleetManagersWithPotentialUpdates.second.toSet(),
                        currentResponsibleFleetIds = existingFleetManagerAndFleets.second,
                    )
            }
        val updateFleetManagerResult =
            vehicleResponsiblePersonOutPort.updateFleetManagers(parsedFleetManagersWithActualUpdates)
        // NOSONAR
        log.info(
            "New fleet managers in excel to be migrated: ${fleetManagersToBeAdded.size}, successful: ${addFleetManagerResult.successfullyAddedVehicleResponsiblePersons.size},failed: ${addFleetManagerResult.failedVehicleResponsiblePersons.size}"
        )
        // NOSONAR
        log.info(
            "Potential fleet managers in excel to be updated: ${fleetManagersWithPotentialUpdates.size}, actual: ${parsedFleetManagersWithActualUpdates.size}, successful: ${updateFleetManagerResult.successfullyUpdatedVehicleResponsiblePersons.size},failed: ${updateFleetManagerResult.failedVehicleResponsiblePersons.size}"
        )

        return VehicleResponsiblePersonsMigrationResult(
            addVehicleResponsiblePersonsResult = addFleetManagerResult,
            updateVehicleResponsiblePersonsResult = updateFleetManagerResult,
        )
    }

    private fun vehicleResponsiblePersonHasToBeUpdated(
        vehicleResponsiblePersonToBeMigrated: VehicleResponsiblePerson,
        existingVehicleResponsiblePerson: VehicleResponsiblePerson,
    ): Boolean {
        /**
         * We are using .lower() to account for repairfix sided transformation of our inputs (which is at least done to
         * emails)
         */
        val vehicleResponsiblePersonToBeMigratedHash =
            Objects.hash(
                vehicleResponsiblePersonToBeMigrated.email.lowercase(),
                vehicleResponsiblePersonToBeMigrated.lastName.lowercase(),
                vehicleResponsiblePersonToBeMigrated.firstName.lowercase(),
            )
        val existingVehicleResponsiblePersonHash =
            Objects.hash(
                existingVehicleResponsiblePerson.email.lowercase(),
                existingVehicleResponsiblePerson.lastName.lowercase(),
                existingVehicleResponsiblePerson.firstName.lowercase(),
            )
        return vehicleResponsiblePersonToBeMigratedHash != existingVehicleResponsiblePersonHash
    }

    private fun responsibleFleetsChanged(
        newResponsibleFleets: Set<Fleet>,
        currentResponsibleFleetIds: Set<String>,
    ): Boolean {
        return newResponsibleFleets.map { it.id }.toSet() != currentResponsibleFleetIds
    }

    /**
     * Will try to get the most recent versions of employee data for given set of vehicle responsible persons. Each
     * entry that has a representation in the external provider system will be replaced. Vehicle responsible persons,
     * that can not be found will remain unchanged.
     *
     * Also: matching employees and vehicle responsible persons is done using employee number. Currently, vehicle
     * responsible persons enforce 8 digit employee numbers padded with leading zeros at the start. If this does not
     * match the data provided by user-service you will not get proper matches here.
     */
    private fun getVehicleResponsiblePersonUpdatesFromEmployeeProvider(
        vehicleResponsiblePersons: Set<VehicleResponsiblePerson>
    ): Set<VehicleResponsiblePerson> {
        val currentEmployees =
            try {
                employeeOutPort.getEmployeeOverviewsFor(vehicleResponsiblePersons.map { it.employeeNumber.value })
            } catch (exception: EmployeeException) {
                log.warn(
                    "Could not retrieve employee overviews for employee numbers: [${vehicleResponsiblePersons.joinToString(", ")}]."
                )
                throw VehicleResponsiblePersonMigrationException(
                    canRetry = false,
                    message = "Error while trying to retrieve employee updates.",
                    errorType = ErrorType.TECHNICAL,
                    cause = exception,
                )
            }
        return vehicleResponsiblePersons
            .map { vehicleResponsiblePerson ->
                val updatedEmployee =
                    currentEmployees.singleOrNull { vehicleResponsiblePerson.employeeNumber.value == it.employeeNumber }
                updatedEmployee?.toVehicleResponsiblePerson() ?: vehicleResponsiblePerson
            }
            .toSet()
    }

    private fun EmployeeOverview.toVehicleResponsiblePerson(): VehicleResponsiblePerson =
        VehicleResponsiblePerson(
            employeeNumber = EmployeeNumber(this.employeeNumber),
            firstName = this.firstName,
            lastName = this.lastName,
            email = this.companyEmail,
        )

    companion object {
        private val log = LoggerFactory.getLogger(VehicleResponsiblePersonMigrationService::class.java)
    }
}
