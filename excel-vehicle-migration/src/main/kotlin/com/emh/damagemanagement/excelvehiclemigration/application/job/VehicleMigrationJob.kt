/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.job

import com.emh.damagemanagement.excelvehiclemigration.application.ExcelFleetMigrationService
import com.emh.damagemanagement.excelvehiclemigration.application.FleetMigrationException
import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationException
import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationService
import java.util.*
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class VehicleMigrationJob(
    private val vehicleMigrationService: VehicleMigrationService,
    private val fleetMigrationService: ExcelFleetMigrationService,
) : Job {

    override fun execute(context: JobExecutionContext?) {
        log.info("Starting scheduled vehicle migration.")
        // migrate fleets
        try {
            val fleetsAlreadyMigrated = fleetMigrationService.allManagedFleetsHaveBeenMigrated()
            if (!fleetsAlreadyMigrated) {
                log.info("Starting fleet migration.")
                fleetMigrationService.migrateManagedFleets()
                log.info("Finished fleet migration.")
            } else {
                log.info("All fleets have already been migrated. Skipping fleet migration.")
            }
        } catch (exception: FleetMigrationException) {
            log.warn("Fleet migration failed.", exception)
        }

        // migrate vehicles
        try {
            vehicleMigrationService.migrateVehiclesFromS3()

            log.info("Finished scheduled vehicle migration.")
        } catch (exception: VehicleMigrationException) {
            log.warn("Vehicle migration failed.", exception)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleMigrationJob::class.java)
    }
}

@Configuration
class VehicleMigrationJobConfig {

    @Bean
    @Qualifier("vehicleMigrationJob")
    fun vehicleMigrationJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(VehicleMigrationJob::class.java)
            .storeDurably()
            .withIdentity("VehicleMigrationJobDetail")
            .withDescription("Invoke vehicle migration job")
            .build()
    }

    @Bean
    fun vehicleMigrationJobTrigger(
        @Qualifier("vehicleMigrationJob") job: JobDetail,
        @Value("\${vehicle-migration.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("VehicleMigrationJobTrigger")
            .withDescription("Vehicle migration job trigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
