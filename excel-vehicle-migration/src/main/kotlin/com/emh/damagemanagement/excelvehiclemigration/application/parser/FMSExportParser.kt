/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.parser

import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.excelvehiclemigration.domain.Vin
import kotlin.reflect.KClass
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

typealias ColumnNamesToIndex = Map<String, Int>

typealias VehicleAndResponsiblePersonAndFleet = Triple<Vehicle, VehicleResponsiblePerson, Fleet>

/**
 * A parser that is able to parse an excel file created by FMS(fleet management system)-export. It has hardcoded
 * assumption about either the column position or name when identifying properties to be mapped, as well as their
 * respective data types.
 */
@Component
class FMSExportParser {

    /**
     * parses given workbook for vehicles and their respective vehicle responsible persons
     *
     * @throws FMSExportParserException in case of errors.
     */
    fun parseFMSExport(exportedWorkbook: XSSFWorkbook): FMSExportParserResult {
        log.info("## Starting excel parsing ##")
        // we assume that the exported file does only contain a single sheet at position 0
        val sheet =
            try {
                exportedWorkbook.getSheetAt(0)
            } catch (exception: IllegalArgumentException) {
                log.error("Could not find sheet on position 0.")
                throw FMSExportParserException("Could not find sheet on position 0.", exception)
            }
        // fail-fast for empty sheets
        if (START_ROW_INDEX >= sheet.physicalNumberOfRows) {
            val error =
                "Excel sheet does not contain any data rows (current: ${sheet.physicalNumberOfRows}, expected: more than 2 rows in total)."
            log.warn(error)
            throw FMSExportParserException(error)
        }

        val columNamesToIndex = getColumNamesToIndex(sheet.getRow(ROW_INDEX_HEADER))

        /**
         * ResultMap <Row, VehicleAndVehicleResponsiblePerson> Any entry <Row, null> indicates a parsing error and can
         * be used for further error handling or logging if required
         */
        val rowToVehicleAndResponsiblePersonAndFleets =
            sheet
                .filter { START_ROW_INDEX <= it.rowNum }
                .filter { it.isNotEmpty() }
                .associateWith {
                    try {
                        it.toVehicleAndResponsiblePersonAndFleet(columNamesToIndex)
                    } catch (exception: FMSExportParserException) {
                        // do dot fail if a row fails parsing
                        log.debug("Skipping row ${it.rowNum} due to parsing error.")
                        null
                    }
                }
        val vehicleAndResponsiblePersonAndFleets =
            rowToVehicleAndResponsiblePersonAndFleets.values.filterNotNull().toSet()
        log.info(
            "## Finished excel parsing. Successfully parsed ${vehicleAndResponsiblePersonAndFleets.size} of ${sheet.physicalNumberOfRows - START_ROW_INDEX} rows. ##"
        )
        return FMSExportParserResult(
            vehicleAndResponsiblePersonAndFleets = vehicleAndResponsiblePersonAndFleets,
            // if any row failed parsing (null in values) we treat the whole parsing process as
            // "success with warnings"
            hasParsingErrors = rowToVehicleAndResponsiblePersonAndFleets.values.contains(null),
        )
    }

    private fun getColumNamesToIndex(headerRow: Row): ColumnNamesToIndex {
        try {
            val columnNamesToIndex =
                headerRow.cellIterator().asSequence().associate { Pair(it.stringCellValue, it.columnIndex) }
            // check if all required columns are present
            if (!columnNamesToIndex.keys.containsAll(REQUIRED_COLUMNS))
                throw FMSExportParserException(
                    "Excel file is missing at least one required column. Required: ${REQUIRED_COLUMNS.joinToString(", ")}."
                )
            return columnNamesToIndex
        } catch (exception: IllegalArgumentException) {
            log.warn("Error while trying to parse header row.")
            throw FMSExportParserException("Error while trying to parse header row.", exception)
        }
    }

    private fun Row.toVehicleAndResponsiblePersonAndFleet(
        columnNamesToIndex: ColumnNamesToIndex
    ): VehicleAndResponsiblePersonAndFleet {
        val vehicleResponsiblePerson = this.toVehicleResponsiblePerson(columnNamesToIndex)
        val fleet = this.toFleet(columnNamesToIndex, vehicleResponsiblePerson)
        val vehicle =
            this.toVehicle(
                columnNamesToIndex = columnNamesToIndex,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
                fleet = fleet,
            )

        return Triple(vehicle, vehicleResponsiblePerson, fleet)
    }

    private fun Row.toFleet(
        columnNamesToIndex: ColumnNamesToIndex,
        vehicleResponsiblePerson: VehicleResponsiblePerson,
    ): Fleet =
        try {
            val fleetId =
                getOptionalStringCellValueForColumnIndex(
                    this,
                    requireNotNull(columnNamesToIndex[COLUMN_NAME_LEASING_ART]),
                    Vehicle::class,
                    columnNamesToIndex,
                )
            val primStatus =
                getStringCellValueForColumnIndex(
                    this,
                    requireNotNull(columnNamesToIndex[COLUMN_NAME_PRIM_STATUS]),
                    Vehicle::class,
                    columnNamesToIndex,
                )
            Fleet.determineFleet(
                primStatus = primStatus,
                fleetId = fleetId,
                firstName = vehicleResponsiblePerson.firstName,
                lastName = vehicleResponsiblePerson.lastName,
                employeeNumber = vehicleResponsiblePerson.employeeNumber.value,
            )
        } catch (exception: IllegalArgumentException) {
            val message =
                logParsingErrorAsWarning(this.rowNum, null, Vehicle::class, exception.message, columnNamesToIndex)
            throw FMSExportParserException(message, exception)
        }

    private fun Row.toVehicle(
        columnNamesToIndex: ColumnNamesToIndex,
        vehicleResponsiblePerson: VehicleResponsiblePerson,
        fleet: Fleet,
    ): Vehicle =
        try {
            Vehicle(
                vin =
                    Vin(
                        getStringCellValueForColumnIndex(
                            this,
                            requireNotNull(columnNamesToIndex[COLUMN_NAME_VIN]),
                            Vehicle::class,
                            columnNamesToIndex,
                        )
                    ),
                vGuid =
                    getStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_VGUID]),
                        Vehicle::class,
                        columnNamesToIndex,
                    ),
                model =
                    getStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_MODEL]),
                        Vehicle::class,
                        columnNamesToIndex,
                    ),
                licensePlate =
                    getOptionalStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_LICENSE_PLATE]),
                        Vehicle::class,
                        columnNamesToIndex,
                    ),
                fleetId = fleet.id,
                responsiblePerson = vehicleResponsiblePerson.employeeNumber,
                costCenter =
                    getStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_COST_CENTER]),
                        Vehicle::class,
                        columnNamesToIndex,
                    ),
                usingCostCenter =
                    getOptionalStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_USING_COST_CENTER]),
                        Vehicle::class,
                        columnNamesToIndex,
                    ),
                leasingArt =
                    getOptionalStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_LEASING_ART]),
                        Vehicle::class,
                        columnNamesToIndex,
                    ),
                // vehicles contained in the Excel file are always considered to be active
                isActive = true,
            )
        } catch (exception: IllegalArgumentException) {
            val message =
                logParsingErrorAsWarning(this.rowNum, null, Vehicle::class, exception.message, columnNamesToIndex)
            throw FMSExportParserException(message, exception)
        }

    private fun Row.toVehicleResponsiblePerson(columnNamesToIndex: ColumnNamesToIndex): VehicleResponsiblePerson =
        try {
            VehicleResponsiblePerson(
                employeeNumber =
                    EmployeeNumber(
                        getStringCellValueForColumnIndex(
                            this,
                            requireNotNull(columnNamesToIndex[COLUMN_NAME_EMPLOYEE_NUMBER]),
                            VehicleResponsiblePerson::class,
                            columnNamesToIndex,
                        )
                    ),
                firstName =
                    getStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_FIRST_NAME]),
                        VehicleResponsiblePerson::class,
                        columnNamesToIndex,
                    ),
                lastName =
                    getStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_LAST_NAME]),
                        VehicleResponsiblePerson::class,
                        columnNamesToIndex,
                    ),
                email =
                    getStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_EMAIL]),
                        VehicleResponsiblePerson::class,
                        columnNamesToIndex,
                    ),
                fleetId =
                    getOptionalStringCellValueForColumnIndex(
                        this,
                        requireNotNull(columnNamesToIndex[COLUMN_NAME_LEASING_ART]),
                        Vehicle::class,
                        columnNamesToIndex,
                    ),
            )
        } catch (exception: IllegalArgumentException) {
            val message =
                logParsingErrorAsWarning(
                    this.rowNum,
                    null,
                    VehicleResponsiblePerson::class,
                    exception.message,
                    columnNamesToIndex,
                )
            throw FMSExportParserException(message, exception)
        }

    private fun logParsingErrorAsWarning(
        rowNumber: Int,
        columnIndex: Int?,
        domainClass: KClass<*>,
        errorMessage: String?,
        columnNamesToIndex: ColumnNamesToIndex,
    ): String {
        val rowLog = "[Row: ${getVisibleRowNum(rowNumber)}]"
        val columnLog =
            columnIndex
                ?.let { index -> columnNamesToIndex.filter { index == it.value }.keys.singleOrNull() }
                ?.let { columnName -> "[Column: $columnName]" } ?: ""
        val className = "[${domainClass.simpleName}]"
        val warning = "$rowLog $columnLog $className $errorMessage"
        log.warn(warning)
        return warning
    }

    private fun getOptionalStringCellValueForColumnIndex(
        row: Row,
        columnIndex: Int,
        domainClass: KClass<*>,
        columnNamesToIndex: ColumnNamesToIndex,
    ): String? =
        try {
            // any null-cell will be treated as a BLANK cell (avoid null-pointer)
            val cell = row.getCell(columnIndex, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK)
            if (CellType.BLANK == cell.cellType) {
                null
            } else {
                row.getCell(columnIndex).stringCellValue
            }
        } catch (exception: IllegalStateException) {
            val message =
                logParsingErrorAsWarning(
                    row.rowNum,
                    columnIndex,
                    domainClass,
                    "Data conversion error. ${exception.message}",
                    columnNamesToIndex,
                )
            throw FMSExportParserException(message, exception)
        } catch (exception: IllegalArgumentException) {
            val message =
                logParsingErrorAsWarning(row.rowNum, columnIndex, domainClass, exception.message, columnNamesToIndex)
            throw FMSExportParserException(message, exception)
        } catch (exception: NullPointerException) {
            /**
             * This should no longer happen, as we treat null-values as BLANK cells, and blank cells are handled
             * explicitly.
             */
            val message =
                logParsingErrorAsWarning(
                    row.rowNum,
                    columnIndex,
                    domainClass,
                    "Cell value is null.",
                    columnNamesToIndex,
                )
            throw FMSExportParserException(message, exception)
        }

    private fun getStringCellValueForColumnIndex(
        row: Row,
        columnIndex: Int,
        domainClass: KClass<*>,
        columnNamesToIndex: ColumnNamesToIndex,
    ): String {

        val cellValue =
            getOptionalStringCellValueForColumnIndex(
                row = row,
                columnIndex = columnIndex,
                domainClass = domainClass,
                columnNamesToIndex = columnNamesToIndex,
            )

        if (null == cellValue) {
            val message =
                logParsingErrorAsWarning(
                    row.rowNum,
                    columnIndex,
                    domainClass,
                    "Cell value is null.",
                    columnNamesToIndex,
                )
            throw FMSExportParserException(message)
        }

        return cellValue
    }

    private fun Row.isNotEmpty(): Boolean = (-1).toShort() != this.lastCellNum

    /** Offsets rowNum by one to match the percieved one, when viewing the file with excel */
    private fun getVisibleRowNum(rowNum: Int) = rowNum + 1

    companion object {
        private val log = LoggerFactory.getLogger(FMSExportParser::class.java)
        const val COLUMN_NAME_VIN = "FahrzeugidentNr"
        const val COLUMN_NAME_MODEL = "Baureihe"
        const val COLUMN_NAME_LICENSE_PLATE = "Kfz-Kennzeichen"
        const val COLUMN_NAME_LEASING_ART = "Leasing Art"
        const val COLUMN_NAME_FIRST_NAME = "Vorname"
        const val COLUMN_NAME_LAST_NAME = "Nachname"
        const val COLUMN_NAME_EMAIL = "E-mail Empfänger"
        const val COLUMN_NAME_EMPLOYEE_NUMBER = "Interner Fahrzeugverantwortlicher"
        const val COLUMN_NAME_VGUID = "VGUID"
        const val COLUMN_NAME_COST_CENTER = "Kostenstelle Kostenträger"
        const val COLUMN_NAME_PRIM_STATUS = "PrimStatus"
        const val COLUMN_NAME_USING_COST_CENTER = "Nutzende Kostenstelle"
        private val REQUIRED_COLUMNS =
            setOf(
                COLUMN_NAME_LEASING_ART,
                COLUMN_NAME_LICENSE_PLATE,
                COLUMN_NAME_MODEL,
                COLUMN_NAME_VIN,
                COLUMN_NAME_FIRST_NAME,
                COLUMN_NAME_LAST_NAME,
                COLUMN_NAME_EMAIL,
                COLUMN_NAME_EMPLOYEE_NUMBER,
                COLUMN_NAME_VGUID,
                COLUMN_NAME_COST_CENTER,
                COLUMN_NAME_PRIM_STATUS,
            )
        // the index of the first row to hold actual data
        const val START_ROW_INDEX = 2
        const val ROW_INDEX_HEADER = 0
    }
}

data class FMSExportParserResult(
    val vehicleAndResponsiblePersonAndFleets: Set<VehicleAndResponsiblePersonAndFleet>,
    val hasParsingErrors: Boolean,
)
