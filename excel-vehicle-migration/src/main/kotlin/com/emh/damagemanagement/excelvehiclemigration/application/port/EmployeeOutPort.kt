/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.port

import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeOverview

fun interface EmployeeOutPort {
    /**
     * Returns a list of employees for given set of employee numbers. Resulting list will only contain matches in
     * employee master data system.
     *
     * @throws EmployeeOutPortException in case of exception.
     */
    fun getEmployeeOverviewsFor(employeeNumbers: Collection<String>): List<EmployeeOverview>
}
