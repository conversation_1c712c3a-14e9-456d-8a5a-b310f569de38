/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.port

import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet

interface FleetOutPort {

    /** Will migrate given list of managed fleets. Only missing fleets will be created. No update will be performed. */
    fun migrateLeasingFleets(fleetsToBeMigrated: Set<Fleet>)

    /** Will migrate given list of pool fleets. Only missing fleets will be created. No update will be performed. */
    fun migratePoolFleets(fleetsToBeMigrated: Set<Fleet>)

    /** Will check if all provided fleets exist in 3rd party system. */
    fun allFleetsExist(fleetsToCompare: Set<Fleet>): Boolean
}
