/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.port

import org.springframework.core.io.Resource

interface StorageOutPort {

    /** Fetches all file keys for given prefix. */
    fun findAllFileKeysForPrefix(prefix: String): List<String>

    /**
     * Downloads file for given [key].
     *
     * @throws StorageException if file to be downloaded does not exist.
     */
    fun downloadFile(key: String): Resource

    /** Moves file with given [sourceKey] to [targetKey]. Source will be deleted afterward. */
    fun moveFile(sourceKey: String, targetKey: String)
}

class StorageException(message: String? = null, cause: Throwable? = null) :
    RuntimeException(message ?: cause?.message, cause)
