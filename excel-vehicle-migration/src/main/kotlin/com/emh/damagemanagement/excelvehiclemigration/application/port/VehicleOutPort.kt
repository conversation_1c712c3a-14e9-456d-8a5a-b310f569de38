/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.port

import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle

interface VehicleOutPort {

    /**
     * Will add new vehicles to remote system. Adding is done in batches. Existing vehicles will be skipped (delta
     * calculation has to be done beforehand).
     */
    fun addVehicles(vehicles: List<Vehicle>): AddVehiclesResult

    fun getAllVehicles(): List<Vehicle>

    fun updateVehicles(vehicles: List<Vehicle>): UpdateVehiclesResult

    fun deactivateVehicles(vehicles: List<Vehicle>): UpdateVehiclesResult
}

data class AddVehiclesResult(val successfullyAddedVehicles: Set<Vehicle>, val failedVehicles: Set<Vehicle>) {
    companion object {
        val empty
            get() = AddVehiclesResult(emptySet(), emptySet())
    }
}

data class UpdateVehiclesResult(val successfulVehicles: Set<Vehicle>, val failedVehicles: Set<Vehicle>) {
    companion object {
        val empty
            get() = UpdateVehiclesResult(emptySet(), emptySet())
    }
}
