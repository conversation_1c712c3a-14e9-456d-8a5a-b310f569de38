/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.port

import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson

interface VehicleResponsiblePersonOutPort {

    /** Will fetch all users as VehicleResponsiblePerson regardless of role * */
    fun getAllVehicleResponsiblePersons(): List<Pair<VehicleResponsiblePerson, Set<String>>>

    fun addVehicleResponsiblePersons(
        vehicleResponsiblePersons: List<VehicleResponsiblePerson>
    ): AddVehicleResponsiblePersonsResult

    fun addFleetManagers(
        fleetManagerAndFleets: List<Pair<VehicleResponsiblePerson, Set<Fleet>>>
    ): AddVehicleResponsiblePersonsResult

    /**
     * Will update given list of vehicle responsible persons. Currently supported properties include:
     * - first name
     * - last name
     * - email
     */
    fun updateVehicleResponsiblePersons(
        vehicleResponsiblePersons: Collection<VehicleResponsiblePerson>
    ): UpdateVehicleResponsiblePersonsResult

    fun updateFleetManagers(
        fleetManagerAndFleets: Collection<Pair<VehicleResponsiblePerson, Set<Fleet>>>
    ): UpdateVehicleResponsiblePersonsResult
}

data class AddVehicleResponsiblePersonsResult(
    val successfullyAddedVehicleResponsiblePersons: Set<VehicleResponsiblePerson>,
    val failedVehicleResponsiblePersons: Set<VehicleResponsiblePerson>,
) {
    companion object {
        val empty =
            AddVehicleResponsiblePersonsResult(
                successfullyAddedVehicleResponsiblePersons = emptySet(),
                failedVehicleResponsiblePersons = emptySet(),
            )
    }
}

data class UpdateVehicleResponsiblePersonsResult(
    val successfullyUpdatedVehicleResponsiblePersons: Set<VehicleResponsiblePerson>,
    val failedVehicleResponsiblePersons: Set<VehicleResponsiblePerson>,
) {
    companion object {
        val empty =
            UpdateVehicleResponsiblePersonsResult(
                successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                failedVehicleResponsiblePersons = emptySet(),
            )
    }
}

data class VehicleResponsiblePersonsMigrationResult(
    val addVehicleResponsiblePersonsResult: AddVehicleResponsiblePersonsResult,
    val updateVehicleResponsiblePersonsResult: UpdateVehicleResponsiblePersonsResult,
) {
    companion object {
        val empty =
            VehicleResponsiblePersonsMigrationResult(
                addVehicleResponsiblePersonsResult = AddVehicleResponsiblePersonsResult.empty,
                updateVehicleResponsiblePersonsResult = UpdateVehicleResponsiblePersonsResult.empty,
            )
    }
}
