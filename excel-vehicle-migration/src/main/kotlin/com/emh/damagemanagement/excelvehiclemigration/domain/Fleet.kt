/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.domain

/** This class is the internal domain representation of the fleet. */
class Fleet(id: String, description: String) {
    var id: String = id
        private set

    var description: String = description
        private set

    val isPoolFleet: Boolean
        get() = leasingFleets.none { id == it.id }

    constructor(leasingArt: LeasingArt) : this(id = leasingArt.id, description = leasingArt.description)

    companion object {
        // MANAGED FLEETS
        val preUsageFleet = Fleet(id = "preUsage", description = "Vorlauf")
        val postUsageFleet = Fleet(id = "postUsage", description = "Nachlauf")
        /** Returns all leasing fleets */
        val leasingFleets: Set<Fleet> = LeasingArt.entries.map { it.toFleet() }.toSet()

        private val PRE_USAGE_STATUS = setOf(VehicleStatus.YF10, VehicleStatus.YF11)
        private val POST_USAGE_STATUS =
            setOf(VehicleStatus.YF30, VehicleStatus.YF35, VehicleStatus.YF40, VehicleStatus.YF70)
        private val USAGE_STATUS = setOf(VehicleStatus.YF20)

        /*
         * Will determine fleet based on PrimStatus.
         * Additional information like firstName, lastName and employee number are only need when dynamically creating a pool fleet, as description uses those value.
         * This is done because excel can not supply us with a proper fleet name (as those are kept in moves)
         */
        fun determineFleet(
            primStatus: String,
            fleetId: String?,
            firstName: String?,
            lastName: String?,
            employeeNumber: String?,
        ): Fleet {
            return when {
                PRE_USAGE_STATUS.any { it.status.equals(primStatus, true) } -> preUsageFleet
                POST_USAGE_STATUS.any { it.status.equals(primStatus, true) } -> postUsageFleet
                USAGE_STATUS.any { it.status.equals(primStatus, true) } -> {
                    requireNotNull(fleetId) { "FleetId may not be null." }
                    try {
                        // try first as leasing fleet
                        LeasingArt.of(fleetId).toFleet()
                    } catch (exception: UnknownLeasingFleetException) {
                        requireNotNull(firstName) { "First name may not be null when creating a pool fleet." }
                        requireNotNull(lastName) { "Last name may not be null when creating a pool fleet." }
                        requireNotNull(employeeNumber) { "Employee number may not be null when creating a pool fleet." }
                        Fleet(
                            id =
                                poolFleetId(
                                    firstName = firstName,
                                    lastName = lastName,
                                    employeeNumber = employeeNumber,
                                ),
                            description =
                                poolFleetDescription(
                                    firstName = firstName,
                                    lastName = lastName,
                                    employeeNumber = employeeNumber,
                                ),
                        )
                    }
                }
                else -> throw IllegalArgumentException("PrimStatus $primStatus is not supported.")
            }
        }

        // 2024-07-30 Luise/Elena build description lastName+firstName+employeeNumber
        private fun poolFleetDescription(firstName: String, lastName: String, employeeNumber: String) =
            "$lastName, $firstName ($employeeNumber)"

        /**
         * We are constructing a unique fleetId for pool fleets, as provided leasingArt information is not unique for
         * pool fleets.
         */
        private fun poolFleetId(firstName: String, lastName: String, employeeNumber: String) =
            "${lastName}_${firstName}_${employeeNumber}".replace(" ", "")
    }
}

/** Unique identifier for a fleet. No proper translation available at the time. */
enum class LeasingArt(val id: String, val description: String) {
    L1_1("L1.1", "ÜT2 Leasing"),
    L1_2("L1.2", "Leasing ab P11"),
    L1_3("L1.3", "Treueleasing 25 Jahre"),
    L1_4("L1.4", "Rentner"),
    L2("L2", "Zweitleasing"),
    L9_1("L9.1", "Dienstwagen");

    companion object {
        fun of(id: String): LeasingArt =
            try {
                entries.single { id.equals(it.id, true) }
            } catch (exception: NoSuchElementException) {
                throw UnknownLeasingFleetException("Fleet with id: $id is not known by the system.", exception)
            }
    }
}

enum class VehicleStatus(val status: String, val description: String) {
    QP03("QP03", "Fahrzeug abgelehnt"),
    YF09("YF09", "Fahrzeugverantw. zugeordnet (Rückläufer)"),
    YF10("YF10", "Fahrzeugverantwortlicher zugeordnet"),
    YF11("YF11", "Fahrzeugverantw. zugeordnet (Ersatzfzg.)"),
    YF20("YF20", "Fahrzeug in fahrender Bestand"),
    YF30("YF30", "Fahrzeug zurückgenommen"),
    YF35("YF35", "Für Verkauf bestimmt"),
    YF40("YF40", "Fahrzeug an Verkauf übergeben"),
    YF69("YF69", "Schrottfahrzeug"),
    YF70("YF70", "Fahrzeug zu verschrotten"),
    YF90("YF90", "Fahrzeug gestohlen"),
    YQ98("YQ98", "Fahrzeug verkauft"),
    YQ99("YQ99", "Fakturiert und abgeschlossen"),
    YXXX("YXXX", "Fahrzeug verschrottet"),
}

fun LeasingArt.toFleet() = Fleet(this)

// NOSONAR
class UnknownLeasingFleetException(override val message: String, override val cause: Throwable?) : RuntimeException()
