/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.domain

import java.util.*

/**
 * This class is the internal domain representation of the FMS-exported vehicle.
 *
 * Properties try to match those used in the excel file (our main way of migration vehicles for now).
 */
class Vehicle(
    /**
     * FahrzeugidentNr.
     *
     * Use this when trying to uniquely identify a vehicle (in the excel and the third party system)
     */
    val vin: Vin,
    /**
     * VGUID
     *
     * Globally unique vehicle identifier. Required for PACE
     */
    val vGuid: String?,

    /** Baureihe */
    val model: String,

    /** Kfz-Kennzeichen */
    licensePlate: String?,

    // artificial id for pool fleets or leasing art for leasing vehicles
    val fleetId: String,

    /** A reference to the [VehicleResponsiblePerson] for this vehicle. Only used for leasing vehicles */
    responsiblePerson: EmployeeNumber?,
    // parser will return emptyString from blank cell
    costCenter: String?,
    // parser will return emptyString from blank cell
    usingCostCenter: String?,

    // original value of leasingArt column (required for PVCC later)
    val leasingArt: String?,

    // a flag indicating, if the vehicle is considered 'active'
    val isActive: Boolean,
) {

    init {
        require(model.isNotBlank()) { "Model may not be blank." }
    }

    /** Kfz-Kennzeichen */
    // FPT1-417 licensePlate is now optional (to accommodate pre- and post-usage vehicles)
    val licensePlate: String? = licensePlate?.takeIf { it.isNotBlank() }

    // not defined (in the excel)
    val make: String = "Porsche"

    /** Kostenstelle Kostenträger */
    val costCenter: String? = costCenter?.ifBlank { null }

    /** Kostenträger */
    val usingCostCenter: String? = usingCostCenter?.ifBlank { null }

    /** If given fleetId is not contained in the managed leasing fleets it will be treated as a pool vehicle */
    val isPoolVehicle: Boolean = Fleet.leasingFleets.none { fleetId.equals(it.id, true) }

    /** A reference to the [VehicleResponsiblePerson] for this vehicle */
    val responsiblePerson: EmployeeNumber? =
        if (isPoolVehicle) null
        else requireNotNull(responsiblePerson) { "VehicleResponsiblePerson may not be null for a leasing vehicle" }

    // vehicle identifier in repair fix (required for vehicle update later)
    lateinit var externalId: UUID
        private set

    internal fun updateExternalId(externalId: UUID) {
        this.externalId = externalId
    }
}

@JvmInline
value class Vin(val value: String) {
    init {
        require(value.isNotBlank()) { "VIN may not be blank." }
    }
}
