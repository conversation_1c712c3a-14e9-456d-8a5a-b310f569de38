/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.domain

class VehicleResponsiblePerson(
    /** Uniquely identifies a person */
    val employeeNumber: EmployeeNumber,
    val firstName: String,
    val lastName: String,
    val email: String,
    fleetId: String? = null,
    isFleetManager: Boolean? = null,
) {
    init {
        require(firstName.isNotBlank()) { "First name may not be blank." }
        require(lastName.isNotBlank()) { "Last name may not be blank." }
        require(email.isNotBlank()) { "Email may not be blank." }
    }

    /**
     * Check if fleetId set for this vehicleResponsiblePerson matches a leasing, pre- or post-usage fleet. If false, we
     * assume that this VehicleResponsiblePerson is a fleet manager which is relevant during the actual vehicle
     * responsible person onboarding.
     */
    val isAlsoFleetManager: Boolean =
        isFleetManager
            ?: fleetId?.let {
                (setOf(Fleet.preUsageFleet, Fleet.postUsageFleet) + Fleet.leasingFleets).none { fleet ->
                    fleetId.equals(fleet.id, true)
                }
            }
            ?: true

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VehicleResponsiblePerson

        return employeeNumber == other.employeeNumber
    }

    override fun hashCode(): Int {
        return employeeNumber.hashCode()
    }
}

class EmployeeNumber(employeeNumber: String) {
    /** TODO remove once we solely depend on user service (as service will ensure employee number validity */
    val value: String = employeeNumber.padStart(8, '0')

    init {
        require(8 >= employeeNumber.length) { "Employee number may not exceed 8 characters." }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EmployeeNumber

        return value == other.value
    }

    override fun hashCode(): Int {
        return value.hashCode()
    }
}
