package {{packageName}}.infrastructure;

import org.springframework.core.ParameterizedTypeReference
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpMethod
import org.springframework.http.MediaType
import org.springframework.web.util.UriUtils
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.http.ResponseEntity
import reactor.core.publisher.Mono

open class ApiClient(protected val client: WebClient) {

    protected inline fun <reified I : Any, reified T: Any?> request(requestConfig: RequestConfig<I>): Mono<ResponseEntity<T>> {
        return prepare(defaults(requestConfig))
            .retrieve()
            .toEntity(object : ParameterizedTypeReference<T>() {})
    }

    protected fun <I : Any> prepare(requestConfig: RequestConfig<I>) =
        client.method(requestConfig)
            .uri(requestConfig)
            .headers(requestConfig)
            .body(requestConfig)

    protected fun <I> defaults(requestConfig: RequestConfig<I>) =
        requestConfig.apply {
            if (body != null && headers[HttpHeaders.CONTENT_TYPE].isNullOrEmpty()) {
                headers[HttpHeaders.CONTENT_TYPE] = MediaType.APPLICATION_JSON_VALUE
            }
            if (headers[HttpHeaders.ACCEPT].isNullOrEmpty()) {
                headers[HttpHeaders.ACCEPT] = MediaType.APPLICATION_JSON_VALUE
            }
        }

    protected fun encodeURIComponent(uriComponent: kotlin.String) =
        UriUtils.encodeFragment(uriComponent, Charsets.UTF_8)

    private fun <I> WebClient.method(requestConfig: RequestConfig<I>)=
        method(HttpMethod.valueOf(requestConfig.method.name))

    private fun <I> WebClient.RequestBodyUriSpec.uri(requestConfig: RequestConfig<I>) =
        uri { builder ->
            builder.path(requestConfig.path).apply {
                requestConfig.query.forEach { (name, value) ->
                    queryParam(name, value)
                }
            }.build()
        }

    private fun <I> WebClient.RequestBodySpec.headers(requestConfig: RequestConfig<I>) =
        apply { requestConfig.headers.forEach { (name, value) -> header(name, value) } }

    private fun <I : Any> WebClient.RequestBodySpec.body(requestConfig: RequestConfig<I>) =
        apply { if (requestConfig.body != null) bodyValue(requestConfig.body) }
}