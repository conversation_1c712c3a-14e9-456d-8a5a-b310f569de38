package com.emh.damagemanagement.excelvehiclemigration

import com.emh.damagemanagement.shared.WebTestClientConfiguration
import java.lang.annotation.Inherited
import org.junit.jupiter.api.Tag
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource

@Tag("integration")
@SpringBootTest(
    classes =
        [
            TestVehicleMigrationApplication::class,
            TestObjectMapperConfiguration::class,
            TestAdapterMocks::class,
            TestS3SetupConfig::class,
        ]
)
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
annotation class IntegrationTest

@Tag("integration")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes =
        [
            TestVehicleMigrationApplication::class,
            WebTestClientConfiguration::class,
            TestObjectMapperConfiguration::class,
            TestAdapterMocks::class,
        ],
)
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
annotation class WebServiceTest
