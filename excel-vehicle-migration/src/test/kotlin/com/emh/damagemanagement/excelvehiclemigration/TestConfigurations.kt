package com.emh.damagemanagement.excelvehiclemigration

import com.emh.damagemanagement.excelvehiclemigration.adapter.out.s3.StorageProperties
import com.emh.damagemanagement.excelvehiclemigration.adapter.out.s3.VehicleMigrationS3StorageAdapter
import com.emh.damagemanagement.excelvehiclemigration.application.port.EmployeeOutPort
import com.emh.shared.s3.client.adapter.S3StorageClient
import com.fasterxml.jackson.databind.ObjectMapper
import org.mockito.Mockito.mock
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.DependsOn
import org.springframework.context.annotation.Primary
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CreateBucketRequest

@SpringBootApplication(
    scanBasePackages =
        [
            "com.emh.damagemanagement.excelvehiclemigration.*",
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.repairfix.*",
            "com.emh.damagemanagement.employee.*",
        ]
)
@ConfigurationPropertiesScan(
    basePackages =
        [
            "com.emh.damagemanagement.excelvehiclemigration.*",
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.repairfix.*",
            "com.emh.damagemanagement.employee.*",
        ]
)
class TestVehicleMigrationApplication {

    fun main(args: Array<String>) {
        runApplication<TestVehicleMigrationApplication>(*args)
    }
}

@TestConfiguration
class TestObjectMapperConfiguration {
    @Bean
    @Primary
    fun objectMapper(@Qualifier("repairfix") jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder): ObjectMapper =
        jackson2ObjectMapperBuilder.createXmlMapper(false).build()
}

@TestConfiguration
class TestAdapterMocks {
    @Bean @Primary fun employeeOutPort(): EmployeeOutPort = mock()
}

@TestConfiguration
class TestS3SetupConfig(
    private val s3StorageClient: S3StorageClient,
    @Qualifier("vehicle-migration") private val storageProperties: StorageProperties,
) {

    @Bean
    @DependsOn("prepareVehicleMigrationBucket")
    fun storageOutPort(s3Client: S3Client) =
        VehicleMigrationS3StorageAdapter(storageProperties = storageProperties, s3StorageClient = s3StorageClient)

    /** Custom bean for tests, to ensure bucket creation before S3StorageAdapter init fires */
    @Bean
    fun prepareVehicleMigrationBucket(
        s3Client: S3Client,
        @Value("\${storage.vehicle-migration.bucket}") vehicleMigrationBucket: String,
    ) = BucketInit(s3Client = s3Client, vehicleMigrationBucket = vehicleMigrationBucket)

    class BucketInit(s3Client: S3Client, vehicleMigrationBucket: String) {
        init {
            val buckets = s3Client.listBuckets().buckets().map { it.name() }
            if (buckets.none { vehicleMigrationBucket == it }) {
                s3Client.createBucket(CreateBucketRequest.builder().bucket(vehicleMigrationBucket).build())
            }
        }
    }
}
