package com.emh.damagemanagement.excelvehiclemigration

import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeOverviewDto
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeTypeDto
import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeOverview
import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeType
import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.excelvehiclemigration.domain.Vin
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarDriver
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarInsurance
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.User
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.VisibilityScope
import com.emh.damagemanagement.shared.createRandomString
import java.time.OffsetDateTime
import java.util.*
import kotlin.random.Random
import net.datafaker.Faker

class CarBuilder {
    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var userPartnerId: String = createRandomString(Random.nextInt(6, 8))

    fun vin(vin: String) = apply { this.vin = vin }

    fun userPartnerId(userPartnerId: String) = apply { this.userPartnerId = userPartnerId }

    fun build(): Car {
        val isPool = Random.nextBoolean()
        return Car(
            vin = vin,
            carPartnerId = createRandomString(22),
            make = createRandomString(Random.nextInt(4, 23)),
            model = createRandomString(Random.nextInt(4, 23)),
            isPool = isPool,
            licensePlate = createRandomString(Random.nextInt(4, 23)),
            id = UUID.randomUUID(),
            availability = Car.Availability.AVAILABLE,
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
            isActive = true,
            bodyType = Car.BodyType.sedan,
            fleetId = UUID.randomUUID().toString(),
            fleetPartnerId = if (isPool) PoolFleetBuilder.poolFleets.random() else Fleet.leasingFleets.random().id,
            driver =
                CarDriver(
                    firstName = createRandomString(Random.nextInt(4, 23)),
                    lastName = createRandomString(Random.nextInt(4, 23)),
                    email = createRandomString(Random.nextInt(4, 23)),
                    partnerId = userPartnerId,
                    phoneNumber = createRandomString(Random.nextInt(4, 23)),
                ),
            insurances =
                listOf(
                    CarInsurance(
                        id = UUID.randomUUID(),
                        costCenter = createRandomString(Random.nextInt(4, 23)),
                        insuranceCompany = createRandomString(Random.nextInt(4, 23)),
                        insurerAddress = if (Random.nextBoolean()) createRandomString(Random.nextInt(4, 23)) else null,
                        createdAt = OffsetDateTime.now(),
                        updatedAt = OffsetDateTime.now(),
                    )
                ),
        )
    }

    companion object {
        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<Car> = (1..count).map { CarBuilder().build() }.toSet()
    }
}

class VehicleBuilder {
    private var vin: Vin = Vin(createRandomString(Random.nextInt(4, 23)))
    private var costCenter: String = createRandomString(Random.nextInt(4, 23))
    private var usingCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var vGuid: String = createRandomString(22)
    private var fleetId: String = createRandomString(23)
    private var leasingArt: String = Fleet.leasingFleets.random().id
    private var employeeNumber: EmployeeNumber? = EmployeeNumber(createRandomString(6))
    private var licensePlate: String? = if (Random.nextBoolean()) createRandomString(Random.nextInt(4, 23)) else null
    private var isActive: Boolean = true

    fun vin(vin: Vin) = apply { this.vin = vin }

    fun vGuid(vGuid: String) = apply { this.vGuid = vGuid }

    fun employeeNumber(employeeNumber: EmployeeNumber?) = apply { this.employeeNumber = employeeNumber }

    fun fleetId(fleetId: String) = apply { this.fleetId = fleetId }

    fun costCenter(costCenter: String) = apply { this.costCenter = costCenter }

    fun usingCostCenter(usingCostCenter: String) = apply { this.usingCostCenter = usingCostCenter }

    fun leasingArt(leasingArt: String) = apply { this.leasingArt = leasingArt }

    fun leasing() = apply { this.fleetId = Fleet.leasingFleets.random().id }

    fun licensePlate(licensePlate: String?) = apply { this.licensePlate = licensePlate }

    fun active(active: Boolean = true) = apply { this.isActive = active }

    fun build(): Vehicle {
        return Vehicle(
                vin = this.vin,
                vGuid = this.vGuid,
                model = createRandomString(Random.nextInt(4, 23)),
                licensePlate = this.licensePlate,
                fleetId = this.fleetId,
                responsiblePerson = this.employeeNumber,
                costCenter = this.costCenter,
                usingCostCenter = this.usingCostCenter,
                leasingArt = this.leasingArt,
                isActive = this.isActive,
            )
            .apply { updateExternalId(UUID.randomUUID()) }
    }

    companion object {
        fun buildSingle() = VehicleBuilder().build()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<Vehicle> = (1..count).map { VehicleBuilder().build() }.toSet()
    }
}

class VehicleResponsiblePersonBuilder {
    private val faker = Faker()

    private var employeeNumber: EmployeeNumber = EmployeeNumber(faker.numerify("00######"))
    private var firstName: String = createRandomString(Random.nextInt(4, 23))
    private var lastName: String = createRandomString(Random.nextInt(4, 23))
    private var email: String = createRandomString(Random.nextInt(4, 23))
    private var isFleetManager: Boolean = false

    fun employeeNumber(employeeNumber: EmployeeNumber) = apply { this.employeeNumber = employeeNumber }

    fun firstName(firstName: String) = apply { this.firstName = firstName }

    fun lastName(lastName: String) = apply { this.lastName = lastName }

    fun email(email: String) = apply { this.email = email }

    fun alsoFleetManager() = apply { this.isFleetManager = true }

    fun build(): VehicleResponsiblePerson {
        return VehicleResponsiblePerson(
            employeeNumber = employeeNumber,
            firstName = firstName,
            lastName = lastName,
            email = email,
            isFleetManager = isFleetManager,
        )
    }

    companion object {
        fun buildSingle() = VehicleResponsiblePersonBuilder().build()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<VehicleResponsiblePerson> =
            (1..count).map { VehicleResponsiblePersonBuilder().build() }.toSet()
    }
}

class UserBuilder {
    private var role: User.Role = User.Role.driver

    fun role(role: User.Role) = apply { this.role = role }

    fun build(): User {
        return User(
            id = UUID.randomUUID(),
            role = role,
            firstName = createRandomString(Random.nextInt(4, 23)),
            lastName = createRandomString(Random.nextInt(4, 23)),
            email = createRandomString(Random.nextInt(4, 23)),
            userPartnerId = createRandomString(6),
            visibilityScope =
                (0..Random.nextInt(1, 5)).map { VisibilityScope(fleetPartnerId = PoolFleetBuilder.buildSingle().id) },
        )
    }

    companion object {
        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<User> = (1..count).map { UserBuilder().build() }.toSet()
    }
}

class EmployeeOverviewBuilder {
    private val faker = Faker()

    private var firstName: String = createRandomString(Random.nextInt(4, 23))
    private var lastName: String = createRandomString(Random.nextInt(4, 23))
    private var employeeNumber: String = faker.numerify("00######")
    private var accountingArea: String = createRandomString(4)

    fun employeeNumber(employeeNumber: String) = apply { this.employeeNumber = employeeNumber }

    fun accountingArea(accountingArea: String) = apply { this.accountingArea = accountingArea }

    fun build(): EmployeeOverview {
        return EmployeeOverview(
            key = createRandomString(Random.nextInt(4, 23)),
            firstName = firstName,
            lastName = lastName,
            employeeNumber = employeeNumber,
            accountingArea = accountingArea,
            companyEmail = createRandomString(Random.nextInt(4, 23)),
            type = EmployeeType.entries.random(),
        )
    }

    companion object {

        fun buildSingle(): EmployeeOverview = EmployeeOverviewBuilder().build()

        fun buildMultiple(count: Int): Set<EmployeeOverview> =
            (1..count).map { EmployeeOverviewBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class PoolFleetBuilder {

    fun build(): Fleet {
        return poolFleets.random().let { Fleet(id = it, description = it) }
    }

    companion object {
        val poolFleets = setOf("L9.0", "L9.2", "A1.0", "B2.4", "G4.4", "XX.X", "KF.C")

        fun buildSingle(): Fleet = PoolFleetBuilder().build()

        fun buildMultiple(count: Int): Set<Fleet> =
            (1..count).map { PoolFleetBuilder().build() }.distinctBy { it.id }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(2, 5))
    }
}

fun buildFleetManagersWithFleets(): List<Pair<VehicleResponsiblePerson, Fleet>> =
    (1..40)
        .map { VehicleResponsiblePersonBuilder().alsoFleetManager().build() }
        .map { fleetManager -> (1..5).map { Pair(fleetManager, PoolFleetBuilder.buildSingle()) } }
        .flatten()

fun List<Pair<VehicleResponsiblePerson, Fleet>>.groupedByFleetManagerWithFleet() =
    this.groupBy({ it.first }, { it.second }).map { Pair(it.key, it.value.distinctBy { fleet -> fleet.id }.toSet()) }

fun List<Pair<VehicleResponsiblePerson, Fleet>>.groupedByFleetManagerWithFleetId() =
    this.groupBy({ it.first }, { it.second.id }).map { Pair(it.key, it.value.toSet()) }

class EmployeeOverviewDtoBuilder {
    private val faker = Faker()

    private var firstName: String = createRandomString(Random.nextInt(4, 23))
    private var lastName: String = createRandomString(Random.nextInt(4, 23))
    private var employeeNumber: String = faker.numerify("00######")
    private var accountingArea: String = createRandomString(4)

    fun employeeNumber(employeeNumber: String) = apply { this.employeeNumber = employeeNumber }

    fun accountingArea(accountingArea: String) = apply { this.accountingArea = accountingArea }

    fun build(): EmployeeOverviewDto {
        return EmployeeOverviewDto(
            key = createRandomString(Random.nextInt(4, 23)),
            firstName = firstName,
            lastName = lastName,
            employeeNumber = employeeNumber,
            accountingArea = accountingArea,
            companyEmail = createRandomString(Random.nextInt(4, 23)),
            type = EmployeeTypeDto.entries.random(),
        )
    }

    companion object {

        fun buildSingle(): EmployeeOverviewDto = EmployeeOverviewDtoBuilder().build()

        fun buildMultiple(count: Int): Set<EmployeeOverviewDto> =
            (1..count).map { EmployeeOverviewDtoBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
