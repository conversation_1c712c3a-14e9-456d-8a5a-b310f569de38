/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.employee

import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeOverviewDto
import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeTypeDto
import com.emh.damagemanagement.excelvehiclemigration.EmployeeOverviewDtoBuilder
import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeOverview
import com.emh.damagemanagement.excelvehiclemigration.application.EmployeeType
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class EmployeeOverviewAdapterConverterTest {

    @ParameterizedTest
    @MethodSource("employeeOverviewDtoProvider")
    fun `should convert EmployeeOverviewDto to EmployeeOverview`(employeeOverviewDto: EmployeeOverviewDto) {
        val employeeOverview = employeeOverviewDto.toEmployeeOverview()

        assertThat(employeeOverview).satisfies(employeeOverviewDtoRequirements(employeeOverviewDto))
    }

    companion object {

        private fun employeeOverviewDtoRequirements(employeeOverviewDto: EmployeeOverviewDto) =
            Consumer<EmployeeOverview> { employeeOverview: EmployeeOverview ->
                assertThat(employeeOverview.firstName).isEqualTo(employeeOverviewDto.firstName)
                assertThat(employeeOverview.lastName).isEqualTo(employeeOverviewDto.lastName)
                assertThat(employeeOverview.accountingArea).isEqualTo(employeeOverviewDto.accountingArea)
                assertThat(employeeOverview.companyEmail).isEqualTo(employeeOverviewDto.companyEmail)
                assertThat(employeeOverview.key).isEqualTo(employeeOverviewDto.key)
                assertThat(employeeOverview.employeeNumber).isEqualTo(employeeOverviewDto.employeeNumber)
                assertThat(employeeOverview.type).satisfies(employeeTypeDtoRequirements(employeeOverviewDto.type))
            }

        private fun employeeTypeDtoRequirements(employeeTypeDto: EmployeeTypeDto) =
            Consumer<EmployeeType> { employeeType: EmployeeType ->
                when (employeeType) {
                    EmployeeType.PAG_EMPLOYEE -> assertThat(employeeTypeDto).isEqualTo(EmployeeTypeDto.PAG_EMPLOYEE)
                    EmployeeType.SUBSIDIARY_EMPLOYEE ->
                        assertThat(employeeTypeDto).isEqualTo(EmployeeTypeDto.SUBSIDIARY_EMPLOYEE)
                }
            }

        @JvmStatic
        fun employeeOverviewDtoProvider(): Stream<Arguments> =
            EmployeeOverviewDtoBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
