/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.employee

import com.emh.damagemanagement.employee.generated.adapter.out.rest.model.EmployeeOverviewDto
import com.emh.damagemanagement.excelvehiclemigration.EmployeeOverviewDtoBuilder
import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.fasterxml.jackson.databind.ObjectMapper
import java.io.BufferedReader
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ClassPathResource
import org.springframework.security.oauth2.client.ClientAuthorizationException
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@TestPropertySource(properties = ["employes.base-url=http://localhost:8090"])
class EmployeeOverviewAdapterTest {

    @Autowired private lateinit var employeeAdapter: EmployeeOverviewAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    private val tokenResponse =
        ClassPathResource("token_response.json").inputStream.bufferedReader().use(BufferedReader::readText)

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8090)
    }

    private val validTokenResponse =
        MockResponse.Builder().body(tokenResponse).addHeader("Content-Type", "application/json").code(200).build()

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    @Test
    @DirtiesContext
    fun `should request oauth token and set jwt in authorization header`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(emptyList<EmployeeOverviewDto>()))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        assertThatNoException().isThrownBy {
            employeeAdapter.getEmployeeOverviewsFor(setOf("42"))
            employeeAdapter.getEmployeeOverviewsFor(setOf("42"))
        }
        val tokenRequest: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val employeeRequest: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val employeeRequest2: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val jwt = objectMapper.readTree(tokenResponse)["access_token"].textValue()
        assertThat(tokenRequest.url.toString()).contains("oauth/token")
        assertThat(employeeRequest.headers["Authorization"]).isEqualTo("Bearer $jwt")
        assertThat(employeeRequest2.headers["Authorization"]).isEqualTo("Bearer $jwt")
        assertThat(employeeRequest2.url.toString()).contains("queryEmployeesForDms")
        // should be true, as we assume that token get properly cached
        assertThat(mockWebServer.requestCount).isEqualTo(3)
    }

    @Test
    @DirtiesContext
    fun `should throw ClientAuthorizationException when token could not be obtained`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder().code(401).build()
                }
            }

        assertThatExceptionOfType(ClientAuthorizationException::class.java).isThrownBy {
            employeeAdapter.getEmployeeOverviewsFor(setOf("42"))
        }
    }

    @Test
    @DirtiesContext
    fun `should return employee overviews for employee numbers`() {
        val employees = EmployeeOverviewDtoBuilder.buildMultiple()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(employees))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val employeesFromAdapter = employeeAdapter.getEmployeeOverviewsFor(setOf("42"))
        assertThat(employeesFromAdapter)
            .extracting("employeeNumber")
            .containsExactlyInAnyOrderElementsOf(employees.map { it.employeeNumber })
    }
}
