package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.CarBuilder
import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.VehicleBuilder
import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationException
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AddCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarDetails
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarsList
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.OperationResult
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnauthorizedErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnprocessableEntityErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarDto
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@TestPropertySource(properties = ["repairfix.base-url=http://localhost:8180"])
class RepairfixCarsAdapterTest {
    @Autowired private lateinit var repairfixCarsAdapter: RepairfixCarsAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8180)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val unauthorizedResponse =
        UnauthorizedErrorResponse(
            statusCode = BigDecimal.valueOf(401),
            message = "You need to be authenticated for this operation.",
            error = "Unauthorized",
        )

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")

    private val unprocessableEntityResponse =
        UnprocessableEntityErrorResponse(status = BigDecimal.valueOf(422), message = "I am an error of business.")

    private val okResponse = OperationResult(true)
    private val successFalseResponse = OperationResult(false)

    @Test
    fun `should set apikey in authorization header`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(okResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        assertThatNoException().isThrownBy {
            repairfixCarsAdapter.addVehicles(VehicleBuilder.buildMultiple(42).toList())
        }
        val request: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        assertThat(request.headers["Authorization"]).isEqualTo("Bearer apiKey")
    }

    @Test
    fun `should add vehicles chunked`() {
        val vehicles = VehicleBuilder.buildMultiple(350).toList()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/cars") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixCarsAdapter.addVehicles(vehicles)

        val chunkedVehiclesForComparison = vehicles.map { it.toAddCarDto() }.chunked(100)
        assertThat(mockWebServer.requestCount).isEqualTo(4)
        repeat(4) {
            val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val requestPayload: List<AddCarDto> = objectMapper.readValue(request!!.body!!.utf8())
            assertThat(chunkedVehiclesForComparison.contains(requestPayload)).isTrue()
        }
    }

    @Test
    fun `should reduce chunk size until failing vehicle can be identified when adding cars with 4xx technical error response`() {
        val vehicles = VehicleBuilder.buildMultiple(250).toList()
        val failingVehicle = vehicles.random()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: List<AddCarDto> = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        payload.any { failingVehicle.vin.value == it.vin } ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(422)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val result = repairfixCarsAdapter.addVehicles(vehicles)
        assertThat(result.successfullyAddedVehicles).hasSize(249)
        assertThat(result.failedVehicles.single()).isEqualTo(failingVehicle)
    }

    @Test
    fun `should reduce chunk size until failing vehicle can be identified when adding cars with success=false response`() {
        val vehicles = VehicleBuilder.buildMultiple(250).toList()
        val failingVehicle = vehicles.random()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: List<AddCarDto> = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        payload.any { failingVehicle.vin.value == it.vin } ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(successFalseResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val result = repairfixCarsAdapter.addVehicles(vehicles)
        assertThat(result.successfullyAddedVehicles).hasSize(249)
        assertThat(result.failedVehicles.single()).isEqualTo(failingVehicle)
    }

    @Test
    fun `should throw VehicleMigrationException in case of retryable 4xx response when adding vehicles`() {
        val vehicles = VehicleBuilder.buildMultiple(250).toList()

        // business exception first
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unauthorizedResponse))
                .addHeader("Content-Type", "application/json")
                .code(422)
                .build()
        )
        // misleading retry
        repeat(2) {
            mockWebServer.enqueue(
                MockResponse.Builder()
                    .body(objectMapper.writeValueAsString(okResponse))
                    .addHeader("Content-Type", "application/json")
                    .code(200)
                    .build()
            )
        }
        // technical error to fail and quit
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unauthorizedResponse))
                .addHeader("Content-Type", "application/json")
                .code(401)
                .build()
        )

        assertThatExceptionOfType(VehicleMigrationException::class.java).isThrownBy {
            repairfixCarsAdapter.addVehicles(vehicles)
        }
    }

    @Test
    fun `should throw VehicleMigrationException in case of retryable 4xx response when getting all vehicles`() {
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unauthorizedResponse))
                .addHeader("Content-Type", "application/json")
                .code(401)
                .build()
        )

        assertThatExceptionOfType(VehicleMigrationException::class.java).isThrownBy {
            repairfixCarsAdapter.getAllVehicles()
        }
    }

    @Test
    fun `should return all vehicles`() {
        val resultPages =
            listOf(
                CarsList(cars = CarBuilder.buildMultiple(100).toList(), total = BigDecimal.valueOf(405)),
                CarsList(cars = CarBuilder.buildMultiple(100).toList(), total = BigDecimal.valueOf(405)),
                CarsList(cars = CarBuilder.buildMultiple(100).toList(), total = BigDecimal.valueOf(405)),
                CarsList(cars = CarBuilder.buildMultiple(100).toList(), total = BigDecimal.valueOf(405)),
                CarsList(cars = CarBuilder.buildMultiple(5).toList(), total = BigDecimal.valueOf(405)),
            )

        val totalCarsResultPage = CarsList(cars = emptyList(), total = BigDecimal.valueOf(405))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalCarsResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "2" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[1]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "3" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[2]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "4" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[3]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "5" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[4]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val listOfAllVehicles = repairfixCarsAdapter.getAllVehicles().sortedBy { it.vin.value }
        val resultPageVehicles =
            resultPages.flatMap { page -> page.cars.map { it.toVehicle() } }.sortedBy { it.vin.value }
        assertThat(listOfAllVehicles).zipSatisfy(resultPageVehicles) { vehicle1: Vehicle, vehicle2: Vehicle ->
            assertThat(vehicle1)
                .usingRecursiveComparison()
                .comparingOnlyFields("vin", "model", "isPoolVehicle", "licensePlate")
                .isEqualTo(vehicle2)
        }
    }

    @Test
    fun `should return emptyList if no vehicles exist`() {
        val totalCarsResultPage = CarsList(cars = emptyList(), total = BigDecimal.ZERO)

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(totalCarsResultPage))
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        val listOfAllVehicles = repairfixCarsAdapter.getAllVehicles()

        assertThat(listOfAllVehicles).isEmpty()
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should update vehicles`() {
        val vehicles =
            VehicleBuilder.buildMultiple(3).map { it.apply { this.updateExternalId(UUID.randomUUID()) } }.toList()

        val carDetails = CarDetails(car = CarBuilder().build())

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/cars") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carDetails))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixCarsAdapter.updateVehicles(vehicles)

        val updateCarDtos = vehicles.map { it.toUpdateCarDto() }

        assertThat(mockWebServer.requestCount).isEqualTo(3)
        repeat(3) {
            val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val requestPayload: UpdateCarDto = objectMapper.readValue(request!!.body!!.utf8())
            val pathParam: String = request.url.toString().split("/").last()
            assertThat(updateCarDtos.contains(requestPayload)).isTrue()
            assertThat(requestPayload.isActive).isTrue()
            assertThat(vehicles.map { it.externalId.toString() }.contains(pathParam)).isTrue()
        }
    }

    @Test
    fun `should deactivate vehicles`() {
        val vehicles =
            VehicleBuilder.buildMultiple(3).map { it.apply { this.updateExternalId(UUID.randomUUID()) } }.toList()

        val carDetails = CarDetails(car = CarBuilder().build())

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/cars") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carDetails))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixCarsAdapter.deactivateVehicles(vehicles)

        val updateCarDtos = vehicles.map { it.toUpdateCarDto(deactivate = true) }

        assertThat(mockWebServer.requestCount).isEqualTo(3)
        repeat(3) {
            val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val requestPayload: UpdateCarDto = objectMapper.readValue(request!!.body!!.utf8())
            val pathParam: String = request.url.toString().split("/").last()
            assertThat(updateCarDtos.contains(requestPayload)).isTrue()
            assertThat(requestPayload.isActive).isFalse()
            assertThat(vehicles.map { it.externalId.toString() }.contains(pathParam)).isTrue()
        }
    }

    @Test
    fun `update payload should contain null values, even if serializer is set to NON_NULL`() {
        val vehicle = VehicleBuilder().licensePlate(null).employeeNumber(null).build()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(CarDetails(car = CarBuilder().build())))
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        repairfixCarsAdapter.updateVehicles(listOf(vehicle))

        val updateCarDto = vehicle.toUpdateCarDto()
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestBody = request!!.body!!.utf8()
        val requestPayload: UpdateCarDto = objectMapper.readValue(requestBody)
        assertThat(updateCarDto).isEqualTo(requestPayload)
        assertThat(requestBody).contains("\"licensePlate\":null")
        assertThat(requestBody).contains("\"driver\":null")
    }
}
