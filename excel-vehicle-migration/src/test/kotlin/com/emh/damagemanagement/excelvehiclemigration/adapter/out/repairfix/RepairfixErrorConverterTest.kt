/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.application.ErrorType
import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.adapter.RepairfixException.Type as RepairfixExceptionType
import com.emh.damagemanagement.shared.createRandomString
import java.util.function.Consumer
import java.util.stream.Stream
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.web.reactive.function.client.WebClientResponseException

class RepairfixErrorConverterTest {

    @ParameterizedTest
    @MethodSource("repairfixExceptionProvider")
    fun `should convert RepairfixException to FleetMigrationException`(repairfixException: RepairfixException) {
        val fleetMigrationException = repairfixException.toFleetMigrationException()

        assertThat(fleetMigrationException.errorType)
            .satisfies(repairfixExceptionTypeRequirements(repairfixException.type))
        assertThat(fleetMigrationException.message).isEqualTo(repairfixException.message)
        assertThat(fleetMigrationException.cause).isEqualTo(repairfixException.cause)
    }

    @ParameterizedTest
    @MethodSource("repairfixExceptionProvider")
    fun `should convert RepairfixException to VehicleMigrationException`(repairfixException: RepairfixException) {
        val vehicleMigrationException = repairfixException.toVehicleMigrationException()

        assertThat(vehicleMigrationException.errorType)
            .satisfies(repairfixExceptionTypeRequirements(repairfixException.type))
        assertThat(vehicleMigrationException.message).isEqualTo(repairfixException.message)
        assertThat(vehicleMigrationException.cause).isEqualTo(repairfixException.cause)
        when (repairfixException.type) {
            RepairfixException.Type.BUSINESS -> assertThat(vehicleMigrationException.canRetry).isFalse()
            RepairfixException.Type.TECHNICAL -> assertThat(vehicleMigrationException.canRetry).isTrue()
        }
    }

    @ParameterizedTest
    @MethodSource("repairfixExceptionProvider")
    fun `should convert RepairfixException to VehicleResponsiblePersonMigrationException`(
        repairfixException: RepairfixException
    ) {
        val vehicleResponsiblePersonMigrationException =
            repairfixException.toVehicleResponsiblePersonMigrationException()

        assertThat(vehicleResponsiblePersonMigrationException.errorType)
            .satisfies(repairfixExceptionTypeRequirements(repairfixException.type))
        assertThat(vehicleResponsiblePersonMigrationException.message).isEqualTo(repairfixException.message)
        assertThat(vehicleResponsiblePersonMigrationException.cause).isEqualTo(repairfixException.cause)
        when (repairfixException.type) {
            RepairfixException.Type.BUSINESS ->
                assertThat(vehicleResponsiblePersonMigrationException.canRetry).isFalse()
            RepairfixException.Type.TECHNICAL ->
                assertThat(vehicleResponsiblePersonMigrationException.canRetry).isTrue()
        }
    }

    companion object {

        fun repairfixExceptionTypeRequirements(repairfixExceptionType: RepairfixExceptionType) =
            Consumer<ErrorType> { errorType: ErrorType ->
                when (repairfixExceptionType) {
                    RepairfixExceptionType.BUSINESS -> assertThat(errorType).isEqualTo(ErrorType.BUSINESS)
                    RepairfixExceptionType.TECHNICAL -> assertThat(errorType).isEqualTo(ErrorType.TECHNICAL)
                }
            }

        @JvmStatic
        fun repairfixExceptionProvider(): Stream<Arguments> =
            (1..10)
                .map {
                    RepairfixException(
                        type = RepairfixException.Type.entries.random(),
                        message = createRandomString(23),
                        cause =
                            if (Random.nextBoolean())
                                WebClientResponseException("error", 409, "status", null, null, null)
                            else null,
                    )
                }
                .map { Arguments.of(it) }
                .stream()
    }
}
