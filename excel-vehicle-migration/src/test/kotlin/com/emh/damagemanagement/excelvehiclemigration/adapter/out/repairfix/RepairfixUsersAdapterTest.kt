/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.PoolFleetBuilder
import com.emh.damagemanagement.excelvehiclemigration.UserBuilder
import com.emh.damagemanagement.excelvehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.excelvehiclemigration.application.VehicleResponsiblePersonMigrationException
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CreateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.OperationResult
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnauthorizedErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnprocessableEntityErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.User
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UsersList
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.math.BigDecimal
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@TestPropertySource(properties = ["repairfix.base-url=http://localhost:8081"])
class RepairfixUsersAdapterTest {
    @Autowired private lateinit var repairfixUsersAdapter: RepairfixUsersAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8081)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")
    private val unprocessableEntityResponse =
        UnprocessableEntityErrorResponse(status = BigDecimal.valueOf(422), message = "I am an error of business.")
    private val unauthorizedResponse =
        UnauthorizedErrorResponse(
            statusCode = BigDecimal.valueOf(401),
            message = "You need to be authenticated for this operation.",
            error = "Unauthorized",
        )

    private val okResponse = OperationResult(true)
    private val successFalseResponse = OperationResult(false)

    @Test
    fun `should return all vehicle responsible persons`() {
        val resultPages =
            listOf(
                UsersList(users = UserBuilder.buildMultiple(100).toList(), total = BigDecimal.valueOf(142)),
                UsersList(users = UserBuilder.buildMultiple(42).toList(), total = BigDecimal.valueOf(142)),
            )

        val totalUsersResultPage = UsersList(users = emptyList(), total = BigDecimal.valueOf(142))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalUsersResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "2" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[1]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val vehicleResponsiblePersonsFromAdapter = repairfixUsersAdapter.getAllVehicleResponsiblePersons()
        val resultPageVehicleResponsiblePersons =
            resultPages
                .flatMap { it.users }
                .map { user -> user.toVehicleResponsiblePersonAndFleetIds() }
                .sortedBy { it.first.employeeNumber.value }

        assertThat(vehicleResponsiblePersonsFromAdapter)
            .containsExactlyInAnyOrderElementsOf(resultPageVehicleResponsiblePersons)
    }

    @Test
    fun `should return all fleet managers with fleet ids`() {
        val resultPages =
            listOf(
                UsersList(
                    users = (1..100).map { UserBuilder().role(User.Role.local_fleet_manager).build() },
                    total = BigDecimal.valueOf(123),
                ),
                UsersList(
                    users = (1..23).map { UserBuilder().role(User.Role.local_fleet_manager).build() },
                    total = BigDecimal.valueOf(123),
                ),
            )

        val totalUsersResultPage = UsersList(users = emptyList(), total = BigDecimal.valueOf(123))

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        "1" == request.url.queryParameter("limit") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(totalUsersResultPage))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "1" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[0]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        "2" == request.url.queryParameter("page") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultPages[1]))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val fleetManagersFromAdapter = repairfixUsersAdapter.getAllVehicleResponsiblePersons()
        val resultPageFleetManagers =
            resultPages
                .flatMap { it.users }
                .map { user -> user.toVehicleResponsiblePersonAndFleetIds() }
                .sortedBy { it.first.employeeNumber.value }

        assertThat(fleetManagersFromAdapter).containsExactlyInAnyOrderElementsOf(resultPageFleetManagers)
    }

    @Test
    fun `should add all provided vehicleResponsiblePersons chunked`() {
        val persons = VehicleResponsiblePersonBuilder.buildMultiple(230).toList()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixUsersAdapter.addVehicleResponsiblePersons(vehicleResponsiblePersons = persons)

        val chunkedVehicleResponsiblePersonsForComparison = persons.map { it.toCreateUserDto() }.chunked(10)
        assertThat(mockWebServer.requestCount).isEqualTo(23)
        repeat(23) {
            val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val requestPayload: List<CreateUserDto> = objectMapper.readValue(request!!.body!!.utf8())
            assertThat(chunkedVehicleResponsiblePersonsForComparison).contains(requestPayload)
        }
    }

    @Test
    fun `should add all provided fleet managers chunked`() {
        val fleetManagersAndFleets =
            (1..201).map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixUsersAdapter.addFleetManagers(fleetManagerAndFleets = fleetManagersAndFleets)

        val chunkedFleetManagersForComparison = fleetManagersAndFleets.map { it.toCreateUserDto() }.chunked(10)
        assertThat(mockWebServer.requestCount).isEqualTo(21)
        repeat(20) {
            val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val requestPayload: List<CreateUserDto> = objectMapper.readValue(request!!.body!!.utf8())
            assertThat(chunkedFleetManagersForComparison).contains(requestPayload)
        }
    }

    @Test
    fun `should reduce chunk size until failing vehicle responsible persons can be identified when adding users with 4xx technical error response`() {
        val vehicleResponsiblePersons = VehicleResponsiblePersonBuilder.buildMultiple(40).toList()
        val failingVehicleResponsiblePerson = vehicleResponsiblePersons.random()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: List<CreateUserDto> = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        payload.any { failingVehicleResponsiblePerson.employeeNumber.value == it.userPartnerId } ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(422)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val result = repairfixUsersAdapter.addVehicleResponsiblePersons(vehicleResponsiblePersons)
        assertThat(result.successfullyAddedVehicleResponsiblePersons).hasSize(39)
        assertThat(result.failedVehicleResponsiblePersons.single()).isEqualTo(failingVehicleResponsiblePerson)
    }

    @Test
    fun `should reduce chunk size until failing fleet manager can be identified when adding users with 4xx technical error response`() {
        val fleetManagerWithFleets =
            (1..40).map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }

        val failingPoolManager = fleetManagerWithFleets.random().first

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: List<CreateUserDto> = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        payload.any { failingPoolManager.employeeNumber.value == it.userPartnerId } ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(422)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val result = repairfixUsersAdapter.addFleetManagers(fleetManagerWithFleets)
        assertThat(result.successfullyAddedVehicleResponsiblePersons).hasSize(39)
        assertThat(result.failedVehicleResponsiblePersons.single()).isEqualTo(failingPoolManager)
    }

    @Test
    fun `should reduce chunk size until failing vehicle responsible persons can be identified when adding users with success=false response`() {
        val vehicleResponsiblePersons = VehicleResponsiblePersonBuilder.buildMultiple(40).toList()
        val failingVehicleResponsiblePerson = vehicleResponsiblePersons.random()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: List<CreateUserDto> = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        payload.any { failingVehicleResponsiblePerson.employeeNumber.value == it.userPartnerId } ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(successFalseResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val result = repairfixUsersAdapter.addVehicleResponsiblePersons(vehicleResponsiblePersons)
        assertThat(result.successfullyAddedVehicleResponsiblePersons).hasSize(39)
        assertThat(result.failedVehicleResponsiblePersons.single()).isEqualTo(failingVehicleResponsiblePerson)
    }

    @Test
    fun `should reduce chunk size until failing fleet manager can be identified when adding users with success=false response`() {
        val fleetManagerWithFleets =
            (1..40).map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }

        val failingPoolManager = fleetManagerWithFleets.random().first

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: List<CreateUserDto> = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        payload.any { failingPoolManager.employeeNumber.value == it.userPartnerId } ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(successFalseResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val result = repairfixUsersAdapter.addFleetManagers(fleetManagerWithFleets)
        assertThat(result.successfullyAddedVehicleResponsiblePersons).hasSize(39)
        assertThat(result.failedVehicleResponsiblePersons.single()).isEqualTo(failingPoolManager)
    }

    @Test
    fun `should throw VehicleResponsiblePersonMigrationException in case of RepairfixException when getting all vehicle responsible persons`() {
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unauthorizedResponse))
                .addHeader("Content-Type", "application/json")
                .code(401)
                .build()
        )

        assertThatExceptionOfType(VehicleResponsiblePersonMigrationException::class.java).isThrownBy {
            repairfixUsersAdapter.getAllVehicleResponsiblePersons()
        }
    }

    @Test
    fun `should throw VehicleResponsiblePersonMigrationException in case of RepairfixException when adding vehicle responsible persons`() {
        val vehicleResponsiblePersons = VehicleResponsiblePersonBuilder.buildMultiple(11).toList()

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(notFoundResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        assertThatExceptionOfType(VehicleResponsiblePersonMigrationException::class.java).isThrownBy {
            repairfixUsersAdapter.addVehicleResponsiblePersons(vehicleResponsiblePersons)
        }
    }

    @Test
    fun `should throw VehicleResponsiblePersonMigrationException in case of RepairfixException when adding fleet managers`() {
        val fleetManagerWithFleets =
            (1..11).map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(notFoundResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        assertThatExceptionOfType(VehicleResponsiblePersonMigrationException::class.java).isThrownBy {
            repairfixUsersAdapter.addFleetManagers(fleetManagerWithFleets)
        }
    }

    @Test
    fun `should update all provided vehicleResponsiblePersons`() {
        val persons = VehicleResponsiblePersonBuilder.buildMultiple(40).toList()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateVehicleResponsiblePersons(vehicleResponsiblePersons = persons)

        assertThat(mockWebServer.requestCount).isEqualTo(persons.size)
        assertThat(updateResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(persons)
        val expectedUpdateUserDtosForComparison = persons.map { it.toUpdateUserDto() }
        repeat(persons.size) {
            val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val requestPayload: UpdateUserDto = objectMapper.readValue(request!!.body!!.utf8())
            assertThat(expectedUpdateUserDtosForComparison).contains(requestPayload)
        }
    }

    @Test
    fun `should update all provided fleet managers`() {
        val fleetManagerWithFleets =
            (1..40).map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateFleetManagers(fleetManagerAndFleets = fleetManagerWithFleets)

        assertThat(mockWebServer.requestCount).isEqualTo(fleetManagerWithFleets.size)
        assertThat(updateResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(fleetManagerWithFleets.map { it.first })
        val expectedUpdateUserDtosForComparison = fleetManagerWithFleets.map { it.toUpdateUserDto() }
        repeat(fleetManagerWithFleets.size) {
            val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val requestPayload: UpdateUserDto = objectMapper.readValue(request!!.body!!.utf8())
            assertThat(expectedUpdateUserDtosForComparison).contains(requestPayload)
        }
    }

    @Test
    fun `should skip success failure vehicle responsible person updates and continue`() {
        val persons = VehicleResponsiblePersonBuilder.buildMultiple(40).toList()
        val failingVehicleResponsiblePerson = persons.random()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: UpdateUserDto = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        failingVehicleResponsiblePerson.employeeNumber.value == payload.userPartnerId ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(successFalseResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateVehicleResponsiblePersons(vehicleResponsiblePersons = persons)

        assertThat(mockWebServer.requestCount).isEqualTo(persons.size)
        assertThat(updateResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(persons - failingVehicleResponsiblePerson)
        assertThat(updateResult.failedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(setOf(failingVehicleResponsiblePerson))
    }

    @Test
    fun `should skip success failure fleet manager updates and continue`() {
        val fleetManagerWithFleets =
            (1..40).map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }
        val failingPoolManager = fleetManagerWithFleets.random().first

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: UpdateUserDto = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        failingPoolManager.employeeNumber.value == payload.userPartnerId ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(successFalseResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateFleetManagers(fleetManagerAndFleets = fleetManagerWithFleets)

        assertThat(mockWebServer.requestCount).isEqualTo(fleetManagerWithFleets.size)
        assertThat(updateResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(fleetManagerWithFleets.map { it.first } - failingPoolManager)
        assertThat(updateResult.failedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(setOf(failingPoolManager))
    }

    @Test
    fun `should skip error on vehicle responsible person updates and continue`() {
        val persons = VehicleResponsiblePersonBuilder.buildMultiple(40).toList()
        val failingVehicleResponsiblePerson = persons.random()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: UpdateUserDto = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        failingVehicleResponsiblePerson.employeeNumber.value == payload.userPartnerId ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(422)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateVehicleResponsiblePersons(vehicleResponsiblePersons = persons)

        assertThat(mockWebServer.requestCount).isEqualTo(persons.size)
        assertThat(updateResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(persons - failingVehicleResponsiblePerson)
        assertThat(updateResult.failedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(setOf(failingVehicleResponsiblePerson))
    }

    @Test
    fun `should skip error on fleet manager updates and continue`() {
        val fleetManagerWithFleets =
            (1..40).map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }
        val failingPoolManager = fleetManagerWithFleets.random().first

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    val payload: UpdateUserDto = objectMapper.readValue(request.body!!.utf8())
                    return when {
                        failingPoolManager.employeeNumber.value == payload.userPartnerId ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(422)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateFleetManagers(fleetManagerAndFleets = fleetManagerWithFleets)

        assertThat(mockWebServer.requestCount).isEqualTo(fleetManagerWithFleets.size)
        assertThat(updateResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(fleetManagerWithFleets.map { it.first } - failingPoolManager)
        assertThat(updateResult.failedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(setOf(failingPoolManager))
    }
}
