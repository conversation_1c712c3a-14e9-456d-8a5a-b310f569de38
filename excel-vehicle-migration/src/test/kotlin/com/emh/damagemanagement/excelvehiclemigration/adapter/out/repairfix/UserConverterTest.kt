/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.PoolFleetBuilder
import com.emh.damagemanagement.excelvehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CreateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateUserDto
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class UserConverterTest {

    @ParameterizedTest
    @MethodSource("vehicleResponsiblePersonProvider")
    fun `should convert VehicleResponsiblePerson to CreateUserDto with role driver`(
        vehicleResponsiblePerson: VehicleResponsiblePerson
    ) {
        val createUserDto = vehicleResponsiblePerson.toCreateUserDto()

        assertThat(createUserDto).satisfies(createVehicleResponsiblePersonRequirements(vehicleResponsiblePerson))
    }

    @ParameterizedTest
    @MethodSource("fleetManagerAndFleetsProvider")
    fun `should convert FleetManager to CreateUserDto with role fleet_manager`(
        fleetManager: Pair<VehicleResponsiblePerson, Set<Fleet>>
    ) {
        val createUserDto = fleetManager.toCreateUserDto()

        assertThat(createUserDto).satisfies(createFleetManagerRequirements(fleetManager))
    }

    @ParameterizedTest
    @MethodSource("vehicleResponsiblePersonProvider")
    fun `should convert VehicleResponsiblePerson to UpdateUserDto`(vehicleResponsiblePerson: VehicleResponsiblePerson) {
        val updateUserDto = vehicleResponsiblePerson.toUpdateUserDto()

        assertThat(updateUserDto).satisfies(updateVehicleResponsiblePersonRequirements(vehicleResponsiblePerson))
    }

    @ParameterizedTest
    @MethodSource("fleetManagerAndFleetsProvider")
    fun `should convert FleetManager to UpdateUserDto`(fleetManager: Pair<VehicleResponsiblePerson, Set<Fleet>>) {
        val updateUserDto = fleetManager.toUpdateUserDto()

        assertThat(updateUserDto).satisfies(updateFleetManagerRequirements(fleetManager))
    }

    companion object {

        fun createVehicleResponsiblePersonRequirements(vehicleResponsiblePerson: VehicleResponsiblePerson) =
            Consumer<CreateUserDto> { createUserDto: CreateUserDto ->
                assertThat(createUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(vehicleResponsiblePerson)
                assertThat(createUserDto.userPartnerId).isEqualTo(vehicleResponsiblePerson.employeeNumber.value)
                assertThat(createUserDto.role).isEqualTo(CreateUserDto.Role.driver)
            }

        fun createFleetManagerRequirements(fleetManagerAndFleets: Pair<VehicleResponsiblePerson, Set<Fleet>>) =
            Consumer<CreateUserDto> { createUserDto: CreateUserDto ->
                assertThat(createUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(fleetManagerAndFleets.first)
                assertThat(createUserDto.userPartnerId).isEqualTo(fleetManagerAndFleets.first.employeeNumber.value)
                assertThat(createUserDto.role).isEqualTo(CreateUserDto.Role.local_fleet_manager)
                assertThat(createUserDto.visibilityScope?.map { it.fleetPartnerId })
                    .containsExactlyInAnyOrderElementsOf(fleetManagerAndFleets.second.map { it.id })
            }

        fun updateVehicleResponsiblePersonRequirements(vehicleResponsiblePerson: VehicleResponsiblePerson) =
            Consumer<UpdateUserDto> { updateUserDto: UpdateUserDto ->
                assertThat(updateUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(vehicleResponsiblePerson)
                assertThat(updateUserDto.userPartnerId).isEqualTo(vehicleResponsiblePerson.employeeNumber.value)
                assertThat(updateUserDto.role).isEqualTo(UpdateUserDto.Role.driver)
            }

        fun updateFleetManagerRequirements(fleetManagerAndFleets: Pair<VehicleResponsiblePerson, Set<Fleet>>) =
            Consumer<UpdateUserDto> { updateUserDto: UpdateUserDto ->
                assertThat(updateUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(fleetManagerAndFleets.first)
                assertThat(updateUserDto.userPartnerId).isEqualTo(fleetManagerAndFleets.first.employeeNumber.value)
                assertThat(updateUserDto.role).isEqualTo(UpdateUserDto.Role.local_fleet_manager)
                assertThat(updateUserDto.visibilityScope?.map { it.fleetPartnerId })
                    .containsExactlyInAnyOrderElementsOf(fleetManagerAndFleets.second.map { it.id })
            }

        @JvmStatic
        fun vehicleResponsiblePersonProvider(): Stream<Arguments> =
            VehicleResponsiblePersonBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()

        @JvmStatic
        fun fleetManagerAndFleetsProvider(): Stream<Arguments> =
            (1..10)
                .map { Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildMultiple()) }
                .map { Arguments.of(it) }
                .stream()
    }
}
