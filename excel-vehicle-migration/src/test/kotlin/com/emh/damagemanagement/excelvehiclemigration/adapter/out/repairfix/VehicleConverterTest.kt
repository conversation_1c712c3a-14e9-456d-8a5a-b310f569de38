/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.excelvehiclemigration.CarBuilder
import com.emh.damagemanagement.excelvehiclemigration.VehicleBuilder
import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AddCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class VehicleConverterTest {
    @ParameterizedTest
    @MethodSource("vehicleProvider")
    fun `should convert Vehicle to AddCarDto`(vehicle: Vehicle) {
        val addCarDto = vehicle.toAddCarDto()

        assertThat(addCarDto).satisfies(vehicleRequirements(vehicle))
    }

    @ParameterizedTest
    @MethodSource("carProvider")
    fun `should convert Car to Vehicle`(car: Car) {
        val vehicle = car.toVehicle()

        assertThat(vehicle).satisfies(carRequirements(car))
    }

    @ParameterizedTest
    @MethodSource("vehicleProvider")
    fun `should convert Vehicle to UpdateCarDto`(vehicle: Vehicle) {
        val updateCarDto = vehicle.toUpdateCarDto()

        assertThat(updateCarDto)
            .satisfies({
                assertThat(it.carPartnerId).isEqualTo(vehicle.vGuid)
                assertThat(it.vin).isEqualTo(vehicle.vin.value)
                assertThat(it.isPool).isEqualTo(vehicle.isPoolVehicle)
                assertThat(it.driver).isEqualTo(vehicle.responsiblePerson?.value)
                assertThat(it.fleetIdentifier).isEqualTo(vehicle.fleetId)
                assertThat(it.licensePlate).isEqualTo(vehicle.licensePlate)
                assertThat(it.insurance?.costCenter).isEqualTo(vehicle.costCenter)
                assertThat(it.insurance?.insuranceBroker).isEqualTo("LeasingArt:${vehicle.leasingArt}")
                assertThat(it.insurance?.insurerAddress).isEqualTo("UsingCostCenter:${vehicle.usingCostCenter}")
            })
    }

    companion object {
        fun vehicleRequirements(vehicle: Vehicle) =
            Consumer<AddCarDto> { addCarDto: AddCarDto ->
                assertThat(addCarDto)
                    .usingRecursiveComparison()
                    .ignoringFields(
                        "vin",
                        "isPool",
                        "insurance",
                        "bodyType",
                        "firstRegistration",
                        "leasing",
                        "warrantyExpirationDate",
                        "nextInspectionDate",
                        "driver",
                        "carPartnerId",
                        "fleetIdentifier",
                        "lastInspectionDate",
                        "location",
                    )
                    .isEqualTo(vehicle)
                assertThat(addCarDto.vin).isEqualTo(vehicle.vin.value)
                assertThat(addCarDto.carPartnerId).isEqualTo(vehicle.vGuid)
                assertThat(addCarDto.isPool).isEqualTo(vehicle.isPoolVehicle)
                assertThat(addCarDto.fleetIdentifier).isEqualTo(vehicle.fleetId)
                if (vehicle.isPoolVehicle) {
                    assertThat(addCarDto.driver).isNull()
                } else {
                    assertThat(EmployeeNumber(addCarDto.driver!!)).isEqualTo(vehicle.responsiblePerson)
                }

                assertThat(addCarDto.insurance?.costCenter).isEqualTo(vehicle.costCenter)
                assertThat(addCarDto.insurance?.insuranceBroker).isEqualTo("LeasingArt:${vehicle.leasingArt}")
                assertThat(addCarDto.insurance?.insurerAddress).isEqualTo("UsingCostCenter:${vehicle.usingCostCenter}")
            }

        fun carRequirements(car: Car) =
            Consumer<Vehicle> { vehicle: Vehicle ->
                assertThat(vehicle)
                    .usingRecursiveComparison()
                    .ignoringFields(
                        "vin",
                        "vGuid",
                        "isPoolVehicle",
                        "fleet",
                        "fleetId",
                        "make",
                        "responsiblePerson",
                        "costCenter",
                        "usingCostCenter",
                        "leasingArt",
                        "externalId",
                    )
                    .isEqualTo(car)
                assertThat(vehicle.vin.value).isEqualTo(car.vin)
                assertThat(vehicle.vGuid).isEqualTo(car.carPartnerId)
                assertThat(vehicle.isPoolVehicle).isEqualTo(car.isPool)
                assertThat(vehicle.fleetId).isEqualTo(car.fleetPartnerId)
                if (car.isPool) {
                    assertThat(vehicle.responsiblePerson).isNull()
                } else {
                    assertThat(vehicle.responsiblePerson).isEqualTo(EmployeeNumber(car.driver!!.partnerId!!))
                }
                assertThat(vehicle.costCenter).isEqualTo(car.insurances?.firstOrNull()?.costCenter)
                val insuranceBroker = car.insurances?.firstOrNull()?.insuranceBroker
                if (null == insuranceBroker) {
                    assertThat(vehicle.leasingArt).isEmpty()
                } else {
                    assertThat(vehicle.leasingArt).isEqualTo(insuranceBroker.replace("LeasingArt:", ""))
                }
                val insurerAddress = car.insurances?.firstOrNull()?.insurerAddress
                if (null == insurerAddress) {
                    assertThat(vehicle.usingCostCenter).isNull()
                } else {
                    assertThat(vehicle.usingCostCenter).isEqualTo(insurerAddress.replace("UsingCostCenter:", ""))
                }
                assertThat(vehicle.externalId).isEqualTo(car.id)
            }

        @JvmStatic
        fun vehicleProvider(): Stream<Arguments> = VehicleBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()

        @JvmStatic fun carProvider(): Stream<Arguments> = CarBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
