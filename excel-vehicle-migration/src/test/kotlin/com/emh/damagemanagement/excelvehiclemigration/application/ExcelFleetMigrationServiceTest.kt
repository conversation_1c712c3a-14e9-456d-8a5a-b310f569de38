/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.application.port.FleetOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [ExcelFleetMigrationServiceTest.TestConfiguration::class])
class ExcelFleetMigrationServiceTest {
    @Autowired private lateinit var fleetMigrationService: ExcelFleetMigrationService
    @Autowired private lateinit var fleetOutPort: FleetOutPort

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(fleetOutPort)
    }

    @Test
    fun `should call FleetOutPort when migrating fleets`() {
        assertThatNoException().isThrownBy { fleetMigrationService.migrateManagedFleets() }
        verify(fleetOutPort).migrateLeasingFleets(eq(Fleet.leasingFleets))
        verify(fleetOutPort).migratePoolFleets(eq(setOf(Fleet.preUsageFleet, Fleet.postUsageFleet)))
    }

    @Test
    fun `should call FleetOutPort when checking fleet migration status`() {
        `when`(fleetOutPort.allFleetsExist(fleetsToCompare = Fleet.leasingFleets)).thenReturn(true)

        val fleetMigrationStatus = fleetMigrationService.allManagedFleetsHaveBeenMigrated()
        assertThat(fleetMigrationStatus).isTrue()
    }

    internal class TestConfiguration {

        @Bean @Primary fun fleetOutPort(): FleetOutPort = mock()
    }
}
