/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.VehicleBuilder
import com.emh.damagemanagement.excelvehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehicleResponsiblePersonsResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehicleResponsiblePersonsResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonsMigrationResult
import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.Vin
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.groups.Tuple.tuple
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [LeasingVehicleMigrationServiceTest.TestConfiguration::class])
class LeasingVehicleMigrationServiceTest {

    @Autowired private lateinit var leasingVehicleMigrationService: LeasingVehicleMigrationService
    @Autowired private lateinit var vehicleResponsiblePersonMigrationService: VehicleResponsiblePersonMigrationService
    @Autowired private lateinit var vehicleOutPort: VehicleOutPort

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(vehicleOutPort, vehicleResponsiblePersonMigrationService)
    }

    @Test
    fun `should migrate all leasing vehicles if none have been added before`() {
        val vehicleAndVehicleResponsiblePersons =
            VehicleResponsiblePersonBuilder.buildMultiple(200).map {
                Pair(VehicleBuilder().employeeNumber(it.employeeNumber).build(), it)
            }

        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(any(), any()))
            .thenReturn(VehicleResponsiblePersonsMigrationResult.empty)

        leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(
            leasingVehicleAndResponsiblePersons = vehicleAndVehicleResponsiblePersons,
            existingVehicles = emptyList(),
            existingVehicleResponsiblePersons = emptyList(),
        )

        val addVehiclesArgumentCaptor = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).addVehicles(addVehiclesArgumentCaptor.capture())
        assertThat(addVehiclesArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(vehicleAndVehicleResponsiblePersons.map { it.first })
    }

    @Test
    fun `should migrate only vehicles that have not been added before`() {
        val existingVehicles = VehicleBuilder.buildMultiple(42).toList()
        val vehiclesToBeAdded = VehicleBuilder.buildMultiple(42)
        whenever(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        whenever(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))
        whenever(vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(any(), any()))
            .thenReturn(VehicleResponsiblePersonsMigrationResult.empty)

        leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(
            leasingVehicleAndResponsiblePersons =
                (vehiclesToBeAdded + existingVehicles).map { Pair(it, VehicleResponsiblePersonBuilder.buildSingle()) },
            existingVehicles = existingVehicles,
            existingVehicleResponsiblePersons = emptyList(),
        )

        val addVehiclesArgumentCaptor = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).addVehicles(addVehiclesArgumentCaptor.capture())
        val newlyAddedVehicles = addVehiclesArgumentCaptor.firstValue
        assertThat(newlyAddedVehicles).containsExactlyInAnyOrderElementsOf(vehiclesToBeAdded)
    }

    @Test
    fun `should continue migration with filtered vehicles if migration of VehicleResponsiblePersons failed for some persons`() {
        val failedEmployeeNumbers = setOf("339605", "310279", "341473", "340270").map(::EmployeeNumber).toSet()
        `when`(vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(any(), any()))
            .thenReturn(
                VehicleResponsiblePersonsMigrationResult(
                    AddVehicleResponsiblePersonsResult(
                        failedVehicleResponsiblePersons =
                            failedEmployeeNumbers
                                .map { VehicleResponsiblePersonBuilder().employeeNumber(it).build() }
                                .toSet(),
                        successfullyAddedVehicleResponsiblePersons = emptySet(),
                    ),
                    UpdateVehicleResponsiblePersonsResult(emptySet(), emptySet()),
                )
            )
        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))

        leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(
            leasingVehicleAndResponsiblePersons =
                failedEmployeeNumbers.map {
                    Pair(
                        VehicleBuilder().leasing().employeeNumber(it).build(),
                        VehicleResponsiblePersonBuilder().employeeNumber(it).build(),
                    )
                } + (0..9).map { Pair(VehicleBuilder.buildSingle(), VehicleResponsiblePersonBuilder().build()) },
            existingVehicles = emptyList(),
            existingVehicleResponsiblePersons = emptyList(),
        )

        val addVehiclesArgumentCaptor = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).addVehicles(addVehiclesArgumentCaptor.capture())
        assertThat(addVehiclesArgumentCaptor.firstValue)
            .extracting(Vehicle::responsiblePerson)
            .doesNotContainAnyElementsOf(failedEmployeeNumbers.map { tuple(it) })
        assertThat(addVehiclesArgumentCaptor.firstValue).hasSize(10)
    }

    @Test
    fun `should update vehicles that have actual changes`() {
        val vehicleWithOldLicensePlate =
            VehicleBuilder()
                .vin(Vin("WP0AC2A84P2240082"))
                .licensePlate("Test 0123")
                .fleetId("L1.2")
                .employeeNumber(EmployeeNumber("355318"))
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithOldFleet =
            VehicleBuilder()
                .vin(Vin("WP0ZZZY1ZNSA69458"))
                .licensePlate("BB-PO 685")
                .fleetId("L1.2")
                .employeeNumber(EmployeeNumber("348235"))
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithOldEmployeeNumber =
            VehicleBuilder()
                .vin(Vin("WP0ZZZ99ZMS266116"))
                .licensePlate("PV-CC 0423")
                .fleetId("L1.2")
                .employeeNumber(EmployeeNumber("111111"))
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithOldUsingCostCenter =
            VehicleBuilder()
                .vin(Vin("WP0ZZZ99ZMS266189"))
                .licensePlate("AB-CD 1234")
                .fleetId("L1.2")
                .costCenter("costCenter")
                .usingCostCenter("**********")
                .employeeNumber(EmployeeNumber("1234523"))
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithLatestData =
            VehicleBuilder()
                .vin(Vin("WP1ZZZ95ZKLB17708"))
                .licensePlate("S-PL 1915")
                .fleetId("L1.3")
                .employeeNumber(EmployeeNumber("342586"))
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithEmptyStringInsteadOfNull =
            VehicleBuilder()
                .vin(Vin("WP1ZZZ95ZKLB17709"))
                .licensePlate("")
                .fleetId("L1.3")
                .leasingArt("L1.3")
                .employeeNumber(EmployeeNumber("342586"))
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val existingVehicles =
            listOf(
                vehicleWithOldLicensePlate,
                vehicleWithOldFleet,
                vehicleWithOldEmployeeNumber,
                vehicleWithOldUsingCostCenter,
                vehicleWithLatestData,
                vehicleWithEmptyStringInsteadOfNull,
            )

        val updatedVehicles =
            listOf(
                VehicleBuilder()
                    .vin(Vin("WP0AC2A84P2240082"))
                    .licensePlate("Test 4567")
                    .fleetId("L1.2")
                    .employeeNumber(EmployeeNumber("355318"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZY1ZNSA69458"))
                    .licensePlate("BB-PO 685")
                    .fleetId("L1.3")
                    .employeeNumber(EmployeeNumber("348235"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZ99ZMS266116"))
                    .licensePlate("PV-CC 0423")
                    .fleetId("L1.2")
                    .employeeNumber(EmployeeNumber("222222"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP1ZZZ95ZKLB17709"))
                    .licensePlate(null)
                    .fleetId("L1.3")
                    .leasingArt("L1.3")
                    .costCenter("123")
                    .employeeNumber(EmployeeNumber("342586"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZ99ZMS266189"))
                    .licensePlate("AB-CD 1234")
                    .fleetId("L1.2")
                    .costCenter("costCenter")
                    .usingCostCenter("**********")
                    .employeeNumber(EmployeeNumber("1234523"))
                    .build()
                    .apply { this.updateExternalId(UUID.randomUUID()) },
            )

        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(any(), any()))
            .thenReturn(VehicleResponsiblePersonsMigrationResult.empty)

        leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(
            leasingVehicleAndResponsiblePersons =
                (updatedVehicles + listOf(vehicleWithLatestData)).map {
                    Pair(it, VehicleResponsiblePersonBuilder.buildSingle())
                },
            existingVehicles = existingVehicles,
            existingVehicleResponsiblePersons = emptyList(),
        )

        val updateVehicleArgumentCapture = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).updateVehicles(updateVehicleArgumentCapture.capture())
        assertThat(updateVehicleArgumentCapture.firstValue).hasSize(4)
        assertThat(updateVehicleArgumentCapture.firstValue.map { it.externalId })
            .containsExactlyInAnyOrderElementsOf(
                listOf(
                        vehicleWithOldLicensePlate,
                        vehicleWithOldFleet,
                        vehicleWithOldEmployeeNumber,
                        vehicleWithOldUsingCostCenter,
                    )
                    .map { it.externalId }
            )
    }

    @Test
    fun `should not update vehicles with only case specific changes`() {
        val existingVehicles =
            listOf(
                VehicleBuilder()
                    .vin(Vin("WP0ZZZ992PS241320"))
                    .licensePlate("pv-cc 0123")
                    .fleetId("L1.2")
                    .employeeNumber(EmployeeNumber("355318"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt(LeasingArt.L2.id)
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZY17PSA63046"))
                    .licensePlate("BB-PO 685")
                    .fleetId("l1.2")
                    .employeeNumber(EmployeeNumber("348235"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt(LeasingArt.L2.id)
                    .build(),
            )
        val updatedVehicles =
            listOf(
                VehicleBuilder()
                    .vin(Vin("WP0ZZZ992PS241320"))
                    .licensePlate("PV-CC 0123")
                    .fleetId("L1.2")
                    .employeeNumber(EmployeeNumber("355318"))
                    .costCenter("costcenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt(LeasingArt.L2.id)
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZY17PSA63046"))
                    .licensePlate("BB-PO 685")
                    .fleetId("L1.2")
                    .employeeNumber(EmployeeNumber("348235"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt(LeasingArt.L2.id)
                    .build(),
            )

        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(any(), any()))
            .thenReturn(VehicleResponsiblePersonsMigrationResult.empty)

        leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(
            leasingVehicleAndResponsiblePersons =
                updatedVehicles.map { Pair(it, VehicleResponsiblePersonBuilder.buildSingle()) },
            existingVehicles = existingVehicles,
            existingVehicleResponsiblePersons = emptyList(),
        )

        val updateVehicleArgumentCapture = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).updateVehicles(updateVehicleArgumentCapture.capture())
        assertThat(updateVehicleArgumentCapture.firstValue.size).isEqualTo(0)
    }

    @Test
    fun `should throw VehicleMigrationException in case of VehicleResponsiblePeronException`() {
        `when`(vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(any(), any()))
            .thenThrow(
                VehicleResponsiblePersonMigrationException(
                    canRetry = false,
                    message = "that did not work out as expected.",
                    errorType = ErrorType.BUSINESS,
                )
            )

        assertThatExceptionOfType(VehicleMigrationException::class.java).isThrownBy {
            leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(
                leasingVehicleAndResponsiblePersons =
                    listOf(Pair(VehicleBuilder.buildSingle(), VehicleResponsiblePersonBuilder.buildSingle())),
                existingVehicles = emptyList(),
                existingVehicleResponsiblePersons = emptyList(),
            )
        }
    }

    internal class TestConfiguration {

        @Bean @Primary fun vehicleOutPort(): VehicleOutPort = mock()

        @Bean @Primary fun vehicleResponsiblePersonMigrationService(): VehicleResponsiblePersonMigrationService = mock()
    }
}
