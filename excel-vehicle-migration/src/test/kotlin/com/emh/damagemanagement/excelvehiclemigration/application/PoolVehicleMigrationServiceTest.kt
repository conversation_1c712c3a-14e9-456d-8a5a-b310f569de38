/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.PoolFleetBuilder
import com.emh.damagemanagement.excelvehiclemigration.VehicleBuilder
import com.emh.damagemanagement.excelvehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.Vin
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [PoolVehicleMigrationServiceTest.TestConfiguration::class])
class PoolVehicleMigrationServiceTest {

    @Autowired private lateinit var poolVehicleMigrationService: PoolVehicleMigrationService
    @Autowired private lateinit var vehicleOutPort: VehicleOutPort
    @Autowired private lateinit var fleetMigrationService: ExcelFleetMigrationService

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(vehicleOutPort, fleetMigrationService)
    }

    @Test
    fun `should migrate all pool vehicles if none have been added before`() {
        val poolVehicleAndFleetManagerAndFleet =
            (1..200).map {
                Triple(
                    VehicleBuilder.buildSingle(),
                    VehicleResponsiblePersonBuilder.buildSingle(),
                    PoolFleetBuilder.buildSingle(),
                )
            }

        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))

        poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(
            poolVehicleAndFleetManagerAndFleet = poolVehicleAndFleetManagerAndFleet,
            existingVehicles = emptyList(),
            existingVehicleResponsiblePersonsWithFleetIds = emptyList(),
        )

        val addVehiclesArgumentCaptor = argumentCaptor<List<Vehicle>>()
        val migratePoolFleetsArgumentCaptor = argumentCaptor<Set<Fleet>>()
        verify(vehicleOutPort).addVehicles(addVehiclesArgumentCaptor.capture())
        assertThat(addVehiclesArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(poolVehicleAndFleetManagerAndFleet.map { it.first })
        verify(fleetMigrationService).migratePoolFleets(migratePoolFleetsArgumentCaptor.capture())
        assertThat(migratePoolFleetsArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(
                poolVehicleAndFleetManagerAndFleet.map { it.third }.distinctBy { it.id }
            )
    }

    @Test
    fun `should migrate only vehicles that have not been added before`() {
        val existingVehicles = VehicleBuilder.buildMultiple(42).toList()
        val vehiclesToBeAdded = VehicleBuilder.buildMultiple(42)
        val poolVehicleAndFleetManagerAndFleet =
            (existingVehicles + vehiclesToBeAdded).map {
                Triple(it, VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildSingle())
            }
        whenever(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        whenever(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))

        poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(
            poolVehicleAndFleetManagerAndFleet = poolVehicleAndFleetManagerAndFleet,
            existingVehicles = existingVehicles,
            existingVehicleResponsiblePersonsWithFleetIds = emptyList(),
        )

        val addVehiclesArgumentCaptor = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).addVehicles(addVehiclesArgumentCaptor.capture())
        val newlyAddedVehicles = addVehiclesArgumentCaptor.firstValue
        assertThat(newlyAddedVehicles).containsExactlyInAnyOrderElementsOf(vehiclesToBeAdded)
    }

    @Test
    fun `should migrate all pool fleets`() {
        val poolFleets = PoolFleetBuilder.buildMultiple(10)
        val poolVehicleAndFleetManagerAndFleet =
            (1..200).map {
                Triple(VehicleBuilder.buildSingle(), VehicleResponsiblePersonBuilder.buildSingle(), poolFleets.random())
            }

        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))

        poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(
            poolVehicleAndFleetManagerAndFleet = poolVehicleAndFleetManagerAndFleet,
            existingVehicles = emptyList(),
            existingVehicleResponsiblePersonsWithFleetIds = emptyList(),
        )

        val migratePoolFleetsArgumentCaptor = argumentCaptor<Set<Fleet>>()
        verify(fleetMigrationService).migratePoolFleets(migratePoolFleetsArgumentCaptor.capture())
        assertThat(migratePoolFleetsArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(
                poolVehicleAndFleetManagerAndFleet.map { it.third }.distinctBy { it.id }
            )
    }

    @Test
    fun `should update vehicles that have actual changes`() {
        val vehicleWithOldLicensePlate =
            VehicleBuilder()
                .vin(Vin("WP0AC2A84P2240082"))
                .licensePlate("Test 0123")
                .fleetId("L1.2")
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .employeeNumber(EmployeeNumber("355318"))
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithOldFleet =
            VehicleBuilder()
                .vin(Vin("WP0ZZZY1ZNSA69458"))
                .licensePlate("BB-PO 685")
                .fleetId("L1.2")
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .employeeNumber(EmployeeNumber("348235"))
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithOldUsingCostCenter =
            VehicleBuilder()
                .vin(Vin("WP0ZZZ99ZMS266189"))
                .licensePlate("AB-CD 1234")
                .fleetId("L1.2")
                .costCenter("costCenter")
                .usingCostCenter("**********")
                .employeeNumber(EmployeeNumber("1234523"))
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithLatestData =
            VehicleBuilder()
                .vin(Vin("WP1ZZZ95ZKLB17708"))
                .licensePlate("S-PL 1915")
                .fleetId("L1.3")
                .costCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .employeeNumber(EmployeeNumber("342586"))
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val vehicleWithEmptyStringInsteadOfNull =
            VehicleBuilder()
                .vin(Vin("WP1ZZZ95ZKLB17709"))
                .licensePlate("")
                .fleetId("L1.3")
                .leasingArt("L1.3")
                .usingCostCenter("usingCostCenter")
                .employeeNumber(EmployeeNumber("342586"))
                .costCenter("123")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val existingVehicles =
            listOf(
                vehicleWithOldLicensePlate,
                vehicleWithOldFleet,
                vehicleWithLatestData,
                vehicleWithEmptyStringInsteadOfNull,
                vehicleWithOldUsingCostCenter,
            )

        val updatedVehicles =
            listOf(
                VehicleBuilder()
                    .vin(Vin("WP0AC2A84P2240082"))
                    .licensePlate("Test 4567")
                    .fleetId("L1.2")
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .employeeNumber(EmployeeNumber("355318"))
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZY1ZNSA69458"))
                    .licensePlate("BB-PO 685")
                    .fleetId("L1.3")
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .employeeNumber(EmployeeNumber("348235"))
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP1ZZZ95ZKLB17709"))
                    .licensePlate(null)
                    .fleetId("L1.3")
                    .leasingArt("L1.3")
                    .costCenter("123")
                    .usingCostCenter("usingCostCenter")
                    .employeeNumber(EmployeeNumber("342586"))
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZ99ZMS266189"))
                    .licensePlate("AB-CD 1234")
                    .fleetId("L1.2")
                    .costCenter("costCenter")
                    .usingCostCenter("**********")
                    .employeeNumber(EmployeeNumber("1234523"))
                    .build()
                    .apply { this.updateExternalId(UUID.randomUUID()) },
            )

        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))

        poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(
            poolVehicleAndFleetManagerAndFleet =
                (updatedVehicles + listOf(vehicleWithLatestData)).map {
                    Triple(it, VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildSingle())
                },
            existingVehicles = existingVehicles,
            existingVehicleResponsiblePersonsWithFleetIds = emptyList(),
        )

        val updateVehicleArgumentCapture = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).updateVehicles(updateVehicleArgumentCapture.capture())
        assertThat(updateVehicleArgumentCapture.firstValue).hasSize(3)
        assertThat(updateVehicleArgumentCapture.firstValue.map { it.externalId })
            .containsExactlyInAnyOrderElementsOf(
                listOf(vehicleWithOldLicensePlate, vehicleWithOldFleet, vehicleWithOldUsingCostCenter).map {
                    it.externalId
                }
            )
    }

    @Test
    fun `should not update vehicles with only case specific changes`() {
        val existingVehicles =
            listOf(
                VehicleBuilder()
                    .vin(Vin("WP0ZZZ992PS241320"))
                    .licensePlate("pv-cc 0123")
                    .fleetId("Pool_fleet_id")
                    .employeeNumber(EmployeeNumber("355318"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt("L9.9")
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZY17PSA63046"))
                    .licensePlate("BB-PO 685")
                    .fleetId("pool_fleet_id")
                    .employeeNumber(EmployeeNumber("348235"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt("L9.9")
                    .build(),
            )
        val updatedVehicles =
            listOf(
                VehicleBuilder()
                    .vin(Vin("WP0ZZZ992PS241320"))
                    .licensePlate("PV-CC 0123")
                    .fleetId("Pool_fleet_id")
                    .employeeNumber(EmployeeNumber("355318"))
                    .costCenter("costcenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt("L9.9")
                    .build(),
                VehicleBuilder()
                    .vin(Vin("WP0ZZZY17PSA63046"))
                    .licensePlate("BB-PO 685")
                    .fleetId("Pool_fleet_id")
                    .employeeNumber(EmployeeNumber("348235"))
                    .costCenter("costCenter")
                    .usingCostCenter("usingCostCenter")
                    .leasingArt("L9.9")
                    .build(),
            )

        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))

        poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(
            poolVehicleAndFleetManagerAndFleet =
                updatedVehicles.map {
                    Triple(it, VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildSingle())
                },
            existingVehicles = existingVehicles,
            existingVehicleResponsiblePersonsWithFleetIds = emptyList(),
        )

        val updateVehicleArgumentCapture = argumentCaptor<List<Vehicle>>()
        verify(vehicleOutPort).updateVehicles(updateVehicleArgumentCapture.capture())
        assertThat(updateVehicleArgumentCapture.firstValue.size).isEqualTo(0)
    }

    internal class TestConfiguration {

        @Bean @Primary fun vehicleOutPort(): VehicleOutPort = mock()

        @Bean @Primary fun fleetMigrationService(): ExcelFleetMigrationService = mock()

        @Bean @Primary fun vehicleResponsiblePersonMigrationService(): VehicleResponsiblePersonMigrationService = mock()
    }
}
