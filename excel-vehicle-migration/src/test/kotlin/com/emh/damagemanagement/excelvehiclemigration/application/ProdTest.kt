/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import java.io.BufferedWriter
import java.io.File
import java.io.FileWriter
import java.nio.charset.StandardCharsets
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.util.FileSystemUtils

@IntegrationTest
class ProdTest {

    @Autowired private lateinit var vehicleOutPort: VehicleOutPort
    @Autowired private lateinit var vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort

    @Test
    @Disabled
    fun getAllVehiclesFromRepairfix() {
        println("Starting...")
        val existingVehicles = vehicleOutPort.getAllVehicles()
        println("Found ${existingVehicles.size} vehicles in repairfix PROD.")

        val existingVehicleVins = existingVehicles.map { it.vin.value }
        val vinsAsString = existingVehicleVins.joinToString(separator = System.lineSeparator())

        vinsAsString.isNotEmpty()
        println("Writing to output file.")
        val outputFolder = File("output")
        FileSystemUtils.deleteRecursively(outputFolder)
        outputFolder.mkdirs()
        BufferedWriter(
                FileWriter(
                    "${outputFolder.absolutePath}${File.separator}existingVehicles_PROD.json",
                    StandardCharsets.UTF_8,
                )
            )
            .use { writer -> writer.write(vinsAsString) }
        println("Finished.")
    }

    @Test
    @Disabled
    fun removePrePostUsageFromVisibilityAndDowngradeLocalFleetManagers() {
        val allUsers = vehicleResponsiblePersonOutPort.getAllVehicleResponsiblePersons()

        val allLocalFleetManagersWithOnlyPreAndOrPostUsage =
            allUsers
                .filter { it.first.isAlsoFleetManager }
                .filter { it.second.all { Fleet.preUsageFleet.id == it || Fleet.postUsageFleet.id == it } }
                .map { it.first }
                .distinctBy { it.employeeNumber.value }

        println("Falsely identified local fleet managers: ${allLocalFleetManagersWithOnlyPreAndOrPostUsage.size}")

        // downgrade to driver
        //        vehicleResponsiblePersonOutPort.updateVehicleResponsiblePersons(
        //            allLocalFleetManagersWithOnlyPreAndOrPostUsage
        //        )

        val remainingUsersWithFleets =
            allUsers
                .filter { it.first.isAlsoFleetManager }
                .distinctBy { it.first.employeeNumber.value }
                .filterNot { person ->
                    allLocalFleetManagersWithOnlyPreAndOrPostUsage.any {
                        it.employeeNumber.value == person.first.employeeNumber.value
                    }
                }

        val allLocalFleetManagersWithPreOrPostUsageFleets =
            remainingUsersWithFleets.filter {
                it.second.contains(Fleet.preUsageFleet.id) || it.second.contains(Fleet.postUsageFleet.id)
            }

        val allLocalFleetManagersWithReducedFleets =
            allLocalFleetManagersWithPreOrPostUsageFleets.map {
                val reducedFleets =
                    it.second
                        .filterNot { Fleet.preUsageFleet.id == it }
                        .filterNot { Fleet.postUsageFleet.id == it }
                        .map { Fleet(it, it) }
                        .toSet()
                Pair(it.first, reducedFleets)
            }

        // update with reduced fleet
        //
        //
        // vehicleResponsiblePersonOutPort.updateFleetManagers(allLocalFleetManagersWithReducedFleets)
        //
        println("done")
    }

    @Test
    @Disabled
    fun removePrePostUsageFromVisibilityOfDrivers() {
        val allUsers = vehicleResponsiblePersonOutPort.getAllVehicleResponsiblePersons()

        val allLocalFleetManagersWithOnlyPreAndOrPostUsage =
            allUsers
                .filterNot { it.first.isAlsoFleetManager }
                .distinctBy { it.first.employeeNumber.value }
                .map { it.first }

        println("Falsely identified local fleet managers: ${allLocalFleetManagersWithOnlyPreAndOrPostUsage.size}")

        ////         downgrade to driver
        //                vehicleResponsiblePersonOutPort.updateVehicleResponsiblePersons(
        //                    allLocalFleetManagersWithOnlyPreAndOrPostUsage
        //                )

        // update with reduced fleet
        //
        //
        // vehicleResponsiblePersonOutPort.updateFleetManagers(allLocalFleetManagersWithReducedFleets)
        //
        println("done")
    }
}
