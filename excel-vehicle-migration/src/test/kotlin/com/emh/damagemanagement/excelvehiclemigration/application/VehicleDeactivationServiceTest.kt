/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.VehicleBuilder
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.bean.override.mockito.MockitoBean

@IntegrationTest
class VehicleDeactivationServiceTest {

    @Autowired private lateinit var vehicleDeactivationService: VehicleDeactivationService

    @MockitoBean private lateinit var vehicleOutPort: VehicleOutPort

    @Test
    fun `should deactivate vehicles not contained in the parsed list of vehicles`() {
        whenever(vehicleOutPort.deactivateVehicles(any())).thenReturn(UpdateVehiclesResult.empty)

        val parsedVehicles = VehicleBuilder.buildMultiple(20).toList()
        val overlappingVehicles = parsedVehicles.take(10)
        val vehiclesToBeDeactivated = VehicleBuilder.buildMultiple(20).toList()
        val alreadyDeactivatedVehicles = (1..10).map { VehicleBuilder().active(false).build() }
        val existingVehicles = overlappingVehicles + vehiclesToBeDeactivated + alreadyDeactivatedVehicles
        val deactivateVehiclesArgumentCapture = argumentCaptor<List<Vehicle>>()

        vehicleDeactivationService.deactivateVehicles(
            existingVehicles = existingVehicles,
            parsedVehicles = parsedVehicles,
        )

        verify(vehicleOutPort).deactivateVehicles(deactivateVehiclesArgumentCapture.capture())
        assertThat(deactivateVehiclesArgumentCapture.firstValue)
            .containsExactlyInAnyOrderElementsOf(vehiclesToBeDeactivated)
    }
}
