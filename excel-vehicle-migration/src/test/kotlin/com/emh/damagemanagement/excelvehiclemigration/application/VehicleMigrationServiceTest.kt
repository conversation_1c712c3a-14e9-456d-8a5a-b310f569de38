/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.VehicleBuilder
import com.emh.damagemanagement.excelvehiclemigration.adapter.out.s3.StorageProperties
import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationService.Companion.REGEX_EXCEL_FILENAME
import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehiclesResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.excelvehiclemigration.domain.Vin
import com.emh.shared.s3.client.adapter.S3StorageClient
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.parallel.ResourceAccessMode
import org.junit.jupiter.api.parallel.ResourceLock
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.Resource
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [VehicleMigrationServiceTest.TestConfiguration::class])
@ResourceLock(value = "s3", mode = ResourceAccessMode.READ_WRITE)
class VehicleMigrationServiceTest {

    @Autowired private lateinit var vehicleMigrationService: VehicleMigrationService
    @Autowired @Qualifier("vehicle-migration") private lateinit var storageProperties: StorageProperties
    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    @Autowired
    private lateinit var s3StorageClient: S3StorageClient
    @Autowired private lateinit var vehicleOutPort: VehicleOutPort
    @Autowired private lateinit var leasingVehicleMigrationService: LeasingVehicleMigrationService
    @Autowired private lateinit var poolVehicleMigrationService: PoolVehicleMigrationService
    @Autowired private lateinit var vehicleDeactivationService: VehicleDeactivationService
    @Autowired private lateinit var vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort
    @Value("classpath:/vehicle-export/empty_file.xlsx") private lateinit var emptyFile: Resource
    @Value("classpath:/vehicle-export/success_excel_leasing.xlsx") private lateinit var successFile: Resource
    @Value("classpath:/vehicle-export/2025-04-01_RepairFix_Staging_Test_Nutzende Kostenstelle.xlsx")
    private lateinit var testExcel: Resource
    @Value("classpath:/vehicle-export/test_sheet_empty.XLSX") private lateinit var emptySheet: Resource
    @Value("classpath:/vehicle-export/2024-07-22 whitespaces.xlsx")
    private lateinit var testFileWithWhitespaces: Resource

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(
            vehicleOutPort,
            vehicleResponsiblePersonOutPort,
            leasingVehicleMigrationService,
            poolVehicleMigrationService,
        )
        whenever(vehicleOutPort.getAllVehicles()).thenReturn(emptyList())
        whenever(vehicleResponsiblePersonOutPort.getAllVehicleResponsiblePersons()).thenReturn(emptyList())
        whenever(vehicleDeactivationService.deactivateVehicles(any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)
    }

    @AfterEach
    fun cleanup() {
        val keys = s3StorageClient.findAllFileKeysForPrefix(prefix = "", bucket = storageProperties.bucket)
        s3StorageClient.deleteFiles(keys = keys, bucket = storageProperties.bucket)
    }

    @Test
    fun `should process multiple excel files on S3 uploaded`() {
        setupS3Uploaded(testExcel, emptySheet)
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)
        `when`(poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_FAILURE)
        val successFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS)
        val dateTimeReference = DateTimeFormatter.ISO_LOCAL_DATE.format(OffsetDateTime.now())
        assertThat(failedFiles)
            .hasSize(1)
            .anySatisfy { it.contains(emptySheet.filename!!) }
            .allSatisfy { it.contains("${dateTimeReference}_") }
            .allSatisfy { it.startsWith(VehicleMigrationService.PREFIX_FAILURE) }
        assertThat(successFiles).hasSize(1).allSatisfy {
            it.contains(testExcel.filename!!)
            it.contains("${dateTimeReference}_")
            it.startsWith(VehicleMigrationService.PREFIX_SUCCESS)
        }
        verify(vehicleDeactivationService).deactivateVehicles(any(), any())
    }

    @Test
    fun `should keep file in uploaded if vehicle migration fails with VehicleMigrationException and canRetry true`() {
        setupS3Uploaded(testExcel)
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenThrow(VehicleMigrationException(canRetry = true, errorType = ErrorType.TECHNICAL))

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_FAILURE)
        val successWithWarningFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS_WITH_WARNING)
        val unmovedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_UPLOAD)

        assertThat(failedFiles).isEmpty()
        assertThat(successWithWarningFiles).isEmpty()
        assertThat(unmovedFiles).hasSize(1)
    }

    @Test
    fun `should move file to failure if vehicle migration fails with VehicleMigrationException and canRetry false`() {
        setupS3Uploaded(testExcel)
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenThrow(VehicleMigrationException(canRetry = false, errorType = ErrorType.TECHNICAL))

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_FAILURE)
        val successWithWarningFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS_WITH_WARNING)
        val unmovedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_UPLOAD)

        assertThat(failedFiles).hasSize(1)
        assertThat(successWithWarningFiles).isEmpty()
        assertThat(unmovedFiles).isEmpty()
    }

    @Test
    fun `should move file to successWithWarnings if some new vehicles failed migration`() {
        setupS3Uploaded(testExcel)
        val failedVehicles =
            setOf("WP0ZZZ99ZNS201266", "WP1ZZZ95ZKLB28605", "WP0ZZZ98ZKS252236", "WP1ZZZ95ZJLB17624")
                .map { vin -> VehicleBuilder().vin(Vin(vin)).build() }
                .toSet()
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)
        `when`(poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(any(), any(), any()))
            .thenReturn(
                VehicleMigrationService.VehicleMigrationResult(
                    addVehiclesResult =
                        AddVehiclesResult(successfullyAddedVehicles = emptySet(), failedVehicles = failedVehicles),
                    skippedVehicleResult = emptySet(),
                    updateVehiclesResult = UpdateVehiclesResult(emptySet(), emptySet()),
                )
            )

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_FAILURE)
        val successWithWarningFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS_WITH_WARNING)
        val unmovedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_UPLOAD)

        assertThat(failedFiles).isEmpty()
        assertThat(successWithWarningFiles).hasSize(1)
        assertThat(unmovedFiles).isEmpty()
    }

    @Test
    fun `should move file to successWithWarnings if some vehicles update failed migration`() {
        setupS3Uploaded(testExcel)
        val failedVehicles =
            setOf("WP0ZZZ99ZNS201266", "WP1ZZZ95ZKLB28605", "WP0ZZZ98ZKS252236", "WP1ZZZ95ZJLB17624")
                .map { vin -> VehicleBuilder().vin(Vin(vin)).build() }
                .toSet()
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)
        `when`(poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(any(), any(), any()))
            .thenReturn(
                VehicleMigrationService.VehicleMigrationResult(
                    addVehiclesResult =
                        AddVehiclesResult(successfullyAddedVehicles = emptySet(), failedVehicles = emptySet()),
                    skippedVehicleResult = emptySet(),
                    updateVehiclesResult =
                        UpdateVehiclesResult(successfulVehicles = emptySet(), failedVehicles = failedVehicles),
                )
            )

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_FAILURE)
        val successWithWarningFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS_WITH_WARNING)
        val unmovedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_UPLOAD)

        assertThat(failedFiles).isEmpty()
        assertThat(successWithWarningFiles).hasSize(1)
        assertThat(unmovedFiles).isEmpty()
    }

    @Test
    fun `should not process non-excel files`() {
        setupS3Uploaded(testExcel)

        // upload a non-excel file
        s3StorageClient.uploadResource(
            key = "${VehicleMigrationService.PREFIX_UPLOAD}not_an_excel.txt",
            bucket = storageProperties.bucket,
            resource = ByteArrayResource("thou shall not use excel".toByteArray(Charsets.UTF_8)),
            contentType = "text/plain",
        )
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)
        `when`(poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_FAILURE)
        val successFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS)
        val unmovedFileKeys =
            s3StorageClient.findAllFileKeysForPrefix(
                prefix = VehicleMigrationService.PREFIX_UPLOAD,
                bucket = storageProperties.bucket,
            )
        assertThat(failedFiles).isEmpty()
        assertThat(successFiles).hasSize(1)
        val successFileKey = successFiles.single()
        val dateTimeReference = DateTimeFormatter.ISO_LOCAL_DATE.format(OffsetDateTime.now())
        assertThat(successFileKey).startsWith(VehicleMigrationService.PREFIX_SUCCESS)
        assertThat(successFileKey).endsWith(testExcel.filename)
        assertThat(successFileKey).contains(dateTimeReference)
        assertThat(unmovedFileKeys)
            .containsExactlyInAnyOrder("${VehicleMigrationService.PREFIX_UPLOAD}not_an_excel.txt")
    }

    @Test
    fun `should process files with whitespaces in filename`() {
        setupS3Uploaded(testFileWithWhitespaces)
        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)
        `when`(poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_FAILURE)
        val successFile = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS).single()
        assertThat(failedFiles).isEmpty()
        val dateTimeReference = DateTimeFormatter.ISO_LOCAL_DATE.format(OffsetDateTime.now())
        assertThat(successFile).endsWith(testFileWithWhitespaces.filename)
        assertThat(successFile).contains(dateTimeReference)
    }

    @Test
    fun `should move file to success if migration finished without parsing and migration errors`() {
        setupS3Uploaded(successFile)
        `when`(vehicleOutPort.addVehicles(any())).thenReturn(AddVehiclesResult(emptySet(), emptySet()))
        `when`(vehicleOutPort.updateVehicles(any())).thenReturn(UpdateVehiclesResult(emptySet(), emptySet()))
        `when`(leasingVehicleMigrationService.migrateLeasingVehiclesAndVehicleResponsiblePersons(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)
        `when`(poolVehicleMigrationService.migratePoolVehiclesAndFleetManagers(any(), any(), any()))
            .thenReturn(VehicleMigrationService.VehicleMigrationResult.empty)

        vehicleMigrationService.migrateVehiclesFromS3()

        val failedFiles =
            s3StorageClient.findAllFileKeysForPrefix(
                prefix = VehicleMigrationService.PREFIX_FAILURE,
                bucket = storageProperties.bucket,
            )
        val successWithWarningFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS_WITH_WARNING)
        val success = getExcelFilesForBucket(VehicleMigrationService.PREFIX_SUCCESS)
        val unmovedFiles = getExcelFilesForBucket(VehicleMigrationService.PREFIX_UPLOAD)
        assertThat(failedFiles).isEmpty()
        assertThat(unmovedFiles).isEmpty()
        assertThat(successWithWarningFiles).isEmpty()
        assertThat(success).hasSize(1)
    }

    private fun setupS3Uploaded(vararg testFile: Resource) {
        val files = testFile.associateBy { "${VehicleMigrationService.PREFIX_UPLOAD}${it.filename}" }
        s3StorageClient.uploadFiles(keysToFiles = files, bucket = storageProperties.bucket)
    }

    private fun getExcelFilesForBucket(prefix: String) =
        s3StorageClient.findAllFileKeysForPrefix(prefix = prefix, bucket = storageProperties.bucket).filter {
            it.matches(REGEX_EXCEL_FILENAME)
        }

    internal class TestConfiguration {

        @Bean @Primary fun vehicleOutPort(): VehicleOutPort = mock()

        @Bean @Primary fun vehicleResponsiblePersonOutPort(): VehicleResponsiblePersonOutPort = mock()

        @Bean @Primary fun leasingVehicleMigrationService(): LeasingVehicleMigrationService = mock()

        @Bean @Primary fun poolVehicleMigrationService(): PoolVehicleMigrationService = mock()

        @Bean @Primary fun vehicleDeactivationService(): VehicleDeactivationService = mock()
    }
}
