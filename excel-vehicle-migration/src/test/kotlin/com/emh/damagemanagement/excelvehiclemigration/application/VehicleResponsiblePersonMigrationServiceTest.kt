/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application

import com.emh.damagemanagement.employee.adapter.EmployeeException
import com.emh.damagemanagement.excelvehiclemigration.EmployeeOverviewBuilder
import com.emh.damagemanagement.excelvehiclemigration.IntegrationTest
import com.emh.damagemanagement.excelvehiclemigration.PoolFleetBuilder
import com.emh.damagemanagement.excelvehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.excelvehiclemigration.application.port.AddVehicleResponsiblePersonsResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.EmployeeOutPort
import com.emh.damagemanagement.excelvehiclemigration.application.port.UpdateVehicleResponsiblePersonsResult
import com.emh.damagemanagement.excelvehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.excelvehiclemigration.buildFleetManagersWithFleets
import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Fleet
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.excelvehiclemigration.groupedByFleetManagerWithFleet
import com.emh.damagemanagement.excelvehiclemigration.groupedByFleetManagerWithFleetId
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.reset
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@ContextConfiguration(classes = [VehicleResponsiblePersonMigrationServiceTest.TestConfiguration::class])
@TestPropertySource(properties = ["feature-flags.vehicle-responsible-person-migration-uses-user-service=true"])
class VehicleResponsiblePersonMigrationServiceTest {
    @Autowired private lateinit var vehicleResponsiblePersonMigrationService: VehicleResponsiblePersonMigrationService
    @Autowired private lateinit var vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort
    @Autowired private lateinit var employeeOutPort: EmployeeOutPort

    @BeforeEach
    fun resetMocks() {
        reset(vehicleResponsiblePersonOutPort)
        reset(employeeOutPort)

        whenever(employeeOutPort.getEmployeeOverviewsFor(any())).thenReturn(emptyList())
    }

    @Test
    fun `should only add non-existing persons`() {
        val vehicleResponsiblePersons = VehicleResponsiblePersonBuilder.buildMultiple(130)
        val existingVehicleResponsiblePersons = vehicleResponsiblePersons.filter { Random.nextBoolean() }
        whenever(vehicleResponsiblePersonOutPort.updateVehicleResponsiblePersons(any()))
            .thenReturn(UpdateVehicleResponsiblePersonsResult(emptySet(), emptySet()))
        val addVehicleResponsiblePersonsArgumentCaptor = argumentCaptor<List<VehicleResponsiblePerson>>()

        val expectedResponsiblePersons =
            vehicleResponsiblePersons.filterNot {
                existingVehicleResponsiblePersons
                    .map { vehicleResponsiblePerson -> vehicleResponsiblePerson.employeeNumber }
                    .contains(it.employeeNumber)
            }
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePersons(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = expectedResponsiblePersons.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val migrationResult =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(
                parsedVehicleAndResponsiblePersons = vehicleResponsiblePersons,
                existingVehicleResponsiblePersons = existingVehicleResponsiblePersons,
            )

        verify(vehicleResponsiblePersonOutPort)
            .addVehicleResponsiblePersons(addVehicleResponsiblePersonsArgumentCaptor.capture())
        assertThat(addVehicleResponsiblePersonsArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(expectedResponsiblePersons)
        assertThat(migrationResult.addVehicleResponsiblePersonsResult.successfullyAddedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(expectedResponsiblePersons)
    }

    @Test
    fun `should only add non-existing fleet managers`() {
        val fleetManagers = buildFleetManagersWithFleets()

        val (existingFleetManagers, expectedFleetManagers) =
            fleetManagers.groupedByFleetManagerWithFleetId().partition { Random.nextBoolean() }

        whenever(vehicleResponsiblePersonOutPort.updateFleetManagers(any()))
            .thenReturn(UpdateVehicleResponsiblePersonsResult(emptySet(), emptySet()))
        val addFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()

        whenever(vehicleResponsiblePersonOutPort.addFleetManagers(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = expectedFleetManagers.map { it.first }.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val migrationResult =
            vehicleResponsiblePersonMigrationService.migrateFleetManagers(
                fleetManagerAndFleets = fleetManagers,
                existingVehicleResponsiblePersonsWithFleetIds = existingFleetManagers,
            )

        verify(vehicleResponsiblePersonOutPort).addFleetManagers(addFleetManagersArgumentCaptor.capture())

        assertThat(addFleetManagersArgumentCaptor.firstValue).zipSatisfy(expectedFleetManagers) {
            fleetManagerAndFleetsFromCaptor: Pair<VehicleResponsiblePerson, Set<Fleet>>,
            expectedFleetManagersAndFleetIds: Pair<VehicleResponsiblePerson, Set<String>> ->
            assertThat(fleetManagerAndFleetsFromCaptor.first).isEqualTo(expectedFleetManagersAndFleetIds.first)
            assertThat(fleetManagerAndFleetsFromCaptor.second.map(Fleet::id))
                .containsExactlyInAnyOrderElementsOf(expectedFleetManagersAndFleetIds.second)
        }
        assertThat(migrationResult.addVehicleResponsiblePersonsResult.successfullyAddedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(expectedFleetManagers.map { it.first })
    }

    @Test
    fun `should only update persons with actual changes`() {
        val vehicleResponsiblePersons = VehicleResponsiblePersonBuilder.buildMultiple(130)
        val (existingVehicleResponsiblePersons, nonExistingVehicleResponsiblePersons) =
            vehicleResponsiblePersons.partition { Random.nextBoolean() }
        val (vehicleResponsiblePersonsWithUpdatesPlaceholder, vehicleResponsiblePersonsWithoutUpdates) =
            existingVehicleResponsiblePersons.partition { Random.nextBoolean() }
        val vehicleResponsiblePersonsWithUpdates =
            vehicleResponsiblePersonsWithUpdatesPlaceholder.map {
                VehicleResponsiblePersonBuilder().employeeNumber(it.employeeNumber).build()
            }
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePersons(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = nonExistingVehicleResponsiblePersons.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )

        whenever(vehicleResponsiblePersonOutPort.updateVehicleResponsiblePersons(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons = vehicleResponsiblePersonsWithUpdates.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val updateVehicleResponsiblePersonsArgumentCaptor = argumentCaptor<List<VehicleResponsiblePerson>>()

        val migrationResult =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(
                parsedVehicleAndResponsiblePersons =
                    (nonExistingVehicleResponsiblePersons +
                            vehicleResponsiblePersonsWithoutUpdates +
                            vehicleResponsiblePersonsWithUpdates)
                        .toSet(),
                existingVehicleResponsiblePersons = existingVehicleResponsiblePersons,
            )

        verify(vehicleResponsiblePersonOutPort)
            .updateVehicleResponsiblePersons(updateVehicleResponsiblePersonsArgumentCaptor.capture())
        assertThat(updateVehicleResponsiblePersonsArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(vehicleResponsiblePersonsWithUpdates)
        assertThat(migrationResult.updateVehicleResponsiblePersonsResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(vehicleResponsiblePersonsWithUpdates)
        assertThat(migrationResult.addVehicleResponsiblePersonsResult.successfullyAddedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(nonExistingVehicleResponsiblePersons)
    }

    @Test
    fun `should only update fleet managers with manager changes`() {
        val fleetManagers = buildFleetManagersWithFleets()
        val (existingFleetManagers, notExistingFleetManagers) =
            fleetManagers.groupedByFleetManagerWithFleet().partition { Random.nextBoolean() }

        val (fleetManagersWithUpdatesPlaceholder, fleetManagersWithoutUpdates) =
            existingFleetManagers.partition { Random.nextBoolean() }
        val fleetManagersWithUpdates =
            fleetManagersWithUpdatesPlaceholder.map {
                Pair(VehicleResponsiblePersonBuilder().employeeNumber(it.first.employeeNumber).build(), it.second)
            }
        val existingFleetManagersWithFleeIds =
            existingFleetManagers.map { Pair(it.first, it.second.map { it.id }.toSet()) }
        whenever(vehicleResponsiblePersonOutPort.addFleetManagers(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = notExistingFleetManagers.map { it.first }.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )

        whenever(vehicleResponsiblePersonOutPort.updateFleetManagers(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons = fleetManagersWithUpdates.map { it.first }.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val updateFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()

        val migrationResult =
            vehicleResponsiblePersonMigrationService.migrateFleetManagers(
                fleetManagerAndFleets =
                    ((notExistingFleetManagers + fleetManagersWithoutUpdates + fleetManagersWithUpdates))
                        .map { fleetManagerAndFleets ->
                            fleetManagerAndFleets.second.map { Pair(fleetManagerAndFleets.first, it) }
                        }
                        .flatten(),
                existingVehicleResponsiblePersonsWithFleetIds = existingFleetManagersWithFleeIds,
            )

        verify(vehicleResponsiblePersonOutPort).updateFleetManagers(updateFleetManagersArgumentCaptor.capture())
        assertThat(updateFleetManagersArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(fleetManagersWithUpdates)
        assertThat(migrationResult.updateVehicleResponsiblePersonsResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(fleetManagersWithUpdates.map { it.first })
        assertThat(migrationResult.addVehicleResponsiblePersonsResult.successfullyAddedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(notExistingFleetManagers.map { it.first })
    }

    @Test
    fun `should only update fleet managers with fleet changes`() {
        val fleetManagers = buildFleetManagersWithFleets()
        val (existingFleetManagers, notExistingFleetManagers) =
            fleetManagers.groupedByFleetManagerWithFleet().partition { Random.nextBoolean() }

        val (fleetManagersWithUpdatesPlaceholder, fleetManagersWithoutUpdates) =
            existingFleetManagers.partition { Random.nextBoolean() }
        val fleetManagersWithUpdates =
            fleetManagersWithUpdatesPlaceholder.map {
                Pair(
                    it.first,
                    // ensure, that the updated fleet actually differ (by either adding or removing
                    // a fleet)
                    // this was flaky before, when fleets were randomly matching
                    if (it.second.isEmpty()) PoolFleetBuilder.buildMultiple() else it.second.minus(it.second.random()),
                )
            }
        val allFleetManagersWithFleetIds =
            fleetManagers.groupedByFleetManagerWithFleet().map { fleetManagerWithFleets ->
                Pair(fleetManagerWithFleets.first, fleetManagerWithFleets.second.map { it.id }.toSet())
            }
        whenever(vehicleResponsiblePersonOutPort.addFleetManagers(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = notExistingFleetManagers.map { it.first }.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )

        whenever(vehicleResponsiblePersonOutPort.updateFleetManagers(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons = fleetManagersWithUpdates.map { it.first }.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val updateFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()

        val migrationResult =
            vehicleResponsiblePersonMigrationService.migrateFleetManagers(
                fleetManagerAndFleets =
                    ((notExistingFleetManagers + fleetManagersWithoutUpdates + fleetManagersWithUpdates))
                        .map { fleetManagerAndFleets ->
                            fleetManagerAndFleets.second.map { Pair(fleetManagerAndFleets.first, it) }
                        }
                        .flatten(),
                existingVehicleResponsiblePersonsWithFleetIds = allFleetManagersWithFleetIds,
            )

        verify(vehicleResponsiblePersonOutPort).updateFleetManagers(updateFleetManagersArgumentCaptor.capture())
        assertThat(updateFleetManagersArgumentCaptor.firstValue)
            .containsExactlyInAnyOrderElementsOf(fleetManagersWithUpdates)
        assertThat(migrationResult.updateVehicleResponsiblePersonsResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(fleetManagersWithUpdates.map { it.first })
        assertThat(migrationResult.addVehicleResponsiblePersonsResult.successfullyAddedVehicleResponsiblePersons)
            .containsExactlyInAnyOrderElementsOf(notExistingFleetManagers.map { it.first })
    }

    @Test
    fun `should upgrade drivers to fleet managers`() {
        val existingFleetManagers = buildFleetManagersWithFleets()
        val vehicleResponsiblePersonThatWantsToBeAFleetManager =
            Pair(VehicleResponsiblePersonBuilder.buildSingle(), PoolFleetBuilder.buildSingle())
        val vehicleResponsiblePersons = VehicleResponsiblePersonBuilder.buildMultiple(40)

        val existingVehicleResponsiblePersonsWithFleetIds =
            vehicleResponsiblePersons.map { Pair(it, emptySet<String>()) } +
                setOf(vehicleResponsiblePersonThatWantsToBeAFleetManager).map { Pair(it.first, emptySet()) }
        whenever(vehicleResponsiblePersonOutPort.addFleetManagers(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = emptySet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )

        whenever(vehicleResponsiblePersonOutPort.updateFleetManagers(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons =
                        setOf(vehicleResponsiblePersonThatWantsToBeAFleetManager.first),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val updateFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()

        val migrationResult =
            vehicleResponsiblePersonMigrationService.migrateFleetManagers(
                fleetManagerAndFleets =
                    (existingFleetManagers + setOf(vehicleResponsiblePersonThatWantsToBeAFleetManager)),
                existingVehicleResponsiblePersonsWithFleetIds = existingVehicleResponsiblePersonsWithFleetIds,
            )

        verify(vehicleResponsiblePersonOutPort).updateFleetManagers(updateFleetManagersArgumentCaptor.capture())
        assertThat(updateFleetManagersArgumentCaptor.firstValue)
            .containsExactlyInAnyOrder(
                vehicleResponsiblePersonThatWantsToBeAFleetManager.let { Pair(it.first, setOf(it.second)) }
            )
        assertThat(migrationResult.updateVehicleResponsiblePersonsResult.successfullyUpdatedVehicleResponsiblePersons)
            .containsExactlyInAnyOrder(vehicleResponsiblePersonThatWantsToBeAFleetManager.first)
    }

    @Test
    fun `should not update persons with just case-delta in relevant properties`() {
        val existingVehicleResponsiblePerson =
            VehicleResponsiblePersonBuilder()
                .firstName("IamVery")
                .lastName("caseSENSITIVe")
                .email("case@<EMAIL>")
                .build()
        val vehicleResponsiblePersonWithDeltaUpdates =
            VehicleResponsiblePersonBuilder()
                .firstName("IAMVERY")
                .lastName("casesensitive")
                .email("CASE@<EMAIL>")
                .build()
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePersons(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = emptySet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )

        whenever(vehicleResponsiblePersonOutPort.updateVehicleResponsiblePersons(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val updateVehicleResponsiblePersonsArgumentCaptor = argumentCaptor<List<VehicleResponsiblePerson>>()

        vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(
            parsedVehicleAndResponsiblePersons = setOf(vehicleResponsiblePersonWithDeltaUpdates),
            existingVehicleResponsiblePersons = listOf(existingVehicleResponsiblePerson),
        )

        verify(vehicleResponsiblePersonOutPort)
            .updateVehicleResponsiblePersons(updateVehicleResponsiblePersonsArgumentCaptor.capture())
        assertThat(updateVehicleResponsiblePersonsArgumentCaptor.firstValue).isEmpty()
    }

    @Test
    fun `should use requested employee information during vehicle responsible person update`() {
        val existingEmployees = EmployeeOverviewBuilder.buildMultiple()
        whenever(employeeOutPort.getEmployeeOverviewsFor(any())).thenReturn(existingEmployees.toList())
        val vehicleResponsiblePersons =
            VehicleResponsiblePersonBuilder.buildMultiple(130) +
                existingEmployees.map {
                    VehicleResponsiblePersonBuilder().employeeNumber(EmployeeNumber(it.employeeNumber)).build()
                }
        val (existingVehicleResponsiblePersons, nonExistingVehicleResponsiblePersons) =
            vehicleResponsiblePersons.partition { Random.nextBoolean() }
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePersons(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = nonExistingVehicleResponsiblePersons.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )

        whenever(vehicleResponsiblePersonOutPort.updateVehicleResponsiblePersons(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons = existingVehicleResponsiblePersons.toSet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val updateVehicleResponsiblePersonsArgumentCaptor = argumentCaptor<List<VehicleResponsiblePerson>>()
        val addVehicleResponsiblePersonsArgumentCaptor = argumentCaptor<List<VehicleResponsiblePerson>>()

        vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(
            parsedVehicleAndResponsiblePersons =
                (nonExistingVehicleResponsiblePersons + existingVehicleResponsiblePersons).toSet(),
            existingVehicleResponsiblePersons = existingVehicleResponsiblePersons,
        )

        verify(vehicleResponsiblePersonOutPort)
            .updateVehicleResponsiblePersons(updateVehicleResponsiblePersonsArgumentCaptor.capture())
        verify(vehicleResponsiblePersonOutPort)
            .addVehicleResponsiblePersons(addVehicleResponsiblePersonsArgumentCaptor.capture())
        val vehicleResponsiblePersonsMatchingExistingEmployees =
            (updateVehicleResponsiblePersonsArgumentCaptor.firstValue +
                    addVehicleResponsiblePersonsArgumentCaptor.firstValue)
                .filter { vehicleResponsiblePerson ->
                    existingEmployees.map { it.employeeNumber }.contains(vehicleResponsiblePerson.employeeNumber.value)
                }

        assertThat(vehicleResponsiblePersonsMatchingExistingEmployees).hasSameSizeAs(existingEmployees)
        assertThat(vehicleResponsiblePersonsMatchingExistingEmployees).allSatisfy { matchingVehicleResponsiblePerson ->
            val existingEmployee =
                existingEmployees.single { matchingVehicleResponsiblePerson.employeeNumber.value == it.employeeNumber }
            assertThat(matchingVehicleResponsiblePerson.firstName).isEqualTo(existingEmployee.firstName)
            assertThat(matchingVehicleResponsiblePerson.lastName).isEqualTo(existingEmployee.lastName)
            assertThat(matchingVehicleResponsiblePerson.email).isEqualTo(existingEmployee.companyEmail)
        }
    }

    @Test
    fun `should throw VehicleResponsiblePersonMigrationException in case of EmployeeOutPortException when requesting employee information`() {
        val vehicleResponsiblePersons = VehicleResponsiblePersonBuilder.buildMultiple(130)
        whenever(employeeOutPort.getEmployeeOverviewsFor(any()))
            .thenThrow(EmployeeException("you broke the internet!", null))

        assertThatExceptionOfType(VehicleResponsiblePersonMigrationException::class.java).isThrownBy {
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePersons(
                parsedVehicleAndResponsiblePersons = vehicleResponsiblePersons,
                existingVehicleResponsiblePersons = emptyList(),
            )
        }
    }

    @Test
    fun `should not migrate fleet managers that are assigned to pre or post usage fleets`() {
        val personWithPreUsageFleet = VehicleResponsiblePersonBuilder.buildSingle()
        val personWithPostUsageFleet = VehicleResponsiblePersonBuilder.buildSingle()
        val personWithPreAndPostUsageFleet = VehicleResponsiblePersonBuilder.buildSingle()
        val personWithPreUsageAndPoolFleet = VehicleResponsiblePersonBuilder().alsoFleetManager().build()
        val randomPoolFleet = PoolFleetBuilder.buildSingle()
        val personsToBeMigrated =
            listOf(
                Pair(personWithPreUsageFleet, Fleet.preUsageFleet),
                Pair(personWithPostUsageFleet, Fleet.postUsageFleet),
                Pair(personWithPreAndPostUsageFleet, Fleet.preUsageFleet),
                Pair(personWithPreAndPostUsageFleet, Fleet.postUsageFleet),
                Pair(personWithPreUsageAndPoolFleet, Fleet.preUsageFleet),
                Pair(personWithPreUsageAndPoolFleet, randomPoolFleet),
            )
        whenever(vehicleResponsiblePersonOutPort.addFleetManagers(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = emptySet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        whenever(vehicleResponsiblePersonOutPort.updateFleetManagers(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val addFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()
        val updateFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()

        vehicleResponsiblePersonMigrationService.migrateFleetManagers(
            fleetManagerAndFleets = personsToBeMigrated,
            existingVehicleResponsiblePersonsWithFleetIds = emptyList(),
        )

        verify(vehicleResponsiblePersonOutPort).addFleetManagers(addFleetManagersArgumentCaptor.capture())
        verify(vehicleResponsiblePersonOutPort).updateFleetManagers(updateFleetManagersArgumentCaptor.capture())
        assertThat(addFleetManagersArgumentCaptor.firstValue)
            .containsExactlyInAnyOrder(Pair(personWithPreUsageAndPoolFleet, setOf(randomPoolFleet)))
        assertThat(updateFleetManagersArgumentCaptor.firstValue).isEmpty()
    }

    @Test
    fun `should not update fleet managers that are assigned to pre or post usage fleets`() {
        val personWithPreUsageFleet = VehicleResponsiblePersonBuilder.buildSingle()
        val personWithPostUsageFleet = VehicleResponsiblePersonBuilder.buildSingle()
        val personWithPreAndPostUsageFleet = VehicleResponsiblePersonBuilder.buildSingle()
        val personWithPreUsageAndPoolFleet = VehicleResponsiblePersonBuilder().alsoFleetManager().build()
        val randomPoolFleet = PoolFleetBuilder.buildSingle()
        val existingFleetManagers =
            listOf(
                Pair(personWithPreUsageFleet, setOf(PoolFleetBuilder.buildSingle().id)),
                Pair(personWithPostUsageFleet, setOf(PoolFleetBuilder.buildSingle().id)),
                Pair(personWithPreAndPostUsageFleet, setOf(PoolFleetBuilder.buildSingle().id)),
                Pair(personWithPreUsageAndPoolFleet, setOf("random_pool_fleet")),
            )
        val personsToBeMigrated =
            listOf(
                Pair(personWithPreUsageFleet, Fleet.preUsageFleet),
                Pair(personWithPostUsageFleet, Fleet.postUsageFleet),
                Pair(personWithPreAndPostUsageFleet, Fleet.preUsageFleet),
                Pair(personWithPreAndPostUsageFleet, Fleet.postUsageFleet),
                Pair(personWithPreUsageAndPoolFleet, Fleet.preUsageFleet),
                Pair(personWithPreUsageAndPoolFleet, randomPoolFleet),
            )
        whenever(vehicleResponsiblePersonOutPort.addFleetManagers(any()))
            .thenReturn(
                AddVehicleResponsiblePersonsResult(
                    successfullyAddedVehicleResponsiblePersons = emptySet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        whenever(vehicleResponsiblePersonOutPort.updateFleetManagers(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonsResult(
                    successfullyUpdatedVehicleResponsiblePersons = emptySet(),
                    failedVehicleResponsiblePersons = emptySet(),
                )
            )
        val addFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()
        val updateFleetManagersArgumentCaptor = argumentCaptor<List<Pair<VehicleResponsiblePerson, Set<Fleet>>>>()

        vehicleResponsiblePersonMigrationService.migrateFleetManagers(
            fleetManagerAndFleets = personsToBeMigrated,
            existingVehicleResponsiblePersonsWithFleetIds = existingFleetManagers,
        )

        verify(vehicleResponsiblePersonOutPort).addFleetManagers(addFleetManagersArgumentCaptor.capture())
        verify(vehicleResponsiblePersonOutPort).updateFleetManagers(updateFleetManagersArgumentCaptor.capture())
        assertThat(updateFleetManagersArgumentCaptor.firstValue)
            .containsExactlyInAnyOrder(Pair(personWithPreUsageAndPoolFleet, setOf(randomPoolFleet)))
        assertThat(addFleetManagersArgumentCaptor.firstValue).isEmpty()
    }

    internal class TestConfiguration {

        @Bean @Primary fun vehicleResponsiblePersonOutPort(): VehicleResponsiblePersonOutPort = mock()
    }
}
