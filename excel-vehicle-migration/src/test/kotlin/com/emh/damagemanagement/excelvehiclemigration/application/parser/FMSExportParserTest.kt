/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.parser

import com.emh.damagemanagement.excelvehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.excelvehiclemigration.domain.Vehicle
import com.emh.damagemanagement.excelvehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.excelvehiclemigration.domain.Vin
import org.apache.poi.xssf.usermodel.XSSFWorkbook
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.springframework.core.io.ClassPathResource

class FMSExportParserTest {

    private val testExcel =
        ClassPathResource("vehicle-export/2025-04-01_RepairFix_Staging_Test_Nutzende Kostenstelle.xlsx")
    private val testSheetEmptyExcel = ClassPathResource("vehicle-export/test_sheet_empty.XLSX")
    private val testSheetEmptyRowExcel = ClassPathResource("vehicle-export/test_sheet_empty_row.XLSX")
    private val testSheetMalformedRowsExcel = ClassPathResource("vehicle-export/2024-07-22_malformed_rows.xlsx")

    private val parser = FMSExportParser()

    @Test
    fun `should successfully parse test excel`() {
        val workbook = XSSFWorkbook(testExcel.inputStream)
        val fmsExportParserResult = parser.parseFMSExport(workbook)

        val sampleVehicleResponsiblePerson1 =
            VehicleResponsiblePerson(
                employeeNumber = EmployeeNumber("342586"),
                lastName = "Schummer",
                firstName = "Jochen",
                email = "<EMAIL>",
            )
        val sampleVehicleResponsiblePerson2 =
            VehicleResponsiblePerson(
                employeeNumber = EmployeeNumber("351650"),
                lastName = "Linon",
                firstName = "Jerome",
                email = "<EMAIL>",
            )

        val sampleLeasingVehicle1 =
            Vehicle(
                vin = Vin("WP0ZZZ995RS214650"),
                model = "Carrera",
                licensePlate = "S-RF 1014",
                fleetId = "L9.1",
                responsiblePerson = sampleVehicleResponsiblePerson1.employeeNumber,
                vGuid = "051MkxPk7jsxnYXphZLAhW",
                costCenter = "**********",
                leasingArt = "L9.1",
                isActive = true,
                usingCostCenter = null,
            )
        val sampleLeasingVehicle2 =
            Vehicle(
                vin = Vin("WP0ZZZY1ZMSA30019"),
                model = "Taycan",
                licensePlate = "S-RF 1030",
                fleetId = "L1.2",
                responsiblePerson = sampleVehicleResponsiblePerson2.employeeNumber,
                vGuid = "051MkwJW7jo2ibl9swuh5W",
                costCenter = "**********",
                leasingArt = "L1.2",
                isActive = true,
                usingCostCenter = null,
            )
        val samplePoolVehicle1 =
            Vehicle(
                vin = Vin("WP0ZZZ99ZNS240595"),
                model = "Carrera",
                licensePlate = "S-RF 1022",
                fleetId = "Schummer_Jochen_00342586",
                responsiblePerson = null,
                vGuid = "U}hoQJUz7k{0fuJODRu9bG",
                costCenter = "**********",
                leasingArt = "L9.2",
                isActive = true,
                usingCostCenter = "00H0015702",
            )
        val samplePoolVehicle2 =
            Vehicle(
                vin = Vin("WP0ZZZ99ZLS256174"),
                model = "Carrera",
                licensePlate = "S-RF 1037",
                fleetId = "Wüst_Susanne_00178814",
                responsiblePerson = null,
                vGuid = "051MfW2Z7jcHpX6vnKfP6m",
                costCenter = "**********",
                leasingArt = "L9.2",
                isActive = true,
                usingCostCenter = "5702",
            )

        val (parsedPoolVehicles, parsedLeasingVehicles) =
            fmsExportParserResult.vehicleAndResponsiblePersonAndFleets
                .map(VehicleAndResponsiblePersonAndFleet::first)
                .partition { it.isPoolVehicle }
        val parsedVehicleResponsiblePersons =
            fmsExportParserResult.vehicleAndResponsiblePersonAndFleets
                .map(VehicleAndResponsiblePersonAndFleet::second)
                .distinctBy { it.employeeNumber }
        assertThat(fmsExportParserResult.hasParsingErrors).isFalse()
        assertThat(parsedPoolVehicles).hasSize(19)
        assertThat(parsedLeasingVehicles).hasSize(19)
        assertThat(parsedVehicleResponsiblePersons).hasSize(27)
        assertThat(parsedLeasingVehicles)
            .anySatisfy {
                assertThat(it)
                    .usingRecursiveComparison()
                    .comparingOnlyFields(
                        "vin",
                        "model",
                        "isPoolVehicle",
                        "licensePlate",
                        "fleetId",
                        "vGuid",
                        "costCenter",
                    )
                    .isEqualTo(sampleLeasingVehicle1)
            }
            .anySatisfy {
                assertThat(it)
                    .usingRecursiveComparison()
                    .comparingOnlyFields(
                        "vin",
                        "model",
                        "isPoolVehicle",
                        "licensePlate",
                        "fleetId",
                        "vGuid",
                        "costCenter",
                    )
                    .isEqualTo(sampleLeasingVehicle2)
            }
        assertThat(parsedPoolVehicles)
            .anySatisfy {
                assertThat(it)
                    .usingRecursiveComparison()
                    .comparingOnlyFields(
                        "vin",
                        "model",
                        "isPoolVehicle",
                        "licensePlate",
                        "fleetId",
                        "vGuid",
                        "costCenter",
                    )
                    .isEqualTo(samplePoolVehicle1)
            }
            .anySatisfy {
                assertThat(it)
                    .usingRecursiveComparison()
                    .comparingOnlyFields(
                        "vin",
                        "model",
                        "isPoolVehicle",
                        "licensePlate",
                        "fleetId",
                        "vGuid",
                        "costCenter",
                    )
                    .isEqualTo(samplePoolVehicle2)
            }

        assertThat(parsedVehicleResponsiblePersons)
            .anySatisfy {
                assertThat(it)
                    .usingRecursiveComparison()
                    .ignoringFields("isAlsoFleetManager")
                    .isEqualTo(sampleVehicleResponsiblePerson1)
            }
            .anySatisfy {
                assertThat(it)
                    .usingRecursiveComparison()
                    .ignoringFields("isAlsoFleetManager")
                    .isEqualTo(sampleVehicleResponsiblePerson2)
            }
    }

    @Test
    fun `should throw FMSExportParserException when sheet on position 0 is missing`() {
        val emptyWorkbook = XSSFWorkbook()

        assertThatExceptionOfType(FMSExportParserException::class.java)
            .isThrownBy { parser.parseFMSExport(emptyWorkbook) }
            .withMessageContaining("Could not find sheet on position 0.")
    }

    @Test
    fun `should throw FMSExportParserException when sheet is empty`() {
        val workbook = XSSFWorkbook(testSheetEmptyExcel.inputStream)

        assertThatExceptionOfType(FMSExportParserException::class.java)
            .isThrownBy { parser.parseFMSExport(workbook) }
            .withMessageContaining("Excel sheet does not contain any data rows")
    }

    @Test
    fun `should not fail parsing for file with some malformed rows`() {
        val workbook = XSSFWorkbook(testSheetMalformedRowsExcel.inputStream)

        val vehicleAndResponsiblePersons = parser.parseFMSExport(workbook).vehicleAndResponsiblePersonAndFleets

        assertThat(vehicleAndResponsiblePersons).hasSize(3420)
    }

    @Test
    fun `should not fail on empty rows`() {
        val workbook = XSSFWorkbook(testSheetEmptyRowExcel.inputStream)

        val vehicleAndResponsiblePersons = parser.parseFMSExport(workbook).vehicleAndResponsiblePersonAndFleets
        assertThat(vehicleAndResponsiblePersons).isEmpty()
    }
}
