/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.application.scheduler

import com.emh.damagemanagement.excelvehiclemigration.application.ErrorType
import com.emh.damagemanagement.excelvehiclemigration.application.ExcelFleetMigrationService
import com.emh.damagemanagement.excelvehiclemigration.application.FleetMigrationException
import com.emh.damagemanagement.excelvehiclemigration.application.VehicleMigrationService
import com.emh.damagemanagement.excelvehiclemigration.application.job.VehicleMigrationJob
import org.assertj.core.api.Assertions.*
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.mockito.Mockito.*
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

class VehicleMigrationJobTest {

    private val vehicleMigrationService: VehicleMigrationService = mock()
    private val fleeMigrationService: ExcelFleetMigrationService = mock()

    private val vehicleMigrationJob: VehicleMigrationJob =
        VehicleMigrationJob(
            vehicleMigrationService = vehicleMigrationService,
            fleetMigrationService = fleeMigrationService,
        )

    @Test
    fun `should migrate fleets and vehicles when scheduler is called`() {
        `when`(fleeMigrationService.allManagedFleetsHaveBeenMigrated()).thenReturn(false)

        vehicleMigrationJob.execute(null)

        verify(fleeMigrationService, times(1)).allManagedFleetsHaveBeenMigrated()
        verify(fleeMigrationService, times(1)).migrateManagedFleets()
        verify(vehicleMigrationService, times(1)).migrateVehiclesFromS3()
    }

    @Test
    fun `should skip fleet migration if all fleets have already been migrated`() {
        `when`(fleeMigrationService.allManagedFleetsHaveBeenMigrated()).thenReturn(true)

        vehicleMigrationJob.execute(null)

        verify(fleeMigrationService, times(1)).allManagedFleetsHaveBeenMigrated()
        verify(fleeMigrationService, times(0)).migrateManagedFleets()
        verify(vehicleMigrationService, times(1)).migrateVehiclesFromS3()
    }

    @Test
    @Disabled(
        "Disabled until either repairfix allows for 'L2' fleet to be added, or fixes their BadRequest error response body"
    )
    fun `should skip vehicle migration if fleet migration fails`() {
        `when`(fleeMigrationService.allManagedFleetsHaveBeenMigrated()).thenReturn(false)
        `when`(fleeMigrationService.migrateManagedFleets())
            .thenThrow(FleetMigrationException(message = "Danger!!", errorType = ErrorType.TECHNICAL))

        assertThatNoException().isThrownBy { vehicleMigrationJob.execute(null) }

        verify(fleeMigrationService, times(1)).allManagedFleetsHaveBeenMigrated()
        verify(fleeMigrationService, times(1)).migrateManagedFleets()
        verify(vehicleMigrationService, times(0)).migrateVehiclesFromS3()
    }
}
