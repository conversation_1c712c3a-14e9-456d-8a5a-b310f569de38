/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.domain

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class FleetTest {

    @Test
    fun `should build pre usage fleet`() {
        val preUsageFleet =
            Fleet.determineFleet(
                fleetId = "i don´t care",
                primStatus = "YF10",
                firstName = "I",
                lastName = "don´t",
                employeeNumber = "care!",
            )

        assertThat(preUsageFleet).isEqualTo(Fleet.preUsageFleet)
    }

    @Test
    fun `should build post usage fleet`() {
        val postUsageFleet =
            Fleet.determineFleet(
                fleetId = "i don´t care at all",
                primStatus = "YF35",
                firstName = "I",
                lastName = "don´t",
                employeeNumber = "care!",
            )

        assertThat(postUsageFleet).isEqualTo(Fleet.postUsageFleet)
    }

    @Test
    fun `should build pool fleet`() {
        val postUsageFleet =
            Fleet.determineFleet(
                fleetId = "L9.0",
                primStatus = "YF20",
                firstName = "Fleet",
                lastName = "Manager",
                employeeNumber = "123400",
            )

        assertThat(postUsageFleet)
            .usingRecursiveComparison()
            .isEqualTo(Fleet(id = "Manager_Fleet_123400", description = "Manager, Fleet (123400)"))
    }

    @Test
    fun `should build leasing fleet`() {
        val postUsageFleet =
            Fleet.determineFleet(
                fleetId = "L1.4",
                primStatus = "YF20",
                firstName = "I",
                lastName = "don´t",
                employeeNumber = "care!",
            )

        assertThat(postUsageFleet).usingRecursiveComparison().isEqualTo(Fleet(id = "L1.4", description = "Rentner"))
    }
}
