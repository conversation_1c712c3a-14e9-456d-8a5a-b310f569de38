package com.emh.damagemanagement.excelvehiclemigration.domain

import com.emh.damagemanagement.shared.createRandomString
import java.util.stream.Stream
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class VehicleResponsiblePersonTest {

    @ParameterizedTest
    @MethodSource("employeeNumberProvider")
    fun `should padStart employeeNumber with Zeros`(employeeNumberAsString: String) {
        val employeeNumber = EmployeeNumber(employeeNumberAsString)
        assertThat(employeeNumber.value).isEqualTo(employeeNumberAsString.padStart(8, '0'))
    }

    @ParameterizedTest
    @MethodSource("employeeNumberProvider")
    fun `should check if employee number has exactly 8 characters`(employeeNumberAsString: String) {
        val employeeNumber = EmployeeNumber(employeeNumberAsString)
        assertThat(employeeNumber.value).hasSize(8)
    }

    @Test
    fun `should fail if employee number has more than 8 characters`() {
        assertThatExceptionOfType(IllegalArgumentException::class.java)
            .isThrownBy {
                VehicleResponsiblePerson(
                    email = "email",
                    employeeNumber = EmployeeNumber("123456789"),
                    lastName = "last",
                    firstName = "first",
                )
            }
            .withMessage("Employee number may not exceed 8 characters.")
    }

    @Test
    fun `person with preUsage fleet should not be considered to be a fleet manager`() {
        val vehicleResponsiblePeron =
            VehicleResponsiblePerson(
                employeeNumber = EmployeeNumber("00123456"),
                firstName = "Not",
                lastName = "Manager",
                email = "<EMAIL>",
                fleetId = Fleet.preUsageFleet.id,
            )

        assertThat(vehicleResponsiblePeron.isAlsoFleetManager).isFalse()
    }

    @Test
    fun `person with postUsage fleet should not be considered to be a fleet manager`() {
        val vehicleResponsiblePeron =
            VehicleResponsiblePerson(
                employeeNumber = EmployeeNumber("00123456"),
                firstName = "Not",
                lastName = "Manager",
                email = "<EMAIL>",
                fleetId = Fleet.postUsageFleet.id,
            )

        assertThat(vehicleResponsiblePeron.isAlsoFleetManager).isFalse()
    }

    companion object {
        @JvmStatic
        fun employeeNumberProvider(): Stream<Arguments> =
            (0..10).map { Arguments.of(createRandomString(Random.nextInt(1, 9))) }.stream()
    }
}
