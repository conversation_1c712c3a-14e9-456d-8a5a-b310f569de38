/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.excelvehiclemigration.domain

import com.emh.damagemanagement.excelvehiclemigration.VehicleBuilder
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class VehicleTest {

    @Test
    fun `should correctly determine pool status for leasing vehicle`() {
        val vehicle = VehicleBuilder().fleetId("L9.1").build()

        assertThat(vehicle.isPoolVehicle).isFalse()
        assertThat(vehicle.responsiblePerson).isNotNull
    }

    @Test
    fun `should correctly determine pool status for pool vehicle`() {
        val vehicle = VehicleBuilder().fleetId("L9.0").build()

        assertThat(vehicle.isPoolVehicle).isTrue()
        assertThat(vehicle.responsiblePerson).isNull()
    }

    @Test
    fun `should correctly determine pool status for pre usage vehicle`() {
        val vehicle = VehicleBuilder().fleetId("preUsage").build()

        assertThat(vehicle.isPoolVehicle).isTrue()
        assertThat(vehicle.responsiblePerson).isNull()
    }

    @Test
    fun `should correctly determine pool status for post usage vehicle`() {
        val vehicle = VehicleBuilder().fleetId("postUsage").build()

        assertThat(vehicle.isPoolVehicle).isTrue()
        assertThat(vehicle.responsiblePerson).isNull()
    }
}
