{"local": {"host": "eu-1.dev.api.porsche.io", "environment": "dev", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "oauth_m2m_api_client_id": "575b41a7-052a-4265-a6a3-4f8cf64257bf", "api_scope": "de2c475e-e393-425d-a89e-a1f394c90ae1"}, "ibm-gw-dev": {"host": "eu-1.dev.api.porsche.io", "environment": "dev", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "oauth_m2m_api_client_id": "575b41a7-052a-4265-a6a3-4f8cf64257bf", "ibm_api_gateway_client_id": "f848ab1be68a3f55701fac0bc63cb61d", "api_scope": "de2c475e-e393-425d-a89e-a1f394c90ae1"}, "ibm-gw-test": {"host": "eu-1.test.api.porsche.io", "environment": "test", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "oauth_m2m_api_client_id": "575b41a7-052a-4265-a6a3-4f8cf64257bf", "ibm_api_gateway_client_id": "0203a3fac549e7de0d8bd009480119be", "api_scope": "de2c475e-e393-425d-a89e-a1f394c90ae1"}, "ibm-gw-prod": {"host": "eu-1.api.porsche.io", "environment": "prod", "azure_tenant_id": "56564e0f-83d3-4b52-92e8-a6bb9ea36564", "oauth_m2m_api_client_id": "bd818647-a548-403a-b7c1-188e056c5b38", "ibm_api_gateway_client_id": "01ab637e7c30c33c3f292ed978366eed", "api_scope": "51b2b817-bd6b-4c1a-9067-aab2356b602d"}}