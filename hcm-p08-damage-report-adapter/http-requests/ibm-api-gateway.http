### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
&client_secret={{oauth_m2m_api_client_secret}}
&grant_type=client_credentials
&scope={{api_scope}}/.default

> {%
  client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Get damage reports by a date range
GET https://{{host}}/porsche-group/{{environment}}/damage/damageReports?startDate=2025-07-01&endDate=2025-07-11
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{ibm_api_gateway_client_secret}}
accept: application/json
Content-Type: application/json

