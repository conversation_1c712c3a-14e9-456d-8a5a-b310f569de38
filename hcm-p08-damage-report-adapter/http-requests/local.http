### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
&client_secret={{oauth_m2m_api_client_secret}}
&grant_type=client_credentials
&scope={{api_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Get damage reports by a date range
GET /api/damage/damageReports?startDate=2023-06-11&endDate=2023-07-11
Authorization: Bearer {{auth_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json