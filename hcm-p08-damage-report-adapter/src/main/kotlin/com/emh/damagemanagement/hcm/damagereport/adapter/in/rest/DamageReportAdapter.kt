/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.hcm.damagereport.adapter.`in`.rest

import com.emh.damagemanagement.damagefile.application.port.FindDamageFilesWithDeductibleUpdateUseCase
import com.emh.damagemanagement.generated.hcm.damagereport.adapter.rest.DamageService
import com.emh.damagemanagement.generated.hcm.damagereport.adapter.rest.model.DamageReportsDto
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import org.springframework.stereotype.Component

@Component
class DamageReportAdapter(
    private val findDamageFilesWithDeductibleUpdateUseCase: FindDamageFilesWithDeductibleUpdateUseCase
) : DamageService {
    override fun damageDamageReportsGet(startDate: LocalDate, endDate: LocalDate): DamageReportsDto {
        val damageFiles =
            findDamageFilesWithDeductibleUpdateUseCase.findAllNonExternalDamageFilesWithDeductibleUpdateBetween(
                from = OffsetDateTime.of(startDate, LocalTime.MIN, ZoneOffset.UTC),
                to = OffsetDateTime.of(endDate, LocalTime.MAX, ZoneOffset.UTC),
            )
        return DamageReportsDto(damageFiles.map { it.toDamageReportDto() })
    }
}
