/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.hcm.damagereport.adapter.`in`.rest

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.generated.hcm.damagereport.adapter.rest.model.DamageReportDto

fun DamageFile.toDamageReportDto() =
    DamageReportDto(
        unfallkennzeichen = this.key.value,
        employeeToBeCharged = this.employeeToBeCharged.value,
        deductible = requireNotNull(this.deductible).value.toDouble(),
        createdAt = this.damageDate,
        zuordnungsnummer = this.vehicle.licensePlate,
    )
