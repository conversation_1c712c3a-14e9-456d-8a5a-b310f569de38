package com.emh.damagemanagement.hcm.damagereport

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@SpringBootApplication(
    scanBasePackages =
        [
            "com.emh.damagemanagement.damagefile.*",
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.hcm.damagereport.*",
            "com.emh.damagemanagement.generated.hcm.damagereport",
        ]
)
@ConfigurationPropertiesScan(
    "com.emh.damagemanagement.damagefile.*",
    "com.emh.damagemanagement.shared.*",
    "com.emh.damagemanagement.hcm.damagereport.*",
    "com.emh.damagemanagement.generated.hcm.damagereport",
)
@EntityScan("com.emh.damagemanagement.damagefile.*")
@EnableJpaRepositories("com.emh.damagemanagement.damagefile.*")
class TestDamageReportApplication {

    fun main(args: Array<String>) {
        runApplication<TestDamageReportApplication>(*args)
    }
}
