package com.emh.damagemanagement.hcm.damagereport

import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.generated.hcm.damagereport.adapter.rest.model.DamageReportDto
import com.emh.damagemanagement.shared.createRandomString
import java.time.OffsetDateTime
import kotlin.random.Random

class DamageReportDtoBuilder {
    private var unfallkennzeichen: String = validDamageFileKey.value
    private var employeeToBeCharged: String = createRandomString(Random.nextInt(4, 23))
    private var zuordnungsnummer: String = createRandomString(Random.nextInt(4, 23))
    private var deductible: Double = Random.nextDouble()
    private var createdAt: OffsetDateTime = OffsetDateTime.now()

    fun unfallkennzeichen(unfallkennzeichen: String) = apply { this.unfallkennzeichen = unfallkennzeichen }

    fun zuordnungsnummer(zuordnungsnummer: String) = apply { this.zuordnungsnummer = zuordnungsnummer }

    fun employeeToBeCharged(employeeToBeCharged: String) = apply { this.employeeToBeCharged = employeeToBeCharged }

    fun deductible(deductible: Double) = apply { this.deductible = deductible }

    fun createdAt(createdAt: OffsetDateTime) = apply { this.createdAt = createdAt }

    fun build(): DamageReportDto {
        return DamageReportDto(
            unfallkennzeichen = unfallkennzeichen,
            employeeToBeCharged = employeeToBeCharged,
            deductible = deductible,
            createdAt = createdAt,
            zuordnungsnummer = zuordnungsnummer,
        )
    }

    companion object {

        fun buildMultiple(count: Int): Set<DamageReportDto> =
            (1..count).map { DamageReportDtoBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
