package com.emh.damagemanagement.hcm.damagereport.adapter.`in`.rest

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.domain.service.DamageFileCreateService
import com.emh.damagemanagement.damagefile.validDamageFileKey
import com.emh.damagemanagement.hcm.damagereport.IntegrationTest
import jakarta.persistence.EntityManager
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class DamageReportAdapterTest {

    @Autowired private lateinit var damageReportAdapter: DamageReportAdapter

    @Autowired private lateinit var damageFileCreateService: DamageFileCreateService

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should return non-external damage reports by update timestamp`() {
        (1..10)
            .map { DamageFileNewOrUpdateBuilder().claimTowardsEmployee(null).deductible(null).build() }
            .map { damageFileCreateService.createDamageFile(it, validDamageFileKey) }
        (1..10)
            .map { DamageFileNewOrUpdateBuilder().isExternal(true).build() }
            .map { damageFileCreateService.createDamageFile(it, validDamageFileKey) }
        val damageFilesNew =
            DamageFileNewOrUpdateBuilder.buildMultiple(10).map {
                damageFileCreateService.createDamageFile(it, validDamageFileKey)
            }
        entityManager.flush()

        val damageReportDto =
            damageReportAdapter.damageDamageReportsGet(
                startDate = LocalDate.now().minusDays(1),
                endDate = LocalDate.now().plusDays(1),
            )

        assertThat(damageReportDto.damageReports?.map { it.unfallkennzeichen })
            .containsExactlyElementsOf(damageFilesNew.sortedBy { it.deductibleUpdated }.map { it.key.value })
    }
}
