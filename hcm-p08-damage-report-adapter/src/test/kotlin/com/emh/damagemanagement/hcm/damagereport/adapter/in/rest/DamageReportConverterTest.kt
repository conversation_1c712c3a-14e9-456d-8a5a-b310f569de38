package com.emh.damagemanagement.hcm.damagereport.adapter.`in`.rest

import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.generated.hcm.damagereport.adapter.rest.model.DamageReportDto
import java.time.temporal.ChronoUnit
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.within
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class DamageReportConverterTest {

    @ParameterizedTest
    @MethodSource("damageFileProvider")
    fun `should convert DamageFile to DamageReportDto`(damageFile: DamageFile) {
        val damageReportDto = damageFile.toDamageReportDto()

        assertThat(damageReportDto).satisfies(damageFileRequirements(damageFile))
    }

    companion object {
        fun damageFileRequirements(damageReport: DamageFile) =
            Consumer<DamageReportDto> { damageReportDto: DamageReportDto ->
                assertThat(damageReportDto.createdAt).isCloseTo(damageReport.damageDate, within(1, ChronoUnit.MILLIS))
                assertThat(damageReportDto.deductible).isEqualTo(damageReport.deductible?.value?.toDouble())
                assertThat(damageReportDto.unfallkennzeichen).isEqualTo(damageReport.key.value)
                assertThat(damageReportDto.employeeToBeCharged).isEqualTo(damageReport.employeeToBeCharged.value)
                assertThat(damageReportDto.zuordnungsnummer).isEqualTo(damageReport.vehicle.licensePlate)
            }

        @JvmStatic
        fun damageFileProvider(): Stream<Arguments> =
            DamageFileBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
