package com.emh.damagemanagement.hcm.damagereport.adapter.`in`.rest

import com.emh.damagemanagement.generated.hcm.damagereport.adapter.rest.model.DamageReportsDto
import com.emh.damagemanagement.hcm.damagereport.DamageReportDtoBuilder
import com.emh.damagemanagement.hcm.damagereport.WebServiceTest
import com.emh.damagemanagement.hcm.damagereport.adapter.`in`.rest.DamageReportResourceTest.TestConfiguration
import com.emh.damagemanagement.shared.converter.JsonConverter
import java.nio.charset.StandardCharsets
import java.time.LocalDate
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.mock
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.http.MediaType
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers

@WebServiceTest
@ContextConfiguration(classes = [TestConfiguration::class])
class DamageReportResourceTest {

    @Autowired private lateinit var damageReportAdapter: DamageReportAdapter

    @Autowired @Qualifier("mockMvc") private lateinit var mockMvc: MockMvc

    @Autowired private lateinit var jsonConverter: JsonConverter

    @Test
    fun `should filter damage reports`() {
        val damageReportsDtos = DamageReportsDto(DamageReportDtoBuilder.buildMultiple(10).toList())
        whenever(damageReportAdapter.damageDamageReportsGet(startDate = any(), endDate = any()))
            .thenReturn(damageReportsDtos)

        val response =
            mockMvc
                .perform(
                    MockMvcRequestBuilders.get(
                            "/damage/damageReports?startDate={startDate}&endDate={endDate}",
                            LocalDate.now().minusWeeks(1).toString(),
                            LocalDate.now().toString(),
                        )
                        .content(jsonConverter.objectMapper.writeValueAsString(damageReportsDtos))
                        .contentType(MediaType.APPLICATION_JSON)
                )
                .andExpect(MockMvcResultMatchers.status().isOk())
                .andReturn()
                .response

        val damageReportsFromEndpoint =
            jsonConverter.objectMapper.readValue(
                response.getContentAsString(StandardCharsets.UTF_8),
                DamageReportsDto::class.java,
            )

        assertThat(damageReportsFromEndpoint.damageReports)
            .usingRecursiveFieldByFieldElementComparatorIgnoringFields("createdAt")
            .containsExactlyElementsOf(damageReportsDtos.damageReports)
    }

    internal class TestConfiguration {

        @Bean @Primary fun damageReportAdapter(): DamageReportAdapter = mock()
    }
}
