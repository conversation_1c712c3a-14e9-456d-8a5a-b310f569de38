import com.diffplug.spotless.LineEnding
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jpa)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

evaluationDependsOn(":shared")
val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    api(libs.api.dms)

    implementation(project(":shared"))
    implementation(project(":legal-hold"))
    implementation(libs.bundles.base)

    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(libs.bundles.tests)
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
        targetExclude("**/generated/**")
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

val legalHoldGeneratedSourcesPath = "src/generated/"
sourceSets {
    main { kotlin.srcDir("$legalHoldGeneratedSourcesPath/kotlin") }
    test { kotlin { kotlin.srcDir(sharedTestSrcSetsOutput) } }
    test { resources { resources.srcDir(project(":shared").sourceSets.test.get().resources) } }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }

tasks.withType<KotlinCompile> { dependsOn("setupGeneratedSources") }

val apiFolder: String by rootProject.extra
tasks.register<GenerateTask>("generateLegalHoldServerApi") {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin-spring")
    inputSpec.set("$projectDir/$apiFolder/legal-hold.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.emh.damagemanagement.generated.legal.hold.adapter.rest")
    modelPackage.set("com.emh.damagemanagement.generated.legal.hold.adapter.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
        mapOf(
            "serviceInterface" to true,
            "serviceImplementation" to false,
            "exceptionHandler" to false,
            "apiSuffix" to "",
            "useBeanValidation" to false,
            "serializationLibrary" to "jackson",
            "annotationLibrary" to "none",
            "documentationProvider" to "none",
            "useSwaggerUI" to false
        )
    )
    dependsOn("unpackApi")
}

tasks.register<Copy>("setupGeneratedSources") {
    doFirst { delete(legalHoldGeneratedSourcesPath) }
    from("generated/src/main") {
        exclude("*/org")
        exclude("resources")
        exclude("**/ApiUtil*")
    }
    into(legalHoldGeneratedSourcesPath)
    includeEmptyDirs = false
    doLast { delete("$projectDir/generated/") }
    dependsOn("generateLegalHoldServerApi")
}
