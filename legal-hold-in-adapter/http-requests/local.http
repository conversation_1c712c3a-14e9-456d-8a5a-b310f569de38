### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
&client_secret={{oauth_m2m_api_client_secret}}
&grant_type=client_credentials
&scope={{api_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Apply legal hold for a damage file
POST /api/legal-hold/damage-files
Authorization: Bearer {{auth_token}}
Host: localhost:8081
accept: application/json
Content-Type: application/json

{
  "issuer": "someone",
  "reference": "kittycase#1",
  "damageFileKeys": [
    "139f8a4c0da444d48702c25793a8dd07"
  ]
}

### Release legal hold for a legalHoldKey
PUT /api/legal-hold/damage-files/20240620_kittycase1/release
Authorization: Bearer {{auth_token}}
Host: localhost:8081
accept: application/json
Content-Type: application/json

{
  "issuer": "someone"
}