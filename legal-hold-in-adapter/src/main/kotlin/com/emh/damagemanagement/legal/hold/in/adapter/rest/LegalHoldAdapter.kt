/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.`in`.adapter.rest

import com.emh.damagemanagement.generated.legal.hold.adapter.rest.LegalHoldService
import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ApplyLegalHoldDamageFilesDto
import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.CreatedDto
import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ReleaseLegalHoldDto
import com.emh.damagemanagement.legal.hold.application.port.ApplyLegalHoldUseCase
import com.emh.damagemanagement.legal.hold.application.port.ReleaseLegalHoldUseCase
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import org.springframework.stereotype.Component

@Component
class LegalHoldAdapter(
    private val applyLegalHoldUseCase: ApplyLegalHoldUseCase,
    private val releaseLegalHoldUseCase: ReleaseLegalHoldUseCase,
) : LegalHoldService {
    override fun applyLegalHold(applyLegalHoldDamageFilesDto: ApplyLegalHoldDamageFilesDto): CreatedDto {
        return applyLegalHoldUseCase
            .applyLegalHold(legalHoldNew = applyLegalHoldDamageFilesDto.toLegalHoldNew())
            .toCreatedDto()
    }

    override fun releaseLegalHold(key: String, releaseLegalHoldDto: ReleaseLegalHoldDto) {
        releaseLegalHoldUseCase.releaseLegalHold(
            legalHoldKey = LegalHoldKey(key),
            releasedBy = releaseLegalHoldDto.issuer,
        )
    }
}
