/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.`in`.adapter.rest

import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ApplyLegalHoldDamageFilesDto
import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.CreatedDto
import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldNew

fun ApplyLegalHoldDamageFilesDto.toLegalHoldNew() =
    LegalHoldNew(
        issuer = this.issuer,
        domainEntityReferences = this.damageFileKeys.map { DomainEntityReference(it) }.toSet(),
        externalReference = this.reference,
    )

fun LegalHoldKey.toCreatedDto() = CreatedDto(key = this.value)
