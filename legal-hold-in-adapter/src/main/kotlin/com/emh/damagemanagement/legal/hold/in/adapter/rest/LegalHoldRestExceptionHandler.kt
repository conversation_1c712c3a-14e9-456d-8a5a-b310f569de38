/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.`in`.adapter.rest

import com.emh.damagemanagement.generated.shared.rest.model.ErrorDto
import com.emh.damagemanagement.generated.shared.rest.model.ErrorTypeDto
import com.emh.damagemanagement.legal.hold.domain.LegalHoldAlreadyExistsException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotAppliedException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotFoundException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotReleasedException
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

@ControllerAdvice
class LegalHoldRestExceptionHandler {

    @ExceptionHandler(LegalHoldNotFoundException::class)
    private fun handleLegalHoldNotFoundException(exception: LegalHoldNotFoundException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message =
                    exception.message ?: "LegalHold with key [${exception.legalHoldKey.value}] could not be found.",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.NOT_FOUND,
        )

    @ExceptionHandler(LegalHoldAlreadyExistsException::class)
    private fun handleLegalHoldAlreadyExistsException(
        exception: LegalHoldAlreadyExistsException
    ): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message =
                    exception.message
                        ?: "LegalHold with key [${exception.legalHoldKey.value}] and external reference [${exception.externalReference}] already exists.",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.CONFLICT,
        )

    @ExceptionHandler(LegalHoldNotAppliedException::class)
    private fun handleLegalHoldNotAppliedException(exception: LegalHoldNotAppliedException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(message = exception.message ?: "LegalHold could not be applied.", type = ErrorTypeDto.BUSINESS),
            HttpStatus.CONFLICT,
        )

    @ExceptionHandler(LegalHoldNotReleasedException::class)
    private fun handleLegalHoldNotReleasedException(
        exception: LegalHoldNotReleasedException
    ): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(message = exception.message ?: "LegalHold could not be released.", type = ErrorTypeDto.BUSINESS),
            HttpStatus.CONFLICT,
        )
}
