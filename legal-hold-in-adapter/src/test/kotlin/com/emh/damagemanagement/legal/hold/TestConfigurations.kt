package com.emh.damagemanagement.legal.hold

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@SpringBootApplication(scanBasePackages = ["com.emh.damagemanagement.*"])
@ConfigurationPropertiesScan("com.emh.damagemanagement.*")
@EntityScan("com.emh.damagemanagement.*")
@EnableJpaRepositories("com.emh.damagemanagement.*")
class TestLegalHoldAdapterApplication {

    fun main(args: Array<String>) {
        runApplication<TestLegalHoldAdapterApplication>(*args)
    }
}
