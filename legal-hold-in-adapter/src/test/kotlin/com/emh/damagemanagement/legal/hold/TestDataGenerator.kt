package com.emh.damagemanagement.legal.hold

import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ApplyLegalHoldDamageFilesDto
import com.emh.damagemanagement.shared.createRandomString
import kotlin.random.Random

private val ANY_NON_WORD_CHARACTER = Regex("\\W")
private val validDamageKey
    get() = createRandomString(Random.nextInt(10, 1000)).replace(ANY_NON_WORD_CHARACTER, "")

class ApplyLegalHoldDamageFilesDtoBuilder {
    private var issuer: String = createRandomString(Random.nextInt(4, 23))
    private var reference: String = createRandomString(Random.nextInt(4, 23))
    private var damageFileKeys: List<String> = (0..10).map { validDamageKey }

    fun issuer(issuer: String) = apply { this.issuer = issuer }

    fun damageFileKeys(damageFileKeys: Collection<String>) = apply { this.damageFileKeys = damageFileKeys.toList() }

    fun build(): ApplyLegalHoldDamageFilesDto {
        return ApplyLegalHoldDamageFilesDto(issuer = issuer, damageFileKeys = damageFileKeys, reference = reference)
    }

    companion object {

        fun buildMultiple(count: Int): Set<ApplyLegalHoldDamageFilesDto> =
            (1..count).map { ApplyLegalHoldDamageFilesDtoBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun single(): ApplyLegalHoldDamageFilesDto = ApplyLegalHoldDamageFilesDtoBuilder().build()
    }
}
