package com.emh.damagemanagement.legal.hold.architecture

import com.emh.damagemanagement.shared.architecture.DEFAULT_PACKAGE
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses

@AnalyzeClasses(packages = [DEFAULT_PACKAGE], importOptions = [ImportOption.DoNotIncludeTests::class])
class ArchitectureRulesTest : com.emh.damagemanagement.shared.architecture.ArchitectureRulesTest()

@AnalyzeClasses(packages = [DEFAULT_PACKAGE], importOptions = [ImportOption.DoNotIncludeTests::class])
class ApplicationRulesTest : com.emh.damagemanagement.shared.architecture.ApplicationRulesTest()
