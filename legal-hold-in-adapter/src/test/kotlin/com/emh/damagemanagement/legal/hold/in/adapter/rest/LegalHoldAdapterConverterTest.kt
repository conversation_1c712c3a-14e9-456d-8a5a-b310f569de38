/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.`in`.adapter.rest

import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ApplyLegalHoldDamageFilesDto
import com.emh.damagemanagement.legal.hold.ApplyLegalHoldDamageFilesDtoBuilder
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldNew
import com.emh.damagemanagement.shared.createRandomString
import java.util.function.Consumer
import java.util.stream.Stream
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class LegalHoldAdapterConverterTest {
    @ParameterizedTest
    @MethodSource("applyLegalHoldDamageFilesDtoProvider")
    fun `should convert ApplyLegalHoldDamageFilesDto to LegalHoldNew`(
        applyLegalHoldDamageFilesDto: ApplyLegalHoldDamageFilesDto
    ) {
        val legalHoldNew = applyLegalHoldDamageFilesDto.toLegalHoldNew()

        assertThat(legalHoldNew).satisfies(applyLegalHoldDamageFilesDtoRequirements(applyLegalHoldDamageFilesDto))
    }

    @ParameterizedTest
    @MethodSource("legalHoldKeyProvider")
    fun `should convert LegalHoldKey to CreatedDto`(legalHoldKey: LegalHoldKey) {
        val createdDto = legalHoldKey.toCreatedDto()

        assertThat(createdDto.key).isEqualTo(legalHoldKey.value)
    }

    companion object {
        fun applyLegalHoldDamageFilesDtoRequirements(applyLegalHoldDamageFilesDto: ApplyLegalHoldDamageFilesDto) =
            Consumer<LegalHoldNew> { legalHoldNew: LegalHoldNew ->
                assertThat(legalHoldNew.issuer).isEqualTo(applyLegalHoldDamageFilesDto.issuer)
                assertThat(legalHoldNew.domainEntityReferences)
                    .extracting("entityIdentifier")
                    .containsExactlyInAnyOrderElementsOf(applyLegalHoldDamageFilesDto.damageFileKeys)
                assertThat(legalHoldNew.externalReference).isEqualTo(applyLegalHoldDamageFilesDto.reference)
            }

        @JvmStatic
        fun applyLegalHoldDamageFilesDtoProvider(): Stream<Arguments> =
            ApplyLegalHoldDamageFilesDtoBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()

        @JvmStatic
        fun legalHoldKeyProvider(): Stream<Arguments> =
            (0..10).map { LegalHoldKey(createRandomString(Random.nextInt(4, 23))) }.map { Arguments.of(it) }.stream()
    }
}
