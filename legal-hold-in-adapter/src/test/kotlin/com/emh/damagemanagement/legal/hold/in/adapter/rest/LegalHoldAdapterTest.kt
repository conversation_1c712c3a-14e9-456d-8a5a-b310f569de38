/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.`in`.adapter.rest

import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ReleaseLegalHoldDto
import com.emh.damagemanagement.legal.hold.ApplyLegalHoldDamageFilesDtoBuilder
import com.emh.damagemanagement.legal.hold.application.port.ApplyLegalHoldUseCase
import com.emh.damagemanagement.legal.hold.application.port.ReleaseLegalHoldUseCase
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldNew
import com.emh.damagemanagement.legal.hold.`in`.adapter.rest.LegalHoldAdapterConverterTest.Companion.applyLegalHoldDamageFilesDtoRequirements
import com.emh.damagemanagement.shared.createRandomString
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class LegalHoldAdapterTest {

    private val applyLegalHoldUseCase: ApplyLegalHoldUseCase = mock()
    private val releaseLegalHoldUseCase: ReleaseLegalHoldUseCase = mock()

    private val legalHoldAdapter: LegalHoldAdapter =
        LegalHoldAdapter(
            applyLegalHoldUseCase = applyLegalHoldUseCase,
            releaseLegalHoldUseCase = releaseLegalHoldUseCase,
        )

    @Test
    fun `should call ApplyLegalHoldUseCase`() {
        val applyLegalHoldPayload = ApplyLegalHoldDamageFilesDtoBuilder.single()
        whenever(applyLegalHoldUseCase.applyLegalHold(any())).thenReturn(LegalHoldKey(createRandomString(23)))
        val legalHoldNewArgumentCaptor = argumentCaptor<LegalHoldNew>()

        legalHoldAdapter.applyLegalHold(applyLegalHoldPayload)

        verify(applyLegalHoldUseCase).applyLegalHold(legalHoldNewArgumentCaptor.capture())
        assertThat(legalHoldNewArgumentCaptor.firstValue)
            .satisfies(applyLegalHoldDamageFilesDtoRequirements(applyLegalHoldPayload))
    }

    @Test
    fun `should call ReleaseLegalHoldUseCase`() {
        val legalHoldKey = createRandomString(23)
        val releasedBy = createRandomString(23)

        legalHoldAdapter.releaseLegalHold(key = legalHoldKey, releaseLegalHoldDto = ReleaseLegalHoldDto(releasedBy))

        verify(releaseLegalHoldUseCase)
            .releaseLegalHold(legalHoldKey = LegalHoldKey(legalHoldKey), releasedBy = releasedBy)
    }
}
