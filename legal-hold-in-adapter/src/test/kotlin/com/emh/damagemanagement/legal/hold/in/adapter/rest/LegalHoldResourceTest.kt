/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.`in`.adapter.rest

import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ApplyLegalHoldDamageFilesDto
import com.emh.damagemanagement.generated.legal.hold.adapter.rest.model.ReleaseLegalHoldDto
import com.emh.damagemanagement.legal.hold.ApplyLegalHoldDamageFilesDtoBuilder
import com.emh.damagemanagement.legal.hold.WebServiceTest
import com.emh.damagemanagement.shared.converter.JsonConverter
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.http.MediaType
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers
import org.springframework.test.web.servlet.result.isEqualTo

@WebServiceTest
@ContextConfiguration(classes = [LegalHoldResourceTest.TestConfiguration::class])
class LegalHoldResourceTest {

    @Autowired private lateinit var legalHoldAdapter: LegalHoldAdapter

    @Autowired @Qualifier("mockMvc") private lateinit var mockMvc: MockMvc

    @Autowired private lateinit var jsonConverter: JsonConverter

    @Test
    fun `should apply legal hold`() {
        val applyLegalHoldPayload = ApplyLegalHoldDamageFilesDtoBuilder.single()

        mockMvc
            .perform(
                MockMvcRequestBuilders.post("/legal-hold/damage-files")
                    .content(jsonConverter.objectMapper.writeValueAsString(applyLegalHoldPayload))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(MockMvcResultMatchers.status().isCreated())

        val applyLegalHoldDamageFilesDtoArgumentCaptor = argumentCaptor<ApplyLegalHoldDamageFilesDto>()
        verify(legalHoldAdapter).applyLegalHold(applyLegalHoldDamageFilesDtoArgumentCaptor.capture())
        assertThat(applyLegalHoldDamageFilesDtoArgumentCaptor.firstValue).isEqualTo(applyLegalHoldPayload)
    }

    @Test
    fun `should release legal hold`() {
        val legalHoldKey = "2024_04_04_legal_hold_key"
        val releasePayload = ReleaseLegalHoldDto(issuer = "releasedByMe")
        mockMvc
            .perform(
                MockMvcRequestBuilders.put("/legal-hold/damage-files/{key}/release", legalHoldKey)
                    .content(jsonConverter.objectMapper.writeValueAsString(releasePayload))
                    .contentType(MediaType.APPLICATION_JSON)
            )
            .andExpect(MockMvcResultMatchers.status().isOk())

        val releaseLegalHoldKeyArgumentCaptor = argumentCaptor<String>()
        val releaseLegalHoldPayloadArgumentCaptor = argumentCaptor<ReleaseLegalHoldDto>()
        verify(legalHoldAdapter)
            .releaseLegalHold(
                key = releaseLegalHoldKeyArgumentCaptor.capture(),
                releaseLegalHoldDto = releaseLegalHoldPayloadArgumentCaptor.capture(),
            )
        assertThat(releaseLegalHoldKeyArgumentCaptor.firstValue).isEqualTo(legalHoldKey)
        assertThat(releaseLegalHoldPayloadArgumentCaptor.firstValue).isEqualTo(releasePayload)
    }

    internal class TestConfiguration {

        @Bean @Primary fun legalHoldAdapter(): LegalHoldAdapter = mock()
    }
}
