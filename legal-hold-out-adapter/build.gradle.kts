import com.diffplug.spotless.LineEnding

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jpa)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

evaluationDependsOn(":shared")
val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    api(libs.api.dms)

    implementation(project(":shared"))
    implementation(project(":legal-hold"))
    implementation(libs.s3.client)
    implementation(libs.bundles.base)

    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(libs.bundles.tests)
    testImplementation(libs.bundles.tests.aws)
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

sourceSets {
    test { kotlin { kotlin.srcDir(sharedTestSrcSetsOutput) } }
    test { resources { resources.srcDir(project(":shared").sourceSets.test.get().resources) } }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }
