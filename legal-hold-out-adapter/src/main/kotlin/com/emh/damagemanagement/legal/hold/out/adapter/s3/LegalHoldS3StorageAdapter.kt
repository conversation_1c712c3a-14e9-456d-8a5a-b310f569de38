/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.out.adapter.s3

import com.emh.damagemanagement.legal.hold.out.application.port.KeysToFiles
import com.emh.damagemanagement.legal.hold.out.application.port.StorageException
import com.emh.damagemanagement.legal.hold.out.application.port.StorageOutPort
import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.S3StorageClientException
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Profile
import org.springframework.stereotype.Component

@Component
@Profile("!test")
class LegalHoldS3StorageAdapter(
    @Qualifier("legal-hold") private val storageProperties: StorageProperties,
    private val s3StorageClient: S3StorageClient,
) : StorageOutPort {
    init {
        val bucketExists = s3StorageClient.bucketExists(storageProperties.bucket)
        if (!bucketExists) {
            throw StorageException("Bucket [${storageProperties.bucket}] does not exist.")
        }
    }

    override fun uploadAndLockFiles(keysToFiles: KeysToFiles) =
        try {
            s3StorageClient.uploadAndLockFiles(keysToFiles = keysToFiles, bucket = storageProperties.bucket)
        } catch (exception: S3StorageClientException) {
            throw StorageException(message = exception.message, cause = exception)
        }

    override fun unlockAndMarkFilesDeleted(prefix: String) =
        try {
            s3StorageClient.unlockAndMarkFilesDeleted(prefix = prefix, bucket = storageProperties.bucket)
        } catch (exception: S3StorageClientException) {
            throw StorageException(message = exception.message, cause = exception)
        }
}

@ConfigurationProperties("storage.legal-hold")
@Qualifier("legal-hold")
data class StorageProperties(val bucket: String)
