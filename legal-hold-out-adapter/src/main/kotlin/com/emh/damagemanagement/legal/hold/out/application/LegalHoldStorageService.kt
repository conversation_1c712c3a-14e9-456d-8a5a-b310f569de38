/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.out.application

import com.emh.damagemanagement.legal.hold.application.LegalHoldDomainEntityEventListener
import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotAppliedException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotReleasedException
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldAppliedToDomainEntityEvent
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldReleasedFromDomainEntityEvent
import com.emh.damagemanagement.legal.hold.out.application.port.StorageException
import com.emh.damagemanagement.legal.hold.out.application.port.StorageOutPort
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class LegalHoldStorageService(private val storageOutPort: StorageOutPort) : LegalHoldDomainEntityEventListener {
    override fun handleLegalHoldReleasedFromDomainEntityEvent(event: LegalHoldReleasedFromDomainEntityEvent) {
        log.info(
            """LegalHoldReleasedFromDomainEntityEvent for legal hold with key [${event.legalHoldKey.value}] 
                and domain entity reference [${event.domainEntityReference.entityIdentifier}] received."""
                .trimMargin()
        )
        val prefix = buildPrefix(legalHoldKey = event.legalHoldKey, domainEntityReference = event.domainEntityReference)

        try {
            storageOutPort.unlockAndMarkFilesDeleted(prefix)
        } catch (exception: StorageException) {
            log.error(
                """Error while trying to unloack and delete legal hold documents for 
                    legal hold with key: [${event.legalHoldKey.value}] and 
                    domain entity with reference: [${event.domainEntityReference.entityIdentifier}]."""
                    .trimMargin(),
                exception,
            )
            throw LegalHoldNotReleasedException(
                legalHoldKey = event.legalHoldKey,
                domainEntityReference = event.domainEntityReference,
                cause = exception,
            )
        }
        log.info("Successfully deleted all files with prefix: [$prefix] from legal-hold bucket.")
    }

    override fun handleLegalHoldAppliedToDomainEntityEvent(event: LegalHoldAppliedToDomainEntityEvent) {
        log.info(
            """LegalHoldAppliedToDomainEntityEvent for legal hold with key [${event.legalHoldKey.value}] 
                and domain entity reference [${event.domainEntityReference.entityIdentifier}] received."""
                .trimMargin()
        )
        val keysToFiles =
            event.legalHoldDocuments.entries.associate { entry ->
                buildKey(
                    legalHoldKey = event.legalHoldKey,
                    domainEntityReference = event.domainEntityReference,
                    fileName = entry.key,
                ) to entry.value
            }

        try {
            storageOutPort.uploadAndLockFiles(keysToFiles)
        } catch (exception: StorageException) {
            log.error(
                """Error while trying to upload and lock legal hold documents for 
                    |legal hold with key: [${event.legalHoldKey.value}] and 
                    |domain entity with reference: [${event.domainEntityReference.entityIdentifier}]."""
                    .trimMargin(),
                exception,
            )
            throw LegalHoldNotAppliedException(
                legalHoldKey = event.legalHoldKey,
                domainEntityReference = event.domainEntityReference,
                cause = exception,
            )
        }
        log.info("Finished processing LegalHoldAppliedToDomainEntityEvent. All files have been successfully uploaded.")
    }

    private fun buildKey(legalHoldKey: LegalHoldKey, domainEntityReference: DomainEntityReference, fileName: String) =
        "${buildPrefix(legalHoldKey = legalHoldKey, domainEntityReference = domainEntityReference)}$fileName"

    private fun buildPrefix(legalHoldKey: LegalHoldKey, domainEntityReference: DomainEntityReference) =
        "${legalHoldKey.value}/${domainEntityReference.entityIdentifier}/"

    companion object {
        private val log = LoggerFactory.getLogger(LegalHoldStorageService::class.java)
    }
}
