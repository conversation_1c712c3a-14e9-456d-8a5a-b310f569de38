/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.out.application.port

import org.springframework.core.io.Resource

typealias KeysToFiles = Map<String, Resource>

interface StorageOutPort {

    /** Uploads given list of files with respective keys and applies legal hold lock */
    fun uploadAndLockFiles(keysToFiles: KeysToFiles)

    /** Removes legal hold lock from all files for given prefix and deletes them afterward */
    fun unlockAndMarkFilesDeleted(prefix: String)
}

class StorageException(message: String? = null, cause: Throwable? = null) :
    RuntimeException(message ?: cause?.message, cause)
