package com.emh.damagemanagement.legal.hold.out

import com.emh.damagemanagement.legal.hold.out.adapter.s3.LegalHoldS3StorageAdapter
import com.emh.damagemanagement.legal.hold.out.adapter.s3.StorageProperties
import com.emh.shared.s3.client.adapter.S3StorageClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.DependsOn
import org.springframework.data.jpa.repository.config.EnableJpaRepositories
import org.springframework.scheduling.annotation.EnableAsync
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CreateBucketRequest

@SpringBootApplication(scanBasePackages = ["com.emh.damagemanagement.*"])
@ConfigurationPropertiesScan("com.emh.damagemanagement.*")
@EnableAsync
@EntityScan("com.emh.damagemanagement.*")
@EnableJpaRepositories("com.emh.damagemanagement.*")
class TestLegalHoldOutAdapterApplication {

    fun main(args: Array<String>) {
        runApplication<TestLegalHoldOutAdapterApplication>(*args)
    }
}

@TestConfiguration
class TestS3SetupConfig(
    private val s3StorageClient: S3StorageClient,
    @Qualifier("legal-hold") private val storageProperties: StorageProperties,
) {

    @Bean
    @DependsOn("prepareLegalHoldBucket")
    fun storageOutPort(s3Client: S3Client) =
        LegalHoldS3StorageAdapter(storageProperties = storageProperties, s3StorageClient = s3StorageClient)

    /** Custom bean for tests, to ensure bucket creation before S3StorageAdapter init fires */
    @Bean
    fun prepareLegalHoldBucket(s3Client: S3Client, @Value("\${storage.legal-hold.bucket}") legalHoldBucket: String) =
        BucketInit(s3Client = s3Client, legalHoldBucket = legalHoldBucket)

    class BucketInit(s3Client: S3Client, legalHoldBucket: String) {
        init {
            val buckets = s3Client.listBuckets().buckets().map { it.name() }
            if (buckets.none { legalHoldBucket == it }) {
                s3Client.createBucket(
                    CreateBucketRequest.builder().bucket(legalHoldBucket).objectLockEnabledForBucket(true).build()
                )
            }
        }
    }
}
