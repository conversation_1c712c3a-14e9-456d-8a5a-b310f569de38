/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.out.application

import com.emh.damagemanagement.legal.hold.application.LegalHoldDomainEntityEventListener
import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotAppliedException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotReleasedException
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldAppliedToDomainEntityEvent
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldReleasedFromDomainEntityEvent
import com.emh.damagemanagement.legal.hold.out.IntegrationTest
import com.emh.damagemanagement.legal.hold.out.application.port.KeysToFiles
import com.emh.damagemanagement.legal.hold.out.application.port.StorageException
import com.emh.damagemanagement.legal.hold.out.application.port.StorageOutPort
import com.emh.damagemanagement.shared.createRandomString
import java.time.OffsetDateTime
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.core.io.ByteArrayResource
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@IntegrationTest
@ContextConfiguration(classes = [LegalHoldStorageServiceTest.TestConfiguration::class])
class LegalHoldStorageServiceTest {

    @Autowired private lateinit var applicationEventPublisher: ApplicationEventPublisher

    @MockitoSpyBean private lateinit var legalHoldDomainEntityEventListener: LegalHoldDomainEntityEventListener

    @Autowired private lateinit var storageOutPort: StorageOutPort

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(storageOutPort)
    }

    @Test
    fun `should receive LegalHoldReleasedFromDomainEntityEvent`() {
        val event =
            LegalHoldReleasedFromDomainEntityEvent(
                legalHoldKey = LegalHoldKey("anyKey"),
                domainEntityReference = DomainEntityReference("ref"),
                occurredOn = OffsetDateTime.now(),
            )
        applicationEventPublisher.publishEvent(event)

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(legalHoldDomainEntityEventListener)
                    .handleLegalHoldReleasedFromDomainEntityEvent(any<LegalHoldReleasedFromDomainEntityEvent>())
            }
    }

    @Test
    fun `should receive LegalHoldAppliedToDomainEntityEvent`() {
        val event =
            LegalHoldAppliedToDomainEntityEvent(
                legalHoldKey = LegalHoldKey("anyKey"),
                domainEntityReference = DomainEntityReference("ref"),
                occurredOn = OffsetDateTime.now(),
                legalHoldDocuments = emptyMap(),
            )
        applicationEventPublisher.publishEvent(event)

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(legalHoldDomainEntityEventListener)
                    .handleLegalHoldAppliedToDomainEntityEvent(any<LegalHoldAppliedToDomainEntityEvent>())
            }
    }

    @Test
    fun `should successfully process LegalHoldAppliedToDomainEntityEvent`() {
        val file = ByteArrayResource(createRandomString(10).toByteArray(Charsets.UTF_8))
        val event =
            LegalHoldAppliedToDomainEntityEvent(
                legalHoldKey = LegalHoldKey("anyKey"),
                domainEntityReference = DomainEntityReference("ref"),
                occurredOn = OffsetDateTime.now(),
                legalHoldDocuments = mapOf("i am a file.json" to file),
            )
        val uploadFilesArgumentCaptor = argumentCaptor<KeysToFiles>()

        legalHoldDomainEntityEventListener.handleLegalHoldAppliedToDomainEntityEvent(event)

        verify(storageOutPort).uploadAndLockFiles(uploadFilesArgumentCaptor.capture())
        assertThat(uploadFilesArgumentCaptor.firstValue).isEqualTo(mapOf("anyKey/ref/i am a file.json" to file))
    }

    @Test
    fun `should throw LegalHoldNotAppliedException in case of StorageException`() {
        val event =
            LegalHoldAppliedToDomainEntityEvent(
                legalHoldKey = LegalHoldKey("anyKey"),
                domainEntityReference = DomainEntityReference("ref"),
                occurredOn = OffsetDateTime.now(),
                legalHoldDocuments = emptyMap(),
            )
        whenever(storageOutPort.uploadAndLockFiles(any())).thenThrow(StorageException("danger"))

        assertThatExceptionOfType(LegalHoldNotAppliedException::class.java).isThrownBy {
            legalHoldDomainEntityEventListener.handleLegalHoldAppliedToDomainEntityEvent(event)
        }
    }

    @Test
    fun `should successfully process LegalHoldReleasedFromDomainEntityEvent`() {
        val event =
            LegalHoldReleasedFromDomainEntityEvent(
                legalHoldKey = LegalHoldKey("anyKey"),
                domainEntityReference = DomainEntityReference("ref"),
                occurredOn = OffsetDateTime.now(),
            )
        val prefixArgumentCaptor = argumentCaptor<String>()

        legalHoldDomainEntityEventListener.handleLegalHoldReleasedFromDomainEntityEvent(event)

        verify(storageOutPort).unlockAndMarkFilesDeleted(prefixArgumentCaptor.capture())
        assertThat(prefixArgumentCaptor.firstValue).isEqualTo("anyKey/ref/")
    }

    @Test
    fun `should throw LegalHoldNotReleasedException in case of StorageException`() {
        val event =
            LegalHoldReleasedFromDomainEntityEvent(
                legalHoldKey = LegalHoldKey("anyKey"),
                domainEntityReference = DomainEntityReference("ref"),
                occurredOn = OffsetDateTime.now(),
            )
        whenever(storageOutPort.unlockAndMarkFilesDeleted(any())).thenThrow(StorageException("danger!!111"))

        assertThatExceptionOfType(LegalHoldNotReleasedException::class.java).isThrownBy {
            legalHoldDomainEntityEventListener.handleLegalHoldReleasedFromDomainEntityEvent(event)
        }
    }

    internal class TestConfiguration {

        @Bean @Primary fun storageOutPort(): StorageOutPort = mock()
    }
}
