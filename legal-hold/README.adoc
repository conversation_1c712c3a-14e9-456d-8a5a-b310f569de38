:toc:
:numbered:
= Legal Hold

== Module description

This module provides legal hold domain functionality, as well as all necessary interface to enable legal hold for your domain module.

== How to

=== Setup Legal Hold module

This module comes with basic functionality for creating and deleting legal holds. See link:./src/main/kotlin/com/emh/damagemanagement/legal/hold/application/LegalHoldApplicationService.kt[LegalHoldApplicationService] as a starting point.

To function properly (at all) this module requires some DB scripts. As long as we do not offer it as a public dependency scripts are container in the shared-module (together with all the other change sets) see link:../shared/src/main/resources/db/changelog/changes/FPT1-339-legal-hold.yaml[legal-hold-changelog]

If those requirements are met you can either depend on this module as an incoming adapter for applying or relasing legal hold, or implement the necessary interface to <<21-setup-legal-hold-domain--1,enable legal hold on your domain entities>>.

=== Setup Legal Hold for your domain

. Add a dependency to this module
+
[,kotlin]
----
 implementation(project(":legal-hold")) or api(project(":legal-hold"))
----
+
or
+
[,kotlin]
----
 api(project(":legal-hold"))
----
+
use `api()` instead of `implementation()` if you get issues with missing supertypes in domain dependent modules.

. Implement link:./src/main/kotlin/com/emh/damagemanagement/legal/hold/domain/LegallyHoldable.kt[LegallyHoldable] interface on your domain aggregate</br>
 This is the interface to implement on your domain entity, the one that the legal hold will be applied to. You have to provide a set of `LegalHoldKeys` as well as the option to add and remove those
+
____
be aware, that you have to deal with persisting the legalHoldKeys by yourself
____
+
You also get a helper function `requireNoLegalHolds()`, that you can use to enforce legalHold checks on your mutating domain functions. It will throw a link:./src/main/kotlin/com/emh/damagemanagement/legal/hold/domain/LegalHoldEvent.kt[LegalHoldInPlaceException] that you can deal with, as you see fit.

. Implement link:./src/main/kotlin/com/emh/damagemanagement/legal/hold/domain/service/LegalHoldDomainEntityService.kt[LegalHoldDomainEntityService] +
Just provide a `@Component` that implements this interface and access to the `ApplicationEventPublisher`. The interface will take care of everything else
. Implement link:./src/main/kotlin/com/emh/damagemanagement/legal/hold/application/LegallyHoldableDomainEntityFinder.kt[LegallyHoldableDomainEntityFinder] +
You can use a small `@Component` with access to your repository. This is required to find the correct domain entites when apllying or removing legal holds.
. Implement link:./src/main/kotlin/com/emh/damagemanagement/legal/hold/application/LegalHoldEventListener.kt[LegalHoldEventListener] +
Use a `@Transactional` `@Component` and wire your typed `LegalHoldDomainEntityService` and `LegallyHoldableDomainEntityFinder`. The interface will then take care of event handling and calling the application service.

=== Setup Outbound Legal Hold Event Handler

If you want to consume and handle outbound events such as `LegalHoldReleasedFromDomainEntityEvent` or `LegalHoldReleasedFromDomainEntityEvent` you just have to implement the link:./src/main/kotlin/com/emh/damagemanagement/legal/hold/application/LegalHoldDomainEntityEventListener.kt[LegalHoldDomainEntityEventListener].

____
Be aware, that the EventListener is an asynchronous one, and you might have to deal with your transactions manually.
____
