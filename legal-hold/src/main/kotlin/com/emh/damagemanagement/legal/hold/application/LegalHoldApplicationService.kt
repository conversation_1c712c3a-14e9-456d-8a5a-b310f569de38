/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application

import com.emh.damagemanagement.legal.hold.application.port.ApplyLegalHoldUseCase
import com.emh.damagemanagement.legal.hold.application.port.ReleaseLegalHoldUseCase
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldCreateService
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldNew
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldReleaseService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional
class LegalHoldApplicationService(
    private val legalHoldCreateService: LegalHoldCreateService,
    private val legalHoldReleaseService: LegalHoldReleaseService,
    private val legalHoldFinder: LegalHoldFinder,
) : ApplyLegalHoldUseCase, ReleaseLegalHoldUseCase {

    override fun applyLegalHold(legalHoldNew: LegalHoldNew): LegalHoldKey {
        val legalHold = legalHoldCreateService.createLegalHold(legalHoldNew)
        return legalHold.key
    }

    override fun releaseLegalHold(legalHoldKey: LegalHoldKey, releasedBy: String) {
        val legalHold = legalHoldFinder.getLegalHold(legalHoldKey)
        legalHoldReleaseService.releaseLegalHold(legalHold = legalHold, releasedBy = releasedBy)
    }
}
