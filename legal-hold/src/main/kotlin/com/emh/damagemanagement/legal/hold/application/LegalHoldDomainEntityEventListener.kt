/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application

import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldAppliedToDomainEntityEvent
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldReleasedFromDomainEntityEvent
import org.springframework.context.event.EventListener

/** Event listener to be implemented */
interface LegalHoldDomainEntityEventListener {

    /**
     * Receive [LegalHoldReleasedFromDomainEntityEvent] and deal with them however you see fit.
     *
     * @throws LegalHoldNotReleasedException in case of error.
     */
    @EventListener fun handleLegalHoldReleasedFromDomainEntityEvent(event: LegalHoldReleasedFromDomainEntityEvent)

    /**
     * Receive [LegalHoldAppliedToDomainEntityEvent] and deal with them however you see fit.
     *
     * @throws LegalHoldNotAppliedException in case of error.
     */
    @EventListener fun handleLegalHoldAppliedToDomainEntityEvent(event: LegalHoldAppliedToDomainEntityEvent)
}
