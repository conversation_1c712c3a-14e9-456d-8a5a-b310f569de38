/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application

import com.emh.damagemanagement.legal.hold.domain.LegalHoldCreatedEvent
import com.emh.damagemanagement.legal.hold.domain.LegalHoldReleasedEvent
import com.emh.damagemanagement.legal.hold.domain.LegallyHoldable
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldDomainEntityService
import org.springframework.context.event.EventListener

/** Event listener to be implemented */
interface LegalHoldEventListener<T : LegallyHoldable> {
    val legalHoldDomainEntityService: LegalHoldDomainEntityService<T>
    val legallyHoldableDomainEntityFinder: LegallyHoldableDomainEntityFinder<T>

    /**
     * Synchronous event listener that will call respective, domain-specific, legal hold service to apply legal hold to
     * all domain entities found in provided set.
     */
    @EventListener
    fun applyLegalHold(legalHoldCreatedEvent: LegalHoldCreatedEvent) {
        val legallyHoldableEntities =
            legallyHoldableDomainEntityFinder.findDomainEntitiesFor(legalHoldCreatedEvent.domainEntityReferences)
        legallyHoldableEntities.forEach {
            legalHoldDomainEntityService.applyLegalHold(legalHoldKey = legalHoldCreatedEvent.legalHoldKey, entity = it)
        }
    }

    /**
     * Synchronous event listener that will call respective, domain-specific, legal hold service to release a single
     * legal hold (for given legalHoldKey) for all matching domain entities.
     */
    @EventListener
    fun releaseLegalHold(legalHoldReleasedEvent: LegalHoldReleasedEvent) {
        val legallyHoldableEntities =
            legallyHoldableDomainEntityFinder.findDomainEntitiesFor(legalHoldKey = legalHoldReleasedEvent.legalHoldKey)
        legallyHoldableEntities.forEach {
            legalHoldDomainEntityService.releaseLegalHold(
                legalHoldKey = legalHoldReleasedEvent.legalHoldKey,
                entity = it,
            )
        }
    }
}
