/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application

import com.emh.damagemanagement.legal.hold.domain.LegalHold
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.LegalHoldNotFoundException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional(readOnly = true)
class LegalHoldFinder(private val legalHoldRepository: LegalHoldRepository) {
    fun getLegalHold(legalHoldKey: LegalHoldKey): LegalHold =
        legalHoldRepository.findByKey(legalHoldKey) ?: throw LegalHoldNotFoundException(legalHoldKey = legalHoldKey)
}
