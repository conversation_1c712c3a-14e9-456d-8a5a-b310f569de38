/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application

import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.LegallyHoldable

/**
 * Interface for a service that will provide [LegallyHoldable] domain entities. Implement this for each domain module
 * that provides [LegallyHoldable] domain entities.
 */
interface LegallyHoldableDomainEntityFinder<T : LegallyHoldable> {

    /** Return all domain entities for given [LegalHoldKey] */
    fun findDomainEntitiesFor(legalHoldKey: LegalHoldKey): Set<T>

    /**
     * Return all domain entities for given set of [DomainEntityReference]s. As references are mot domain specific
     * implementation has to ensure, that the proper (sub)set is provided here.
     */
    fun findDomainEntitiesFor(domainEntityReferences: Collection<DomainEntityReference>): Set<T>
}
