/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application.port

import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldNew

fun interface ApplyLegalHoldUseCase {
    /**
     * Will create a new legal hold and apply legal hold to all referenced domain entities.
     *
     * @return [LegalHoldKey] for the newly created legal hold
     */
    fun applyLegalHold(legalHoldNew: LegalHoldNew): LegalHoldKey
}
