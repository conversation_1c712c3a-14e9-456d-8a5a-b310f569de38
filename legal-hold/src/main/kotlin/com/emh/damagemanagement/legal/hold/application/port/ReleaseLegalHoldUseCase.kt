/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application.port

import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey

fun interface ReleaseLegalHoldUseCase {
    /** Will delete legal hold with given key and release all domain entities this legal hold is currently applied to */
    fun releaseLegalHold(legalHoldKey: LegalHoldKey, releasedBy: String)
}
