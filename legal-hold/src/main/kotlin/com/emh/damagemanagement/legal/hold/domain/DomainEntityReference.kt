/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Converter
import java.io.Serializable

/**
 * An untyped reference to a domain entity.
 *
 * Be aware, this class will enforce character limitation [VALID_ENTITY_REFERENCE] on construction.
 */
@JvmInline
value class DomainEntityReference(val entityIdentifier: String) : Serializable {
    init {
        require(entityIdentifier.matches(VALID_ENTITY_REFERENCE)) {
            "Entity Identifier [${this.entityIdentifier}] does not match regex [${VALID_ENTITY_REFERENCE}] "
        }
    }
}

val VALID_ENTITY_REFERENCE = Regex("^[\\p{L}\\d_\\-.][\\p{L}_\\d\\-.\\s]*\$")

@Converter
internal class DomainEntityReferenceSetConverter : AttributeConverter<Set<DomainEntityReference>, String> {
    override fun convertToDatabaseColumn(attribute: Set<DomainEntityReference>): String? {
        return if (attribute.isEmpty()) {
            null
        } else {
            attribute.joinToString(separator = ",") { it.entityIdentifier }
        }
    }

    override fun convertToEntityAttribute(dbData: String?): Set<DomainEntityReference> {
        return when (dbData) {
            null -> return emptySet()
            "" -> return emptySet()
            else -> dbData.split(",").map { DomainEntityReference(it) }.toSet()
        }
    }
}
