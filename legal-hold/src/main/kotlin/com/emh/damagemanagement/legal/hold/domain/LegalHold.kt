/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

import com.emh.damagemanagement.shared.domain.Auditable
import com.emh.damagemanagement.shared.domain.Key
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Convert
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import java.io.Serializable
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.*

/**
 * Aggregate Root for a legal hold. A legal hold may affect multiple aggregates and multiple aggregates may be affected
 * by multiple legal holds, thus we opt for a separate aggregate with loose references managed by the affected domain
 * entity. As we assume that legal hold will be implemented for multiple domains we do not save references to the
 * affected entities within the legal hold aggregate, so that it can remain domain independent. If some form of legal
 * hold management is at some point necessary this decision may have to be reevaluated.
 */
@Entity
@Table(name = "legal_hold")
class LegalHold(
    /** The person that issued the legal hold */
    @Column(name = "issuer") val issuer: String,
    /**
     * The external reference uniquely identifying this legal hold. Will also be used to construct the legal hold key.
     */
    @Column(name = "external_reference") val externalReference: String,
    domainEntityReferences: Set<DomainEntityReference>,
) : Auditable() {

    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: LegalHoldId = LegalHoldId()

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "legal_hold_key"))
    val key: LegalHoldKey = buildLegalHoldKey()

    /** The untyped domain entity references supplied by the issuer. */
    @Convert(converter = DomainEntityReferenceSetConverter::class)
    @Column(name = "domain_entities")
    private val _domainEntityReferences: Set<DomainEntityReference> = domainEntityReferences

    val domainEntityReferences
        get() = _domainEntityReferences.toSet()

    private fun buildLegalHoldKey(): LegalHoldKey =
        LegalHoldKey("${LocalDate.now()}_$externalReference".replace(ANY_NON_WORD_CHARACTER, ""))

    val createdEvent: LegalHoldCreatedEvent
        get() =
            LegalHoldCreatedEvent(
                legalHoldKey = this.key,
                occurredOn = OffsetDateTime.now(),
                domainEntityReferences = this.domainEntityReferences,
            )

    /** Flag indicating if this legal hold has been released */
    @Column(name = "released") private var released: Boolean = false

    /** The person that issues the release of this legal hold */
    @Column(name = "released_by")
    var releasedBy: String? = null
        private set

    val isReleased: Boolean
        get() = released

    val releasedAt: OffsetDateTime?
        get() = if (released) this.lastModified else null

    internal fun release(releasedBy: String) {
        require(releasedBy.isNotBlank()) { "The releasing person may not be blank" }
        if (!released) {
            this.released = true
            this.releasedBy = releasedBy
        }
    }

    companion object {
        private val ANY_NON_WORD_CHARACTER = Regex("\\W")
    }
}

@Embeddable data class LegalHoldId(@Basic val value: UUID = UUID.randomUUID()) : Serializable

@Embeddable
data class LegalHoldKey(@Basic override val value: String) : Key {
    init {
        require(value.isNotBlank()) { "LegalHoldKey may not be blank." }
    }
}

data class LegalHoldCreatedEvent(
    override val legalHoldKey: LegalHoldKey,
    val domainEntityReferences: Set<DomainEntityReference>,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
) : LegalHoldEvent

data class LegalHoldReleasedEvent(
    override val legalHoldKey: LegalHoldKey,
    val domainEntityReferences: Set<DomainEntityReference>,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
) : LegalHoldEvent
