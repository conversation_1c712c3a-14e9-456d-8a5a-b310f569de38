/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

class LegalHoldNotFoundException(val legalHoldKey: LegalHoldKey) :
    NoSuchElementException("LegalHold with key [${legalHoldKey.value}] could not be found.")

class LegalHoldAlreadyExistsException(val legalHoldKey: LegalHoldKey, val externalReference: String) :
    NoSuchElementException(
        "LegalHold with key [${legalHoldKey.value}] and external reference [$externalReference] already exists."
    )

class LegalHoldInPlaceException(domainEntityReference: DomainEntityReference, legalHoldKeys: Set<LegalHoldKey>) :
    IllegalStateException(
        """Entity with domain entity reference [${domainEntityReference.entityIdentifier}] 
            has at least one legal hold in place [${legalHoldKeys.joinToString(separator = ", ", transform = { it.value })}] 
            and can not be modified."""
            .trimMargin()
    )

class LegalHoldNotAppliedException(
    legalHoldKey: LegalHoldKey,
    domainEntityReference: DomainEntityReference,
    cause: Throwable?,
) :
    RuntimeException(
        "LegalHold for key: [${legalHoldKey.value}] and domain entity with reference: [${domainEntityReference.entityIdentifier}] could not be applied.",
        cause,
    )

class LegalHoldNotReleasedException(
    legalHoldKey: LegalHoldKey,
    domainEntityReference: DomainEntityReference,
    cause: Throwable?,
) :
    RuntimeException(
        "LegalHold for key: [${legalHoldKey.value}] and domain entity with reference: [${domainEntityReference.entityIdentifier}] could not be released.",
        cause,
    )
