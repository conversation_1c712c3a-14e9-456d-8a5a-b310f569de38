/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

import org.springframework.data.repository.Repository

interface LegalHoldRepository : Repository<LegalHold, LegalHoldId> {

    fun save(legalHold: LegalHold)

    fun findByKey(legalHoldKey: LegalHoldKey): LegalHold?

    fun existsByKey(legalHoldKey: LegalHoldKey): Bo<PERSON>an
}
