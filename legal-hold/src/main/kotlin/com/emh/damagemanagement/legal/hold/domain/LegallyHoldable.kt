/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

/** An interface for domain entities to declare them legally holdable */
interface LegallyHoldable {

    /** The unique reference that is used to identify this entity during a legal hold. */
    val domainEntityReference: DomainEntityReference

    /** The set of legal hold references currently enforced on this entity. */
    val legalHolds: Set<LegalHoldKey>

    /** Will add a new legal hold onto this entity. */
    fun addLegalHold(legalHoldKey: LegalHoldKey)

    /** Will release given legal hold from this entity. */
    fun releaseLegalHold(legalHoldKey: LegalHoldKey)

    fun requireNoLegalHolds() {
        if (legalHolds.isNotEmpty())
            throw LegalHoldInPlaceException(
                domainEntityReference = this.domainEntityReference,
                legalHoldKeys = this.legalHolds,
            )
    }
}
