/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain.service

import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHold
import com.emh.damagemanagement.legal.hold.domain.LegalHoldAlreadyExistsException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldRepository
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class LegalHoldCreateService(
    private val legalHoldRepository: LegalHoldRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    fun createLegalHold(legalHoldNew: LegalHoldNew): LegalHold {
        val legalHold = legalHoldNew.toLegalHold()
        if (legalHoldRepository.existsByKey(legalHold.key))
            throw throw LegalHoldAlreadyExistsException(
                legalHoldKey = legalHold.key,
                externalReference = legalHold.externalReference,
            )

        legalHoldRepository.save(legalHold)

        log.info("Created new legal hold with key [${legalHold.key.value}].")
        applicationEventPublisher.publishEvent(legalHold.createdEvent)
        return legalHold
    }

    private fun LegalHoldNew.toLegalHold() =
        LegalHold(
            issuer = this.issuer,
            externalReference = externalReference,
            domainEntityReferences = domainEntityReferences,
        )

    companion object {
        private val log = LoggerFactory.getLogger(LegalHoldCreateService::class.java)
    }
}

data class LegalHoldNew(
    val issuer: String,
    val externalReference: String,
    val domainEntityReferences: Set<DomainEntityReference>,
)
