/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain.service

import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHoldEvent
import com.emh.damagemanagement.legal.hold.domain.LegalHoldKey
import com.emh.damagemanagement.legal.hold.domain.LegallyHoldable
import java.time.OffsetDateTime
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.core.io.Resource

/** documentName to document */
typealias LegalHoldDocuments = Map<String, Resource>

/** Domain service to apply and release [LegallyHoldable] domain entities. Implement this in you domain module. */
interface LegalHoldDomainEntityService<T : LegallyHoldable> {
    val applicationEventPublisher: ApplicationEventPublisher

    /**
     * Provides a list of [Resource]s that have to persisted as per legal hold requirements. Each domain has to
     * determine which resources are relevant for legal hold.
     */
    fun getLegalHoldRelevantDocuments(entity: T): LegalHoldDocuments

    /** Applies legal hold to given [LegallyHoldable] entity. */
    fun applyLegalHold(legalHoldKey: LegalHoldKey, entity: T) {
        entity.addLegalHold(legalHoldKey)
        log.info(
            """Applied legal hold with key [${legalHoldKey.value}] to entity with reference [${entity.domainEntityReference.entityIdentifier}].
                 Entity has now ${entity.legalHolds.size} legal holds in place."""
                .trimMargin()
        )
        applicationEventPublisher.publishEvent(
            LegalHoldAppliedToDomainEntityEvent(
                legalHoldKey = legalHoldKey,
                domainEntityReference = entity.domainEntityReference,
                legalHoldDocuments = getLegalHoldRelevantDocuments(entity),
                occurredOn = OffsetDateTime.now(),
            )
        )
    }

    /** Releases legal hold from given [LegallyHoldable] entity. */
    fun releaseLegalHold(legalHoldKey: LegalHoldKey, entity: T) {
        entity.releaseLegalHold(legalHoldKey)
        log.info(
            """Released legal hold with key [${legalHoldKey.value}] from entity with reference [${entity.domainEntityReference.entityIdentifier}]. 
                Entity has now ${entity.legalHolds.size} legal holds in place."""
                .trimMargin()
        )
        applicationEventPublisher.publishEvent(
            LegalHoldReleasedFromDomainEntityEvent(
                legalHoldKey = legalHoldKey,
                domainEntityReference = entity.domainEntityReference,
                occurredOn = OffsetDateTime.now(),
            )
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(LegalHoldDomainEntityService::class.java)
    }
}

data class LegalHoldAppliedToDomainEntityEvent(
    override val legalHoldKey: LegalHoldKey,
    val domainEntityReference: DomainEntityReference,
    /** A map of */
    val legalHoldDocuments: LegalHoldDocuments,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
) : LegalHoldEvent

data class LegalHoldReleasedFromDomainEntityEvent(
    override val legalHoldKey: LegalHoldKey,
    val domainEntityReference: DomainEntityReference,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
) : LegalHoldEvent
