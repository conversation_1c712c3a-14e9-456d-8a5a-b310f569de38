/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain.service

import com.emh.damagemanagement.legal.hold.domain.LegalHold
import com.emh.damagemanagement.legal.hold.domain.LegalHoldReleasedEvent
import java.time.OffsetDateTime
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class LegalHoldReleaseService(private val applicationEventPublisher: ApplicationEventPublisher) {
    fun releaseLegalHold(legalHold: LegalHold, releasedBy: String) {
        if (legalHold.isReleased) {
            log.warn("Could not release legal hold with key [${legalHold.key}], as it has already been released.")
            return
        }
        legalHold.release(releasedBy)

        applicationEventPublisher.publishEvent(
            LegalHoldReleasedEvent(
                legalHoldKey = legalHold.key,
                occurredOn = OffsetDateTime.now(),
                domainEntityReferences = legalHold.domainEntityReferences,
            )
        )

        log.info("Released legal hold with key [${legalHold.key.value}].")
    }

    companion object {
        private val log = LoggerFactory.getLogger(LegalHoldReleaseService::class.java)
    }
}
