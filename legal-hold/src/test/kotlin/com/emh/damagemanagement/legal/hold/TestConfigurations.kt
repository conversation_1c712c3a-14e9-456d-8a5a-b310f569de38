package com.emh.damagemanagement.legal.hold

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.data.jpa.repository.config.EnableJpaRepositories

@SpringBootApplication(scanBasePackages = ["com.emh.damagemanagement.*"])
@ConfigurationPropertiesScan("com.emh.damagemanagement.*")
@EnableJpaRepositories("com.emh.damagemanagement.legal.hold.*")
class TestLegalHoldApplication {

    fun main(args: Array<String>) {
        runApplication<TestLegalHoldApplication>(*args)
    }
}
