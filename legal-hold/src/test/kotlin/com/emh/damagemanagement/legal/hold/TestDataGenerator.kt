/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold

import com.emh.damagemanagement.legal.hold.domain.DomainEntityReference
import com.emh.damagemanagement.legal.hold.domain.LegalHold
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldNew
import com.emh.damagemanagement.shared.createRandomString
import kotlin.random.Random

private val ANY_NON_WORD_CHARACTER = Regex("\\W")
val validDomainEntityReference
    get() = createRandomString(Random.nextInt(10, 1000)).replace(ANY_NON_WORD_CHARACTER, "")

class LegalHoldBuilder {
    private var issuer: String = createRandomString(Random.nextInt(4, 23))
    private var externalReference: String = createRandomString(Random.nextInt(4, 23))
    private var domainEntityReferences: Set<DomainEntityReference> =
        (1..10).map { DomainEntityReference(validDomainEntityReference) }.toSet()

    fun externalReference(reference: String) = apply { this.externalReference = reference }

    fun build(): LegalHold {
        return LegalHold(
            issuer = issuer,
            externalReference = externalReference,
            domainEntityReferences = domainEntityReferences,
        )
    }

    companion object {
        fun buildSingle() = LegalHoldBuilder().build()

        fun buildMultiple(count: Int): Set<LegalHold> = (1..count).map { LegalHoldBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class LegalHoldNewBuilder {
    private var issuer: String = createRandomString(Random.nextInt(4, 23))
    private var externalReference: String = createRandomString(Random.nextInt(4, 23))
    private var domainEntityReferences: Set<DomainEntityReference> =
        (1..10).map { DomainEntityReference(validDomainEntityReference) }.toSet()

    fun build(): LegalHoldNew {
        return LegalHoldNew(
            issuer = issuer,
            externalReference = externalReference,
            domainEntityReferences = domainEntityReferences,
        )
    }

    companion object {
        fun buildSingle() = LegalHoldNewBuilder().build()

        fun buildMultiple(count: Int): Set<LegalHoldNew> = (1..count).map { LegalHoldNewBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
