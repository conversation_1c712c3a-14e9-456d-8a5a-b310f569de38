/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application.port

import com.emh.damagemanagement.legal.hold.IntegrationTest
import com.emh.damagemanagement.legal.hold.LegalHoldNewBuilder
import com.emh.damagemanagement.legal.hold.application.LegalHoldFinder
import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class ApplyLegalHoldUseCaseTest {
    @Autowired private lateinit var applyLegalHoldUseCase: ApplyLegalHoldUseCase

    @Autowired private lateinit var legalHoldFinder: LegalHoldFinder

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should create new legal hold when calling applyLegalHold`() {
        val legalHoldNew = LegalHoldNewBuilder.buildSingle()

        val legalHoldKey = applyLegalHoldUseCase.applyLegalHold(legalHoldNew)
        entityManager.flush()

        val legalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey)
        assertThat(legalHoldFromFinder)
            .usingRecursiveComparison()
            .ignoringFieldsMatchingRegexes("^(_|mutable)\\S*")
            .ignoringFields(*TestJpaAuditingConfiguration.AUDIT_FIELDS, "id", "key", "released", "releasedBy")
            .isEqualTo(legalHoldNew)
    }
}
