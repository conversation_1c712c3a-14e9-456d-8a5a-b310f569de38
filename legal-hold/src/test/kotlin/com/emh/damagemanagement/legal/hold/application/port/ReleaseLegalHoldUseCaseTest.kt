/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.application.port

import com.emh.damagemanagement.legal.hold.IntegrationTest
import com.emh.damagemanagement.legal.hold.LegalHoldNewBuilder
import com.emh.damagemanagement.legal.hold.application.LegalHoldFinder
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class ReleaseLegalHoldUseCaseTest {
    @Autowired private lateinit var applyLegalHoldUseCase: ApplyLegalHoldUseCase
    @Autowired private lateinit var releaseLegalHoldUseCase: ReleaseLegalHoldUseCase
    @Autowired private lateinit var entityManager: EntityManager

    @Autowired private lateinit var legalHoldFinder: LegalHoldFinder

    @Test
    fun `should release legal hold when calling releaseLegalHold`() {
        val legalHoldNew = LegalHoldNewBuilder.buildSingle()
        val legalHoldKey = applyLegalHoldUseCase.applyLegalHold(legalHoldNew)
        entityManager.flush()
        val legalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey)

        releaseLegalHoldUseCase.releaseLegalHold(legalHoldKey = legalHoldFromFinder.key, releasedBy = "me")
        entityManager.flush()

        val releasedLegalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey)
        assertThat(releasedLegalHoldFromFinder)
            .satisfies({
                assertThat(it.isReleased).isTrue()
                assertThat(it.releasedAt).isNotNull()
                assertThat(it.releasedBy).isEqualTo("me")
            })
    }
}
