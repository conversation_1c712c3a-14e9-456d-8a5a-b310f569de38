/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

import com.emh.damagemanagement.legal.hold.validDomainEntityReference
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class DomainEntityReferenceSetConverterTest {

    private val domainEntityReferenceSetConverter: DomainEntityReferenceSetConverter =
        DomainEntityReferenceSetConverter()

    @ParameterizedTest
    @MethodSource("domainEntityReferencesProvider")
    fun `should convert set of DomainEntityReferences to dbColumn`(domainEntityReferences: Set<DomainEntityReference>) {
        val dbColumn = domainEntityReferenceSetConverter.convertToDatabaseColumn(domainEntityReferences)
        assertThat(dbColumn)
            .isEqualTo(domainEntityReferences.map { it.entityIdentifier }.joinToString(separator = (",")))
    }

    @Test
    fun `should convert empty set to dbColumn`() {
        val dbColumn = domainEntityReferenceSetConverter.convertToDatabaseColumn(emptySet())
        assertThat(dbColumn).isEqualTo(null)
    }

    @ParameterizedTest
    @MethodSource("dbColumnProvider")
    fun `should convert dbColumn to set of DomainEntityReferences`(dbColumn: String) {
        val domainReferences = domainEntityReferenceSetConverter.convertToEntityAttribute(dbColumn)
        assertThat(domainReferences)
            .containsExactlyInAnyOrderElementsOf(dbColumn.split(",").map { DomainEntityReference(it) })
    }

    @Test
    fun `should convert null dbColumn to set of DomainEntityReferences`() {
        val set = domainEntityReferenceSetConverter.convertToEntityAttribute(null)
        assertThat(set).isEmpty()
    }

    @Test
    fun `should convert empty dbColumn to set of DomainEntityReferences`() {
        val set = domainEntityReferenceSetConverter.convertToEntityAttribute("")
        assertThat(set).isEmpty()
    }

    companion object {

        @JvmStatic
        fun domainEntityReferencesProvider(): Stream<Arguments> =
            (0..10)
                .map { (0..10).map { DomainEntityReference(validDomainEntityReference) }.toSet() }
                .map { Arguments.of(it) }
                .stream()

        @JvmStatic
        fun dbColumnProvider(): Stream<Arguments> =
            (0..10).map { (0..10).joinToString(",") { validDomainEntityReference } }.map { Arguments.of(it) }.stream()
    }
}
