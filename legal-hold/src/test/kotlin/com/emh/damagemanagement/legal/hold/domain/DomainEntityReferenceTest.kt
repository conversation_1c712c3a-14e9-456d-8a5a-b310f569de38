/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

import org.assertj.core.api.Assertions.*
import org.junit.jupiter.api.Test

class DomainEntityReferenceTest {

    @Test
    fun `should not fail construction using valid characters`() {
        assertThatNoException().isThrownBy { DomainEntityReference("5i_am very vÄlid.-___ê") }
    }

    @Test
    fun `should fail construction when starting with a whitespace`() {
        assertThatExceptionOfType(IllegalArgumentException::class.java).isThrownBy { DomainEntityReference(" invalid") }
    }

    @Test
    fun `should fail construction when containing invalid character`() {
        assertThatExceptionOfType(IllegalArgumentException::class.java).isThrownBy { DomainEntityReference("in,valid") }
    }

    @Test
    fun `should fail construction if blank`() {
        assertThatExceptionOfType(IllegalArgumentException::class.java).isThrownBy { DomainEntityReference("") }
    }
}
