/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

import com.emh.damagemanagement.legal.hold.IntegrationTest
import com.emh.damagemanagement.legal.hold.LegalHoldNewBuilder
import com.emh.damagemanagement.legal.hold.application.LegalHoldFinder
import com.emh.damagemanagement.legal.hold.domain.service.LegalHoldCreateService
import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class LegalHoldFinderTest {

    @Autowired private lateinit var legalHoldFinder: LegalHoldFinder

    @Autowired private lateinit var legalHoldCreateService: LegalHoldCreateService

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should get legal hold by key`() {
        val legalHoldNew = LegalHoldNewBuilder.buildSingle()
        val legalHold = legalHoldCreateService.createLegalHold(legalHoldNew)
        entityManager.flush()

        val legalHoldFromFinder = legalHoldFinder.getLegalHold(legalHold.key)
        assertThat(legalHoldFromFinder)
            .usingRecursiveComparison()
            .ignoringFieldsMatchingRegexes("^(_|mutable)\\S*")
            .ignoringFields(*TestJpaAuditingConfiguration.AUDIT_FIELDS)
            .isEqualTo(legalHold)
    }

    @Test
    fun `should throw LegalHoldNotFoundException if legal hold could not be found`() {
        assertThatExceptionOfType(LegalHoldNotFoundException::class.java).isThrownBy {
            legalHoldFinder.getLegalHold(LegalHoldKey("not me"))
        }
    }
}
