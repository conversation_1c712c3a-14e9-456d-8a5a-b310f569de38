/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain

import com.emh.damagemanagement.legal.hold.IntegrationTest
import com.emh.damagemanagement.legal.hold.LegalHoldBuilder
import com.emh.damagemanagement.shared.TEST_USER
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.hibernate.exception.ConstraintViolationException
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class LegalHoldRepositoryTest {
    @Autowired private lateinit var legalHoldRepository: LegalHoldRepository

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should set auditing field when saving legal hold`() {
        val legalHold = LegalHoldBuilder().build()
        legalHoldRepository.save(legalHold)
        entityManager.flush()

        val legalHoldFromRepository = requireNotNull(legalHoldRepository.findByKey(legalHold.key))

        assertThat(legalHoldFromRepository.createdBy).isEqualTo(TEST_USER)
        assertThat(legalHoldFromRepository.created).isNotNull()
    }

    @Test
    fun `should throw ConstraintViolationException when legal could not be created due to constraint violation`() {
        val externalReference = "reference"
        val legalHold = LegalHoldBuilder().externalReference(externalReference).build()
        legalHoldRepository.save(legalHold)
        entityManager.flush()

        assertThatExceptionOfType(ConstraintViolationException::class.java).isThrownBy {
            val legalHoldWithDuplicateKey = LegalHoldBuilder().externalReference(externalReference).build()
            legalHoldRepository.save(legalHoldWithDuplicateKey)
            entityManager.flush()
        }
    }
}
