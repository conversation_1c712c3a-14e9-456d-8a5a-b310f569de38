/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain.service

import com.emh.damagemanagement.legal.hold.IntegrationTest
import com.emh.damagemanagement.legal.hold.LegalHoldNewBuilder
import com.emh.damagemanagement.legal.hold.application.LegalHoldFinder
import com.emh.damagemanagement.legal.hold.domain.LegalHoldAlreadyExistsException
import com.emh.damagemanagement.legal.hold.domain.LegalHoldCreatedEvent
import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.reset
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [LegalHoldCreateServiceTest.TestConfiguration::class])
class LegalHoldCreateServiceTest {
    @Autowired private lateinit var legalHoldCreateService: LegalHoldCreateService
    @Autowired private lateinit var legalHoldFinder: LegalHoldFinder
    @Autowired private lateinit var applicationEventPublisher: ApplicationEventPublisher
    @Autowired private lateinit var entityManager: EntityManager

    @BeforeEach
    fun resetCounter() {
        reset(applicationEventPublisher)
    }

    @Test
    fun `should create legal hold`() {
        val legalHoldNew = LegalHoldNewBuilder.buildSingle()
        val legalHoldCreatedEventArgumentCaptor = argumentCaptor<LegalHoldCreatedEvent>()

        val legalHold = legalHoldCreateService.createLegalHold(legalHoldNew)
        entityManager.flush()

        verify(applicationEventPublisher).publishEvent(legalHoldCreatedEventArgumentCaptor.capture())
        val legalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey = legalHold.key)
        assertThat(legalHoldFromFinder)
            .usingRecursiveComparison()
            .ignoringFieldsMatchingRegexes("^(_|mutable)\\S*")
            .ignoringFields(*TestJpaAuditingConfiguration.AUDIT_FIELDS)
            .isEqualTo(legalHold)
        assertThat(legalHoldCreatedEventArgumentCaptor.firstValue)
            .satisfies({
                assertThat(it.legalHoldKey).isEqualTo(legalHold.key)
                assertThat(it.domainEntityReferences)
                    .containsExactlyInAnyOrderElementsOf(legalHold.domainEntityReferences)
            })
    }

    @Test
    fun `should throw LegalHoldAlreadyExistsException when trying to create legal hold with duplicate key`() {
        val legalHoldNew = LegalHoldNewBuilder.buildSingle()

        legalHoldCreateService.createLegalHold(legalHoldNew)
        entityManager.flush()

        verify(applicationEventPublisher).publishEvent(any<LegalHoldCreatedEvent>())
        assertThatExceptionOfType(LegalHoldAlreadyExistsException::class.java).isThrownBy {
            legalHoldCreateService.createLegalHold(legalHoldNew)
            entityManager.flush()
        }
        verifyNoMoreInteractions(applicationEventPublisher)
    }

    internal class TestConfiguration {

        @Bean @Primary fun applicationEventPublisher(): ApplicationEventPublisher = mock()
    }
}
