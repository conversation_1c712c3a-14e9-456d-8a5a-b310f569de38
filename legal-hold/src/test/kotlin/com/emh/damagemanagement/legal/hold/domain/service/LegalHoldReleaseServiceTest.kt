/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.legal.hold.domain.service

import com.emh.damagemanagement.legal.hold.IntegrationTest
import com.emh.damagemanagement.legal.hold.LegalHoldNewBuilder
import com.emh.damagemanagement.legal.hold.application.LegalHoldFinder
import com.emh.damagemanagement.legal.hold.domain.LegalHoldCreatedEvent
import com.emh.damagemanagement.legal.hold.domain.LegalHoldReleasedEvent
import com.emh.damagemanagement.legal.hold.domain.LegalHoldRepository
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.verifyNoMoreInteractions
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [LegalHoldReleaseServiceTest.TestConfiguration::class])
class LegalHoldReleaseServiceTest {
    @Autowired private lateinit var legalHoldReleaseService: LegalHoldReleaseService
    @Autowired private lateinit var legalHoldRepository: LegalHoldRepository
    @Autowired private lateinit var legalHoldCreateService: LegalHoldCreateService
    @Autowired private lateinit var legalHoldFinder: LegalHoldFinder
    @Autowired private lateinit var applicationEventPublisher: ApplicationEventPublisher
    @Autowired private lateinit var entityManager: EntityManager

    @BeforeEach
    fun resetCounter() {
        reset(applicationEventPublisher)
    }

    @Test
    fun `should release legal hold`() {
        val legalHoldNew = LegalHoldNewBuilder.buildSingle()
        val legalHoldReleasedEventArgumentCaptor = argumentCaptor<LegalHoldReleasedEvent>()
        val legalHold = legalHoldCreateService.createLegalHold(legalHoldNew)
        entityManager.flush()
        verify(applicationEventPublisher).publishEvent(any<LegalHoldCreatedEvent>())
        val legalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey = legalHold.key)

        legalHoldReleaseService.releaseLegalHold(legalHold = legalHoldFromFinder, releasedBy = "someone")

        verify(applicationEventPublisher).publishEvent(legalHoldReleasedEventArgumentCaptor.capture())
        val releasedLegalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey = legalHold.key)
        assertThat(releasedLegalHoldFromFinder)
            .satisfies({
                assertThat(it.isReleased).isTrue()
                assertThat(it.releasedAt).isNotNull()
                assertThat(it.releasedBy).isEqualTo("someone")
            })
        assertThat(legalHoldReleasedEventArgumentCaptor.firstValue)
            .satisfies({
                assertThat(it.legalHoldKey).isEqualTo(legalHold.key)
                assertThat(it.domainEntityReferences)
                    .containsExactlyInAnyOrderElementsOf(legalHold.domainEntityReferences)
            })
    }

    @Test
    fun `should not publish release event if no legal hold was release because it was already released before`() {
        val legalHoldNew = LegalHoldNewBuilder.buildSingle()
        val legalHold = legalHoldCreateService.createLegalHold(legalHoldNew)
        entityManager.flush()
        verify(applicationEventPublisher).publishEvent(any<LegalHoldCreatedEvent>())
        val legalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey = legalHold.key)
        legalHoldReleaseService.releaseLegalHold(legalHold = legalHoldFromFinder, releasedBy = "someone")
        entityManager.flush()
        verify(applicationEventPublisher).publishEvent(any<LegalHoldReleasedEvent>())

        assertThatNoException().isThrownBy {
            legalHoldReleaseService.releaseLegalHold(legalHold = legalHoldFromFinder, releasedBy = "someone again")
            entityManager.flush()
        }

        val alreadyReleasedLegalHoldFromFinder = legalHoldFinder.getLegalHold(legalHoldKey = legalHold.key)
        assertThat(alreadyReleasedLegalHoldFromFinder.releasedBy).isEqualTo("someone")
        verifyNoMoreInteractions(applicationEventPublisher)
    }

    internal class TestConfiguration {

        @Bean @Primary fun applicationEventPublisher(): ApplicationEventPublisher = mock()
    }
}
