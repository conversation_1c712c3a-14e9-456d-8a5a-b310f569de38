[versions]
#plugins
ben-manes-versions = "0.52.0"
docker = "9.4.0"
kotlin = "2.2.0"
openapi = "7.14.0"
spotless = "7.0.3"
spring-boot = "3.5.3"
spring-dependency-management = "1.1.7"

apache-poi = "5.4.1"
apache-commons-compress = "1.27.1"
archunit = "1.4.1"
awaitility = "4.3.0"
aws = "2.31.68"
datafaker = "2.4.3"
hibernate-types = "3.10.2"
mockito = "5.4.0"
mockwebserver = "5.1.0"
okhttp = "5.1.0"
preliquibase = "1.6.1"
spring-cloud = "3.4.0"
wiremock = "3.12.1"

#internal
api-space-dms = "3.1.0-dev"
api-space-emhfxp = "13.0.0-dev"
s3-storage-client = "2.1.4-dev"
api-space-fuhrpark = "4.1.0-dev"

[plugins]
ben-manes-versions = { id = "com.github.ben-manes.versions", version.ref = "ben-manes-versions" }
docker = { id = "com.bmuschko.docker-spring-boot-application", version.ref = "docker" }
kotlin-jpa = { id = "org.jetbrains.kotlin.plugin.jpa", version.ref = "kotlin" }
kotlin-jvm = { id = "org.jetbrains.kotlin.jvm", version.ref = "kotlin" }
kotlin-spring = { id = "org.jetbrains.kotlin.plugin.spring", version.ref = "kotlin" }
openapi-generator = { id = "org.openapi.generator", version.ref = "openapi" }
spring-dependency-management = { id = "io.spring.dependency-management", version.ref = "spring-dependency-management" }
spring-boot = { id = "org.springframework.boot", version.ref = "spring-boot" }
spotless = { id = "com.diffplug.spotless", version.ref = "spotless" }

[libraries]
#internal
api-dms = { module = "com.emh.damagemanagement:space-dms", version.ref = "api-space-dms" }
api-emhfxp = { module = "com.emh:space-emhfxp", version.ref = "api-space-emhfxp" }
s3-client = { module = "com.emh.shared:s3-storage-client", version.ref = "s3-storage-client" }
api-fuhrpark = { module = "com.emh:space-fuhrpark", version.ref = "api-space-fuhrpark" }

#apache
apache-poi = { module = "org.apache.poi:poi-ooxml", version.ref = "apache-poi" }
apache-commons-compress = { module = "org.apache.commons:commons-compress", version.ref = "apache-commons-compress" }

#aws
aws-kms = { module = "software.amazon.awssdk:kms", version.ref = "aws" }

#rds
awssdk-rds = { module = "software.amazon.awssdk:rds", version.ref = "aws" }


#h2 (no longer used)
h2 = { module = "com.h2database:h2" }

#hibernate-types
hibernate-types = { module = "io.hypersistence:hypersistence-utils-hibernate-63", version.ref = "hibernate-types" }

#jackson
jackson-annotations = { module = "com.fasterxml.jackson.core:jackson-annotations" }
jackson-module-kotlin = { module = "com.fasterxml.jackson.module:jackson-module-kotlin" }
jackson-module-jakarta-xmlbind-annotations = { module = "com.fasterxml.jackson.module:jackson-module-jakarta-xmlbind-annotations" }

#kotlin
kotlin-reflect = { module = "org.jetbrains.kotlin:kotlin-reflect" }
kotlin-stdlib = { module = "org.jetbrains.kotlin:kotlin-stdlib" }

#liquibase
liquibase = { module = "org.liquibase:liquibase-core" }

#micrometer
micrometer-core = { module = "io.micrometer:micrometer-core" }
cloudwatch = { module = "io.micrometer:micrometer-registry-cloudwatch2" }

#okhttp
okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "okhttp" }

#postgres
postgres = { module = "org.postgresql:postgresql" }

#pre-liquibase
preliquibase = { module = "net.lbruun.springboot:preliquibase-spring-boot-starter", version.ref = "preliquibase" }

#spring
spring-boot-actuator = { module = "org.springframework.boot:spring-boot-starter-actuator" }
spring-boot-aws-s3 = { module = "io.awspring.cloud:spring-cloud-aws-starter-s3", version.ref = "spring-cloud" }
spring-boot-cache = { module = "org.springframework.boot:spring-boot-starter-cache" }
spring-boot-data-jpa = { module = "org.springframework.boot:spring-boot-starter-data-jpa" }
spring-boot-oauth2 = { module = "org.springframework.boot:spring-boot-starter-oauth2-client" }
spring-boot-quartz = { module = "org.springframework.boot:spring-boot-starter-quartz" }
spring-boot-starter = { module = "org.springframework.boot:spring-boot-starter" }
spring-boot-web = { module = "org.springframework.boot:spring-boot-starter-web" }
spring-boot-webflux = { module = "org.springframework.boot:spring-boot-starter-webflux" }
spring-kafka = { module = "org.springframework.kafka:spring-kafka" }
spring-security-config = { module = "org.springframework.security:spring-security-config" }
spring-security-oauth2-jose = { module = "org.springframework.security:spring-security-oauth2-jose" }
spring-security-oauth2-resource-server = { module = "org.springframework.security:spring-security-oauth2-resource-server" }

# tests
awaitility-kotlin = { module = "org.awaitility:awaitility-kotlin", version.ref = "awaitility" }
datafaker = { module = "net.datafaker:datafaker", version.ref = "datafaker" }
mockwebserver = { module = "com.squareup.okhttp3:mockwebserver3-junit5", version.ref = "mockwebserver" }
test-archunit-junit5 = { module = "com.tngtech.archunit:archunit-junit5", version.ref = "archunit" }
test-mockito-kotlin = { module = "org.mockito.kotlin:mockito-kotlin", version.ref = "mockito" }
test-spring-boot = { module = "org.springframework.boot:spring-boot-starter-test" }
test-spring-security = { module = "org.springframework.security:spring-security-test" }
test-spring-kafka = { module = "org.springframework.kafka:spring-kafka-test" }

#wiremock
#use wiremock.standalone instead of wiremock until wiremock upgrades to jetty12
wiremock = { module = "org.wiremock:wiremock-standalone", version.ref = "wiremock" }

[bundles]
base = [
    "jackson-annotations",
    "jackson-module-kotlin",
    "jackson-module-jakarta-xmlbind-annotations",
    "kotlin-reflect",
    "kotlin-stdlib",
    "spring-boot-data-jpa",
    "spring-boot-quartz",
    "spring-boot-web"
]
databases = [
    "liquibase",
    "preliquibase",
    "awssdk-rds"
]
runtime-databases = [
    "postgres"
]
webclient = [
    "spring-boot-webflux",
    "spring-boot-oauth2"
]
starter = [
    "spring-boot-actuator",
    "spring-boot-starter"
]
tests = [
    "awaitility-kotlin",
    "datafaker",
    "test-archunit-junit5",
    "test-mockito-kotlin",
    "test-spring-boot",
    "test-spring-security"
]
spring-security = [
    "spring-security-config",
    "spring-security-oauth2-jose",
    "spring-security-oauth2-resource-server"
]
tests-aws = [
    "spring-boot-aws-s3",
    "aws-kms"
]
