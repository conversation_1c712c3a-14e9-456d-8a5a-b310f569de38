#!/bin/sh
echo "creating vehicle-migration bucket"
awslocal s3api create-bucket --bucket test-vehicle-migration
echo "creating legal-hold bucket"
awslocal s3api create-bucket --bucket test-legal-hold --object-lock-enabled-for-bucket
echo "creating archive bucket"
awslocal s3api create-bucket --bucket test-archive
echo "creating kms key"
keyId=$(awslocal kms create-key --output text --query "KeyMetadata.KeyId")
awslocal kms create-alias --alias-name alias/valid-kms-key --target-key-id $keyId
echo "Initialized."
