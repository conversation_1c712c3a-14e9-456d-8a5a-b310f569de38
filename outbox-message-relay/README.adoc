:toc:
:numbered:
= Outbox Message Relay

== Module description

This module provides an implementation of the outbox pattern for reliable messaging (see https://microservices.io/patterns/data/transactional-outbox.html[Pattern: Transactional Outbox])

== Getting started

=== Apache Kafka

We are using https://kafka.apache.org/[Apache Kafka] for testing Filezilla (Porsche managed Kafka) related functionality.
This requires you (for local tests) to have a https://hub.docker.com/r/bitnami/kafka[Apache Kafka container] or pod running.

==== Docker

----
docker run -d --rm --name kafka-server --hostname kafka-server --network app-tier -p "9094:9094" -e KAFKA_CFG_NODE_ID=0 -e KAFKA_CFG_PROCESS_ROLES=controller,broker -e KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094 -e <PERSON>AFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,EXTERNAL://localhost:9094 -e KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT -e KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka-server:9093 -e KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER bitnami/kafka:latest
----

A docker compose.yaml file including a kafka server and a database is provided in the project root:

----
docker compose up
----

==== Kubernetes

----
helm install -f kafka_deployment.yaml kafka oci://registry-1.docker.io/bitnamicharts/kafka
----

may also require additional port forwarding

----
kubectl port-forward svc/kafka-controller-0-external 31000:9094
----

For debugging the Kafka plugin for JetBrains IDEs is recommended.
