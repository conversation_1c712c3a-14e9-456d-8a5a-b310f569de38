import com.diffplug.spotless.LineEnding

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jpa)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

evaluationDependsOn(":shared")
val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    api(libs.api.dms)

    implementation(project(":shared"))

    implementation(libs.bundles.base)
    implementation(libs.spring.kafka)
    implementation(libs.hibernate.types)

    testImplementation(libs.test.spring.kafka)
    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(libs.bundles.tests)
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

val monetaryBenefitGeneratedSourcesPath = "src/generated/"
sourceSets {
    main { kotlin.srcDir("$monetaryBenefitGeneratedSourcesPath/kotlin") }
    test { kotlin { kotlin.srcDir(sharedTestSrcSetsOutput) } }
    test { resources { resources.srcDir(project(":shared").sourceSets.test.get().resources) } }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }
