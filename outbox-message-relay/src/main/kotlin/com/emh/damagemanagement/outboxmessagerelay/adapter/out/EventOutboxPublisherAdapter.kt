/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.adapter.out

import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventRepository
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import com.fasterxml.jackson.databind.JsonNode
import org.springframework.stereotype.Component

@Component
class EventOutboxPublisherAdapter(val outboxRepository: OutboxEventRepository) {
    fun publish(outboxEventType: OutboxEventType, payload: JsonNode) {
        val event = OutboxEvent(outboxEventType = outboxEventType, payload = payload)

        outboxRepository.save(event)
    }
}
