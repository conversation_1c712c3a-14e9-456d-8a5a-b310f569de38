/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.adapter.out

import com.emh.damagemanagement.outboxmessagerelay.application.port.MessagingOutPort
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import java.util.concurrent.TimeUnit
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Component

@Component
class KafkaPublishAdapter(
    private val kafkaTemplate: KafkaTemplate<String, Any>,
    @Value("\${monetary-benefit.accountable.kafka.producer.topic}") val monetaryBenefitTopic: String,
    @Value("\${number-of-damages.kafka.producer.topic}") val damageFileCountTopic: String,
) : MessagingOutPort {
    override fun publish(outboxEvent: OutboxEvent) {
        val topic =
            when (outboxEvent.outboxEventType) {
                OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE -> monetaryBenefitTopic
                OutboxEventType.DAMAGE_FILE_COUNT -> damageFileCountTopic
            }
        val payload = outboxEvent.payload
        kafkaTemplate.send(topic, payload).get(3, TimeUnit.SECONDS)
        log.info("Sent $payload to topic \"${topic}\"")
    }

    companion object {
        private val log = LoggerFactory.getLogger(KafkaPublishAdapter::class.java)
    }
}
