/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.application

import com.emh.damagemanagement.outboxmessagerelay.application.port.MessagingOutPort
import com.emh.damagemanagement.outboxmessagerelay.application.port.RelayEventUseCase
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventRepository
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventState
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@Transactional
class OutboxMessageService(
    private val publisher: MessagingOutPort,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val repository: OutboxEventRepository,
) : RelayEventUseCase {

    fun publishAllPendingMessages() {
        val pendingMessages = repository.findAllByState(OutboxEventState.CREATED)

        // publish all messages
        pendingMessages.forEach(::publishEventAndSetToPublished)
    }

    private fun publishEventAndSetToPublished(outboxEvent: OutboxEvent) {
        try {
            log.trace("Publishing OutboxEvent with eventId: {}.", outboxEvent.eventId)
            publisher.publish(outboxEvent)

            // update publishing timestamp
            outboxEvent.setToPublished()
        } catch (exception: RuntimeException) {
            /**
             * TODO try to find out which exception kafkaTemplate will throw as RuntimeException is not precise enough.
             */
            log.warn(
                "Publishing OutboxEvent with eventId: ${outboxEvent.eventId} failed. Publishing will be retried lated.",
                exception,
            )
        }
    }

    override fun relay(event: OutboxEvent) {
        repository.save(event)
        log.trace("Persisted OutboxEvent with eventId [{}].", event.eventId)
    }

    override fun relayImmediately(event: OutboxEvent) {
        relay(event)
        applicationEventPublisher.publishEvent(PublishOutboxEventImmediatelyEvent(event.eventId))
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handlePublishOutboxEventImmediatelyEvent(event: PublishOutboxEventImmediatelyEvent) {
        val outboxEvent = repository.findByEventId(event.eventId)

        if (null == outboxEvent || outboxEvent.hasBeenPublished) {
            return
        }

        publishEventAndSetToPublished(outboxEvent)
    }

    companion object {
        private val log = LoggerFactory.getLogger(OutboxMessageService::class.java)
    }
}

data class PublishOutboxEventImmediatelyEvent(val eventId: UUID)
