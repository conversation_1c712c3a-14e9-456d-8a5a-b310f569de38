/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.application.job

import com.emh.damagemanagement.outboxmessagerelay.application.OutboxMessageService
import java.util.*
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class MessageRelayJob(private val outboxMessageService: OutboxMessageService) : Job {

    override fun execute(context: JobExecutionContext?) {
        log.info("Starting OutboxMessage publisher job.")

        outboxMessageService.publishAllPendingMessages()

        log.info("Finished OutboxMessage publisher job.")
    }

    companion object {
        private val log = LoggerFactory.getLogger(MessageRelayJob::class.java)
    }
}

@Configuration
class MessageRelayJobConfig {

    @Bean
    @Qualifier("messageRelayJob")
    fun messageRelayJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(MessageRelayJob::class.java)
            .storeDurably()
            .withIdentity("MessageRelayJobDetail")
            .withDescription("Invoke message relay outbox job")
            .build()
    }

    @Bean
    fun messageRelayJobTrigger(
        @Qualifier("messageRelayJob") job: JobDetail,
        @Value("\${outbox-message-relay.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("MessageRelayJobTrigger")
            .withDescription("Message relay outbox job trigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
