/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.application.port

import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent

interface RelayEventUseCase {
    /** Will relay given event to outbox. Event will be persisted in the database and scheduled for publishing. */
    fun relay(event: OutboxEvent)

    fun relayImmediately(event: OutboxEvent)
}
