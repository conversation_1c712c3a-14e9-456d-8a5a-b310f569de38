/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.domain

import com.emh.damagemanagement.shared.domain.Auditable
import com.fasterxml.jackson.databind.JsonNode
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.*
import org.hibernate.annotations.Type

@Entity
@Table(name = "event_outbox")
class OutboxEvent(
    @Enumerated(EnumType.STRING) @Column(name = "event_type") val outboxEventType: OutboxEventType,
    @Column(name = "payload", columnDefinition = "json") @Type(JsonType::class) val payload: JsonNode,
) : Auditable() {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: OutboxEventId = OutboxEventId()

    @Column(name = "event_id") val eventId: UUID = UUID.randomUUID()

    @Enumerated(EnumType.STRING)
    @Column(name = "state")
    var state: OutboxEventState = OutboxEventState.CREATED
        private set

    @Column(name = "published_at")
    var publishedAt: OffsetDateTime? = null
        private set

    fun setToPublished() {
        state = OutboxEventState.PUBLISHED
        publishedAt = OffsetDateTime.now()
    }

    val hasBeenPublished
        get() = state == OutboxEventState.PUBLISHED
}

@Embeddable data class OutboxEventId(@Basic val value: UUID = UUID.randomUUID()) : Serializable

enum class OutboxEventState {
    CREATED,
    PUBLISHED,
}

enum class OutboxEventType {
    MONETARY_BENEFIT_ACCOUNTABLE,
    DAMAGE_FILE_COUNT,
}

data class OutboxEventPayload(val name: String, val age: Int)
