/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.domain

import java.util.*
import org.springframework.data.repository.Repository

interface OutboxEventRepository : Repository<OutboxEvent, UUID> {
    fun save(event: OutboxEvent)

    fun findByEventId(eventId: UUID): OutboxEvent?

    fun findAllByState(outboxEventState: OutboxEventState): List<OutboxEvent>
}
