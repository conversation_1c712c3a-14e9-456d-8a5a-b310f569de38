/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay

import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.fasterxml.jackson.databind.JsonNode
import kotlin.random.Random

class OutboxEventBuilder {
    private var outboxEventType: OutboxEventType = OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE
    private var payload: JsonNode = TestPayload("Some Value").toJsonNode()

    fun withEventType(outboxEventType: OutboxEventType): OutboxEventBuilder {
        this.outboxEventType = outboxEventType
        return this
    }

    fun withPayload(payload: JsonNode): OutboxEventBuilder {
        this.payload = payload
        return this
    }

    fun build(): OutboxEvent = OutboxEvent(outboxEventType = outboxEventType, payload = payload)

    fun build(function: OutboxEventBuilder.() -> OutboxEventBuilder) = this.function().build()

    companion object {
        fun build(function: OutboxEventBuilder.() -> OutboxEventBuilder) = OutboxEventBuilder().build(function)

        fun buildMultiple(count: Int): Set<OutboxEvent> = (1..count).map { OutboxEventBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

data class TestPayload(val value: String)
