/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.adapter.db

import com.emh.damagemanagement.outboxmessagerelay.IntegrationTest
import com.emh.damagemanagement.outboxmessagerelay.OutboxEventBuilder
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventRepository
import com.emh.damagemanagement.shared.TEST_USER
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired

@IntegrationTest
class OutboxEventRepositoryTest {
    @Autowired lateinit var outboxEventRepository: OutboxEventRepository

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should save and load event`() {
        val outboxEvent = OutboxEventBuilder().build()
        outboxEventRepository.save(outboxEvent)
        entityManager.flush()

        val outboxEvents = requireNotNull(outboxEventRepository.findByEventId(outboxEvent.eventId))

        assertThat(outboxEvents.createdBy).isEqualTo(TEST_USER)
        assertThat(outboxEvents.created).isNotNull()
    }
}
