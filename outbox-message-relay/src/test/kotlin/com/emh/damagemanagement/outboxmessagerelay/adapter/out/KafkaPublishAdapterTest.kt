package com.emh.damagemanagement.outboxmessagerelay.adapter.out

import com.emh.damagemanagement.outboxmessagerelay.IntegrationTest
import com.emh.damagemanagement.outboxmessagerelay.OutboxEventBuilder
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import java.time.LocalDate
import java.util.concurrent.atomic.AtomicInteger
import org.assertj.core.api.Assertions
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [KafkaPublishAdapterTest.KafkaTestConfig::class])
@EmbeddedKafka(
    controlledShutdown = true,
    partitions = 1,
    topics = ["\${monetary-benefit.accountable.kafka.producer.topic}"],
)
class KafkaPublishAdapterTest {
    @Autowired lateinit var kafkaTestListener: KafkaTestListener

    @Autowired lateinit var kafkaPublishAdapter: KafkaPublishAdapter

    @Test
    fun `expect test listener to receive one message when an event is published to its topic`() {
        whenIPublishAnEvent()
        thenIExpectTheListenerToBeCalled()
    }

    private fun whenIPublishAnEvent() {
        val outboxEvent =
            OutboxEventBuilder.build {
                withEventType(OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE)
                withPayload(
                    mapOf(
                            "employeeToBeCharged" to "P123",
                            "vin" to "V123",
                            "deductible" to 100.0,
                            "occurredAt" to LocalDate.now(),
                            "closedAt" to LocalDate.now(),
                            "licensePlate" to "PO - 123",
                            "claimClassification" to "Damage",
                            "leasingType" to "L9.1",
                            "pvccId" to "1234",
                            "orderNumber" to "100001",
                            "damageClaim" to 200.0,
                        )
                        .toJsonNode()
                )
            }
        kafkaPublishAdapter.publish(outboxEvent)
    }

    private fun thenIExpectTheListenerToBeCalled() {
        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.ONE_HUNDRED_MILLISECONDS untilAsserted
            {
                Assertions.assertThat(kafkaTestListener.counter.get()).isEqualTo(1)
            }
    }

    @TestConfiguration
    internal class KafkaTestConfig {
        @Bean fun kafkaTestListener(): KafkaTestListener = KafkaTestListener()
    }

    class KafkaTestListener {
        val counter = AtomicInteger(0)

        @KafkaListener(
            groupId = "kafkaTestListener",
            topics = ["\${monetary-benefit.accountable.kafka.producer.topic}"],
        )
        fun listen(payload: Any) {
            counter.incrementAndGet()
        }
    }
}
