package com.emh.damagemanagement.outboxmessagerelay.application.scheduler

import com.emh.damagemanagement.outboxmessagerelay.application.OutboxMessageService
import com.emh.damagemanagement.outboxmessagerelay.application.job.MessageRelayJob
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify

class MessageRelayJobTest {
    private lateinit var event: OutboxEvent

    private val outboxMessageService: OutboxMessageService = mock()

    @Test
    fun `should call OutboxMessageService on cron trigger`() {
        val job = MessageRelayJob(outboxMessageService)

        job.execute(null)

        verify(outboxMessageService, times(1)).publishAllPendingMessages()
    }
}
