/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.outboxmessagerelay.application.scheduler

import com.emh.damagemanagement.outboxmessagerelay.IntegrationTest
import com.emh.damagemanagement.outboxmessagerelay.OutboxEventBuilder
import com.emh.damagemanagement.outboxmessagerelay.application.OutboxMessageService
import com.emh.damagemanagement.outboxmessagerelay.application.port.MessagingOutPort
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventRepository
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventState
import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.argThat
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [OutboxMessageServiceTest.TestConfiguration::class])
class OutboxMessageServiceTest {

    @Autowired private lateinit var outboxMessageService: OutboxMessageService

    @Autowired private lateinit var outboxEventRepository: OutboxEventRepository

    @Autowired private lateinit var entityManager: EntityManager

    @Autowired private lateinit var messagingOutPort: MessagingOutPort

    @Test
    fun `should publish all pending messages`() {
        val unpublishedEvents = OutboxEventBuilder.buildMultiple(100)
        val publishedEvents = OutboxEventBuilder.buildMultiple(100).onEach { it.setToPublished() }

        unpublishedEvents.forEach { outboxEventRepository.save(it) }
        publishedEvents.forEach { outboxEventRepository.save(it) }
        entityManager.flush()
        val publishEventsCaptor = argumentCaptor<OutboxEvent>()

        outboxMessageService.publishAllPendingMessages()

        val unpublishedMessagesFromRepository = outboxEventRepository.findAllByState(OutboxEventState.CREATED)
        assertThat(unpublishedMessagesFromRepository).isEmpty()
        verify(messagingOutPort, times(unpublishedEvents.size)).publish(publishEventsCaptor.capture())
        assertThat(publishEventsCaptor.allValues)
            .extracting("eventId")
            .containsExactlyInAnyOrderElementsOf(unpublishedEvents.map { it.eventId })
    }

    @Test
    fun `should skip messages that fail publishing`() {
        val unpublishedEvents = OutboxEventBuilder.buildMultiple(100)
        val failingEvent = unpublishedEvents.random()
        unpublishedEvents.forEach { outboxEventRepository.save(it) }
        entityManager.flush()
        whenever(messagingOutPort.publish(argThat { event -> failingEvent.eventId == event.eventId }))
            .thenThrow(RuntimeException("Kafka ain´t what he used be."))

        outboxMessageService.publishAllPendingMessages()

        val unpublishedMessageFromRepository = outboxEventRepository.findAllByState(OutboxEventState.CREATED).single()
        assertThat(unpublishedMessageFromRepository)
            .usingRecursiveComparison()
            .ignoringFields(*TestJpaAuditingConfiguration.AUDIT_FIELDS)
            .isEqualTo(failingEvent)
    }

    internal class TestConfiguration {

        @Bean @Primary fun messagingOutPort(): MessagingOutPort = mock()
    }
}
