package com.emh.damagemanagement.outboxmessagerelay.domain

import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class OutboxEventTest {
    @Test
    fun `newly created OutboxEvent should have CREATED state and has not publishedAt DateTime`() {
        val outboxEvent =
            OutboxEvent(
                outboxEventType = OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE,
                payload = "Any Payload".toJsonNode(),
            )

        assertThat(outboxEvent.state).isEqualTo(OutboxEventState.CREATED)
        assertThat(outboxEvent.publishedAt).isNull()
    }

    @Test
    fun `a published OutboxEvent should have PUBLISHED state and a publishedAt DateTime`() {
        val outboxEvent =
            OutboxEvent(
                outboxEventType = OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE,
                payload = "Any Payload".toJsonNode(),
            )

        outboxEvent.setToPublished()
        assertThat(outboxEvent.state).isEqualTo(OutboxEventState.PUBLISHED)
        assertThat(outboxEvent.publishedAt).isNotNull()
    }
}
