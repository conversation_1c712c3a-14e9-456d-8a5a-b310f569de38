openapi: 3.0.3
info:
  title: PACE P40 Internal Order Number API
  description: PACE P40 Internal Order Number API using SPINE Gateway
  version: 1.0.0

servers:
  - url: 'https'

tags:
  - name: pace
    description: PACE

paths:
  /http/FMSInternalOrders/3541:
    get:
      operationId: requestInternalOrderNumber
      summary: Requests a new internal order number from PACE
      tags:
        - pace
      requestBody:
        $ref: '#/components/requestBodies/RequestInternalOrderNumberBody'
      responses:
        '200':
          $ref: '#/components/responses/RequestInternalOrderNumberResponse'

components:
  schemas:
    InternalOrderNumber:
      type: object
      description: Response when requesting an internal order number
      properties:
        n0:YCO_INT_ORDER_REPLICATION_FMSResponse:
          $ref: '#/components/schemas/FMSResponse'
      required:
        - n0:YCO_INT_ORDER_REPLICATION_FMSResponse

    FMSResponse:
      type: object
      properties:
        ET_RETURN:
          $ref: '#/components/schemas/ET_RETURN'
        EV_OBJNR:
          $ref: '#/components/schemas/EV_OBJNR'

    ET_RETURN:
      type: object

    EV_OBJNR:
      type: string
      description: the created internal order number

    RequestInternalOrderNumber:
      type: object
      description: Payload used to request a new iternal order number
      properties:
        ORDER_TYPE:
          $ref: '#/components/schemas/OrderType'
        ORDER_NAME:
          type: string
          description: short description for the request
        CO_AREA:
          type: string
          default: "0001"
          description: Kostenrechnungskreis
        COMP_CODE:
          type: string
          description: accounting area
        RESPCCTR:
          type: string
          description: redundant cost center property
        S_ORD_ITEM:
          type: string
          default: "000000"
          description: position within the order
        PERSON_RESP:
          type: string
          description: responsible person, employee number
        ESTIMATED_COSTS:
          type: string
          description: estimated costs
        APPLICATION_DATE:
          type: string
          description: date of order application
        DATE_WORK_BEGINS:
          type: string
          description: Arbeitsbeginn
          default: "0000-00-00"
        DATE_WORK_ENDS:
          type: string
          description: Arbeitsende
          default: "0000-00-00"
        PROCESSING_GROUP:
          type: string
          description: Verarbeitungsgruppe
          default: "00"
        PLN_RELEASE:
          type: string
          description: Geplantes Freigabedatum
          default: "0000-00-00"
        PLN_COMPLETION:
          type: string
          description: Geplantes Freigabedatum
          default: "0000-00-00"
        PLN_CLOSE:
          type: string
          description: Geplantes Abschlußdatum
          default: "0000-00-00"
        SETTL_TYPE:
          $ref: '#/components/schemas/SettlType'
        SOURCE:
          $ref: '#/components/schemas/Source'
        PERCENTAGE:
          type: string
          description: 100 for Y477, 0.00 otherwise
        COSTCENTER:
          type: string
          description: cost center
        VIN:
          type: string
          description: vin of the vehicle
      required:
        - ORDER_TYPE
        - ORDER_NAME
        - CO_AREA
        - COMP_CODE
        - RESPCCTR
        - S_ORD_ITEM
        - PERSON_RESP
        - ESTIMATED_COSTS
        - APPLICATION_DATE
        - DATE_WORK_BEGINS
        - DATE_WORK_ENDS
        - PROCESSING_GROUP
        - PLN_RELEASE
        - PLN_COMPLETION
        - PLN_CLOSE
        - PERCENTAGE
        - COSTCENTER
        - VIN

    SettlType:
      type: string
      description: FUL for Y477, null otherwise
      enum:
        - FUL

    Source:
      type: string
      description: GKO for Y477, null otherwise
      enum:
        - GKO

    OrderType:
      type: string
      description: the order type, use Y477 if driver is at fault, use Y479 if a third party is at fault
      enum:
        - Y477
        - Y479

  requestBodies:
    RequestInternalOrderNumberBody:
      required: true
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/RequestInternalOrderNumber'

  responses:
    RequestInternalOrderNumberResponse:
      description: Response for requesting internal order number
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/InternalOrderNumber'


  
