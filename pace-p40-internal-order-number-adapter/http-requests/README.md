# pace-p40-internal-order-number-service

## Getting started

Generate a http-client.private.env.json to safe the credentials on your local machine:
{
    "spine-gw-test": {
        "oauth_m2m_api_client_id": "<client_id>",
        "oauth_m2m_api_client_secret": "<client_secret>"
    }
}

### Credentials

All credentials are available on AWS Parameter Store:
- client_id: <AWS_PARAMETER_STORE:/damage-management-service/external/pace/clientid>
- clientsecret: <AWS_PARAMETER_STORE:/damage-management-service/external/pace/clientsecret>
- client name: W40760FMSBTP (Staging) | W_L1667XFMS (Prod)

### Use Cases

We have only 2 different use cases:
- third party fault: ORDER_TYPE=Y479
- own fault: ORDER_TYPE=Y477

### Further Information
- https://skyway.porsche.com/jira/browse/FPT1-264

### Support (PROD)
- ITSM Ticket: https://itsm-smartit.helix.porsche.services/smartit/app/#/create/incidentPV
- Assign to: <PERSON> | <PERSON> | <PERSON><PERSON><PERSON>
- A2R:
  - A2R_SST (Interface)
  - A2R_CO (SAP-Module)