### SAP IDP Test Token
POST https://{{host_idp}}/oauth/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
    &client_secret={{oauth_m2m_api_client_secret}}
    &grant_type=client_credentials

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Create internal order number for the case own fault (Y477)
## PLease note: It is designed from SPInE Team that we have to use a GET-Methode.
GET https://{{host_cpi}}/http/FMSInternalOrders/3541
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "ORDER_TYPE": "Y477",
  "ORDER_NAME": "Rep. Eigenve PV-CC 16 WP1ZZZ95ZKLB17399",
  "VIN": "WP1ZZZ95ZKLB17399",
  "CO_AREA": "0001",
  "COMP_CODE": "0001",
  "RESPCCTR": "0000005130",
  "S_ORD_ITEM": "000000",
  "PERSON_RESP": "00123456",
  "ESTIMATED_COSTS": "4500.49",
  "APPLICATION_DATE": "2024-11-13",
  "DATE_WORK_BEGINS": "0000-00-00",
  "DATE_WORK_ENDS": "0000-00-00",
  "PROCESSING_GROUP": "00",
  "PLN_RELEASE": "0000-00-00",
  "PLN_COMPLETION": "0000-00-00",
  "PLN_CLOSE": "0000-00-00",
  "SETTL_TYPE": "FUL",
  "SOURCE": "GKO",
  "PERCENTAGE": "100",
  "COSTCENTER": "0000005130"
}

### Create internal order number for the case of third party fault (Y479)
## PLease note: It is designed from SPInE Team that we have to use a GET-Methode.
GET https://{{host_cpi}}/http/FMSInternalOrders/3541
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "ORDER_TYPE": "Y479",
  "ORDER_NAME": "Rep. Fremdve BB-PL 765 WP1ZZZ95ZLLB3154",
  "VIN": "WP1ZZZ95ZLLB3154",
  "CO_AREA": "0001",
  "COMP_CODE": "0001",
  "RESPCCTR": "0000005129",
  "S_ORD_ITEM": "000000",
  "PERSON_RESP": "094862",
  "ESTIMATED_COSTS": "9500.49",
  "APPLICATION_DATE": "2024-11-13",
  "DATE_WORK_BEGINS": "0000-00-00",
  "DATE_WORK_ENDS": "0000-00-00",
  "PROCESSING_GROUP": "00",
  "PLN_RELEASE": "0000-00-00",
  "PLN_COMPLETION": "0000-00-00",
  "PLN_CLOSE": "0000-00-00",
  "SETTL_TYPE": null,
  "SOURCE": null,
  "PERCENTAGE": "0.00",
  "COSTCENTER": "0000005129"
}


### Create internal order number for the case maintanance of the vehicles (Y73X)
## PLease note: It is designed from SPInE Team that we have to use a GET-Methode.
GET https://{{host_cpi}}/http/FMSInternalOrders/3541
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "ORDER_TYPE": "Y73X",
  "ORDER_NAME": "Rep./Inst. S-PT 5166 WP0ZZZ98ZHS231363",
  "VIN": "WP0ZZZ98ZHS231363",
  "CO_AREA": "0001",
  "COMP_CODE": "0001",
  "RESPCCTR": "0000005130",
  "S_ORD_ITEM": "000000",
  "PERSON_RESP": "094862",
  "ESTIMATED_COSTS": "0.00",
  "APPLICATION_DATE": "0000-00-00",
  "DATE_WORK_BEGINS": "0000-00-00",
  "DATE_WORK_ENDS": "0000-00-00",
  "PROCESSING_GROUP": "00",
  "PLN_RELEASE": "0000-00-00",
  "PLN_COMPLETION": "0000-00-00",
  "PLN_CLOSE": "0000-00-00",
  "SETTL_TYPE": "PER",
  "SOURCE": null,
  "PERCENTAGE": "100.00",
  "COSTCENTER": "0000005130"
}


### Create internal order number for the case maintanance of the vehicles (Y73L)
## PLease note: It is designed from SPInE Team that we have to use a GET-Methode.
GET https://{{host_cpi}}/http/FMSInternalOrders/3541
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "ORDER_TYPE": "Y73L",
  "ORDER_NAME": "Rep./Inst. S-PT 5166 WP0ZZZ98ZHS231363",
  "VIN": "WP0ZZZ98ZHS231363",
  "CO_AREA": "0001",
  "COMP_CODE": "0001",
  "RESPCCTR": "0000005129",
  "S_ORD_ITEM": "000000",
  "PERSON_RESP": "0331438",
  "ESTIMATED_COSTS": "0.00",
  "APPLICATION_DATE": "0000-00-00",
  "DATE_WORK_BEGINS": "0000-00-00",
  "DATE_WORK_ENDS": "0000-00-00",
  "PROCESSING_GROUP": "00",
  "PLN_RELEASE": "0000-00-00",
  "PLN_COMPLETION": "0000-00-00",
  "PLN_CLOSE": "0000-00-00",
  "SETTL_TYPE": null,
  "SOURCE": null,
  "PERCENTAGE": "0.00",
  "COSTCENTER": null
}


### Create internal order number for the case maintanance of the vehicles (Y49B)
## PLease note: It is designed from SPInE Team that we have to use a GET-Methode.
GET https://{{host_cpi}}/http/FMSInternalOrders/3541
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "ORDER_TYPE": "Y49B",
  "ORDER_NAME": "Aufb.zum V S-DB 1885 WP0ZZZ98ZJS201760",
  "VIN": "WP0ZZZ98ZJS201760",
  "CO_AREA": "0001",
  "COMP_CODE": "0001",
  "RESPCCTR": "0000005609",
  "S_ORD_ITEM": "000000",
  "PERSON_RESP": null,
  "ESTIMATED_COSTS": "0.00",
  "APPLICATION_DATE": "0000-00-00",
  "DATE_WORK_BEGINS": "0000-00-00",
  "DATE_WORK_ENDS": "0000-00-00",
  "PROCESSING_GROUP": "00",
  "PLN_RELEASE": "0000-00-00",
  "PLN_COMPLETION": "0000-00-00",
  "PLN_CLOSE": "0000-00-00",
  "SETTL_TYPE": null,
  "SOURCE": null,
  "PERCENTAGE": "0.00",
  "COSTCENTER": null
}