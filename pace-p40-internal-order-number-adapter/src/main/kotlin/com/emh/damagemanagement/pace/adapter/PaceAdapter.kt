/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pace.adapter

import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPort
import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPortException
import com.emh.damagemanagement.pace.generated.adapter.out.rest.PaceApi
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.OrderTypeDto
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.RequestInternalOrderNumberDto
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.SettlTypeDto
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.SourceDto
import com.emh.damagemanagement.shared.converter.JsonConverter
import java.time.LocalDate
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class PaceAdapter(private val paceApi: PaceApi, private val jsonConverter: JsonConverter) : InternalOrderNumberOutPort {

    override fun requestInternalOrderNumber(
        employeeNumber: String,
        costCenter: String,
        usingCostCenter: String,
        driverIsAtFault: Boolean,
        licensePlate: String,
        vin: String,
        estimatedCosts: Double,
        damageDate: LocalDate,
    ): InternalOrderNumberOutPort.InternalOrderNumber {

        /**
         * We have to check if the created ORDER_NAME exceeds 40 characters. If so we build a "shortened" version. This
         * is done to avoid response error 500 from PACE interface, most likely thrown as a result of a 40 character DB
         * column constraint for orderName.
         *
         * Sadly this constraint is neither documented nor conveyed through a proper error message, so we are currently
         * stuck with 'educated guess'.
         */
        val expectedOrderName =
            buildOrderName(driverIsAtFault = driverIsAtFault, licensePlate = licensePlate, vin = vin)
        val orderNameExceedsMaxLength = ORDER_NAME_MAX_LENGTH < expectedOrderName.length
        val requestPayload =
            buildRequest(
                shortenOrderName = orderNameExceedsMaxLength,
                employeeNumber = employeeNumber,
                costCenter = costCenter,
                usingCostCenter = usingCostCenter,
                driverIsAtFault = driverIsAtFault,
                licensePlate = licensePlate,
                vin = vin,
                estimatedCosts = estimatedCosts,
                damageDate = damageDate,
            )

        log.info("Requesting new internal order number from PACE for vin $vin...")
        // just for debugging purposes, it´s also using default jackson config, so results may
        // differ from actual serialized request
        log.debug(jsonConverter.objectMapper.writeValueAsString(requestPayload))

        return try {
            paceApi
                .requestInternalOrderNumber(requestPayload)
                .block()
                ?.n0YCOINTORDERREPLICATIONFMSResponse
                ?.EV_OBJNR
                ?.let { InternalOrderNumberOutPort.InternalOrderNumber(it) }
                ?: throw InternalOrderNumberOutPortException(
                    "No internal order number was returned on internal order number request.",
                    null,
                )
        } catch (exception: PaceException) {
            throw InternalOrderNumberOutPortException(
                message = "Error while requesting a new internal order number.",
                cause = exception,
            )
        }
    }

    private fun buildRequest(
        shortenOrderName: Boolean = false,
        employeeNumber: String,
        costCenter: String,
        usingCostCenter: String,
        driverIsAtFault: Boolean,
        licensePlate: String,
        vin: String,
        estimatedCosts: Double,
        damageDate: LocalDate,
    ) =
        RequestInternalOrderNumberDto(
            ORDER_TYPE = if (driverIsAtFault) OrderTypeDto.Y477 else OrderTypeDto.Y479,
            ORDER_NAME =
                if (shortenOrderName)
                    buildOrderNameShort(driverIsAtFault = driverIsAtFault, licensePlate = licensePlate, vin = vin)
                else buildOrderName(driverIsAtFault = driverIsAtFault, licensePlate = licensePlate, vin = vin),
            CO_AREA = CO_AREA,
            COMP_CODE = COMP_CODE,
            // FPT1-986 responsible/using costCenter is provided explicitly now
            RESPCCTR = usingCostCenter,
            S_ORD_ITEM = S_ORDER_ITEM,
            PERSON_RESP = employeeNumber,
            ESTIMATED_COSTS = estimatedCosts.toString(),
            APPLICATION_DATE = damageDate.toString(),
            DATE_WORK_BEGINS = EMPTY_DATE,
            DATE_WORK_ENDS = EMPTY_DATE,
            PROCESSING_GROUP = PROCESSING_GROUP,
            PLN_RELEASE = EMPTY_DATE,
            PLN_COMPLETION = EMPTY_DATE,
            PLN_CLOSE = EMPTY_DATE,
            SETTL_TYPE = if (driverIsAtFault) SettlTypeDto.FUL else null,
            SOURCE = if (driverIsAtFault) SourceDto.GKO else null,
            PERCENTAGE = if (driverIsAtFault) PERCENTAGE_100 else PERCENTAGE_0,
            COSTCENTER = costCenter,
            VIN = vin,
        )

    private fun buildOrderName(driverIsAtFault: Boolean, licensePlate: String, vin: String): String {
        val stringJoiner = StringJoiner(" ").add(ORDER_NAME_PREFIX)
        if (driverIsAtFault) stringJoiner.add(ORDER_NAME_DRIVER_FAULT)
        else stringJoiner.add(ORDER_NAME_THIRD_PARTY_FAULT)
        return stringJoiner.add(licensePlate).add(vin).toString()
    }

    /**
     * This will build a 'shorter' OrderName to compensate for license plates with more than 9 characters, which in turn
     * would result in orderNames with more than 40 characters, which in turn would crash on PACE side (most likely DB
     * column constrains)
     */
    private fun buildOrderNameShort(driverIsAtFault: Boolean, licensePlate: String, vin: String): String {
        val stringJoiner = StringJoiner(" ").add(ORDER_NAME_PREFIX_SHORT)
        if (driverIsAtFault) stringJoiner.add(ORDER_NAME_DRIVER_FAULT_SHORT)
        else stringJoiner.add(ORDER_NAME_THIRD_PARTY_FAULT_SHORT)
        return stringJoiner.add(licensePlate).add(vin).toString()
    }

    companion object {
        private val log = LoggerFactory.getLogger(PaceAdapter::class.java)
        // assumed may character limit on SAP side (unverified)
        private const val ORDER_NAME_MAX_LENGTH = 40

        private const val ORDER_NAME_PREFIX = "Rep."
        private const val ORDER_NAME_PREFIX_SHORT = "Rep"
        private const val ORDER_NAME_DRIVER_FAULT = "Eigenve"
        private const val ORDER_NAME_DRIVER_FAULT_SHORT = "Eigen"
        private const val ORDER_NAME_THIRD_PARTY_FAULT = "Fremdve"
        private const val ORDER_NAME_THIRD_PARTY_FAULT_SHORT = "Fremd"
        private const val CO_AREA = "0001"
        private const val EMPTY_DATE = "0000-00-00"
        private const val S_ORDER_ITEM = "000000"
        private const val PROCESSING_GROUP = "00"
        private const val PERCENTAGE_100 = "100"
        private const val PERCENTAGE_0 = "0.00"
        private const val COMP_CODE = "0001"
    }
}
