/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pace.adapter.config

import com.emh.damagemanagement.pace.generated.adapter.out.rest.PaceApi
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class PaceApiConfig {
    @Bean fun paceApi(@Qualifier("pace") webClient: WebClient) = Pace<PERSON><PERSON>(webClient)
}
