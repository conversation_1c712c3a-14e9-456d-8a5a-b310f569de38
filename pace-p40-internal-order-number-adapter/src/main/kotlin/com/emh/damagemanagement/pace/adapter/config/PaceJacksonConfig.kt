/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pace.adapter.config

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.SerializationFeature
import java.time.ZoneId
import java.util.*
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

/** Dedicated jacksonConfig for PACE to deal with some PACE specific requirements */
@Configuration
class PaceJacksonConfig {

    @Bean
    @Qualifier("pace")
    fun paceJackson2ObjectMapperBuilder(): Jackson2ObjectMapperBuilder =
        Jackson2ObjectMapperBuilder()
            .featuresToEnable(
                DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY,
                DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS,
                JsonParser.Feature.ALLOW_COMMENTS,
                MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS,
                JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN,
            )
            .featuresToDisable(
                DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
            )
            // null values need to be included or PACE will crash
            .serializationInclusion(JsonInclude.Include.ALWAYS)
            .timeZone(TimeZone.getTimeZone(DEFAULT_ZONE_ID))
            .createXmlMapper(false)

    companion object {
        private val DEFAULT_ZONE_ID = ZoneId.of("UTC")
    }
}
