/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pace.adapter.config

import com.emh.damagemanagement.pace.adapter.PaceWebClientExceptionHandler
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.convert.converter.Converter
import org.springframework.http.MediaType
import org.springframework.http.RequestEntity
import org.springframework.http.codec.ClientCodecConfigurer
import org.springframework.http.codec.ServerCodecConfigurer
import org.springframework.http.codec.json.Jackson2JsonDecoder
import org.springframework.http.codec.json.Jackson2JsonEncoder
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.security.oauth2.client.AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ClientCredentialsReactiveOAuth2AuthorizedClientProvider
import org.springframework.security.oauth2.client.InMemoryReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.endpoint.OAuth2ClientCredentialsGrantRequest
import org.springframework.security.oauth2.client.endpoint.OAuth2ClientCredentialsGrantRequestEntityConverter
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.security.oauth2.client.web.server.AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository
import org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository
import org.springframework.util.CollectionUtils
import org.springframework.web.reactive.config.WebFluxConfigurer
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class PaceWebClientConfig {

    @Bean
    @Qualifier("pace")
    fun paceExchangeStrategies(
        @Qualifier("pace") jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder
    ): ExchangeStrategies =
        ExchangeStrategies.builder()
            .codecs { clientDefaultCodecsConfigurer: ClientCodecConfigurer ->
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonEncoder(
                        Jackson2JsonEncoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonDecoder(
                        Jackson2JsonDecoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
                clientDefaultCodecsConfigurer.defaultCodecs().enableLoggingRequestDetails(true)
            }
            .build()

    @Bean
    @Qualifier("pace")
    fun reactivePaceClientRegistrationRepository(
        clientRegistrationRepository: ClientRegistrationRepository
    ): ReactiveClientRegistrationRepository {
        return InMemoryReactiveClientRegistrationRepository(
            clientRegistrationRepository.findByRegistrationId(CLIENT_REGISTRATION_ID)
        )
    }

    @Bean
    @Qualifier("pace")
    fun reactivePaceAuthorizedClientService(
        @Qualifier("pace") clientRegistrationRepository: ReactiveClientRegistrationRepository
    ): ReactiveOAuth2AuthorizedClientService {
        return InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrationRepository)
    }

    @Bean
    @Qualifier("pace")
    fun reactivePaceAuthorizedClientRepository(
        @Qualifier("pace") authorizedClientService: ReactiveOAuth2AuthorizedClientService
    ): ServerOAuth2AuthorizedClientRepository {
        return AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository(authorizedClientService)
    }

    class CustomClientCredentialsGrantRequestEntityConverter :
        Converter<OAuth2ClientCredentialsGrantRequest, org.springframework.util.MultiValueMap<String, String>> {
        private val defaultRequestEntityConverter: Converter<OAuth2ClientCredentialsGrantRequest, RequestEntity<*>> =
            OAuth2ClientCredentialsGrantRequestEntityConverter()

        override fun convert(
            source: OAuth2ClientCredentialsGrantRequest
        ): org.springframework.util.MultiValueMap<String, String> {
            return CollectionUtils.toMultiValueMap(emptyMap())
        }
    }

    @Bean
    @Qualifier("pace")
    fun reactivePaceAuthorizedClientManager(
        @Qualifier("pace") reactiveRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("pace") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ReactiveOAuth2AuthorizedClientManager {
        return AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(
                reactiveRegistrationRepository,
                authorizedClientService,
            )
            .apply { setAuthorizedClientProvider(ClientCredentialsReactiveOAuth2AuthorizedClientProvider()) }
    }

    @Bean
    @Qualifier("pace")
    fun paceWebClient(
        @Qualifier("pace") reactiveRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("pace") authorizedClientRepository: ServerOAuth2AuthorizedClientRepository,
        @Qualifier("pace") reactiveOAuth2AuthorizedClientManager: ReactiveOAuth2AuthorizedClientManager,
        paceWebClientProperties: PaceWebClientProperties,
        @Qualifier("pace") exchangeStrategies: ExchangeStrategies,
        paceWebClientExceptionHandler: PaceWebClientExceptionHandler,
    ): WebClient {
        val oauth2Client = ServerOAuth2AuthorizedClientExchangeFilterFunction(reactiveOAuth2AuthorizedClientManager)
        // ExchangeFilterFunction already has an apply function, so we do it the imperative way
        oauth2Client.setDefaultClientRegistrationId(CLIENT_REGISTRATION_ID)
        return WebClient.builder()
            .baseUrl(paceWebClientProperties.baseUrl)
            .exchangeStrategies(exchangeStrategies)
            .filter(
                ExchangeFilterFunction.ofResponseProcessor(paceWebClientExceptionHandler::clientErrorResponseProcessor)
            )
            .filter(paceWebClientExceptionHandler::clientErrorRequestProcessor)
            .filter(oauth2Client)
            .build()
    }

    companion object {
        const val CLIENT_REGISTRATION_ID = "pace"
    }
}

@ConfigurationProperties("pace") data class PaceWebClientProperties(val baseUrl: String)

@Configuration
class MyConfig : WebFluxConfigurer {
    override fun configureHttpMessageCodecs(configurer: ServerCodecConfigurer) {
        configurer.defaultCodecs().enableLoggingRequestDetails(true)
    }
}
