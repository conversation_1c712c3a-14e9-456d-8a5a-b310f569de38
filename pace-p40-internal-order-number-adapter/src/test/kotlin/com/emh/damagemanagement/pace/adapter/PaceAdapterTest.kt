/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pace.adapter

import com.emh.damagemanagement.damagefilemigration.application.port.InternalOrderNumberOutPortException
import com.emh.damagemanagement.pace.IntegrationTest
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.OrderTypeDto
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.RequestInternalOrderNumberDto
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.SettlTypeDto
import com.emh.damagemanagement.pace.generated.adapter.out.rest.model.SourceDto
import java.time.LocalDate
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.security.oauth2.core.OAuth2AuthorizationException
import org.springframework.test.context.TestPropertySource
import org.springframework.util.ReflectionUtils

/**
 * This class is missing some tests, that use the actual function provided by the adapter. This is, because
 * MockWebServer does not support (actively throws exceptions) when using GET with requestBodies (see
 * https://github.com/square/okhttp/issues/5803 and https://github.com/square/okhttp/issues/5852).
 *
 * As we are forces to take the API as is, and we do not want to add another mock server for a single APU we are
 * skipping integration tests here. If, for some reason API get updated to a proper one add the missing tests here
 */
@IntegrationTest
@TestPropertySource(properties = ["pace.base-url=http://localhost:8092"])
class PaceAdapterTest {

    @Autowired private lateinit var paceAdapter: PaceAdapter

    private lateinit var mockWebServer: MockWebServer

    private val buildRequestFunction =
        requireNotNull(
            ReflectionUtils.findMethod(
                PaceAdapter::class.java,
                "buildRequest",
                Boolean::class.java,
                String::class.java,
                String::class.java,
                String::class.java,
                Boolean::class.java,
                String::class.java,
                String::class.java,
                Double::class.java,
                LocalDate::class.java,
            )
        )

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8092)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    @Test
    fun `should throw InternalOrderNumberOutPortException when token could not be obtained`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder().code(401).build()
                }
            }

        assertThatExceptionOfType(InternalOrderNumberOutPortException::class.java)
            .isThrownBy { requestInternalOrderNumber(true) }
            .withCauseInstanceOf(PaceException::class.java)
            .withRootCauseInstanceOf(OAuth2AuthorizationException::class.java)
    }

    @Test
    fun `should create correct request payload when requesting internal order number and driver is at fault`() {
        ReflectionUtils.makeAccessible(buildRequestFunction)
        val date = LocalDate.now()
        val result =
            ReflectionUtils.invokeMethod(
                buildRequestFunction,
                paceAdapter,
                false,
                "094862",
                "**********",
                "**********",
                true,
                "S-PL 1234",
                "WP1ZZZ95ZLL",
                4500.49,
                date,
            ) as RequestInternalOrderNumberDto

        assertThat(result)
            .satisfies({
                assertThat(it.ORDER_TYPE).isEqualTo(OrderTypeDto.Y477)
                assertThat(it.ORDER_NAME).isEqualTo("Rep. Eigenve S-PL 1234 WP1ZZZ95ZLL")
                assertThat(it.CO_AREA).isEqualTo("0001")
                assertThat(it.RESPCCTR).isEqualTo("**********")
                assertThat(it.S_ORD_ITEM).isEqualTo("000000")
                assertThat(it.PERSON_RESP).isEqualTo("094862")
                assertThat(it.ESTIMATED_COSTS).isEqualTo("4500.49")
                assertThat(it.APPLICATION_DATE).isEqualTo(date.toString())
                assertThat(it.DATE_WORK_BEGINS).isEqualTo("0000-00-00")
                assertThat(it.DATE_WORK_ENDS).isEqualTo("0000-00-00")
                assertThat(it.PROCESSING_GROUP).isEqualTo("00")
                assertThat(it.PLN_RELEASE).isEqualTo("0000-00-00")
                assertThat(it.PLN_COMPLETION).isEqualTo("0000-00-00")
                assertThat(it.PLN_CLOSE).isEqualTo("0000-00-00")
                assertThat(it.SETTL_TYPE).isEqualTo(SettlTypeDto.FUL)
                assertThat(it.SOURCE).isEqualTo(SourceDto.GKO)
                assertThat(it.PERCENTAGE).isEqualTo("100")
                assertThat(it.COSTCENTER).isEqualTo("**********")
                assertThat(it.VIN).isEqualTo("WP1ZZZ95ZLL")
            })
    }

    @Test
    fun `should create correct request payload when requesting internal order number and driver is not at fault`() {
        ReflectionUtils.makeAccessible(buildRequestFunction)
        val date = LocalDate.now()
        val result =
            ReflectionUtils.invokeMethod(
                buildRequestFunction,
                paceAdapter,
                false,
                "094862",
                "**********",
                "**********",
                false,
                "S-PL 1234",
                "WP1ZZZ95ZLL",
                4500.49,
                date,
            ) as RequestInternalOrderNumberDto

        assertThat(result)
            .satisfies({
                assertThat(it.ORDER_TYPE).isEqualTo(OrderTypeDto.Y479)
                assertThat(it.ORDER_NAME).isEqualTo("Rep. Fremdve S-PL 1234 WP1ZZZ95ZLL")
                assertThat(it.CO_AREA).isEqualTo("0001")
                assertThat(it.RESPCCTR).isEqualTo("**********")
                assertThat(it.S_ORD_ITEM).isEqualTo("000000")
                assertThat(it.PERSON_RESP).isEqualTo("094862")
                assertThat(it.ESTIMATED_COSTS).isEqualTo("4500.49")
                assertThat(it.APPLICATION_DATE).isEqualTo(date.toString())
                assertThat(it.DATE_WORK_BEGINS).isEqualTo("0000-00-00")
                assertThat(it.DATE_WORK_ENDS).isEqualTo("0000-00-00")
                assertThat(it.PROCESSING_GROUP).isEqualTo("00")
                assertThat(it.PLN_RELEASE).isEqualTo("0000-00-00")
                assertThat(it.PLN_COMPLETION).isEqualTo("0000-00-00")
                assertThat(it.PLN_CLOSE).isEqualTo("0000-00-00")
                assertThat(it.SETTL_TYPE).isNull()
                assertThat(it.SOURCE).isNull()
                assertThat(it.PERCENTAGE).isEqualTo("0.00")
                assertThat(it.COSTCENTER).isEqualTo("**********")
                assertThat(it.VIN).isEqualTo("WP1ZZZ95ZLL")
            })
    }

    @Test
    fun `should create request payload with shortened orderName`() {
        ReflectionUtils.makeAccessible(buildRequestFunction)
        val date = LocalDate.now()
        val result =
            ReflectionUtils.invokeMethod(
                buildRequestFunction,
                paceAdapter,
                true,
                "094862",
                "**********",
                "**********",
                false,
                "PV-CC 1234",
                "WP1ZZZ95ZLL",
                0.0,
                date,
            ) as RequestInternalOrderNumberDto

        assertThat(result.ORDER_NAME).isEqualTo("Rep Fremd PV-CC 1234 WP1ZZZ95ZLL")
    }

    private fun requestInternalOrderNumber(driverIsAtFault: Boolean) {
        paceAdapter.requestInternalOrderNumber(
            employeeNumber = "094862",
            costCenter = "**********",
            usingCostCenter = "**********",
            driverIsAtFault = driverIsAtFault,
            licensePlate = "S-PL 1234",
            vin = "WP1ZZZ95ZLL",
            estimatedCosts = 4500.49,
            damageDate = LocalDate.now(),
        )
    }
}
