:toc:
:numbered:
= PVCC-P40-Monetary-Benefit-Adapter

== Module description

This module integrates the PVCC via Streamzilla (Kafka).

== Getting started

=== Apache Kafka [[apache-kafka]]

We are using https://kafka.apache.org/[Apache Kafka] for testing Filezilla (Porsche managed Kafka) related functionality.
This requires you (for local tests) to have a https://hub.docker.com/r/bitnami/kafka[Apache Kafka container] or pod running.

==== Docker

----
docker run -d --rm --name kafka-server --hostname kafka-server --network app-tier -p "9094:9094" -e KAFKA_CFG_NODE_ID=0 -e KAFKA_CFG_PROCESS_ROLES=controller,broker -e KAFKA_CFG_LISTENERS=PLAINTEXT://:9092,CONTROLLER://:9093,EXTERNAL://:9094 -e KAFKA_CFG_ADVERTISED_LISTENERS=PLAINTEXT://kafka:9092,EXTERNAL://localhost:9094 -e <PERSON>AFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP=CONTROLLER:PLAINTEXT,EXTERNAL:PLAINTEXT,PLAINTEXT:PLAINTEXT -e KAFKA_CFG_CONTROLLER_QUORUM_VOTERS=0@kafka-server:9093 -e KAFKA_CFG_CONTROLLER_LISTENER_NAMES=CONTROLLER bitnami/kafka:latest
----

==== Kubernetes

----
helm install -f kafka_deployment.yaml kafka oci://registry-1.docker.io/bitnamicharts/kafka
----

may also require additional port forwarding

----
kubectl port-forward svc/kafka-controller-0-external 31000:9094
----

For debugging the Kafka plugin for JetBrains IDEs is recommended.

==== Configuration

The configuration for listeners and for producers can be found here:
link:/src/main/kotlin/com/damagemanagement/damagereport/adapter/in/kafka/KafkaConfiguration.kt[damagereport/adapter/in/messaging/KafkaConfiguration.kt].

Additionally, the following values can be set in the application properties file:

* _kafka.bootstrap-servers_ - this is the address of the Kafka Server
* _kafka.retry.interval_ - sets the intervall for a FixedBackoff retry strategy
* _kafka.retry.maxAttempts_ - sets the number of retries for a FixedBackoff retry strategy

There is only a retry, when the listener fails with a link:/src/main/kotlin/com/damagemanagement/damagereport/adapter/in/kafka/exception/RetryableException.kt[RetryableException].
After the defined number of attempts, the failing message is pushed to a dead letter topic (dead letter topic name: < _topic name of failed message_ >.DLT).
Messages that fail with a link:/src/main/kotlin/com/damagemanagement/damagereport/adapter/in/kafka/exception/NotRetryableException.kt[NotRetryableException]
will be pushed directly to the dead letter topic.

==== Spring Boot Kafka

Messages that are produced with Spring Boot Kafka will have a header `_TYPE_` that will indicate the Class of the
message that was serialized.

_e.g.:_

----
_TYPE_: com.emh.damagemanagement.hcm.damagereport.adapter.in.kafka.events.MonetaryBenefitAccountedEvent
----

This information can be used for deserialization.
