/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pvcc.adapter.`in`.rest

import com.emh.damagemanagement.generated.pvcc.adapter.rest.DamageMonetaryBenefitService
import com.emh.damagemanagement.generated.pvcc.model.MonetaryBenefitAccountableEvent
import com.emh.damagemanagement.outboxmessagerelay.adapter.out.EventOutboxPublisherAdapter
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import org.springframework.stereotype.Component

@Component
class MonetaryBenefitAdapter(val outboxPublisher: EventOutboxPublisherAdapter) : DamageMonetaryBenefitService {
    override fun damageMonetaryBenefitAccountablePost(
        monetaryBenefitAccountableEvent: MonetaryBenefitAccountableEvent
    ): MonetaryBenefitAccountableEvent {
        outboxPublisher.publish(
            OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE,
            monetaryBenefitAccountableEvent.toJsonNode(),
        )
        return monetaryBenefitAccountableEvent
    }
}
