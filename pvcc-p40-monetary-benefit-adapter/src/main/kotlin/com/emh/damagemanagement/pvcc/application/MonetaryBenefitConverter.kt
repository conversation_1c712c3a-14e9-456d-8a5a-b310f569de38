/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pvcc.application

import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.generated.pvcc.model.MonetaryBenefitAccountableEvent

fun DamageFile.toMonetaryBenefitAccountableEvent(damageFileWasReopened: Boolean): MonetaryBenefitAccountableEvent {

    val claimClassification =
        when (this.insuranceCoverage) {
            InsuranceCoverage.UNKNOWN -> NO_INSURANCE_COVER
            InsuranceCoverage.FULL -> FULL_INSURANCE_COVER
            InsuranceCoverage.PARTIAL -> PARTIAL_INSURANCE_COVER
            InsuranceCoverage.THIRD_PARTY -> NO_INSURANCE_COVER
            InsuranceCoverage.NOT_SPECIFIED -> NO_INSURANCE_COVER
            InsuranceCoverage.NOT_COVERED -> NO_INSURANCE_COVER
            else -> NO_INSURANCE_COVER
        }
    return MonetaryBenefitAccountableEvent(
        employeeToBeCharged = this.employeeToBeCharged.value,
        vin = this.vehicle.vin,
        vguid = this.vehicle.vGuid,
        /**
         * FPT1-535 after talks with PVCC it was decided to send 0, instead of not sending an event at all, when
         * deductible or damageClaim is null.
         *
         * This is done to ensure that the same number of events exists in both systems, and we do not send an UPDATE
         * event without having a CREATED event before.
         */
        deductible = this.deductible?.value?.toDouble() ?: 0.toDouble(),
        damageClaim = this.claimTowardsEmployee?.value?.toDouble() ?: 0.toDouble(),
        occurredAt = damageDate.withNano(0),
        licensePlate = sanitizeLicensePlate(this.vehicle.licensePlate),
        claimClassification = claimClassification,
        /**
         * cut nanos, to ensure that closedDate is the same for initial event and reopen (where we get the timestamp
         * from DB) this is done to compensate for postgres` lack of precision with timestamps
         */
        closedAt = requireNotNull(this.closedDate) { "DamageFile does not have closedDate set." }.withNano(0),
        leasingType = this.vehicle.leasingArt,
        orderNumber = this.internalOrderNumber?.value,
        pvccId = this.key.value.toInt(),
        action =
            if (damageFileWasReopened) MonetaryBenefitAccountableEvent.Action.UPDATED
            else MonetaryBenefitAccountableEvent.Action.CREATED,
    )
}

/** PVCC can´t deal with licensePlate suffixes like 'E' or 'H', so we are removing them beforehand. */
private fun sanitizeLicensePlate(licensePlate: String?): String {
    if (licensePlate == null) return ""
    return licensePlate.trimEnd('e', 'E', 'h', 'H')
}

const val NO_INSURANCE_COVER = 11
const val FULL_INSURANCE_COVER = 12
const val PARTIAL_INSURANCE_COVER = 13
