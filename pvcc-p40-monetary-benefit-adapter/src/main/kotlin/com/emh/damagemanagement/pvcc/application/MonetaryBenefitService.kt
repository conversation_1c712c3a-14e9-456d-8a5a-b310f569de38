/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pvcc.application

import com.emh.damagemanagement.damagefile.application.DamageFileFinder
import com.emh.damagemanagement.damagefile.domain.DamageFileClosedEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileNotFoundException
import com.emh.damagemanagement.outboxmessagerelay.application.port.RelayEventUseCase
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

/**
 * A service that listens for [com.emh.damagemanagement.damagefile.domain.DamageFileClosedEvent]s and provides matching
 * events to the outbox adapter.
 */
@Component
class MonetaryBenefitService(
    private val relayEventUseCase: RelayEventUseCase,
    private val damageFileFinder: DamageFileFinder,
) {

    /**
     * Hand [DamageFileClosedEvent] synchronously. Ideally, we are still within the initial transaction and have access
     * to the new or updated damage file
     */
    @EventListener
    fun handleDamageFileClosedEvent(event: DamageFileClosedEvent) {
        log.info(
            "Received DamageFileClosedEvent for damage file with key [${event.damageFileKey.value}]. Sending MonetaryBenefitAccountable Event..."
        )
        val damageFile =
            try {
                damageFileFinder.getDamageFile(event.damageFileKey)
            } catch (exception: DamageFileNotFoundException) {
                log.warn(
                    "Could not find damage file with key [${event.damageFileKey.value}] for DamageFileClosedEvent. Skipping event relay."
                )
                return
            }

        // FPT1-482 do not send damages files marked as external
        if (damageFile.isExternal) {
            log.info(
                "Damage file with key [${event.damageFileKey.value}] is marked as external and will not be relayed."
            )
            return
        }

        // convert and relay MonetaryBenefitAccountableEvent
        val monetaryBenefitAccountableEvent =
            try {
                damageFile.toMonetaryBenefitAccountableEvent(damageFileWasReopened = event.hasBeenReopened)
            } catch (exception: IllegalArgumentException) {
                log.warn(
                    "Error while trying to create MonetaryBenefitAccountableEvent. ${exception.message}. Skipping event relay."
                )
                null
            }
        // if event could be successfully converted, relay it to message outbox here
        monetaryBenefitAccountableEvent?.also {
            relayEventUseCase.relay(
                OutboxEvent(outboxEventType = OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE, payload = it.toJsonNode())
            )
            log.info("Successfully relayed MonetaryBenefitAccountableEvent with pvccId [${it.pvccId}] to outbox.")
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(MonetaryBenefitService::class.java)
    }
}
