package com.emh.damagemanagement.pvcc

import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import com.emh.damagemanagement.shared.WebTestClientConfiguration
import com.emh.damagemanagement.shared.security.SecurityConfiguration
import java.lang.annotation.Inherited
import org.junit.jupiter.api.Tag
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import org.springframework.transaction.annotation.Transactional

@Tag("integration")
@SpringBootTest(classes = [TestPvccApplication::class, TestJpaAuditingConfiguration::class])
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@EmbeddedKafka(
    controlledShutdown = true,
    partitions = 1,
    topics = ["\${monetary-benefit.accountable.kafka.producer.topic}"],
)
@Transactional
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
annotation class IntegrationTest

@Tag("integration")
@SpringBootTest(
    webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT,
    classes =
        [
            TestPvccApplication::class,
            WebTestClientConfiguration::class,
            SecurityConfiguration::class,
            TestJpaAuditingConfiguration::class,
        ],
)
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Transactional
@Inherited
annotation class WebServiceTest
