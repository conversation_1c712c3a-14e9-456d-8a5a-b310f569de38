/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pvcc.application

import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.VehicleBuilder
import com.emh.damagemanagement.damagefile.domain.DamageFile
import com.emh.damagemanagement.damagefile.domain.InsuranceCoverage
import com.emh.damagemanagement.generated.pvcc.model.MonetaryBenefitAccountableEvent
import java.util.function.Consumer
import java.util.stream.Stream
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class MonetaryBenefitConverterTest {
    @ParameterizedTest
    @MethodSource("closedDamageFileProvider")
    fun `should convert DamageFile to MonetaryBenefitAccountableEvent`(damageFile: DamageFile) {
        val damageFileHasBeenReopened = Random.nextBoolean()
        val event = damageFile.toMonetaryBenefitAccountableEvent(damageFileWasReopened = damageFileHasBeenReopened)
        assertThat(event).satisfies(damageFileRequirements(damageFile, damageFileHasBeenReopened))
    }

    @Test
    fun `should convert DamageFile to MonetaryBenefitAccountableEvent even if deductible and damageClaim are null`() {
        val damageFile = DamageFileBuilder().deductible(null).claimTowardsEmployee(null).closed().build()
        val event = damageFile.toMonetaryBenefitAccountableEvent(damageFileWasReopened = false)
        assertThat(event).satisfies(damageFileRequirements(damageFile, false))
    }

    @Test
    fun `should remove trailing E or H suffixes from license plates`() {
        val damageFiles =
            listOf("E", "H", "e", "h").map { suffix ->
                DamageFileBuilder().vehicle(VehicleBuilder().licensePlate("AB-CD 3334$suffix").build()).closed().build()
            }
        val convertedEvents = damageFiles.map { it.toMonetaryBenefitAccountableEvent(damageFileWasReopened = false) }

        assertThat(convertedEvents).allSatisfy { assertThat(it.licensePlate).isEqualTo("AB-CD 3334") }
    }

    @Test
    fun `should map insurance coverage to claim classification`() {
        val damageFileHasBeenReopened = Random.nextBoolean()
        val fullInsuranceEvent =
            DamageFileBuilder()
                .closed()
                .insuranceCoverage(InsuranceCoverage.FULL)
                .build()
                .toMonetaryBenefitAccountableEvent(damageFileWasReopened = damageFileHasBeenReopened)
        val partialInsuranceEvent =
            DamageFileBuilder()
                .closed()
                .insuranceCoverage(InsuranceCoverage.PARTIAL)
                .build()
                .toMonetaryBenefitAccountableEvent(damageFileWasReopened = damageFileHasBeenReopened)
        val eventsWithNoInsurance =
            listOf(
                    DamageFileBuilder().closed().insuranceCoverage(InsuranceCoverage.UNKNOWN).build(),
                    DamageFileBuilder().closed().insuranceCoverage(InsuranceCoverage.THIRD_PARTY).build(),
                    DamageFileBuilder().closed().insuranceCoverage(InsuranceCoverage.NOT_SPECIFIED).build(),
                    DamageFileBuilder().closed().insuranceCoverage(InsuranceCoverage.NOT_COVERED).build(),
                )
                .map { it.toMonetaryBenefitAccountableEvent(damageFileWasReopened = damageFileHasBeenReopened) }

        assertThat(eventsWithNoInsurance).allSatisfy {
            assertThat(it.claimClassification).isEqualTo(NO_INSURANCE_COVER)
        }
        assertThat(fullInsuranceEvent.claimClassification).isEqualTo(FULL_INSURANCE_COVER)
        assertThat(partialInsuranceEvent.claimClassification).isEqualTo(PARTIAL_INSURANCE_COVER)
    }

    companion object {

        private fun damageFileRequirements(damageFile: DamageFile, reopened: Boolean) =
            Consumer<MonetaryBenefitAccountableEvent> { monetaryBenefitAccountableEvent: MonetaryBenefitAccountableEvent
                ->
                assertThat(monetaryBenefitAccountableEvent.employeeToBeCharged)
                    .isEqualTo(damageFile.employeeToBeCharged.value)
                assertThat(monetaryBenefitAccountableEvent.vin).isEqualTo(damageFile.vehicle.vin)
                assertThat(monetaryBenefitAccountableEvent.vguid).isEqualTo(damageFile.vehicle.vGuid)
                if (null == damageFile.deductible) {
                    assertThat(monetaryBenefitAccountableEvent.deductible).isEqualTo(0.toDouble())
                } else {
                    assertThat(monetaryBenefitAccountableEvent.deductible)
                        .isEqualTo(damageFile.deductible?.value?.toDouble())
                }
                if (null == damageFile.claimTowardsEmployee) {
                    assertThat(monetaryBenefitAccountableEvent.damageClaim).isEqualTo(0.toDouble())
                } else {
                    assertThat(monetaryBenefitAccountableEvent.damageClaim)
                        .isEqualTo(damageFile.claimTowardsEmployee?.value?.toDouble())
                }
                assertThat(monetaryBenefitAccountableEvent.closedAt).isEqualTo(damageFile.closedDate?.withNano(0))
                assertThat(monetaryBenefitAccountableEvent.occurredAt).isEqualTo(damageFile.damageDate.withNano(0))
                assertThat(monetaryBenefitAccountableEvent.licensePlate).isEqualTo(damageFile.vehicle.licensePlate)
                assertThat(monetaryBenefitAccountableEvent.leasingType).isEqualTo(damageFile.vehicle.leasingArt)
                assertThat(monetaryBenefitAccountableEvent.orderNumber).isEqualTo(damageFile.internalOrderNumber?.value)
                assertThat(monetaryBenefitAccountableEvent.pvccId).isEqualTo(damageFile.key.value.toInt())
                if (reopened) {
                    assertThat(monetaryBenefitAccountableEvent.action)
                        .isEqualTo(MonetaryBenefitAccountableEvent.Action.UPDATED)
                } else {
                    assertThat(monetaryBenefitAccountableEvent.action)
                        .isEqualTo(MonetaryBenefitAccountableEvent.Action.CREATED)
                }
            }

        @JvmStatic
        private fun closedDamageFileProvider(): Stream<Arguments> =
            (0..10).map { DamageFileBuilder().closed().build() }.map { Arguments.of(it) }.stream()
    }
}
