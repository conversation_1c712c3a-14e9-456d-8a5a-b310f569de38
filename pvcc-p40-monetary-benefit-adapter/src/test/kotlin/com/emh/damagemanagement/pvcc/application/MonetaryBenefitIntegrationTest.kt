/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pvcc.application

import com.emh.damagemanagement.damagefile.DamageFileNewOrUpdateBuilder
import com.emh.damagemanagement.damagefile.application.port.CreateOrUpdateDamageFileUseCase
import com.emh.damagemanagement.outboxmessagerelay.application.port.MessagingOutPort
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventRepository
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventState
import com.emh.damagemanagement.pvcc.IntegrationTest
import jakarta.persistence.EntityManager
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [MonetaryBenefitIntegrationTest.TestConfiguration::class])
class MonetaryBenefitIntegrationTest {

    @Autowired private lateinit var outboxEventRepository: OutboxEventRepository
    @Autowired private lateinit var createOrUpdateDamageFileUseCase: CreateOrUpdateDamageFileUseCase

    @Autowired private lateinit var entityManager: EntityManager

    @Test
    fun `should process DamageFileClosedEvent and save outbox event`() {
        val damageFileNew = DamageFileNewOrUpdateBuilder().closed().build()

        /**
         * create with closed damage file will cause a DamageFileClosedEvent, which will trigger MonetaryBenefitService
         * event handler
         */
        createOrUpdateDamageFileUseCase.createOrUpdateDamageFile(damageFileNew)
        entityManager.flush()

        val persistedOutboxEvents = outboxEventRepository.findAllByState(outboxEventState = OutboxEventState.CREATED)
        assertThat(persistedOutboxEvents).hasSize(1)
    }

    internal class TestConfiguration {

        @Bean @Primary fun messagingOutPort(): MessagingOutPort = mock()
    }
}
