/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.pvcc.application

import com.emh.damagemanagement.damagefile.DamageFileBuilder
import com.emh.damagemanagement.damagefile.domain.DamageFileClosedEvent
import com.emh.damagemanagement.damagefile.domain.DamageFileKey
import com.emh.damagemanagement.damagefile.domain.DamageFileRepository
import com.emh.damagemanagement.outboxmessagerelay.application.port.MessagingOutPort
import com.emh.damagemanagement.outboxmessagerelay.application.port.RelayEventUseCase
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEvent
import com.emh.damagemanagement.outboxmessagerelay.domain.OutboxEventType
import com.emh.damagemanagement.pvcc.IntegrationTest
import jakarta.persistence.EntityManager
import java.time.OffsetDateTime
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.never
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [MonetaryBenefitServiceTest.TestConfiguration::class])
class MonetaryBenefitServiceTest {

    @Autowired private lateinit var relayEventUseCase: RelayEventUseCase
    @Autowired private lateinit var monetaryBenefitService: MonetaryBenefitService
    @Autowired private lateinit var damageFileRepository: DamageFileRepository

    @Autowired private lateinit var entityManager: EntityManager

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(relayEventUseCase)
    }

    @Test
    fun `should handle DamageFileClosedEvent`() {
        val damageFile = DamageFileBuilder().closed().build()
        damageFileRepository.save(damageFile)
        entityManager.flush()

        val damageFileClosedEvent =
            DamageFileClosedEvent(
                damageFileKey = damageFile.key,
                occurredOn = OffsetDateTime.now(),
                externalReference = damageFile.externalReference,
                hasBeenReopened = false,
            )

        val relayOutboxEventArgumentCaptor = argumentCaptor<OutboxEvent>()
        monetaryBenefitService.handleDamageFileClosedEvent(damageFileClosedEvent)

        verify(relayEventUseCase).relay(relayOutboxEventArgumentCaptor.capture())
        assertThat(relayOutboxEventArgumentCaptor.firstValue.outboxEventType)
            .isEqualTo(OutboxEventType.MONETARY_BENEFIT_ACCOUNTABLE)
    }

    @Test
    fun `should not relay event if damage file is marked as external`() {
        val damageFile = DamageFileBuilder().isExternal(true).closed().build()
        damageFileRepository.save(damageFile)
        entityManager.flush()

        val damageFileClosedEvent =
            DamageFileClosedEvent(
                damageFileKey = damageFile.key,
                occurredOn = OffsetDateTime.now(),
                externalReference = damageFile.externalReference,
                hasBeenReopened = false,
            )

        monetaryBenefitService.handleDamageFileClosedEvent(damageFileClosedEvent)

        verify(relayEventUseCase, times(0)).relay(any())
    }

    @Test
    fun `should not rollback if DamageFile for DamageFileClosedEvent could not be found`() {
        val damageFileClosedEvent =
            DamageFileClosedEvent(
                damageFileKey = DamageFileKey("no key"),
                occurredOn = OffsetDateTime.now(),
                externalReference = null,
                hasBeenReopened = false,
            )

        assertThatNoException().isThrownBy { monetaryBenefitService.handleDamageFileClosedEvent(damageFileClosedEvent) }
        verify(relayEventUseCase, never()).relay(any())
    }

    @Test
    fun `should still relay outboxEvent if damage file does not have deductible set`() {
        val damageFile = DamageFileBuilder().deductible(null).closed().build()
        damageFileRepository.save(damageFile)
        entityManager.flush()

        val damageFileClosedEvent =
            DamageFileClosedEvent(
                damageFileKey = damageFile.key,
                occurredOn = OffsetDateTime.now(),
                externalReference = damageFile.externalReference,
                hasBeenReopened = false,
            )
        assertThatNoException().isThrownBy { monetaryBenefitService.handleDamageFileClosedEvent(damageFileClosedEvent) }
        verify(relayEventUseCase).relay(any())
    }

    @Test
    fun `should still relay outboxEvent if damage file does not have claim towards employee set`() {
        val damageFile = DamageFileBuilder().claimTowardsEmployee(null).closed().build()
        damageFileRepository.save(damageFile)
        entityManager.flush()

        val damageFileClosedEvent =
            DamageFileClosedEvent(
                damageFileKey = damageFile.key,
                occurredOn = OffsetDateTime.now(),
                externalReference = damageFile.externalReference,
                hasBeenReopened = false,
            )
        assertThatNoException().isThrownBy { monetaryBenefitService.handleDamageFileClosedEvent(damageFileClosedEvent) }
        verify(relayEventUseCase).relay(any())
    }

    internal class TestConfiguration {

        @Bean @Primary fun messagingOutPort(): MessagingOutPort = mock()

        @Bean @Primary fun relayEventUseCase(): RelayEventUseCase = mock()
    }
}
