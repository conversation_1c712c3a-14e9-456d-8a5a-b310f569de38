:toc:
:numbered:
= Repairfix-Client

== Module description

This module hold the generated code for the repairfix rest client. If you need some connection to repaitfix, just use this module and you are good to go.

== How to

=== Update repairfix API

to update repairfix API get the newest version.
https://api.b2b-staging.repairfix.eu/docs/porsche-json[Staging]. Then copy (overwrite)the content into link:./api/repairfix_api.json[the existing API].

As repairfix API is most likely invalid and inconsistent to the actual data provided you have to perform some additional manual steps:

. replace all occurrences of *"String"* with *"string"*
. replace all occurrences of *"boolean | null"* with *"boolean"*
. replace all occurrences of **"string | null"'** with *"string"*
. remove *all* required properties from *ReporterInfo* (_#/components/schemas/ReporterInfo_) schema
. add
+
[,yaml]
----
    "front_left_angle",
    "rear_right_angle",
    "left_diagonal_view",
    "right_diagonal_view",
    "inspection",
    "vehicle_rear_side_view",
    "vehicle_front_side_view",
    "vehicle_driver_side_view",
    "vehicle_passenger_side_view",
----
+
as enum value to *MechanicalReportImage.type*
. remove any *examples* (with the trailing s)
. remove any duplicate tag definitions
. replace *DamageReportResponse* with custom specification
+
[,yaml]
----
 "DamageReportResponse": {
     "type": "object",
     "properties": {
       "damageReport": {
         "type": "object",
         "additionalProperties": true,
         "properties": {
           "type": {
             "type": "string",
             "description": "Damage report type",
             "example": "ExteriorDamageReport",
             "enum": [
               "ExteriorDamageReport",
               "InteriorDamageReport",
               "ServiceLightsReport",
               "MechanicalDamageReport",
               "CompoundDamageReport"
             ]
           }
         },
         "required": [
           "type"
         ]
       }
     },
     "required": [
       "damageReport"
     ]
   }
----
+
This is done to avoid isseus with polymorphism caused by some not so generator-friendly use of oneOf in the original repairfix specification.

. Update the above "Interface" if we need some additional properties on this abstract level, before force-casting to subtype
. remove *totalCosts* required property from *InvoicesList* (_#/components/schemas/InvoicesList_) schema
. add *invoice* enum value to *DamageDocument.type* (_#/components/schemas/DamageDocument_) schema
. replace enum values of *CarPart.type* (_#/components/schemas/CarPart_) schema to following
+
[,yaml]
----
"type": {
            "type": "string",
            "description": "Damaged part's type",
            "enum": [
              "a_pillar_left",
              "a_pillar_right",
              "body_sill_left_front",
              "body_sill_left_rear",
              "body_sill_right_front",
              "body_sill_right_rear",
              "brake_light_left_rear",
              "brake_light_right_rear",
              "bumper_center_front",
              "bumper_center_rear",
              "bumper_left_front",
              "bumper_left_rear",
              "bumper_right_front",
              "bumper_right_rear",
              "bumper_side_left_front",
              "bumper_side_left_rear",
              "bumper_side_right_front",
              "bumper_side_right_rear",
              "b_pillar_left",
              "b_pillar_right",
              "cargo_box_side_left",
              "cargo_box_side_right",
              "condenser",
              "c_pillar_left",
              "c_pillar_right",
              "door_handle_left_front",
              "door_handle_left_rear",
              "door_handle_rear",
              "door_handle_right_front",
              "door_handle_right_rear",
              "door_left_front",
              "door_left_rear",
              "door_quarter_left_front",
              "door_quarter_left_rear",
              "door_quarter_right_front",
              "door_quarter_right_rear",
              "door_right_front",
              "door_right_rear",
              "d_pillar_left",
              "d_pillar_right",
              "fender_left_front",
              "fender_left_rear",
              "fender_right_front",
              "fender_right_rear",
              "fog_light_left_front",
              "fog_light_right_front",
              "fuel_filter_cap_cover",
              "headlights_left",
              "headlights_right",
              "hood",
              "license_plate_holder_front",
              "license_plate_holder_rear",
              "logo_front",
              "logo_rear",
              "mirror_left",
              "mirror_right",
              "missing",
              "panel_left_rear",
              "panel_right_rear",
              "quarter_panel_left_rear",
              "quarter_panel_right_rear",
              "radiator_grille_front",
              "rim_left_front",
              "rim_left_rear",
              "rim_right_front",
              "rim_right_rear",
              "roof",
              "roof_antenna",
              "roof_rail_left",
              "roof_rail_right",
              "side_marker_lights_left",
              "side_marker_lights_right",
              "step_rear",
              "sunroof",
              "tailgate",
              "tailgate_door_handle_rear",
              "taillights_left",
              "taillights_right",
              "tire_left_front",
              "tire_left_rear",
              "tire_right_front",
              "tire_right_rear",
              "trunk",
              "trunk_left",
              "trunk_right",
              "wheel_left_front",
              "wheel_left_rear",
              "wheel_right_front",
              "wheel_right_rear",
              "window_left_front",
              "window_left_rear",
              "window_rear",
              "window_right_front",
              "window_right_rear",
              "windshield_front",
              "console",
              "door",
              "floor",
              "front_panel",
              "other",
              "panels_and_covers",
              "seats",
              "air_conditioning_and_heating",
              "brakes",
              "chassis_and_steering",
              "electronics",
              "engine",
              "maintenance",
              "tyres",
              "a_pillar_left1",
              "a_pillar_left2",
              "a_pillar_right1",
              "a_pillar_right2",
              "bumper_rear1",
              "bumper_rear2",
              "bumper_rear3",
              "bumper_rear5",
              "b_pillar_left1",
              "b_pillar_left2",
              "b_pillar_right1",
              "b_pillar_right2",
              "cargo_left_rear1",
              "cargo_left_rear2",
              "cargo_left_rear3",
              "cargo_left_rear4",
              "cargo_right_rear1",
              "cargo_right_rear2",
              "cargo_right_rear3",
              "cargo_right_rear4",
              "door_left_front1",
              "door_left_front2",
              "door_left_front3",
              "door_left_front4",
              "door_left_rear1",
              "door_left_rear2",
              "door_left_rear3",
              "door_left_rear4",
              "door_quarter_left_rear1",
              "door_quarter_left_rear2",
              "door_quarter_right_rear1",
              "door_quarter_right_rear2",
              "door_right_front1",
              "door_right_front2",
              "door_right_front3",
              "door_right_front4",
              "door_right_rear1",
              "door_right_rear2",
              "door_right_rear3",
              "door_right_rear4",
              "fender_left_front1",
              "fender_left_front2",
              "fender_left_rear1",
              "fender_left_rear2",
              "fender_left_rear3",
              "fender_left_rear4",
              "fender_right_front1",
              "fender_right_front2",
              "fender_right_rear1",
              "fender_right_rear2",
              "fender_right_rear3",
              "fender_right_rear4",
              "hood1",
              "hood2",
              "hood3",
              "hood4",
              "roof1",
              "roof2",
              "roof3",
              "roof4",
              "roof5",
              "sunroof1",
              "sunroof2",
              "tailgate1",
              "tailgate2",
              "tailgate3",
              "tailgate4",
              "window_rear1",
              "window_rear2",
              "windshield_front1",
              "windshield_front2",
              "windshield_front3",
              "windshield_front4"
            ],
            "example": "left_headlights"
          }
----
+
. remove all *required* properties form *CarInsurance* except for _id, createdAt, updatedAt and insuranceCompany_
. replace within _visibilityScope_ of *CreateUserDto* and *UpdateUserDto*
+
[,yaml]
----
"allOf": [
              {
                "$ref": "#/components/schemas/VisibilityScopeDto"
              }
            ]
----
with
+
[,yaml]
----
            "type": "array",
            "items": {
              "$ref": "#/components/schemas/VisibilityScopeDto"
            }
----
+
. replace *BadRequestErrorResponse.message* with
+
[,yaml]
----
 "message": {
    "description": "Error message",
    "example": "Bad data provided",
    "type": "array",
    "items": {
      "type": "string"
    }
}
----
+
. replace *InteriorDamageReport.damageLocation* with
+
[,yaml]
----
"damageLocation": {
    "type": "array",
    "items": {
      "type": "string",
      "enum": [
        "console",
        "door",
        "floor",
        "front_panel",
        "other",
        "panels_and_covers",
        "roof",
        "seats",
        "trunk"
      ]
    },
    "description": "Interior damage locations",
    "example": [
      "door",
      "floor"
    ],
    "nullable": true
  }
----
+
Finally, run `gradlew :repairfix-client:setupGeneratedSources` and you are good to go

=== Add a new API

To add a new API to the configuration navigate to link:./src/main/kotlin/com/emh/damagemanagement/repairfix/adapter/config/RepairfixApiConfig.kt[RepairfixApiConfig] and add a new bean for the API you need, using the provided repairfix-webclient.
