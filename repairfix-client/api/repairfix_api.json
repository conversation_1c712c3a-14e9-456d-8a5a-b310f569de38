{"openapi": "3.0.0", "paths": {"/porsche/v1/web-app": {"post": {"operationId": "PorscheWebAppController_init", "summary": "Initialize new web app", "description": "This method initialize web app and returns link to damage reporting app", "parameters": [], "requestBody": {"required": true, "description": "The payload with app initialization options. Returns web url to open it in a web view.\n\nThe link may have two types of authorization and the type is determined automatically:\n  - ***car*** auth\n\n    in this case, the link can be used to skip car identification step and provides all the reporting options for the car\n\n  - ***report*** auth\n\n    in this case, the link can be used to complete a single report for the car\n\nThe authorization type is determined by the presence of the following fields, in which case the ***report*** auth is applied:\n  - ***externalId***\n  - ***reportPartnerData***\n  - ***flow***\n", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebAppInitDto"}}}}, "responses": {"200": {"description": "It returns web url to open it in a web view.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebAppInitData"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when car cannot be found by vin", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Init web app"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports": {"get": {"operationId": "PorscheDamageReportsController_list", "summary": "Get fleet damage reports list", "description": "This method returns a list of all damage reports registered for a fleet", "parameters": [{"name": "page", "required": false, "in": "query", "description": "Damages report list page number", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of the damage reports on the page", "schema": {"example": 20, "type": "number"}}, {"name": "sortBy", "required": false, "in": "query", "description": "Damage reports list sorting field", "schema": {"example": "Created", "enum": ["Model", "Make", "LicensePlate", "Status", "Number", "Location", "Reported", "Created", "Type", "FleetName", "St<PERSON>er<PERSON><PERSON>", "BodyshopName", "Vin", "InsuranceCoverage", "PartnerId", "CarParts", "Tag", "IsAccident", "Roadworthy"], "type": "string"}}, {"name": "sortOrder", "required": false, "in": "query", "description": "Damage reports list sorting type", "schema": {"example": "DESC", "enum": ["ASC", "DESC"], "type": "string"}}, {"name": "dateFilterField", "required": false, "in": "query", "description": "Field to apply date range filter (createdAt or updatedAt)", "schema": {"default": "createdAt", "example": "createdAt", "enum": ["createdAt", "updatedAt"], "type": "string"}}, {"name": "fromDate", "required": false, "in": "query", "description": "Date range filter from date (yyyy-mm-dd)", "schema": {"example": "2023-02-03", "type": "string"}}, {"name": "toDate", "required": false, "in": "query", "description": "Date range filter to date (yyyy-mm-dd)", "schema": {"example": "2023-02-08", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Parameter to filter damage reports list by status", "schema": {"example": ["closed"], "type": "array", "items": {"type": "string", "enum": ["link_sent", "new", "estimation_postponed", "estimation_requested", "repair_offer_pending_expert_approval", "inspection_on_site", "inspection_on_site_approved", "inspection_scheduled", "repair_offer_estimated", "waiting_for_approval", "repair_offer_accepted", "repair_offer_rejected", "repair_offer_adjusted", "repair_scheduled", "internally_repaired", "in_repair", "repaired", "in_settlement", "settled", "closed"]}}}, {"name": "closeReason", "required": false, "in": "query", "description": "Parameter to filter damage reports list by closeReason", "schema": {"example": ["repaired"], "type": "array", "items": {"type": "string", "enum": ["repaired", "will_no_be_repaired", "duplicate", "not_a_damage", "wrong_damage", "other"]}}}, {"name": "vin", "required": false, "in": "query", "description": "Parameter to filter damage reports list by car vin", "schema": {"example": "JTEHT05J542053195", "type": "string"}}, {"name": "fleetPartnerId", "required": false, "in": "query", "description": "Parameter to filter damage reports list by sub fleet", "schema": {"example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "type": "string"}}, {"name": "carPartnerId", "required": false, "in": "query", "description": "Parameter to filter damage reports list by car identifier", "schema": {"example": "qwe:12345", "type": "string"}}, {"name": "tag", "required": false, "in": "query", "description": "Parameter to filter damage reports list by Tag", "schema": {"example": ["Tag-1"], "type": "array", "items": {"type": "string"}}}, {"name": "type", "required": false, "in": "query", "description": "Damage report type", "schema": {"example": "Breakdown", "enum": ["Breakdown"], "type": "string"}}, {"name": "reportPartnerData", "required": false, "in": "query", "description": "Filter reports using a nested object structure of report partner data.", "style": "deepObject", "explode": true, "schema": {"example": {"customerNumber": "CN-1823", "databaseId": "DB-Germany-288", "reservationNumber": "RN-133212-1"}, "type": "object"}}], "responses": {"200": {"description": "It returns the list of damage reports", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportsListData"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponseExample"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}": {"get": {"operationId": "PorscheDamageReportController_damageReportById", "summary": "Get damage report details", "description": "This method returns damage report details by damage report id or external id.", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "responses": {"200": {"description": "It returns a damage report by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportResponse"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The damage report can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}, "patch": {"operationId": "PorscheDamageReportUpdateController_update", "summary": "Update damage report by id or external id", "description": "This method updates damage report by id or external id", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage report identifier", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "requestBody": {"required": true, "description": "Update damage report", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportUpdateDto"}}}}, "responses": {"200": {"description": "It returns result of the update damage report operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponseExample"}}}}, "404": {"description": "The response when damage cannot be found by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/documents/{documentId}": {"get": {"operationId": "PorscheDamageReportController_documentById", "summary": "Get document by document id", "description": "This method returns document (file stream) by document id.", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}, {"name": "documentId", "required": true, "in": "path", "description": "Document ID", "schema": {"format": "uuid", "example": "23c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "responses": {"200": {"description": "It returns a damage report document (file stream) by id"}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "The document can't be found for this environment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The document can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/documents": {"get": {"operationId": "PorscheDamageReportController_getDocumentsList", "summary": "Get documents by damage report id", "description": "This method returns documents list by damage report id.", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "responses": {"200": {"description": "It returns a damage report documents list by the damage report id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageReportDocumentsList"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The damage report can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/status": {"put": {"operationId": "PorscheDamageReportController_damageReportChangeStatus", "summary": "Change damage report status", "description": "This method changes damage report status by damage report id or external id.", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DamageStatusChangeDto"}}}}, "responses": {"200": {"description": "It operation result", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "400": {"description": "Some of required parameters were missed in request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BadRequestErrorResponse"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "Current damage report is not assignable to this status.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The damage report can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/assessment": {"post": {"operationId": "PorscheDamageReportController_requestEmailAssessmentReport", "summary": "Request a damage assessment report.", "description": "This method requests a damage assessment report.", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestAssessmentReportDto"}}}}, "responses": {"200": {"description": "It returns the result of request assessment method call", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "400": {"description": "Some of required parameters were missed in request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BadRequestErrorResponse"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "In case the request is forbidden", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The damage report can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/images/{imageId}": {"get": {"operationId": "PorscheDamageReportImagesController_image", "summary": "Get damage report image", "description": "This method returns damage report image by image Id.", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}, {"name": "imageId", "required": true, "in": "path", "description": "Damage Image ID", "schema": {"format": "uuid", "example": "aef446d5-aa5e-466b-a9f6-5fbdf2d8407e", "type": "string"}}, {"name": "type", "required": false, "in": "query", "description": "Type of the image. The original image will be returned if the type is not provided", "schema": {"example": "mobile", "enum": ["thumbnail", "mobile", "desktop"], "type": "string"}}], "responses": {"200": {"description": "It returns a damage report image (file stream) by id"}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The image can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/request-repair-offers": {"post": {"operationId": "PorscheRequestRepairOfferController_createRepairOfferRequest", "summary": "Create new request for repair offer", "description": "This method makes reqest to provide repair offer estimation data", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "requestBody": {"required": true, "description": "Comment regarding the estimation request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestRepairOfferDto"}}}}, "responses": {"200": {"description": "It returns boolean if request was successfuly created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RequestRepairOffer"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "In case the request is forbidden"}, "404": {"description": "If the damage report can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "If the request cannot be processed for some reasons", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Damage reports"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers": {"get": {"operationId": "PorscheRepairOffersController_repairOfferList", "summary": "Repair offer list", "description": "This method returns repair offers of the damage report", "parameters": [{"name": "damageReportId", "required": false, "in": "query", "description": "Damage report id", "schema": {"example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}, {"name": "fleetPartnerId", "required": false, "in": "query", "description": "Parameter for filter damage reports list by sub fleet", "schema": {"example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Parameter for filter repair offers list by statuses", "schema": {"example": ["new"], "type": "array", "items": {"type": "string", "enum": ["requested", "waiting_for_approval", "inspection_required", "inspection_approved", "expert_approval_required", "new", "accepted", "cancelled", "rejected"]}}}, {"name": "carId", "required": false, "in": "query", "description": "Parameter for filter repair offers list by car identifier", "schema": {"example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}, {"name": "vin", "required": false, "in": "query", "description": "Parameter for filter repair offers list by car vin", "schema": {"example": "JTEHT05J542053195", "type": "string"}}, {"name": "bodyshopId", "required": false, "in": "query", "description": "Parameter for filter repair offers list by bodyshop", "schema": {"example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}, {"name": "page", "required": false, "in": "query", "description": "Damages report list page number", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of the damage reports on the page", "schema": {"example": 20, "type": "number"}}], "responses": {"200": {"description": "It returns damage report repair offers list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RepairOfferList"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The repair offer can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/repair-offers": {"get": {"operationId": "PorscheRepairOffersController_damageRepairOfferList", "summary": "Repair offer list", "description": "This method returns repair offers of the damage report", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}, {"name": "status", "required": false, "in": "query", "description": "Parameter for filter repair offers list by statuses", "schema": {"example": ["new"], "type": "array", "items": {"type": "string", "enum": ["requested", "waiting_for_approval", "inspection_required", "inspection_approved", "expert_approval_required", "new", "accepted", "cancelled", "rejected"]}}}], "responses": {"200": {"description": "It returns damage report repair offers list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RepairOfferList"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The repair offer can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers/{repairOfferId}": {"get": {"operationId": "PorscheRepairOffersController_repairOfferDetails", "summary": "Repair offer details", "description": "This method returns a repair offer by repair offer id.", "parameters": [{"name": "repairOfferId", "required": true, "in": "path", "description": "Repair offer id", "schema": {"format": "uuid", "example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}], "responses": {"200": {"description": "It returns a repair offer by id", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RepairOfferDetails"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The repair offer can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers/{repairOfferId}/accept": {"put": {"operationId": "PorscheRepairOffersController_acceptRepairOffer", "summary": "Accept repair offer", "description": "This method accepts repair offer", "parameters": [{"name": "repairOfferId", "required": true, "in": "path", "description": "Repair offer id", "schema": {"format": "uuid", "example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}], "responses": {"200": {"description": "It returns the result of repair offer accept method call", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "Repair offer status can not be changed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The repair offer can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers/{repairOfferId}/reject": {"put": {"operationId": "PorscheRepairOffersController_rejectRepairOffer", "summary": "Reject repair offer", "description": "This method rejects repair offer", "parameters": [{"name": "repairOfferId", "required": true, "in": "path", "description": "Repair offer id", "schema": {"format": "uuid", "example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectRepairOfferDto"}}}}, "responses": {"200": {"description": "It returns the result of repair offer reject method call", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "Repair offer status can not be changed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The repair offer can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers/{repairOfferId}/appointments/{appointmentId}/accept": {"put": {"operationId": "PorscheRepairOffersController_acceptAppointment", "summary": "Accept an appointment", "description": "This method accepts an appointment", "parameters": [{"name": "repairOfferId", "required": true, "in": "path", "description": "Repair offer id", "schema": {"format": "uuid", "example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}, {"name": "appointmentId", "required": true, "in": "path", "description": "Appointment id or appointment date", "schema": {"example": "5f6d6390-1d6d-4d85-a0b4-ec6cda089d5a | 2020-10-17T05:00:00.000Z", "type": "string"}}], "responses": {"200": {"description": "It returns the result of appointment accept method call", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "In case additional validation fails", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The repair offer/appointment cannot be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers/{repairOfferId}/appointments/request-custom-appointment": {"put": {"operationId": "PorscheRepairOffersController_acceptCustomAppointment", "summary": "Accept custom appointment", "description": "This method accepts custom appointment", "parameters": [{"name": "repairOfferId", "required": true, "in": "path", "description": "Repair offer id", "schema": {"format": "uuid", "example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}], "responses": {"200": {"description": "It returns the result of repair offer accept appointment method call", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "Repair offer status can not be changed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The repair offer can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/fleets": {"post": {"operationId": "PorscheFleetsController_add", "summary": "Onboard new fleet", "description": "This method adds new fleet", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FleetOnboardingDto"}}}}, "responses": {"200": {"description": "It returns result of the fleet onboarding operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "409": {"description": "The response when conflict exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConflictErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableErrorResponse"}}}}}, "tags": ["Fleets"], "security": [{"bearer": []}]}, "get": {"operationId": "PorscheFleetsController_getList", "summary": "Get fleets list", "description": "This method returns fleets list", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Use this parameter to specify how many records to you want to display", "schema": {"default": 20, "example": 20, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Use this parameter to specify page", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "parentFleetId", "required": false, "in": "query", "description": "Use this parameter to filter by \"parentFleetId\". Only internal system IDs are supported", "schema": {"example": "268e609e-63fe-46ba-acbd-a29fd8cabbb0", "type": "string"}}, {"name": "partnerId", "required": false, "in": "query", "description": "Use this parameter to filter by \"partnerId\"", "schema": {"example": "qwerty:1234567", "type": "string"}}, {"name": "search", "required": false, "in": "query", "description": "Use this parameter to search by \"partnerId\" or \"title\" or \"name\"", "schema": {"example": "ACME", "type": "string"}}], "responses": {"200": {"description": "It returns fleets list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FleetsList"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}}, "tags": ["Fleets"], "security": [{"bearer": []}]}}, "/porsche/v1/fleets/{fleetPartnerId}/configurations": {"get": {"operationId": "PorscheFleetsController_getConfiguration", "summary": "Get fleet configuration", "description": "This method returns fleet configuration", "parameters": [{"name": "fleetPartnerId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "It returns fleet configuration", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FleetConfigurationData"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Fleets"], "security": [{"bearer": []}]}}, "/porsche/v1/fleets/{fleetPartnerId}": {"delete": {"operationId": "PorscheFleetsController_deleteFleet", "summary": "Removes a fleet", "description": "Removes a fleet along with all related data. Cars, users, drivers, damage reports etc. Cannot be reverted", "parameters": [{"name": "fleetPartnerId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "Removed fleet information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FleetRemoval"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Fleets"], "security": [{"bearer": []}]}}, "/porsche/v1/cars": {"post": {"operationId": "PorscheCarsController_add", "summary": "Add cars to the fleet", "description": "This method adds new cars to the fleet", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddCarDto"}}}}}, "responses": {"200": {"description": "It returns result of the create car", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when fleet cannot be found by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Cars"], "security": [{"bearer": []}]}, "get": {"operationId": "PorscheCarsController_get", "summary": "Get cars list", "description": "This method returns cars list for fleet", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Use this parameter to specify how many records to you want to display", "schema": {"default": 20, "example": 20, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Use this parameter to specify page", "schema": {"default": 1, "example": 1, "type": "number"}}, {"name": "isPool", "required": false, "in": "query", "description": "Parameter for filter cars list by isPool parameter", "schema": {"example": true, "type": "boolean"}}, {"name": "fleetPartnerId", "required": false, "in": "query", "description": "Parameter for filter cars list by sub fleet id or partner fleet id", "schema": {"example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "type": "string"}}, {"name": "availability", "required": false, "in": "query", "description": "Parameter for filter cars list by availability", "schema": {"example": "AVAILABLE", "enum": ["AVAILABLE", "UNAVAILABLE", "PARTIALLY_AVAILABLE"], "type": "string"}}, {"name": "location", "required": false, "in": "query", "description": "Parameter for filter cars list by locations", "schema": {"example": "Berlin", "type": "string"}}, {"name": "make", "required": false, "in": "query", "description": "Parameter for filter cars list by make name", "schema": {"example": "Toyota", "type": "string"}}, {"name": "model", "required": false, "in": "query", "description": "Parameter for filter cars list by model name", "schema": {"example": "<PERSON><PERSON><PERSON>", "type": "string"}}], "responses": {"200": {"description": "It returns cars list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarsList"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when sub fleet cannot be found by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "409": {"description": "The response when conflict exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConflictErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Cars"], "security": [{"bearer": []}]}}, "/porsche/v1/fleets/{fleetPartnerId}/cars": {"post": {"operationId": "PorscheCarsController_addDeprecated", "summary": "Add cars to the fleet. Deprecated. Suggest using POST /cars instead", "description": "This method adds new cars to the fleet", "deprecated": true, "parameters": [{"name": "fleetPartnerId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/AddCarDeprecatedDto"}}}}}, "responses": {"200": {"description": "It returns result of the create car", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when fleet cannot be found by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Cars"], "security": [{"bearer": []}]}}, "/porsche/v1/cars/{carIdentifier}": {"get": {"operationId": "PorscheCarsController_getCar", "summary": "Get car by id, vin or partner id", "description": "This method returns car by id, vin or partner id", "parameters": [{"name": "carIdentifier", "required": true, "in": "path", "description": "car identifier (id, vin or partner id)", "schema": {"type": "string"}}], "responses": {"200": {"description": "It returns car by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarDetails"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when fleet cannot be found by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Cars"], "security": [{"bearer": []}]}, "patch": {"operationId": "PorscheCarsController_update", "summary": "Update car by id, vin or partner id", "description": "This method updates cars by id, vin or partner id", "parameters": [{"name": "carIdentifier", "required": true, "in": "path", "description": "car identifier (id, vin or partner id)", "schema": {"type": "string"}}], "requestBody": {"required": true, "description": "Update car", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateCarDto"}}}}, "responses": {"200": {"description": "It returns updated car", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CarDetails"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when fleet cannot be found by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["Cars"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers/{repairOfferId}/adjustments/{adjustmentId}/accept": {"put": {"operationId": "PorscheRepairOffersAdjustmentsController_acceptAdjustment", "summary": "Accept repair offer adjustment", "description": "This method accepts repair offer adjustment", "parameters": [{"name": "adjustmentId", "required": true, "in": "path", "description": "Repair offer adjustment id", "schema": {"format": "uuid", "example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}, {"name": "repairOfferId", "required": true, "in": "path", "description": "Repair offer id", "schema": {"format": "uuid", "example": "3aa7409b-37f2-4cf2-8c19-3c24f49881d9", "type": "string"}}], "responses": {"200": {"description": "It returns the result of repair offer adjustment accept method call", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "400": {"description": "The adjustment can't be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BadRequestErrorResponse"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "Repair offer status can not be changed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The adjustment can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/repair-offers/{repairOfferId}/adjustments/{adjustmentId}/reject": {"put": {"operationId": "PorscheRepairOffersAdjustmentsController_rejectAdjustment", "summary": "Reject repair offer adjustment", "description": "This method rejects repair offer adjustment", "parameters": [{"name": "adjustmentId", "required": true, "in": "path", "description": "Repair offer adjustment id", "schema": {"format": "uuid", "example": "f088f905-ed45-42bd-90db-39f843405aa6", "type": "string"}}, {"name": "repairOfferId", "required": true, "in": "path", "description": "Repair offer id", "schema": {"format": "uuid", "example": "3aa7409b-37f2-4cf2-8c19-3c24f49881d9", "type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RejectRepairOfferAdjustmentDto"}}}}, "responses": {"200": {"description": "It returns the result of repair offer adjustment reject method call", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "400": {"description": "The adjustment can't be processed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BadRequestErrorResponse"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "403": {"description": "Repair offer status can not be changed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ForbiddenErrorResponse"}}}}, "404": {"description": "The adjustment can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Repair offers"], "security": [{"bearer": []}]}}, "/porsche/v1/user/{email}/link/portal": {"get": {"operationId": "PorscheUsersController_getAuthPortalLink", "summary": "Get portal user link with authorization token for fleet manager", "description": "This method returns portal link", "deprecated": true, "parameters": [{"name": "email", "required": true, "in": "path", "description": "Email", "schema": {"example": "<EMAIL>", "type": "string"}}], "responses": {"200": {"description": "This method returns portal link with authorization token", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PortalLinkResponseAuth"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/porsche/v1/users/{userPartnerId}": {"get": {"operationId": "PorscheUsersController_getUserInfo", "summary": "Get user information", "description": "This method returns user information", "parameters": [{"name": "userPartnerId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "It returns user information", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponseExample"}}}}}, "tags": ["User"], "security": [{"bearer": []}]}, "put": {"operationId": "PorscheUsersController_update", "summary": "Update user in the system", "description": "This method updates user in the system", "parameters": [{"name": "userPartnerId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "It returns result of the update user operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one or user have no access to the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "409": {"description": "The response when conflict exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConflictErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/porsche/v1/users": {"get": {"operationId": "PorscheUsersController_getUsers", "summary": "Get users list", "description": "This method returns a list of all users registered for a fleet", "parameters": [{"name": "page", "required": false, "in": "query", "description": "User list page number", "schema": {"example": 1, "type": "number"}}, {"name": "limit", "required": false, "in": "query", "description": "Number of the users on the page", "schema": {"example": 20, "type": "number"}}, {"name": "subFleet", "required": false, "in": "query", "description": "Parameter for filter users list by sub fleet", "schema": {"example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "type": "string"}}, {"name": "role", "required": false, "in": "query", "description": "Parameter for filter users list by role", "schema": {"example": "fleet_manager", "enum": ["fleet_manager", "driver", "local_fleet_manager", "regional_fleet_manager"], "type": "string"}}], "responses": {"200": {"description": "It returns the list of users", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UsersList"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponseExample"}}}}}, "tags": ["User"], "security": [{"bearer": []}]}, "post": {"operationId": "PorscheUsersController_add", "summary": "Create new users in the system for current fleet identifier", "description": "This method creates new users in the system", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CreateUserDto"}}}}}, "responses": {"200": {"description": "It returns result of the create user operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one or user have no access to the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "409": {"description": "The response when conflict exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConflictErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/porsche/v1/fleets/{fleetPartnerId}/users/{userPartnerId}": {"put": {"operationId": "PorscheUsersController_updateUser", "summary": "Update user in the system for current fleet identifier", "description": "This method updates user in the system. \nThe method is deprecated. Please use `PUT /users/{userPartnerId}` instead.", "deprecated": true, "parameters": [{"name": "fleetPartnerId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "userPartnerId", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserDto"}}}}, "responses": {"200": {"description": "It returns result of the update user operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one or user have no access to the resource", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "409": {"description": "The response when conflict exists", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConflictErrorResponse"}}}}, "422": {"description": "The response when you provide invalid body data.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableEntityErrorResponse"}}}}}, "tags": ["User"], "security": [{"bearer": []}]}}, "/porsche/v1/damage-reports/{damageReportId}/invoices": {"get": {"operationId": "PorscheInvoicesController_invoicesList", "summary": "Invoices list", "description": "This method returns invoices of the damage report", "parameters": [{"name": "damageReportId", "required": true, "in": "path", "description": "Damage Report ID or External ID", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}, {"name": "bodyshopId", "required": false, "in": "query", "description": "A filtering parameter: get invoices with the particular bodyshop ID ", "schema": {"example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3", "type": "string"}}], "responses": {"200": {"description": "It returns damage report invoices list", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InvoicesList"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The invoice can't be found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}}, "tags": ["Invoices"], "security": [{"bearer": []}]}}, "/porsche/v1/links": {"post": {"operationId": "PorschePortalController_generate", "summary": "Generates portal links", "description": "This method generates portal links for the damage and car lists and details. Take note when a link is generated with an authorization token, the token is valid for one week (168 hours)", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneratePortalLinkDto"}}}}, "responses": {"200": {"description": "It returns a created link", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GeneratePortalLinkData"}}}}, "401": {"description": "The response when you provide an invalid `api_key` or don't provide one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when an entity cannot be found by identifier", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when an unprocessable entity error occurred", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableErrorResponse"}}}}, "500": {"description": "The response when an internal server error occurred", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InternalServerErrorResponse"}}}}}, "tags": ["Portal"], "security": [{"bearer": []}]}}, "/porsche/v1/tokens": {"get": {"operationId": "PorscheTokensController_list", "summary": "Get a current Swagger API tokens list", "description": "This method returns an API tokens list of a client that are available for use in Swagger", "parameters": [{"name": "limit", "required": false, "in": "query", "description": "Use this parameter to specify how many records you want to display", "schema": {"default": 20, "example": 20, "type": "number"}}, {"name": "page", "required": false, "in": "query", "description": "Use this parameter to specify the page number", "schema": {"default": 1, "example": 1, "type": "number"}}], "responses": {"200": {"description": "It returns a list of API tokens currently available for use in Swagger, along with the total number of tokens", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/TokensList"}}}}, "401": {"description": "The response when an `api_key` is not provided or provided the invalid one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}}, "tags": ["Tokens"], "security": [{"bearer": []}]}, "post": {"operationId": "PorscheTokensController_create", "summary": "Create a new API token", "description": "This method creates a new API token with a specified name that a client can use in Swagger.", "parameters": [], "requestBody": {"required": true, "description": "This payload creates a new API token and returns the token key along with associated details.\n\n  The payload must contain the following fields:\n  \n  - ***name***: a name of the new API token, it will be used as a username when logging in to Swagger.\n    It may contain only letters (upper- or lowercase), digits, underscores, or hyphens (no other characters),\n    from 1 to 255 characters long, and it must be unique.\n  ", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AddTokenDto"}}}}, "responses": {"200": {"description": "It returns a new API token key along with associated details", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NewToken"}}}}, "400": {"description": "Some of required parameters were missed in request.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BadRequestErrorResponse"}}}}, "401": {"description": "The response when an `api_key` is not provided or provided the invalid one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "422": {"description": "The response when an unprocessable entity error occurred", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableErrorResponse"}}}}}, "tags": ["Tokens"], "security": [{"bearer": []}]}}, "/porsche/v1/tokens/{tokenName}": {"delete": {"operationId": "PorscheTokensController_delete", "summary": "Removes a client's API token", "description": "This method removes a client's API token.\n    It means that the token will no longer be valid for login and use in Swagger.\n    A currently used token cannot be removed.\n    ", "parameters": [{"name": "tokenName", "required": true, "in": "path", "description": "A name of an API token to remove", "schema": {"type": "string", "pattern": "^[A-Za-z0-9_-]+$"}}], "responses": {"200": {"description": "It returns the result of the operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OperationResult"}}}}, "401": {"description": "The response when an `api_key` is not provided or provided the invalid one.", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnauthorizedErrorResponse"}}}}, "404": {"description": "The response when something not found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/NotFoundErrorResponse"}}}}, "422": {"description": "The response when an unprocessable entity error occurred", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnprocessableErrorResponse"}}}}}, "tags": ["Tokens"], "security": [{"bearer": []}]}}}, "info": {"title": "Motum API", "description": "Motum API for Porsche<br />Use this API for authentication and for retrieving damages data<br />", "version": "1.0.0", "contact": {}}, "tags": [], "servers": [], "components": {"securitySchemes": {"bearer": {"scheme": "bearer", "bearerFormat": "JWT", "type": "http"}}, "schemas": {"WebAppInitDto": {"type": "object", "properties": {"vin": {"type": "string", "description": "VIN of the car for which damage report link will be created. Either vin or carPartnerId should be provided", "example": "JH4KA3250JC001616", "minLength": 17, "maxLength": 17}, "carPartnerId": {"type": "string", "description": "Car ID reference that was provided on create call. Either vin or carPartnerId should be provided", "example": "qwe:1234"}, "externalId": {"type": "string", "description": "Report ID reference. Can be used to identify the report", "example": "928d12b1-976f-4a47-b23e-1afaee42ca75"}, "id": {"type": "string", "description": "Report ID reference. It can be id / partnerId / externalId", "example": "928d12b1-976f-4a47-b23e-1afaee42ca75"}, "location": {"type": "string", "description": "The location of the car where damage has happened", "example": "Berlin", "minLength": 1, "maxLength": 255}, "reporterName": {"type": "string", "description": "Name of the reporter", "example": "<PERSON>", "minLength": 1, "maxLength": 255}, "flow": {"type": "string", "description": "Reporting scenario. When used, the user is going to be redirected to the specified flow and the link allows to report only one report", "enum": ["exterior", "interior", "mechanical", "service-lights", "checklist", "accident", "simple-accident", "car-damages-overview", "damage-report-update", "breakdown"]}, "checklistId": {"type": "string", "description": "Checklist ID. In case flow=\"checklist\" the checklist ID is required. Available checklists can be found using /checklists endpoint", "format": "uuid"}, "callbackUrl": {"type": "string", "description": "The url to which the user is going to be redirected after the flow is completed.\n\nThe callBackUrl can be used in two ways:\n- as a redirect url to the app\n- as a communication channel between the app and the caller\n\nThe callbackUrl is going to be adjusted with the following query parameters:\n- id - the report ID\n- status - the status of the report. Can be one of: 'completed', 'error', 'timeout'\n- message - the error message in case of error status\n    ", "example": "https://example.com/report-result?id=123&status=completed", "minLength": 5, "maxLength": 255}, "mode": {"type": "string", "description": "The mode of the web app\n- *standalone* - default behavior. The web app is opened in the browser and, if the callbackUrl is provided, the user is redirected to after the flow is completed\n- *embedded* - Similar to standalone mode, the app relies on communication using the postMessage API.\n  - Accepts messages:\n    - CHANGE_LANGUAGE - Changes the language of the app. The payload must be a language code (e.g. 'en') and the language must be supported by the app\n      ```json\n      {\n        type: 'CHANGE_LANGUAGE',\n        payload: 'en'\n      }\n\n  - Sends messages:\n    - LANGUAGE_CHANGED - Notifies about the change of the language. The payload is the language code (e.g. 'en')\n      ```json\n      {\n        type: 'LANGUAGE_CHANGED',\n        payload: 'en'\n      }\n    - REPORT_STATE_CHANGED - Notifies about the change of the report state. This can be used as alternative method to callbackUrl functionality. The payload is the report state\n      ```json\n      {\n        type: 'REPORT_STATE_CHANGED',\n        payload: {\n          id: '928d12b1-976f-4a47-b23e-1afaee42ca75',\n          status: 'completed',\n          message: 'Report completed successfully',\n        }\n      }\n    ", "enum": ["embedded", "standalone"]}, "locale": {"type": "string", "description": "The two lettered language code for the app UI.\n\nIf not provided the app is going to detect from the browser amount the list of supported languages.\nIf not supported language is provided the default language configured for the fleet is going to be used.", "example": "en", "minLength": 2, "maxLength": 2}, "expireAt": {"type": "string", "description": "The token expiration timestamp after which the link expire", "example": "2025-07-31T15:55:44.906Z", "format": "date-time"}, "reportPartnerData": {"type": "object", "description": "The report data storage. Arbitrary data object stored as a context information for tracking back the report.\n\n***(!) Please note that all primitive values of the object are going to be stringified***", "example": {"customerNumber": "CN-1823", "databaseId": "DB-Germany-288", "reservationNumber": "RN-133212-1"}}, "extendedPermissions": {"description": "The report's additional permissions array", "example": ["damage-status:update", "damage-severity:update", "damage-image-visibility:update", "damage-manager-comment:update"], "type": "array", "items": {"type": "object"}}}}, "WebAppInit": {"type": "object", "properties": {"webUrl": {"type": "string", "description": "You can open this URL directly in a web view.", "example": "https://www.example.com/?code=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c&callbackUrl=android-app%3A%2F%2Fcom.company.unit.exampleapplication"}, "isLinkUsed": {"type": "boolean", "description": "Indicates whether this web url has already been sent. If FALSE - this is generated for the first time. If TRUE - it has already been generated before, but the damage has not been reported yet.", "example": false}, "expiresAt": {"type": "string", "description": "Web app token expiration date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}}, "required": ["webUrl", "isLinkUsed"]}, "WebAppInitData": {"type": "object", "properties": {"webApp": {"description": "Web app data", "allOf": [{"$ref": "#/components/schemas/WebAppInit"}]}}, "required": ["webApp"]}, "UnauthorizedErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "example": 401, "description": "HTTP status code"}, "message": {"type": "string", "description": "Error message", "example": "You need to be authenticated for this operation."}, "error": {"type": "string", "description": "A short description of the HTTP error", "example": "Unauthorized"}}, "required": ["statusCode", "message"]}, "NotFoundErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "example": 404, "description": "HTTP status code"}, "message": {"type": "string", "description": "Error message", "example": "Resource could not be found."}, "error": {"type": "string", "description": "A short description of the HTTP error", "example": "Not found"}}, "required": ["statusCode", "message"]}, "UnprocessableEntityErrorResponse": {"type": "object", "properties": {"status": {"type": "number", "example": 422, "description": "HTTP status code"}, "message": {"type": "string", "description": "Error message"}}, "required": ["status", "message"]}, "ReporterInfo": {"type": "object", "properties": {"phone": {"type": "string", "description": "Reporter phone number", "example": "+*********"}, "email": {"type": "string", "description": "Reporter email", "example": "<EMAIL>"}, "name": {"type": "string", "description": "Reporter name", "example": "<PERSON>"}, "companyAffiliation": {"type": "string", "description": "Company affiliation information of reporter", "example": "internal", "enum": ["internal", "external"], "nullable": true}, "personalNumber": {"type": "string", "description": "Personal number of user with internal company affiliation", "example": "P000123", "nullable": true}}}, "DamageRepairOffer": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the repair offer", "example": "f4d26ffc-f3e4-4c01-9ea1-e5b5968343d9", "format": "uuid"}, "status": {"type": "string", "description": "Repair offer status", "example": "new", "enum": ["requested", "waiting_for_approval", "inspection_required", "inspection_approved", "expert_approval_required", "new", "accepted", "cancelled", "rejected"], "nullable": false}, "price": {"type": "number", "description": "Repair offer price", "example": 627.14, "nullable": true}, "bodyshopId": {"type": "string", "description": "Bodyshop assigned to repair offer", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56", "nullable": true}}, "required": ["id", "status"]}, "CarDriver": {"type": "object", "properties": {"email": {"type": "string", "description": "Driver email", "example": "<EMAIL>"}, "phoneNumber": {"type": "string", "description": "Driver phone", "example": "+*********"}, "firstName": {"type": "string", "description": "Driver first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Driver last name", "example": "<PERSON><PERSON>"}, "partnerId": {"type": "string", "description": "Internal driver ID at partner side", "example": "driver-2"}}, "required": ["email"]}, "DamageReportCar": {"type": "object", "properties": {"id": {"type": "string", "description": "Car's ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56"}, "vin": {"type": "string", "description": "Car's VIN", "example": "JH4KA3250JC001616"}, "licensePlate": {"type": "string", "description": "Car's licence plate", "example": "TE ST 10200"}, "firstRegistration": {"type": "string", "description": "Car's first registration date", "example": "24-03-2019", "format": "date-time"}, "createdAt": {"type": "string", "description": "Car created date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Car updated date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "location": {"type": "string", "description": "Car's location", "example": "Berlin"}, "model": {"type": "string", "description": "Car's model", "example": "NV200 / Evalia (M20/M)"}, "make": {"type": "string", "description": "Car's manufacturer name", "example": "Nissan"}, "mileage": {"type": "number", "description": "Car's reported mileage", "example": 127083}, "isPool": {"type": "boolean", "description": "Whether the car is a pool", "example": false}, "carPartnerId": {"type": "string", "description": "Car id at the partner side", "example": "qwe:123"}, "isActive": {"type": "boolean", "description": "Car is active", "example": true}, "driver": {"$ref": "#/components/schemas/CarDriver"}, "fleetPartnerId": {"type": "string", "description": "Car's related sub fleet", "nullable": true, "example": "subfleet test1"}}, "required": ["id", "vin", "createdAt", "updatedAt", "model", "make", "isPool", "isActive"]}, "DamageReportListItem": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3"}, "externalId": {"type": "string", "description": "External Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "nullable": true}, "status": {"type": "string", "description": "Damage report status", "example": "new"}, "type": {"type": "string", "description": "Damage report type", "example": "ExteriorDamageReport"}, "createdAt": {"type": "string", "description": "Damage report created date", "example": "2019-01-01T11:29:38.964Z"}, "updatedAt": {"type": "string", "description": "Damage report updated date", "example": "2019-01-01T11:29:38.964Z"}, "isAccident": {"type": "boolean", "description": "Indicates an accident report", "example": true}, "isBreakdown": {"type": "boolean", "description": "Indicates if a breakdown damage", "example": true}, "isMinor": {"type": "boolean", "description": "Indicates if the damage is minor", "example": true}, "location": {"type": "string", "description": "Location where damage report has happened", "example": "Berlin"}, "accidentResponsibility": {"type": "string", "description": "Accident responsibility (according to a driver)", "example": "responsibility_is_not_clear"}, "subFleet": {"type": "string", "description": "Damaged car fleet", "example": "A fleet name"}, "reporterName": {"type": "string", "description": "Reporter name", "example": "<PERSON>"}, "reporterNameLink": {"type": "string", "description": "Reporter name", "example": "<PERSON>"}, "reporter": {"$ref": "#/components/schemas/ReporterInfo"}, "repairOffers": {"description": "Damage repair offers", "type": "array", "items": {"$ref": "#/components/schemas/DamageRepairOffer"}}, "car": {"$ref": "#/components/schemas/DamageReportCar"}, "fleetRepairPrice": {"type": "string", "description": "Fleet repair price", "example": "20.99"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "closeReason": {"type": "string", "description": "Close reason", "example": "duplicate", "enum": ["repaired", "will_no_be_repaired", "duplicate", "not_a_damage", "wrong_damage", "other"], "nullable": true}, "closeReasonComment": {"type": "string", "description": "Close reason comment", "example": "It's a duplicate of damage X", "nullable": true}, "reportPartnerData": {"type": "object", "description": "Partner report data information sent on web-app init call"}, "driverComment": {"type": "string", "description": "Driver comment about the damage"}, "managerComment": {"type": "string", "description": "Manager comment about the damage"}}, "required": ["id", "status", "type", "createdAt", "updatedAt", "isAccident", "isBreakdown", "isMinor", "car"]}, "DamageReportsListData": {"type": "object", "properties": {"damageReports": {"description": "Damage reports list", "type": "array", "items": {"$ref": "#/components/schemas/DamageReportListItem"}}, "total": {"type": "number", "description": "Total number of the damages", "example": 1}}, "required": ["damageReports", "total"]}, "UnauthorizedErrorResponseExample": {"type": "object", "properties": {"error": {"description": "Error", "allOf": [{"$ref": "#/components/schemas/UnauthorizedErrorResponse"}]}}, "required": ["error"]}, "DamageInsurance": {"type": "object", "properties": {"insuranceNumber": {"type": "string", "description": "Insurance number", "example": "93GBH-4893DB"}, "resultsOfBroker": {"type": "string", "description": "Results of the insurance broker", "example": "Results of the insurance broker"}}}, "InteriorDamageImage": {"type": "object", "properties": {"id": {"type": "string", "description": "Damage image ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56"}, "type": {"type": "string", "description": "Damage image type", "enum": ["interior", "interior_left", "interior_right", "rear_right_angle", "left_diagonal_view", "inspection", "additional", "front_left_angle", "right_diagonal_view", "rear_side_general"], "example": "rear_side_general"}, "category": {"type": "string", "description": "Image category, depends on its type. Subject to manual change - images with common type might eventually have different categories.", "example": "damage", "enum": ["damage", "other"], "nullable": false}}, "required": ["id", "type", "category"]}, "DamageDocument": {"type": "object", "properties": {"id": {"type": "string", "description": "Document ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56"}, "name": {"type": "string", "description": "Document name", "example": "EG-0000-000000_SCHADENANZEIGE_AUSGEFUELLT_0000000_0a8d_2020-10-12_11-30-03.pdf"}, "mimeType": {"type": "string", "description": "Document mime type", "example": "application/pdf"}, "type": {"type": "string", "description": "Compound document  type", "enum": ["SCHADENANZEIGE_AUSGEFUELLT", "GUTACHTEN", "invoice"], "example": "SCHADENANZEIGE_AUSGEFUELLT"}, "comment": {"type": "string", "description": "Comment for document", "nullable": true, "example": "Assessment report is provided"}}, "required": ["id", "name", "mimeType", "type"]}, "InteriorDamageReport": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3"}, "externalId": {"type": "string", "description": "External Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "nullable": true}, "status": {"type": "string", "description": "Damage report status", "example": "new", "enum": ["link_sent", "new", "estimation_postponed", "estimation_requested", "repair_offer_pending_expert_approval", "inspection_on_site", "inspection_on_site_approved", "inspection_scheduled", "repair_offer_estimated", "waiting_for_approval", "repair_offer_accepted", "repair_offer_rejected", "repair_offer_adjusted", "repair_scheduled", "internally_repaired", "in_repair", "repaired", "in_settlement", "settled", "closed"]}, "statusHistory": {"type": "object", "description": "Status change history", "properties": {"linkSent": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "new": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationPostponed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationRequested": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferPendingExpertApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSite": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSiteApproved": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferEstimated": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "waitingForApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAccepted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferRejected": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAdjusted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "internallyRepaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inRepair": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inSettlement": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "settled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "closed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}}}, "type": {"type": "string", "description": "Damage report type", "example": "ExteriorDamageReport", "enum": ["ExteriorDamageReport", "InteriorDamageReport", "ServiceLightsReport", "TelematicDamageReport", "MechanicalDamageReport", "CompoundDamageReport", "PreInspectionReport", "ThirdPartyReport"]}, "closeReason": {"type": "string", "description": "Close reason", "example": "duplicate", "enum": ["repaired", "will_no_be_repaired", "duplicate", "not_a_damage", "wrong_damage", "other"], "nullable": true}, "closeReasonComment": {"type": "string", "description": "Close reason comment", "example": "It's a duplicate of damage X", "nullable": true}, "location": {"type": "string", "description": "Location where damage report has happened", "example": "Berlin", "nullable": true}, "isAccident": {"type": "boolean", "description": "Indicates an accident report", "example": true}, "isMinor": {"type": "boolean", "description": "Indicates if the damage is minor", "example": true}, "reporterNameLink": {"type": "string", "description": "Reporter (who send a link) name", "example": "<PERSON>", "nullable": true}, "reporter": {"$ref": "#/components/schemas/ReporterInfo"}, "car": {"$ref": "#/components/schemas/DamageReportCar"}, "repairOffers": {"description": "Damage repair offers", "type": "array", "items": {"$ref": "#/components/schemas/DamageRepairOffer"}}, "damageInsurance": {"$ref": "#/components/schemas/DamageInsurance"}, "subFleet": {"type": "string", "description": "Damaged car fleet", "example": "596702e0-02bd-4495-a1ee-f1aff42ac302"}, "createdAt": {"type": "string", "description": "Damage report created date", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}, "fleetRepairPrice": {"type": "string", "description": "Fleet repair price", "example": "20.99"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "reportPartnerData": {"type": "object", "description": "Partner report data information sent on web-app init call"}, "driverComment": {"type": "string", "description": "Driver comment about the damage"}, "managerComment": {"type": "string", "description": "Manager comment about the damage"}, "damageDescription": {"type": "string", "description": "Damage report description. Alias for driverComment", "example": "Mehrere leichte Kratzer zwischen 12 und 3 Uhr", "nullable": true}, "damageCategory": {"type": "string", "description": "Interior damage category", "example": "missing_parts"}, "damageLocation": {"type": "array", "items": {"type": "string", "enum": ["console", "door", "floor", "front_panel", "other", "panels_and_covers", "roof", "seats", "trunk"]}, "description": "Interior damage locations", "example": ["door", "floor"], "nullable": true}, "images": {"description": "Interior damage images", "example": {"id": "c108e86e-27db-481c-96e7-80f1afdbd9db", "type": "interior"}, "type": "array", "items": {"$ref": "#/components/schemas/InteriorDamageImage"}}, "documents": {"description": "Documents", "nullable": true, "type": "array", "items": {"$ref": "#/components/schemas/DamageDocument"}}}, "required": ["id", "status", "statusHistory", "type", "location", "isAccident", "isMinor", "car", "subFleet", "createdAt", "damageCategory"]}, "CarPart": {"type": "object", "properties": {"name": {"type": "string", "description": "Damaged part's name", "example": "<PERSON><PERSON>"}, "type": {"type": "string", "description": "Damaged part's type", "enum": ["a_pillar_left", "a_pillar_right", "body_sill_left_front", "body_sill_left_rear", "body_sill_right_front", "body_sill_right_rear", "brake_light_left_rear", "brake_light_right_rear", "bumper_center_front", "bumper_center_rear", "bumper_left_front", "bumper_left_rear", "bumper_right_front", "bumper_right_rear", "bumper_side_left_front", "bumper_side_left_rear", "bumper_side_right_front", "bumper_side_right_rear", "b_pillar_left", "b_pillar_right", "cargo_box_side_left", "cargo_box_side_right", "condenser", "c_pillar_left", "c_pillar_right", "door_handle_left_front", "door_handle_left_rear", "door_handle_rear", "door_handle_right_front", "door_handle_right_rear", "door_left_front", "door_left_rear", "door_quarter_left_front", "door_quarter_left_rear", "door_quarter_right_front", "door_quarter_right_rear", "door_right_front", "door_right_rear", "d_pillar_left", "d_pillar_right", "fender_left_front", "fender_left_rear", "fender_right_front", "fender_right_rear", "fog_light_left_front", "fog_light_right_front", "fuel_filter_cap_cover", "headlights_left", "headlights_right", "hood", "license_plate_holder_front", "license_plate_holder_rear", "logo_front", "logo_rear", "mirror_left", "mirror_right", "missing", "panel_left_rear", "panel_right_rear", "quarter_panel_left_rear", "quarter_panel_right_rear", "radiator_grille_front", "rim_left_front", "rim_left_rear", "rim_right_front", "rim_right_rear", "roof", "roof_antenna", "roof_rail_left", "roof_rail_right", "side_marker_lights_left", "side_marker_lights_right", "step_rear", "sunroof", "tailgate", "tailgate_door_handle_rear", "taillights_left", "taillights_right", "tire_left_front", "tire_left_rear", "tire_right_front", "tire_right_rear", "trunk", "trunk_left", "trunk_right", "wheel_left_front", "wheel_left_rear", "wheel_right_front", "wheel_right_rear", "window_left_front", "window_left_rear", "window_rear", "window_right_front", "window_right_rear", "windshield_front", "console", "door", "floor", "front_panel", "other", "panels_and_covers", "seats", "air_conditioning_and_heating", "brakes", "chassis_and_steering", "electronics", "engine", "maintenance", "tyres", "a_pillar_left1", "a_pillar_left2", "a_pillar_right1", "a_pillar_right2", "bumper_rear1", "bumper_rear2", "bumper_rear3", "bumper_rear5", "b_pillar_left1", "b_pillar_left2", "b_pillar_right1", "b_pillar_right2", "cargo_left_rear1", "cargo_left_rear2", "cargo_left_rear3", "cargo_left_rear4", "cargo_right_rear1", "cargo_right_rear2", "cargo_right_rear3", "cargo_right_rear4", "door_left_front1", "door_left_front2", "door_left_front3", "door_left_front4", "door_left_rear1", "door_left_rear2", "door_left_rear3", "door_left_rear4", "door_quarter_left_rear1", "door_quarter_left_rear2", "door_quarter_right_rear1", "door_quarter_right_rear2", "door_right_front1", "door_right_front2", "door_right_front3", "door_right_front4", "door_right_rear1", "door_right_rear2", "door_right_rear3", "door_right_rear4", "fender_left_front1", "fender_left_front2", "fender_left_rear1", "fender_left_rear2", "fender_left_rear3", "fender_left_rear4", "fender_right_front1", "fender_right_front2", "fender_right_rear1", "fender_right_rear2", "fender_right_rear3", "fender_right_rear4", "hood1", "hood2", "hood3", "hood4", "roof1", "roof2", "roof3", "roof4", "roof5", "sunroof1", "sunroof2", "tailgate1", "tailgate2", "tailgate3", "tailgate4", "window_rear1", "window_rear2", "windshield_front1", "windshield_front2", "windshield_front3", "windshield_front4"], "example": "left_headlights"}, "area": {"type": "string", "description": "Damaged part's area", "enum": ["back", "cabin", "driver_side", "front", "mechanical", "middle", "missing", "other", "passenger_side", "wheel_left_front", "wheel_left_rear", "wheel_right_front", "wheel_right_rear"], "example": "front"}}, "required": ["name", "type", "area"]}, "ExteriorDamageCategory": {"type": "object", "properties": {"name": {"type": "string", "description": "Damage category name", "example": "<PERSON><PERSON><PERSON>"}, "type": {"type": "string", "description": "Damage category type", "enum": ["dent", "stone_chips", "other", "scratch", "hail", "crack", "flood"], "example": "scratch"}}, "required": ["name", "type"]}, "ExteriorDamageImage": {"type": "object", "properties": {"id": {"type": "string", "description": "Damage image ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56"}, "type": {"type": "string", "description": "Damage image type", "enum": ["front_left_angle", "rear_right_angle", "left_diagonal_view", "right_diagonal_view", "rear_side_general", "additional", "registration_document", "dashboard", "inspection", "accident_scene", "other_party_driver_license", "other_party_vehicle", "police_report", "accident_document", "damaged_object", "vehicle_rear_side_view", "vehicle_front_side_view", "vehicle_driver_side_view", "vehicle_passenger_side_view", "wildlife_protection_license_number", "checklist_duplicate"], "example": "left_diagonal_view"}, "category": {"type": "string", "description": "Image category, depends on its type. Subject to manual change - images with common type might eventually have different categories.", "example": "damage", "enum": ["damage", "other"], "nullable": false}}, "required": ["id", "type", "category"]}, "AdditionalInformation": {"type": "object", "properties": {"validDriverLicense": {"type": "boolean", "description": "Does driver has valid driver license", "example": true}, "leftAccidentScene": {"type": "boolean", "description": "Has driver left accident scene without permission", "example": true}, "bloodSampleTaken": {"type": "boolean", "description": "Has blood sample been taken", "example": true, "deprecated": true}, "alcoholBeveragesConsumes": {"type": "boolean", "description": "Did driver consumed alcoholic beverages or intoxicating substances in the last 24 hours before accident", "example": true}, "wasAlcoholDrugTestPerformed": {"type": "boolean", "description": "Was an alcohol or drug test performed", "example": true}, "alcoholConcentration": {"type": "string", "description": "Alcohol concetration in case an alcohol test was taken, ppm", "example": "0.04"}, "wasCarTransferredToThirdParty": {"type": "boolean", "description": "Was car transferred to third party?", "example": true, "nullable": true}, "transferServiceProvider": {"type": "string", "description": "Transfer service provider in case if car was transfered to third party", "example": "Some service", "nullable": true}}, "required": ["validDriverLicense", "leftAccidentScene", "bloodSampleTaken", "alcoholBeveragesConsumes"]}, "PoliceReport": {"type": "object", "properties": {"policeOfficerName": {"type": "string", "description": "Police officer name", "example": "<PERSON>"}, "reportNumber": {"type": "string", "description": "The number of the report", "example": "1234567"}}}, "AccidentThirdParty": {"type": "object", "properties": {"licensePlate": {"type": "string", "description": "License plate of the other car involved in the accident", "example": "TE-ST 1298"}, "personName": {"type": "string", "description": "Name of the other driver involved in the accident", "example": "<PERSON>", "nullable": true}, "insurance": {"type": "string", "description": "Insurance number of the other driver involved in the accident", "example": "1234678", "nullable": true}, "personEmail": {"type": "string", "description": "Email of the other driver involved in the accident", "example": "<EMAIL>", "nullable": true}, "personPhoneNumber": {"type": "string", "description": "Phone number of the other driver involved in the accident", "example": "+49729332923", "nullable": true}, "personAddress": {"type": "string", "description": "Address of the other driver involved in the accident", "example": "Calle del Trinquet de Cavallers, 5", "nullable": true}, "driverIsNotVehicleOwner": {"type": "boolean", "description": "Indicates if the driver is not the vehicle owner", "example": true, "nullable": true}, "vehicleOwnerName": {"type": "string", "description": "Name of the vehicle owner", "example": "<PERSON>", "nullable": true}, "vehicleOwnerEmail": {"type": "string", "description": "Email of the vehicle owner", "example": "<EMAIL>", "nullable": true}, "vehicleOwnerPhone": {"type": "string", "description": "Phone number of the vehicle owner", "example": "+49937999232", "nullable": true}, "vehicleOwnerAddress": {"type": "string", "description": "Address of the vehicle owner", "example": "Hauptstraße 123", "nullable": true}}, "required": ["licensePlate"]}, "AccidentThirdPartyInformation": {"type": "object", "properties": {"damageResponsibility": {"type": "string", "description": "The side that is responsible for the accident", "example": "other_party_caused_accident", "enum": ["driver_caused_accident", "other_party_caused_accident", "both_caused_accident", "responsibility_is_not_clear"], "nullable": true}, "comment": {"type": "string", "description": "A comment about accident third party", "example": "An additional information comment", "nullable": true}, "accidentThirdParties": {"nullable": true, "description": "List of the accident third parties", "type": "array", "items": {"$ref": "#/components/schemas/AccidentThirdParty"}}}}, "DamageReportWildlifeProtectionDto": {"type": "object", "properties": {"authorityOffice": {"type": "string", "description": "Wildlife protection licence office name", "example": "U.S. Fish and Wildlife Service (USFWS)", "nullable": true}, "wildlifeProtectionLicenseNumber": {"type": "string", "description": "Wildlife protection licence number", "example": "*********", "nullable": true}}}, "InjuredPersonInformation": {"type": "object", "properties": {"id": {"type": "string", "description": "Injured person ID", "example": "9d387214-5ae3-4b82-9cf5-631849e4da78", "nullable": false}, "insuranceCompany": {"type": "string", "description": "Health insurance company", "example": "Allianz", "nullable": true}, "personName": {"type": "string", "description": "Injured person full name", "example": "<PERSON>", "nullable": true}, "personEmail": {"type": "string", "description": "Injured person email", "example": "<EMAIL>", "nullable": true}, "personPhoneNumber": {"type": "string", "description": "Injured person phone", "example": "+491429401289", "nullable": true}, "comment": {"type": "string", "description": "Injured person comment", "example": "Comment", "nullable": true}}, "required": ["id"]}, "InjuredPeopleInformation": {"type": "object", "properties": {"areInjuredPeoplePresent": {"type": "boolean", "description": "Indicates if injured people are present at accident scene", "example": true, "nullable": true}, "injuredPeople": {"description": "Information about injured people", "example": [{"id": "9d387214-5ae3-4b82-9cf5-631849e4da78", "insuranceCompany": "Allianz", "personName": "<PERSON>", "personEmail": "<EMAIL>", "personPhoneNumber": "+491429401289", "comment": "Comment"}], "type": "array", "items": {"$ref": "#/components/schemas/InjuredPersonInformation"}}}, "required": ["<PERSON><PERSON><PERSON><PERSON>"]}, "Witness": {"type": "object", "properties": {"id": {"type": "string", "description": "Witness ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56", "nullable": false}, "personName": {"type": "string", "description": "Full name of the witness", "example": "<PERSON>", "nullable": true}, "personEmail": {"type": "string", "description": "Email of the witness", "example": "<EMAIL>", "nullable": true}, "personPhoneNumber": {"type": "string", "description": "Phone number of the witness", "example": "+49721111111", "nullable": true}, "personAddress": {"type": "string", "description": "Address of the witness", "example": "Lindwurmstraße 12, 80805 München", "nullable": true}, "comment": {"type": "string", "description": "Witness person comment", "example": "Comment", "nullable": true}}, "required": ["id"]}, "WitnessInformation": {"type": "object", "properties": {"areWitnessesPresent": {"type": "boolean", "description": "Indicates if witnesses are present at accident scene", "example": true, "nullable": true}, "witnesses": {"description": "Information about witnesses", "example": [{"id": "7c298125-09c4-4d71-a2b4-4ce272a8de56", "personName": "<PERSON>", "personEmail": "<EMAIL>", "personAddress": "Lindwurmstraße 12, 80805 München"}], "type": "array", "items": {"$ref": "#/components/schemas/Witness"}}}, "required": ["witnesses"]}, "ExteriorDamageReport": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3"}, "externalId": {"type": "string", "description": "External Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "nullable": true}, "status": {"type": "string", "description": "Damage report status", "example": "new", "enum": ["link_sent", "new", "estimation_postponed", "estimation_requested", "repair_offer_pending_expert_approval", "inspection_on_site", "inspection_on_site_approved", "inspection_scheduled", "repair_offer_estimated", "waiting_for_approval", "repair_offer_accepted", "repair_offer_rejected", "repair_offer_adjusted", "repair_scheduled", "internally_repaired", "in_repair", "repaired", "in_settlement", "settled", "closed"]}, "statusHistory": {"type": "object", "description": "Status change history", "properties": {"linkSent": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "new": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationPostponed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationRequested": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferPendingExpertApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSite": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSiteApproved": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferEstimated": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "waitingForApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAccepted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferRejected": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAdjusted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "internallyRepaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inRepair": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inSettlement": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "settled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "closed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}}}, "type": {"type": "string", "description": "Damage report type", "example": "ExteriorDamageReport", "enum": ["ExteriorDamageReport", "InteriorDamageReport", "ServiceLightsReport", "TelematicDamageReport", "MechanicalDamageReport", "CompoundDamageReport", "PreInspectionReport", "ThirdPartyReport"]}, "closeReason": {"type": "string", "description": "Close reason", "example": "duplicate", "enum": ["repaired", "will_no_be_repaired", "duplicate", "not_a_damage", "wrong_damage", "other"], "nullable": true}, "closeReasonComment": {"type": "string", "description": "Close reason comment", "example": "It's a duplicate of damage X", "nullable": true}, "location": {"type": "string", "description": "Location where damage report has happened", "example": "Berlin", "nullable": true}, "isAccident": {"type": "boolean", "description": "Indicates an accident report", "example": true}, "isMinor": {"type": "boolean", "description": "Indicates if the damage is minor", "example": true}, "reporterNameLink": {"type": "string", "description": "Reporter (who send a link) name", "example": "<PERSON>", "nullable": true}, "reporter": {"$ref": "#/components/schemas/ReporterInfo"}, "car": {"$ref": "#/components/schemas/DamageReportCar"}, "repairOffers": {"description": "Damage repair offers", "type": "array", "items": {"$ref": "#/components/schemas/DamageRepairOffer"}}, "damageInsurance": {"$ref": "#/components/schemas/DamageInsurance"}, "subFleet": {"type": "string", "description": "Damaged car fleet", "example": "596702e0-02bd-4495-a1ee-f1aff42ac302"}, "createdAt": {"type": "string", "description": "Damage report created date", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}, "fleetRepairPrice": {"type": "string", "description": "Fleet repair price", "example": "20.99"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "reportPartnerData": {"type": "object", "description": "Partner report data information sent on web-app init call"}, "driverComment": {"type": "string", "description": "Driver comment about the damage"}, "managerComment": {"type": "string", "description": "Manager comment about the damage"}, "damageDescription": {"type": "string", "description": "Damage report description. Alias for driverComment", "example": "Mehrere leichte Kratzer zwischen 12 und 3 Uhr", "nullable": true}, "isPrototypeCar": {"type": "boolean", "description": "Indicates whether a car is a prototype car or not", "example": true}, "damageCause": {"type": "array", "description": "Damage cause", "example": ["fixed_object_collision"], "nullable": true, "items": {"type": "string", "enum": ["fixed_object_collision", "person_collision", "animal_collision", "parked_car_collision", "running_over", "theft", "vandalism", "driving_off_the_road", "reversing_maneuvering", "overtaking_lane_change", "red_light_stop_sign", "turning", "environment", "other"]}}, "damageCauseNote": {"type": "string", "description": "Damage cause note", "example": "<PERSON><PERSON><PERSON> mit Tier", "nullable": true}, "damageCircumstances": {"type": "string", "description": "Damage circumstances", "example": "", "nullable": true}, "damageAddress": {"type": "string", "description": "Damage address", "example": "A45, Abfahrt Florstadt", "nullable": true}, "carParts": {"description": "Damaged car parts", "example": [{"name": "<PERSON><PERSON>", "type": "left_headlights", "area": "front"}], "type": "array", "items": {"$ref": "#/components/schemas/CarPart"}}, "damageCategories": {"description": "Damage categories", "example": [{"name": "<PERSON><PERSON><PERSON>", "type": "scratch"}], "type": "array", "items": {"$ref": "#/components/schemas/ExteriorDamageCategory"}}, "images": {"description": "Damage images", "example": [{"id": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "type": "rear_side_general"}, {"id": "846a1788-0441-4bed-9a4c-0f31a4669d41", "type": "left_diagonal_view"}, {"id": "64413987-15ec-4765-864e-2c0437d347b2", "type": "right_diagonal_view"}], "type": "array", "items": {"$ref": "#/components/schemas/ExteriorDamageImage"}}, "tripPurpose": {"type": "string", "description": "Purpose of the trip", "example": "business", "enum": ["business", "private", "commute"], "nullable": true}, "additionalInformation": {"$ref": "#/components/schemas/AdditionalInformation"}, "policeReport": {"$ref": "#/components/schemas/PoliceReport"}, "damageDateTime": {"type": "string", "description": "Date and time of the accident", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}, "documents": {"description": "Documents", "nullable": true, "type": "array", "items": {"$ref": "#/components/schemas/DamageDocument"}}, "accidentThirdPartyInformation": {"description": "Information about accident third parties", "nullable": false, "allOf": [{"$ref": "#/components/schemas/AccidentThirdPartyInformation"}]}, "insuranceCoverage": {"type": "string", "description": "Insurance coverage", "example": "unknown", "enum": ["third party", "full", "partial", "unknown", "not_covered"], "nullable": true}, "wildlifeProtection": {"description": "Wildlife protection data", "nullable": true, "allOf": [{"$ref": "#/components/schemas/DamageReportWildlifeProtectionDto"}]}, "injuredPeopleInformation": {"description": "Injured person data", "nullable": false, "allOf": [{"$ref": "#/components/schemas/InjuredPeopleInformation"}]}, "witnessInformation": {"description": "Information about witnesses", "nullable": false, "allOf": [{"$ref": "#/components/schemas/WitnessInformation"}]}, "damagedObjectDescription": {"type": "string", "description": "Description of the damaged object", "example": "Some description/comment", "nullable": true}, "isBreakdown": {"type": "boolean", "description": "Indicates if a damage report is a breakdown", "example": true}}, "required": ["id", "status", "statusHistory", "type", "location", "isAccident", "isMinor", "car", "subFleet", "createdAt", "carParts", "damageCategories", "images", "accidentThirdPartyInformation", "injuredPeopleInformation", "witnessInformation", "isBreakdown"]}, "ServiceLightsDamageImage": {"type": "object", "properties": {"id": {"type": "string", "description": "Damage image ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56"}, "type": {"type": "string", "description": "Damage image type", "enum": ["inspection", "additional", "rear_side_general", "dashboard"], "example": "rear_side_general"}, "category": {"type": "string", "description": "Image category, depends on its type. Subject to manual change - images with common type might eventually have different categories.", "example": "damage", "enum": ["damage", "other"], "nullable": false}}, "required": ["id", "type", "category"]}, "ServiceLightsReport": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3"}, "externalId": {"type": "string", "description": "External Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "nullable": true}, "status": {"type": "string", "description": "Damage report status", "example": "new", "enum": ["link_sent", "new", "estimation_postponed", "estimation_requested", "repair_offer_pending_expert_approval", "inspection_on_site", "inspection_on_site_approved", "inspection_scheduled", "repair_offer_estimated", "waiting_for_approval", "repair_offer_accepted", "repair_offer_rejected", "repair_offer_adjusted", "repair_scheduled", "internally_repaired", "in_repair", "repaired", "in_settlement", "settled", "closed"]}, "statusHistory": {"type": "object", "description": "Status change history", "properties": {"linkSent": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "new": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationPostponed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationRequested": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferPendingExpertApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSite": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSiteApproved": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferEstimated": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "waitingForApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAccepted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferRejected": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAdjusted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "internallyRepaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inRepair": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inSettlement": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "settled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "closed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}}}, "type": {"type": "string", "description": "Damage report type", "example": "ExteriorDamageReport", "enum": ["ExteriorDamageReport", "InteriorDamageReport", "ServiceLightsReport", "TelematicDamageReport", "MechanicalDamageReport", "CompoundDamageReport", "PreInspectionReport", "ThirdPartyReport"]}, "closeReason": {"type": "string", "description": "Close reason", "example": "duplicate", "enum": ["repaired", "will_no_be_repaired", "duplicate", "not_a_damage", "wrong_damage", "other"], "nullable": true}, "closeReasonComment": {"type": "string", "description": "Close reason comment", "example": "It's a duplicate of damage X", "nullable": true}, "location": {"type": "string", "description": "Location where damage report has happened", "example": "Berlin", "nullable": true}, "isAccident": {"type": "boolean", "description": "Indicates an accident report", "example": true}, "isMinor": {"type": "boolean", "description": "Indicates if the damage is minor", "example": true}, "reporterNameLink": {"type": "string", "description": "Reporter (who send a link) name", "example": "<PERSON>", "nullable": true}, "reporter": {"$ref": "#/components/schemas/ReporterInfo"}, "car": {"$ref": "#/components/schemas/DamageReportCar"}, "repairOffers": {"description": "Damage repair offers", "type": "array", "items": {"$ref": "#/components/schemas/DamageRepairOffer"}}, "damageInsurance": {"$ref": "#/components/schemas/DamageInsurance"}, "subFleet": {"type": "string", "description": "Damaged car fleet", "example": "596702e0-02bd-4495-a1ee-f1aff42ac302"}, "createdAt": {"type": "string", "description": "Damage report created date", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}, "fleetRepairPrice": {"type": "string", "description": "Fleet repair price", "example": "20.99"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "reportPartnerData": {"type": "object", "description": "Partner report data information sent on web-app init call"}, "driverComment": {"type": "string", "description": "Driver comment about the damage"}, "managerComment": {"type": "string", "description": "Manager comment about the damage"}, "damageCategory": {"type": "string", "description": "Service lights description", "example": "RED_COOLANT_TEMPERATURE", "enum": ["RED_BRAKE_SYSTEM", "RED_BATTERY", "RED_OIL_LEVEL", "RED_COOLANT_TEMPERATURE", "RED_AIRBAGS", "YELLOW_BRAKE_WEAR", "YELLOW_ENGINE_CONTROL", "YELLOW_TIRE_PRESSURE", "YELLOW_ABS"]}, "documents": {"description": "Documents", "nullable": true, "type": "array", "items": {"$ref": "#/components/schemas/DamageDocument"}}, "images": {"description": "Damage images", "example": [{"id": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "type": "rear_side_general"}, {"id": "846a1788-0441-4bed-9a4c-0f31a4669d41", "type": "dashboard"}], "type": "array", "items": {"$ref": "#/components/schemas/ServiceLightsDamageImage"}}}, "required": ["id", "status", "statusHistory", "type", "location", "isAccident", "isMinor", "car", "subFleet", "createdAt", "damageCategory", "images"]}, "CompoundDamageImage": {"type": "object", "properties": {"id": {"type": "string", "description": "Damage image ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56"}, "type": {"type": "string", "description": "Damage image type", "enum": ["view_of_damage", "additional", "inspection"], "example": "view_of_damage"}, "category": {"type": "string", "description": "Image category, depends on its type. Subject to manual change - images with common type might eventually have different categories.", "example": "damage", "enum": ["damage", "other"], "nullable": false}}, "required": ["id", "type", "category"]}, "CompoundDamageReport": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3"}, "externalId": {"type": "string", "description": "External Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "nullable": true}, "status": {"type": "string", "description": "Damage report status", "example": "new", "enum": ["link_sent", "new", "estimation_postponed", "estimation_requested", "repair_offer_pending_expert_approval", "inspection_on_site", "inspection_on_site_approved", "inspection_scheduled", "repair_offer_estimated", "waiting_for_approval", "repair_offer_accepted", "repair_offer_rejected", "repair_offer_adjusted", "repair_scheduled", "internally_repaired", "in_repair", "repaired", "in_settlement", "settled", "closed"]}, "statusHistory": {"type": "object", "description": "Status change history", "properties": {"linkSent": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "new": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationPostponed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationRequested": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferPendingExpertApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSite": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSiteApproved": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferEstimated": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "waitingForApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAccepted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferRejected": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAdjusted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "internallyRepaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inRepair": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inSettlement": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "settled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "closed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}}}, "type": {"type": "string", "description": "Damage report type", "example": "ExteriorDamageReport", "enum": ["ExteriorDamageReport", "InteriorDamageReport", "ServiceLightsReport", "TelematicDamageReport", "MechanicalDamageReport", "CompoundDamageReport", "PreInspectionReport", "ThirdPartyReport"]}, "closeReason": {"type": "string", "description": "Close reason", "example": "duplicate", "enum": ["repaired", "will_no_be_repaired", "duplicate", "not_a_damage", "wrong_damage", "other"], "nullable": true}, "closeReasonComment": {"type": "string", "description": "Close reason comment", "example": "It's a duplicate of damage X", "nullable": true}, "location": {"type": "string", "description": "Location where damage report has happened", "example": "Berlin", "nullable": true}, "isAccident": {"type": "boolean", "description": "Indicates an accident report", "example": true}, "isMinor": {"type": "boolean", "description": "Indicates if the damage is minor", "example": true}, "reporterNameLink": {"type": "string", "description": "Reporter (who send a link) name", "example": "<PERSON>", "nullable": true}, "reporter": {"$ref": "#/components/schemas/ReporterInfo"}, "car": {"$ref": "#/components/schemas/DamageReportCar"}, "repairOffers": {"description": "Damage repair offers", "type": "array", "items": {"$ref": "#/components/schemas/DamageRepairOffer"}}, "damageInsurance": {"$ref": "#/components/schemas/DamageInsurance"}, "subFleet": {"type": "string", "description": "Damaged car fleet", "example": "596702e0-02bd-4495-a1ee-f1aff42ac302"}, "createdAt": {"type": "string", "description": "Damage report created date", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}, "fleetRepairPrice": {"type": "string", "description": "Fleet repair price", "example": "20.99"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "reportPartnerData": {"type": "object", "description": "Partner report data information sent on web-app init call"}, "driverComment": {"type": "string", "description": "Driver comment about the damage"}, "managerComment": {"type": "string", "description": "Manager comment about the damage"}, "damageDescription": {"type": "string", "description": "Damage report description. Alias for driverComment", "example": "Mehrere leichte Kratzer zwischen 12 und 3 Uhr", "nullable": true}, "damageCause": {"type": "array", "description": "Damage cause", "example": ["fixed_object_collision"], "nullable": true, "items": {"type": "string", "enum": ["fixed_object_collision", "person_collision", "animal_collision", "parked_car_collision", "running_over", "theft", "vandalism", "driving_off_the_road", "reversing_maneuvering", "overtaking_lane_change", "red_light_stop_sign", "turning", "environment", "other"]}}, "damageCauseNote": {"type": "string", "description": "Damage cause note", "example": "<PERSON><PERSON><PERSON> mit Tier", "nullable": true}, "damageCircumstances": {"type": "string", "description": "Damage circumstances", "example": "", "nullable": true}, "damageAddress": {"type": "string", "description": "Damage address", "example": "A45, Abfahrt Florstadt", "nullable": true}, "documents": {"description": "Documents", "nullable": true, "type": "array", "items": {"$ref": "#/components/schemas/DamageDocument"}}, "carParts": {"description": "Damaged car parts", "example": [{"name": "<PERSON><PERSON>", "type": "left_headlights", "area": "front"}, {"name": "Unterboden", "type": "underbody", "area": "other"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "console", "area": "cabin"}], "type": "array", "items": {"$ref": "#/components/schemas/CarPart"}}, "images": {"description": "Damage images", "example": [{"id": "846a1788-0441-4bed-9a4c-0f31a4669d41", "type": "view_of_damage"}, {"id": "64413987-15ec-4765-864e-2c0437d347b2", "type": "view_of_damage"}], "type": "array", "items": {"$ref": "#/components/schemas/CompoundDamageImage"}}, "damageDateTime": {"type": "string", "description": "Date and time of the accident", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}, "insuranceCoverage": {"type": "string", "description": "Insurance coverage", "example": "unknown", "enum": ["third party", "full", "partial", "unknown", "not_covered"], "nullable": true}}, "required": ["id", "status", "statusHistory", "type", "location", "isAccident", "isMinor", "car", "subFleet", "createdAt", "carParts", "images"]}, "MechanicalDamageCategory": {"type": "object", "properties": {"name": {"type": "string", "description": "Damage category name", "example": "Mechanical"}, "type": {"type": "string", "description": "Damage category type", "enum": ["mechanical"], "nullable": true, "example": "mechanical"}}, "required": ["name"]}, "MechanicalReportImage": {"type": "object", "properties": {"id": {"type": "string", "description": "Damage image ID", "example": "7c298125-09c4-4d71-a2b4-4ce272a8de56"}, "type": {"type": "string", "description": "Damage image type", "enum": ["front_left_angle", "rear_right_angle", "left_diagonal_view", "right_diagonal_view", "rear_side_general", "additional", "dashboard", "inspection", "vehicle_rear_side_view", "vehicle_front_side_view", "vehicle_driver_side_view", "vehicle_passenger_side_view", "uploaded"], "example": "dashboard"}}, "required": ["id", "type"]}, "MechanicalDamageReport": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3"}, "externalId": {"type": "string", "description": "External Id of the damage report", "example": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "nullable": true}, "status": {"type": "string", "description": "Damage report status", "example": "new", "enum": ["link_sent", "new", "estimation_postponed", "estimation_requested", "repair_offer_pending_expert_approval", "inspection_on_site", "inspection_on_site_approved", "inspection_scheduled", "repair_offer_estimated", "waiting_for_approval", "repair_offer_accepted", "repair_offer_rejected", "repair_offer_adjusted", "repair_scheduled", "internally_repaired", "in_repair", "repaired", "in_settlement", "settled", "closed"]}, "statusHistory": {"type": "object", "description": "Status change history", "properties": {"linkSent": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "new": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationPostponed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "estimationRequested": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferPendingExpertApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSite": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionOnSiteApproved": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inspectionScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferEstimated": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "waitingForApproval": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAccepted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferRejected": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairOfferAdjusted": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repairScheduled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "internallyRepaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inRepair": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "repaired": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "inSettlement": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "settled": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}, "closed": {"example": "2025-07-21T15:55:44.411Z", "type": "string", "format": "date-time"}}}, "type": {"type": "string", "description": "Damage report type", "example": "ExteriorDamageReport", "enum": ["ExteriorDamageReport", "InteriorDamageReport", "ServiceLightsReport", "TelematicDamageReport", "MechanicalDamageReport", "CompoundDamageReport", "PreInspectionReport", "ThirdPartyReport"]}, "closeReason": {"type": "string", "description": "Close reason", "example": "duplicate", "enum": ["repaired", "will_no_be_repaired", "duplicate", "not_a_damage", "wrong_damage", "other"], "nullable": true}, "closeReasonComment": {"type": "string", "description": "Close reason comment", "example": "It's a duplicate of damage X", "nullable": true}, "location": {"type": "string", "description": "Location where damage report has happened", "example": "Berlin", "nullable": true}, "isAccident": {"type": "boolean", "description": "Indicates an accident report", "example": true}, "isMinor": {"type": "boolean", "description": "Indicates if the damage is minor", "example": true}, "reporterNameLink": {"type": "string", "description": "Reporter (who send a link) name", "example": "<PERSON>", "nullable": true}, "reporter": {"$ref": "#/components/schemas/ReporterInfo"}, "car": {"$ref": "#/components/schemas/DamageReportCar"}, "repairOffers": {"description": "Damage repair offers", "type": "array", "items": {"$ref": "#/components/schemas/DamageRepairOffer"}}, "damageInsurance": {"$ref": "#/components/schemas/DamageInsurance"}, "subFleet": {"type": "string", "description": "Damaged car fleet", "example": "596702e0-02bd-4495-a1ee-f1aff42ac302"}, "createdAt": {"type": "string", "description": "Damage report created date", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}, "fleetRepairPrice": {"type": "string", "description": "Fleet repair price", "example": "20.99"}, "tags": {"description": "Tags", "type": "array", "items": {"type": "string"}}, "reportPartnerData": {"type": "object", "description": "Partner report data information sent on web-app init call"}, "driverComment": {"type": "string", "description": "Driver comment about the damage"}, "managerComment": {"type": "string", "description": "Manager comment about the damage"}, "damageDescription": {"type": "string", "description": "Damage report description. Alias for driverComment", "example": "Mehrere leichte Kratzer zwischen 12 und 3 Uhr", "nullable": true}, "damageCategories": {"description": "Damage categories", "example": [{"name": "<PERSON><PERSON><PERSON>", "type": "scratch"}], "type": "array", "items": {"$ref": "#/components/schemas/MechanicalDamageCategory"}}, "documents": {"description": "Documents", "nullable": true, "type": "array", "items": {"$ref": "#/components/schemas/DamageDocument"}}, "images": {"description": "Damage images", "example": [{"id": "89e34bfb-e8d8-461e-af91-76e4ce449ce3", "type": "rear_side_general"}], "type": "array", "items": {"$ref": "#/components/schemas/MechanicalReportImage"}}, "category": {"type": "string", "description": "Image category, depends on its type. Subject to manual change - images with common type might eventually have different categories.", "example": "damage", "enum": ["damage", "other"], "nullable": false}, "carParts": {"description": "Damaged car parts", "example": [{"name": "<PERSON><PERSON>", "type": "left_headlights", "area": "front"}], "type": "array", "items": {"$ref": "#/components/schemas/CarPart"}}}, "required": ["id", "status", "statusHistory", "type", "location", "isAccident", "isMinor", "car", "subFleet", "createdAt", "damageCategories", "images", "category", "carParts"]}, "DamageReportResponse": {"type": "object", "properties": {"damageReport": {"type": "object", "additionalProperties": true, "properties": {"type": {"type": "string", "description": "Damage report type", "example": "ExteriorDamageReport", "enum": ["ExteriorDamageReport", "InteriorDamageReport", "ServiceLightsReport", "MechanicalDamageReport", "CompoundDamageReport"]}}, "required": ["type"]}}, "required": ["damageReport"]}, "ForbiddenErrorResponse": {"type": "object", "properties": {"status": {"type": "number", "example": 403, "description": "HTTP status code"}, "message": {"type": "string", "description": "Error message", "example": "You cannot do this operation."}}, "required": ["status", "message"]}, "DamageReportDocument": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the document", "example": "f4d26ffc-f3e4-4c01-9ea1-e5b5968343d9", "format": "uuid"}, "submittedDate": {"type": "string", "description": "Document submitted date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "type": {"type": "string", "description": "Document type", "enum": ["damage_invoice", "damage_repair_offer", "damage_repair_offer_adjustment", "damage_accident_report", "assessment_report_document", "damage_report_document"], "example": "damage_report_document"}, "name": {"type": "string", "description": "Document name", "example": "EG-0000-000000_SCHADENANZEIGE_AUSGEFUELLT_0000000_0a8d_2020-10-12_11-30-03.pdf", "nullable": true}, "mimeType": {"type": "string", "description": "Document mime type", "example": "application/pdf", "nullable": true}, "size": {"type": "number", "description": "Document size, in bytes", "example": 1166084, "nullable": true}, "comment": {"type": "string", "description": "Document comment", "example": "Die Reparaturrechnung ist hochgeladen.", "nullable": true}, "isFinal": {"type": "boolean", "description": "Is final invoice", "nullable": true}}, "required": ["id", "submittedDate", "type", "name"]}, "DamageReportDocumentsList": {"type": "object", "properties": {"documents": {"description": "Damage report documents", "type": "array", "items": {"$ref": "#/components/schemas/DamageReportDocument"}}, "total": {"type": "number", "description": "Total number of the damage report documents, which fall under a given filter", "example": 1}}, "required": ["documents", "total"]}, "DamageStatusChangeDto": {"type": "object", "properties": {"status": {"type": "string", "description": "Parameter status to which change  current damage report status", "enum": ["estimation_postponed", "internally_repaired"], "example": "estimation_postponed"}}, "required": ["status"]}, "OperationResult": {"type": "object", "properties": {"success": {"type": "boolean", "description": "Operation result", "example": true}}, "required": ["success"]}, "BadRequestErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "description": "The HTTP status code.", "example": 400}, "message": {"description": "Error message", "example": "Bad data provided", "type": "array", "items": {"type": "string"}}, "error": {"type": "string", "description": "A short description of the error.", "example": "Bad Request"}}, "required": ["statusCode", "message", "error"]}, "RequestAssessmentReportDto": {"type": "object", "properties": {"comment": {"type": "string", "description": "Comment", "example": "Please provide an assessment report."}}}, "DamageReportUpdateDto": {"type": "object", "properties": {"externalId": {"type": "string", "description": "Internal damage report id at partner side. Send empty string to unset", "example": "qwerty:1234567"}}}, "RequestRepairOfferDto": {"type": "object", "properties": {"comment": {"type": "string", "description": "comment", "example": "Please estimate the cost of fixing the damage"}}}, "RequestRepairOffer": {"type": "object", "properties": {"repairOfferRequestId": {"type": "string", "description": "ID of created repair offer request", "example": "2cfa02e8-1b89-44b7-b933-f683c2d7c3d6"}, "comment": {"type": "string", "description": "Comment", "example": "Please estmiate the cost of fixing the damage"}, "createdAt": {"type": "string", "description": "Repair offer request created timestamp", "example": "2019-03-24T17:11:22.964Z", "format": "date-time"}}, "required": ["repairOfferRequestId", "createdAt"]}, "RepairOfferDocument": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the repair offer document", "example": "f4d26ffc-f3e4-4c01-9ea1-e5b5968343d9", "format": "uuid"}, "type": {"type": "string", "description": "Type of repair offer document", "enum": ["offer"], "example": "offer"}}, "required": ["id", "type"]}, "RepairOfferBodyshop": {"type": "object", "properties": {"name": {"type": "string", "description": "Bodyshop name", "example": "Motum repair"}, "address": {"type": "string", "description": "Bodyshop address", "example": "48155, <PERSON><PERSON><PERSON><PERSON> 16, <PERSON><PERSON>"}, "phoneNumber": {"type": "string", "description": "Phone number", "example": "+4912345678910"}, "contactPerson": {"type": "string", "description": "Contact person", "example": "<PERSON>"}}, "required": ["name", "address", "phoneNumber", "<PERSON><PERSON><PERSON>"]}, "Appointment": {"type": "object", "properties": {"id": {"type": "string", "description": "Appointment identifier", "example": "119a9295-9e2c-4d25-bc1f-8d0dad233234", "format": "uuid"}, "type": {"type": "string", "description": "Appointment type", "example": "car_handover", "enum": ["inspection_on_site", "car_handover"]}, "date": {"type": "string", "description": "Appointment date", "example": "2025-07-21T15:55:44.778Z", "format": "date-time"}, "comment": {"type": "string", "description": "Appointment comment", "example": "You can park on the other side of the street", "nullable": true}, "isAccepted": {"type": "boolean", "description": "If the appointemnt is accepted", "example": false}}, "required": ["id", "type", "date", "comment", "isAccepted"]}, "Adjustment": {"type": "object", "properties": {"id": {"type": "string", "description": "Adjustment identifier", "example": "119a9295-9e2c-4d25-bc1f-8d0dad233234", "format": "uuid"}, "status": {"type": "string", "description": "Adjustment Status", "example": "new", "enum": ["new", "adjustment_approved", "adjustment_rejected"]}, "price": {"type": "number", "description": "Adjustment Price", "example": 1000}, "reason": {"type": "string", "description": "Adjustment Reason", "example": "Need something"}, "documentId": {"type": "string", "description": "Adjustment Document Id", "example": "119a9295-9e2c-4d25-bc1f-8d0dad233234"}, "createdAt": {"type": "string", "description": "Adjustment created date", "example": "2025-07-21T15:55:44.778Z", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Adjustment updated date", "example": "2025-07-21T15:55:44.778Z", "format": "date-time"}, "rejectReason": {"type": "string", "description": "Adjustment rejected reason", "example": "Rejected because of something", "nullable": true}}, "required": ["id", "status", "price", "reason", "documentId", "createdAt", "updatedAt", "rejectReason"]}, "Approvals": {"type": "object", "properties": {"insurance": {"type": "object", "description": "Repair offer approved by insurance date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "leasing": {"type": "object", "description": "Repair offer approved by leasing date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "fleet": {"type": "object", "description": "Repair offer approved by fleet date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}}}, "RepairOffer": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the repair offer", "example": "f4d26ffc-f3e4-4c01-9ea1-e5b5968343d9", "format": "uuid"}, "status": {"type": "string", "description": "Repair offer status", "example": "new", "enum": ["requested", "waiting_for_approval", "inspection_required", "inspection_approved", "expert_approval_required", "new", "accepted", "cancelled", "rejected"]}, "durationInDays": {"type": "number", "description": "Repair duration estimation", "example": 5}, "price": {"type": "number", "description": "Repair offer price", "example": 627.14}, "scope": {"type": "string", "description": "Repair scope", "example": "Der Kotflügel muss erneuert werden sowie lackiert. Die Stoßstange vorne muss ebenfalls instandgesetzt und lackiert werden."}, "repairStart": {"type": "string", "description": "Repair start", "example": "2025-07-21T15:55:44.778Z", "format": "date-time"}, "repairEndPlanned": {"type": "string", "description": "Repair end planned", "example": "2025-07-26T15:55:44.778Z", "format": "date-time"}, "documents": {"description": "Repair offer documents", "example": [{"id": "e13ded49-9292-498e-aaae-4bd1ab555101", "type": "offer"}], "type": "array", "items": {"$ref": "#/components/schemas/RepairOfferDocument"}}, "bodyshop": {"description": "Bodyshop assigned to repair offer", "type": "array", "items": {"$ref": "#/components/schemas/RepairOfferBodyshop"}}, "appointments": {"description": "Appointments", "type": "array", "items": {"$ref": "#/components/schemas/Appointment"}}, "adjustments": {"description": "Adjustments", "nullable": true, "type": "array", "items": {"$ref": "#/components/schemas/Adjustment"}}, "createdAt": {"type": "string", "description": "Repair offer created date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Repair offer updated date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "approvals": {"description": "Repair offer approvals", "example": {"insurance": "2025-07-23T15:55:44.779Z", "leasing": "2025-07-22T15:55:44.779Z", "fleet": "2025-07-21T15:55:44.779Z"}, "type": "array", "items": {"$ref": "#/components/schemas/Approvals"}}}, "required": ["id", "status", "documents", "bodyshop", "appointments", "createdAt", "updatedAt"]}, "RepairOfferList": {"type": "object", "properties": {"total": {"type": "number", "description": "Total number of the damage repair offers", "example": 1}, "repairOffers": {"description": "Damage report repair offers", "type": "array", "items": {"$ref": "#/components/schemas/RepairOffer"}}, "currency": {"type": "string", "description": "Fleet's currency", "enum": ["EUR", "CHF"], "example": "EUR", "default": "EUR"}}, "required": ["total", "repairOffers", "currency"]}, "RepairOfferDetails": {"type": "object", "properties": {"repairOffer": {"description": "Repair offer", "allOf": [{"$ref": "#/components/schemas/RepairOffer"}]}, "currency": {"type": "string", "description": "Fleet's currency", "enum": ["EUR", "CHF"], "example": "EUR", "default": "EUR"}}, "required": ["repairOffer", "currency"]}, "RejectRepairOfferDto": {"type": "object", "properties": {"rejectionReason": {"type": "string", "description": "Rejection reason", "example": "Too long repair duration"}, "comment": {"type": "string", "description": "Comment", "nullable": true, "example": "I need repair asap"}}, "required": ["rejectionReason"]}, "FleetManager": {"type": "object", "properties": {"firstName": {"type": "string", "description": "Fleet manager first name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Fleet manager last name", "example": "<PERSON><PERSON>"}, "email": {"type": "string", "description": "Fleet manager email", "example": "<EMAIL>"}, "phoneNumber": {"type": "string", "description": "Fleet manager phone number", "example": "+49 30 901820"}, "isPrimary": {"type": "boolean", "description": "Use this option for specify primary contact for pool cars", "example": true}}, "required": ["firstName", "lastName", "email", "phoneNumber"]}, "FleetOnboardingDto": {"type": "object", "properties": {"name": {"type": "string", "description": "Fleet name", "example": "Motum"}, "fleetPartnerId": {"type": "string", "description": "Internal fleet id at partner side is required property to identify farther fleet in next api calls", "example": "qwerty:1234567"}, "parentFleetId": {"type": "string", "description": "Parent fleet ID. Required if multiple parent fleets exists", "example": "a0b346d6-6f5f-4893-99f8-ed2358342309"}, "invoiceAddress": {"type": "string", "description": "Invoice address", "example": "Lindwurmstraße 12, 80805 München"}, "invoiceNotificationEmail": {"type": "string", "description": "Invoice email", "example": "<EMAIL>"}, "repairReleaseLimit": {"type": "number", "description": "Repair release limit of the fleet", "example": 1000}, "repairReleaseLimitKasko": {"type": "number", "description": "KASKO repair release limit", "example": 1500}, "expertAssessmentLimit": {"type": "number", "description": "Expert assessment limit", "example": 1500}, "subdomain": {"type": "string", "description": "Subdomain. It is used to create a link to the web app in a format <subdomain>.motum.app. If not provided it will be generated from the fleet name", "example": "subfleet"}, "fleetManagers": {"description": "The list of fleet managers", "type": "array", "items": {"$ref": "#/components/schemas/FleetManager"}}, "rentalCarKasko": {"type": "string", "description": "Class of the rental car", "enum": ["Inquiries", "EXCLUSIVELY Class A", "Class A to equivalent", "Class A to one class lower", "EXCLUSIVELY equivalent", "EXCLUSIVELY one class lower"], "example": "Class A to equivalent"}, "rentalCarThirdParty": {"type": "string", "description": "Class of the rental car", "enum": ["Inquiries", "EXCLUSIVELY Class A", "Class A to equivalent", "Class A to one class lower", "EXCLUSIVELY equivalent", "EXCLUSIVELY one class lower"], "example": "Class A to one class lower"}, "currency": {"type": "string", "description": "Fleet's currency", "enum": ["EUR", "CHF"], "example": "EUR", "default": "EUR"}, "percentageAppliedVAT": {"type": "number", "description": "Fleet's tax rate percent", "example": "19"}, "countryCode": {"type": "string", "description": "The country fleet belongs to", "example": "de", "enum": ["ch", "de", "at", "fr"], "default": "de"}}, "required": ["name", "fleetPartnerId"]}, "ConflictErrorResponse": {"type": "object", "properties": {"status": {"type": "number", "example": 409, "description": "HTTP status code"}, "message": {"type": "string", "description": "Error message", "example": "Conflict"}}, "required": ["status", "message"]}, "UnprocessableErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "example": 422, "description": "HTTP status code"}, "message": {"type": "string", "description": "Error message", "example": "User is disabled"}, "error": {"type": "string", "description": "A short description of the HTTP error", "example": "Unprocessable Entity"}}, "required": ["statusCode", "message"]}, "FleetConfiguration": {"type": "object", "properties": {"name": {"type": "string", "description": "Fleet name", "example": "Motum"}, "fleetPartnerId": {"type": "string", "description": "Internal fleet id at partner side", "example": "qwerty:1234567"}, "invoiceAddress": {"type": "string", "description": "Invoice address", "example": "Lindwurmstraße 12, 80805 München"}, "invoiceNotificationEmail": {"type": "string", "description": "Invoice email", "example": "<EMAIL>"}, "repairReleaseLimit": {"type": "number", "description": "Repair release limit of the fleet", "example": 1000}, "repairReleaseLimitKasko": {"type": "number", "description": "KASKO repair release limit", "example": 1500}, "expertAssessmentLimit": {"type": "number", "description": "Expert assessment limit", "example": 1500}, "subdomain": {"type": "string", "description": "Subdomain", "example": "subfleet"}, "appUrl": {"type": "string", "description": "Driver App URL", "example": "https://subfleet.motum.app"}, "rentalCarKasko": {"type": "string", "description": "Class of the rental car", "enum": ["Inquiries", "EXCLUSIVELY Class A", "Class A to equivalent", "Class A to one class lower", "EXCLUSIVELY equivalent", "EXCLUSIVELY one class lower"], "example": "Class A to equivalent"}, "rentalCarThirdParty": {"type": "string", "description": "Class of the rental car", "enum": ["Inquiries", "EXCLUSIVELY Class A", "Class A to equivalent", "Class A to one class lower", "EXCLUSIVELY equivalent", "EXCLUSIVELY one class lower"], "example": "Class A to one class lower"}, "currency": {"type": "string", "description": "Fleet's currency", "enum": ["EUR", "CHF"], "example": "EUR"}, "percentageAppliedVAT": {"type": "number", "description": "Fleet's tax rate percent", "example": "19"}, "countryCode": {"type": "string", "description": "The country fleet belongs to", "example": "de", "enum": ["ch", "de", "at", "fr"]}}, "required": ["name", "subdomain", "appUrl"]}, "FleetConfigurationData": {"type": "object", "properties": {"fleetConfiguration": {"description": "Fleet configuration", "allOf": [{"$ref": "#/components/schemas/FleetConfiguration"}]}}, "required": ["fleetConfiguration"]}, "FleetListItem": {"type": "object", "properties": {"id": {"type": "string", "description": "Fleet id", "example": "a0b346d6-6f5f-4893-99f8-ed2358342309", "format": "uuid"}, "partnerId": {"type": "string", "description": "Fleet's id on the partner side", "example": "qwerty:1234567"}, "title": {"type": "string", "description": "Fleet's title", "example": "ACME Title"}, "name": {"type": "string", "description": "Fleet's name", "example": "ACME"}, "parentFleetId": {"type": "string", "description": "Fleet's parent fleet ID", "example": "SomeParentFleetID"}, "parentFleetName": {"type": "string", "description": "Name of the parent fleet", "example": "ACME Group"}}, "required": ["id", "title", "name"]}, "FleetsList": {"type": "object", "properties": {"fleets": {"description": "Fleets list", "type": "array", "items": {"$ref": "#/components/schemas/FleetListItem"}}, "total": {"type": "number", "description": "Total number of the fleets", "example": 1}}, "required": ["fleets", "total"]}, "FleetRemoval": {"type": "object", "properties": {"name": {"type": "string", "description": "Fleet name", "example": "Motum"}, "fleetPartnerId": {"type": "string", "description": "Internal fleet id at partner side", "example": "qwerty:287491"}, "isRemoved": {"type": "boolean", "description": "Whether it was removed"}}, "required": ["name", "isRemoved"]}, "CarLeasingDto": {"type": "object", "properties": {"leasingCompany": {"type": "string", "description": "Car leasing company", "example": "Athlon Germany GmbH"}, "leasingRepairShopRegulation": {"type": "string", "description": "Leasing repair shop regulation", "example": "OEM"}, "leasingContractNumber": {"type": "string", "description": "Leasing contract number", "example": "1234567"}, "leasingStartDate": {"type": "string", "description": "Leasing start date", "example": "17-07-2019"}, "leasingEndDate": {"type": "string", "description": "Leasing end date", "example": "17-07-2025"}}, "required": ["leasingCompany", "leasingRepairShopRegulation"]}, "CarInsuranceDto": {"type": "object", "properties": {"insuranceCompany": {"type": "string", "description": "Car insurance company", "example": "ERGO Versicherung AG"}, "insuranceNumber": {"type": "string", "description": "Insurance number", "example": "1234678", "nullable": true}, "insurerPhone": {"type": "string", "description": "Insurer phone number", "example": "+49 30 901821"}, "insurerAddress": {"type": "string", "description": "Insurer address", "example": "Lindwurmstraße 12, 80805 München"}, "insuranceBroker": {"type": "string", "description": "Insurance broker", "example": "<PERSON>"}, "costCenter": {"type": "string", "description": "Cost center", "example": "205-3625", "nullable": true}, "insurer": {"type": "string", "description": "Insurer", "example": "Insurer"}, "deductibleTk": {"type": "number", "description": "TK (SB)", "nullable": true, "example": 1000}, "deductibleVk": {"type": "number", "description": "VK (SB)", "example": 1000, "nullable": true}}, "required": ["insuranceCompany"]}, "AddCarDto": {"type": "object", "properties": {"licensePlate": {"type": "string", "description": "Car license plate", "example": "TE-ST 1298"}, "carPartnerId": {"type": "string", "description": "Internal car id at partner side", "example": "qwerty:1234567"}, "vin": {"type": "string", "description": "Car VIN", "example": "WME4533911K386603"}, "location": {"type": "string", "description": "Car location", "nullable": true, "example": "Berlin"}, "make": {"type": "string", "description": "Car make", "example": "Nissan"}, "model": {"type": "string", "description": "Car Model", "example": "Leaf ZE1 MY 19"}, "driver": {"type": "string", "description": "Identifier of user with driver role", "example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3"}, "firstRegistration": {"type": "string", "description": "Car first registration date", "example": "17-07-2019"}, "warrantyExpirationDate": {"type": "string", "description": "Car warranty expiration date", "example": "17-07-2019"}, "lastInspectionDate": {"type": "string", "description": "Car last inspection date", "example": "17-07-2019"}, "nextInspectionDate": {"type": "string", "description": "Car next inspection date", "example": "17-07-2021"}, "isPool": {"type": "boolean", "description": "Is car is a pool car", "example": true}, "leasing": {"$ref": "#/components/schemas/CarLeasingDto"}, "insurance": {"$ref": "#/components/schemas/CarInsuranceDto"}, "bodyType": {"type": "string", "description": "Car body type", "example": "sedan", "default": "other", "enum": ["hatchback", "fastback", "sedan", "station_wagon", "minivan", "van", "transporter", "roadster", "off_road", "light_truck", "hacknet", "suv", "other", "convertible", "combi", "subcompact", "compact", "compact_e"]}, "fleetIdentifier": {"type": "string", "description": "Fleet's ID. If not provided, car will be added to the default fleet", "example": "a0b346d6-6f5f-4893-99f8-ed2358342309"}}, "required": ["vin", "make", "model", "isPool"]}, "AddCarDeprecatedDto": {"type": "object", "properties": {"licensePlate": {"type": "string", "description": "Car license plate", "example": "TE-ST 1298"}, "carPartnerId": {"type": "string", "description": "Internal car id at partner side", "example": "qwerty:1234567"}, "vin": {"type": "string", "description": "Car VIN", "example": "WME4533911K386603"}, "location": {"type": "string", "description": "Car location", "nullable": true, "example": "Berlin"}, "make": {"type": "string", "description": "Car make", "example": "Nissan"}, "model": {"type": "string", "description": "Car Model", "example": "Leaf ZE1 MY 19"}, "driver": {"type": "string", "description": "Identifier of user with driver role", "example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3"}, "firstRegistration": {"type": "string", "description": "Car first registration date", "example": "17-07-2019"}, "warrantyExpirationDate": {"type": "string", "description": "Car warranty expiration date", "example": "17-07-2019"}, "lastInspectionDate": {"type": "string", "description": "Car last inspection date", "example": "17-07-2019"}, "nextInspectionDate": {"type": "string", "description": "Car next inspection date", "example": "17-07-2021"}, "isPool": {"type": "boolean", "description": "Is car is a pool car", "example": true}, "leasing": {"$ref": "#/components/schemas/CarLeasingDto"}, "insurance": {"$ref": "#/components/schemas/CarInsuranceDto"}, "bodyType": {"type": "string", "description": "Car body type", "example": "sedan", "default": "other", "enum": ["hatchback", "fastback", "sedan", "station_wagon", "minivan", "van", "transporter", "roadster", "off_road", "light_truck", "hacknet", "suv", "other", "convertible", "combi", "subcompact", "compact", "compact_e"]}}, "required": ["vin", "make", "model", "isPool"]}, "CarLeasing": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the leasing", "example": "91a7e506-8fc0-4053-9f4e-fd30be8c16e5", "format": "uuid"}, "createdAt": {"type": "string", "description": "Leasing created date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Leasing updated date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "leasingCompany": {"type": "string", "description": "Name of the leasing company", "example": "Leasing Company Name"}, "leasingRepairShopRegulation": {"type": "string", "description": "Leasing repair shop regulation", "example": "LCN"}, "leasingContractNumber": {"type": "string", "nullable": true, "description": "Contract number of leasing", "example": null}, "leasingStartDate": {"format": "date", "type": "string", "nullable": true, "description": "Leasing start date", "example": "2019-07-17"}, "leasingEndDate": {"format": "date", "type": "string", "nullable": true, "description": "Leasing end date", "example": "2025-07-17"}}, "required": ["id", "createdAt", "updatedAt", "leasingCompany", "leasingRepairShopRegulation"]}, "CarInsurance": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the insurance", "example": "91a7e506-8fc0-4053-9f4e-fd30be8c16e5", "format": "uuid"}, "createdAt": {"type": "string", "description": "Insurance created date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Insurance updated date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "insuranceCompany": {"type": "string", "description": "Car insurance company", "example": "ERGO Versicherung AG"}, "insuranceBroker": {"type": "string", "nullable": true, "description": "Insurance broker", "example": "BrockerName"}, "insuranceNumber": {"type": "string", "description": "Insurance number", "example": "123123", "nullable": true}, "deductibleTk": {"type": "number", "nullable": false, "description": "TK (SB)", "example": "1000"}, "costCenter": {"type": "string", "nullable": true, "description": "Cost center", "example": "205-3625"}, "deductibleVk": {"type": "number", "nullable": false, "description": "VK (SB)", "example": "1000"}, "insurer": {"type": "string", "description": "Insurer", "example": "Insurer"}, "insurerPhone": {"type": "string", "description": "Phone of the insurer", "example": "+*********"}, "insurerAddress": {"type": "string", "description": "Address of the insurer", "example": "Lindwurmstraße 12, 80805 München"}}, "required": ["id", "createdAt", "updatedAt", "insuranceCompany"]}, "Car": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the car", "example": "f4d26ffc-f3e4-4c01-9ea1-e5b5968343d9", "format": "uuid"}, "carPartnerId": {"type": "string", "description": "Cars id on the partner side", "example": "qwerty:1234567"}, "licensePlate": {"type": "string", "description": "Car license plate", "example": "TE ST 0001"}, "vin": {"type": "string", "description": "Car VIN", "example": "WME4533911K386603"}, "mileage": {"type": "number", "description": "Car mileage", "example": 10}, "location": {"type": "string", "description": "Car location", "example": "Berlin", "nullable": true}, "fleetId": {"type": "string", "description": "Fleet id", "example": "a0b346d6-6f5f-4893-99f8-ed2358342309", "nullable": false}, "fleetPartnerId": {"type": "string", "description": "Sub fleet car partner id", "example": "qwerty:1234567"}, "model": {"type": "string", "description": "Car Model", "example": "Transit FT 300K"}, "make": {"type": "string", "description": "Car make", "example": "Ford"}, "bodyType": {"type": "string", "description": "Car body type", "example": "sedan", "enum": ["hatchback", "fastback", "sedan", "station_wagon", "minivan", "van", "transporter", "roadster", "off_road", "light_truck", "hacknet", "suv", "other", "convertible", "combi", "subcompact", "compact", "compact_e"]}, "isPool": {"type": "boolean", "description": "Is pool car", "example": true}, "isActive": {"type": "boolean", "description": "Car is active", "example": true}, "firstRegistration": {"type": "string", "description": "Car first registration date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "createdAt": {"type": "string", "description": "Car created date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "updatedAt": {"type": "string", "description": "Car updated date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "leasings": {"description": "Car leasing information", "type": "array", "items": {"$ref": "#/components/schemas/CarLeasing"}}, "insurances": {"description": "Car insurance information", "type": "array", "items": {"$ref": "#/components/schemas/CarInsurance"}}, "driver": {"$ref": "#/components/schemas/CarDriver"}, "availability": {"type": "string", "description": "Car availability", "example": "AVAILABLE", "enum": ["AVAILABLE", "UNAVAILABLE", "PARTIALLY_AVAILABLE"]}}, "required": ["id", "vin", "fleetId", "model", "make", "bodyType", "isPool", "isActive", "createdAt", "updatedAt", "availability"]}, "CarsList": {"type": "object", "properties": {"cars": {"description": "Fleet cars", "type": "array", "items": {"$ref": "#/components/schemas/Car"}}, "total": {"type": "number", "description": "Total number of the cars", "example": 1}}, "required": ["cars", "total"]}, "CarDetails": {"type": "object", "properties": {"car": {"description": "Fleet car", "allOf": [{"$ref": "#/components/schemas/Car"}]}}, "required": ["car"]}, "UpdateCarInsuranceDto": {"type": "object", "properties": {"insuranceNumber": {"type": "string", "description": "Insurance number", "example": "1234678", "nullable": true}, "insurerPhone": {"type": "string", "description": "Insurer phone number", "example": "+49 30 901821"}, "insurerAddress": {"type": "string", "description": "Insurer address", "example": "Lindwurmstraße 12, 80805 München"}, "insuranceBroker": {"type": "string", "description": "Insurance broker", "example": "<PERSON>"}, "costCenter": {"type": "string", "description": "Cost center", "example": "205-3625", "nullable": true}, "insurer": {"type": "string", "description": "Insurer", "example": "Insurer"}, "deductibleTk": {"type": "number", "description": "TK (SB)", "nullable": true, "example": 1000}, "deductibleVk": {"type": "number", "description": "VK (SB)", "example": 1000, "nullable": true}, "insuranceCompany": {"type": "string", "description": "Car insurance company", "example": "ERGO Versicherung AG"}}}, "UpdateCarDto": {"type": "object", "properties": {"licensePlate": {"type": "string", "description": "Car license plate", "example": "TE-ST 1298"}, "vin": {"type": "string", "description": "Car VIN", "example": "WME4533911K386603"}, "fleetIdentifier": {"type": "string", "description": "Fleet's ID or fleet's Partner-ID", "example": "a0b346d6-6f5f-4893-99f8-ed2358342309"}, "carPartnerId": {"type": "string", "description": "Internal car id at partner side", "example": "qwerty:1234567"}, "location": {"type": "string", "description": "Car location", "nullable": true, "example": "Berlin"}, "isPool": {"type": "boolean", "description": "Is car is a pool car", "example": true}, "isActive": {"type": "boolean", "description": "Is car active", "example": true}, "driver": {"type": "string", "description": "Identifier of user with driver role", "example": "a6c74881-e685-489c-ba69-2cfc9e34c4a3"}, "insurance": {"description": "Car insurance", "allOf": [{"$ref": "#/components/schemas/UpdateCarInsuranceDto"}]}, "leasing": {"$ref": "#/components/schemas/CarLeasingDto"}, "availability": {"type": "string", "description": "Car availability", "example": "AVAILABLE", "enum": ["AVAILABLE", "UNAVAILABLE", "PARTIALLY_AVAILABLE"]}, "bodyType": {"type": "string", "description": "Car body type", "example": "sedan", "enum": ["hatchback", "fastback", "sedan", "station_wagon", "minivan", "van", "transporter", "roadster", "off_road", "light_truck", "hacknet", "suv", "other", "convertible", "combi", "subcompact", "compact", "compact_e"]}}}, "RejectRepairOfferAdjustmentDto": {"type": "object", "properties": {"rejectionReason": {"type": "string", "description": "Rejection reason", "example": "Too long repair duration"}}, "required": ["rejectionReason"]}, "PortalLinkResponseAuth": {"type": "object", "properties": {"url": {"type": "string", "description": "The Portal URl to Send Link Dialog", "example": "http://portal.motum.eu?code=snfllakjsld234"}}, "required": ["url"]}, "VisibilityScope": {"type": "object", "properties": {"location": {"type": "string", "description": "Visibility location", "example": "Berlin"}, "fleetPartnerId": {"type": "string", "description": "Visibility fleet id", "example": "41bca391-d294-4162-81ff-04c41d36b45f"}}}, "User": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the user", "example": "f4d26ffc-f3e4-4c01-9ea1-e5b5968343d9", "format": "uuid"}, "userPartnerId": {"type": "string", "description": "Internal user id at partner side", "example": "qwerty:1234567"}, "email": {"type": "string", "description": "User email", "example": "<EMAIL>"}, "firstName": {"type": "string", "description": "User firstName", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "User lastname", "example": "<PERSON><PERSON>"}, "phoneNumber": {"type": "string", "description": "User phone", "example": "+*********"}, "role": {"type": "string", "enum": ["fleet_manager", "driver", "local_fleet_manager", "regional_fleet_manager"], "description": "User role", "example": "fleet_manager"}, "visibilityScope": {"description": "###User Visibility Scope\n\nThis scope determines what a user can see regarding cars and damages.\n\nIf no scope is specified, the user can see all cars and all damages.\n\nHere are the rules for when a scope is defined:\n- A user can only see cars and damages within their scope.\n- If a car is within the user's scope, they can see all its damages.\n- If a car is outside the user's scope, they can only see damages within their scope.\n\nFor fleets with subfleets, a parent fleet manager's view can be limited to certain subfleets by location, fleetId, or both.\n- Location-based scopes allow users to see all cars and damages in a particular location.\n- FleetId-based scopes allow users to see all cars and damages in a specific fleet.\n- Location and fleetId-based scopes allow users to see all cars and damages in a specific fleet in a particular location.\n\n\nThe VisibilityScope object requires at least one field:\n- location: string\n- fleetPartnerId: string\n\nWith only 'location' defined, the user has unrestricted access to subfleets in that location.\nWith only 'fleetPartnerId' defined, the user has unrestricted access to any car location within the subfleets.\n\nIf both 'location' and 'fleetPartnerId' are defined, the user has limited access to both subfleets and car locations.\n\nNote: One scope can override another. For instance, if a user's visibility scope includes:\n```json\n[\n  {\n    location: 'Berlin',\n    fleetPartnerId: '41bca391',\n  },\n  {\n    location: 'Munich',\n  },\n]\n```\nThey would still be able to see cars in Munich for the fleet with id '41bca391'.\n", "type": "array", "items": {"$ref": "#/components/schemas/VisibilityScope"}}}, "required": ["id", "email", "role"]}, "UsersList": {"type": "object", "properties": {"users": {"description": "Fleet users", "type": "array", "items": {"$ref": "#/components/schemas/User"}}, "total": {"type": "number", "description": "Total number of the cars", "example": 1}}, "required": ["users", "total"]}, "VisibilityScopeDto": {"type": "object", "properties": {"fleetPartnerId": {"type": "object", "description": "Internal fleet ID at partner side", "example": "13ed73c1-afcf-4294-ad63-3b00aeff4ed1"}, "location": {"type": "object", "description": "Location", "example": "Lake Felton"}}}, "CreateUserDto": {"type": "object", "properties": {"fleetPartnerId": {"type": "string", "description": "Internal fleet ID at partner side", "example": "372c26ee-041d-4332-9003-4231c8f6b210"}, "email": {"type": "string", "description": "Email", "example": "<PERSON><PERSON><EMAIL>"}, "userPartnerId": {"type": "string", "description": "Internal user ID at partner side", "example": "driver-2"}, "role": {"type": "string", "enum": ["fleet_manager", "local_fleet_manager", "regional_fleet_manager", "driver"], "description": "UserRole", "example": "fleet_manager"}, "visibilityScope": {"description": "###User Visibility Scope\n\nThis scope determines what a user can see regarding cars and damages.\n\nIf no scope is specified, the user can see all cars and all damages.\n\nHere are the rules for when a scope is defined:\n- A user can only see cars and damages within their scope.\n- If a car is within the user's scope, they can see all its damages.\n- If a car is outside the user's scope, they can only see damages within their scope.\n\nFor fleets with subfleets, a parent fleet manager's view can be limited to certain subfleets by location, fleetId, or both.\n- Location-based scopes allow users to see all cars and damages in a particular location.\n- FleetId-based scopes allow users to see all cars and damages in a specific fleet.\n- Location and fleetId-based scopes allow users to see all cars and damages in a specific fleet in a particular location.\n\n\nThe VisibilityScope object requires at least one field:\n- location: string\n- fleetPartnerId: string\n\nWith only 'location' defined, the user has unrestricted access to subfleets in that location.\nWith only 'fleetPartnerId' defined, the user has unrestricted access to any car location within the subfleets.\n\nIf both 'location' and 'fleetPartnerId' are defined, the user has limited access to both subfleets and car locations.\n\nNote: One scope can override another. For instance, if a user's visibility scope includes:\n```json\n[\n  {\n    location: 'Berlin',\n    fleetPartnerId: '41bca391',\n  },\n  {\n    location: 'Munich',\n  },\n]\n```\nThey would still be able to see cars in Munich for the fleet with id '41bca391'.\n\n", "example": [{"location": "Berlin", "fleetPartnerId": "fleet-1"}, {"location": "Munich"}, {"fleetPartnerId": "fleet-2"}], "type": "array", "items": {"$ref": "#/components/schemas/VisibilityScopeDto"}}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name", "example": "Stoltenberg"}, "phone": {"type": "string", "description": "Phone number", "example": "(************* x81097"}}, "required": ["email", "role"]}, "UpdateUserDto": {"type": "object", "properties": {"email": {"type": "string", "description": "Email", "example": "<EMAIL>"}, "userPartnerId": {"type": "string", "description": "Internal driver ID at partner side", "example": "driver-2"}, "role": {"type": "string", "enum": ["fleet_manager", "local_fleet_manager", "regional_fleet_manager", "driver"], "description": "UserRole", "example": "fleet_manager"}, "visibilityScope": {"description": "### User Visibility Scope\n\nThis scope determines what a user can see regarding cars and damages.\n\nIf no scope is specified, the user can see all cars and all damages.\n\nHere are the rules for when a scope is defined:\n- A user can only see cars and damages within their scope.\n- If a car is within the user's scope, they can see all its damages.\n- If a car is outside the user's scope, they can only see damages within their scope.\n\nFor fleets with subfleets, a parent fleet manager's view can be limited to certain subfleets by location, fleetId, or both.\n- Location-based scopes allow users to see all cars and damages in a particular location.\n- FleetId-based scopes allow users to see all cars and damages in a specific fleet.\n- Location and fleetId-based scopes allow users to see all cars and damages in a specific fleet in a particular location.\n\n\nThe VisibilityScope object requires at least one field:\n- location: string\n- fleetPartnerId: string\n\nWith only 'location' defined, the user has unrestricted access to subfleets in that location.\nWith only 'fleetPartnerId' defined, the user has unrestricted access to any car location within the subfleets.\n\nIf both 'location' and 'fleetPartnerId' are defined, the user has limited access to both subfleets and car locations.\n\nNote: One scope can override another. For instance, if a user's visibility scope includes:\n```json\n[\n  {\n    location: 'Berlin',\n    fleetPartnerId: '41bca391',\n  },\n  {\n    location: 'Munich',\n  },\n]\n```\nThey would still be able to see cars in Munich for the fleet with id '41bca391'.\n\n### Updating Visibility Scope\n- For updates provide the **full list** of scopes\n- an empty list will **remove all** visibility scopes for the user\n- if visibility scope is not provided, the user's visibility scope **will not be changed**\n", "example": [{"location": "Berlin", "fleetPartnerId": "fleet-1"}, {"location": "Munich"}, {"fleetPartnerId": "fleet-2"}], "type": "array", "items": {"$ref": "#/components/schemas/VisibilityScopeDto"}}, "firstName": {"type": "string", "description": "First name", "example": "<PERSON>"}, "lastName": {"type": "string", "description": "Last name", "example": "<PERSON><PERSON>"}, "phone": {"type": "string", "description": "Phone number", "example": "09992300022"}}}, "InvoiceBodyshop": {"type": "object", "properties": {"name": {"type": "string", "description": "Bodyshop name", "example": "Motum repair"}, "address": {"type": "string", "description": "Bodyshop address", "example": "48155, <PERSON><PERSON><PERSON><PERSON> 16, <PERSON><PERSON>"}, "phoneNumber": {"type": "string", "description": "Phone number", "example": "+4912345678910"}, "contactPerson": {"type": "string", "description": "Contact person", "example": "<PERSON>"}}, "required": ["name", "address", "phoneNumber", "<PERSON><PERSON><PERSON>"]}, "Invoice": {"type": "object", "properties": {"id": {"type": "string", "description": "Id of the invoice", "example": "f4d26ffc-f3e4-4c01-9ea1-e5b5968343d9", "format": "uuid"}, "type": {"type": "string", "description": "Invoice type", "enum": ["repair", "towing_service", "car_rental", "assessment", "other", "depreciation", "own_insurance_refund", "opponent_insurance_refund", "aggregated", "claimed_from_employee", "deductible"], "example": "repair"}, "costsNet": {"type": "number", "description": "Invoice Net amount", "example": 738.14}, "costsGross": {"type": "number", "description": "Gross cost. Calculated as net amount + VAT (which is set to {percentageAppliedVAT}%)", "example": 1402.466}, "costsVAT": {"type": "number", "description": "Pure VAT amount. Calculated as {percentageAppliedVAT}% of the new amount.", "example": 1402.466}, "percentageAppliedVAT": {"type": "number", "description": "VAT percentage, used for the amounts calculations.", "example": 19}, "comment": {"type": "string", "description": "Invoice comment", "example": "Die Reparaturrechnung ist hochgeladen.", "nullable": true}, "bodyshop": {"$ref": "#/components/schemas/InvoiceBodyshop"}, "submittedDate": {"type": "string", "description": "Invoice submitted date", "example": "2019-01-01T11:29:38.964Z", "format": "date-time"}, "currency": {"type": "string", "description": "Cost's currency", "enum": ["EUR", "CHF"], "example": "EUR", "default": "EUR"}}, "required": ["id", "type", "costsNet", "costsGross", "costsVAT", "percentageAppliedVAT", "comment", "submittedDate", "currency"]}, "TotalCosts": {"type": "object", "properties": {"repairCostsNet": {"type": "number", "description": "amount from “repair” invoices", "example": 1}, "towingCostsNet": {"type": "number", "description": "amount from “towing” invoice type", "example": 1}, "claimedForEmployeeCostsNet": {"type": "number", "description": "amount from “Claimed for employee” invoice type", "example": 1}, "deductibleCostsNet": {"type": "number", "description": "amount from “Deductible” invoice type", "example": 1}, "replacementCarCostsNet": {"type": "number", "description": "amount from “car rental” invoice type", "example": 1}, "assessorServiceCostsNet": {"type": "number", "description": "amount from “assessor” invoice type", "example": 1}, "otherExpensesCostsNet": {"type": "number", "description": "amount from ”other costs” invoice type", "example": 1}, "costsCoveredByInsurance": {"type": "number", "description": "amount from “settlement” invoice type", "example": 1}, "totalCostsNet": {"type": "number", "description": "calculated as a sum of repair, towing, car rental, assessor, other invoices", "example": 1}, "totalCostsGross": {"type": "number", "description": "total_costs_net x VAT", "example": 1}, "totalCostsPaidByFleetNet": {"type": "number", "description": "amount from ”total” invoice", "example": 1}, "totalCostsPaidByFleetGross": {"type": "number", "description": "(repairCostsNet * VAT + totalCostsNet - costsCoveredByInsurance)", "example": 1}}, "required": ["repairCostsNet", "costsCoveredByInsurance", "totalCostsNet", "totalCostsGross", "totalCostsPaidByFleetNet", "totalCostsPaidByFleetGross"]}, "InvoicesList": {"type": "object", "properties": {"invoices": {"description": "Damage report invoices", "type": "array", "items": {"$ref": "#/components/schemas/Invoice"}}, "total": {"type": "number", "description": "Total number of the invoices", "example": 1}, "totalCosts": {"description": "Total number of the invoices", "allOf": [{"$ref": "#/components/schemas/TotalCosts"}]}}, "required": ["invoices", "total"]}, "GeneratePortalLinkDto": {"type": "object", "properties": {"page": {"type": "string", "description": "An entity type page", "example": "damage", "enum": ["damage", "car"]}, "id": {"type": "string", "description": "An entity ID. For the damage entity, the ID means a damage report ID; for the car entity, the ID can be: a car id, a car VIN, a car partner ID. (if not provided then an entity list link is created)", "example": "f7a13823-8813-4e78-bd09-53ccf0993067"}, "locale": {"type": "string", "description": "Language code. If not provided then the default language will be used. Currently supported languages: de, en", "example": "de"}, "user": {"type": "string", "description": "\n    Identifier of the user context under which the portal link will be generated. \n    Accepted values include: user UUID, user email, or user partner ID. \n    If omitted, the link will be generated without an authentication token.\n    If set to 'guest', the link will open the portal in guest mode with limited access.", "example": "bce00a68-6f2b-4d7f-b7f9-60b759a703cc"}}, "required": ["page"]}, "GeneratePortalLinkData": {"type": "object", "properties": {"link": {"type": "string", "description": "An entity generated link", "example": "https://portal.motum.eu//damages/b7cbd015-8412-447c-8bdc-5b6dfb23795a"}}, "required": ["link"]}, "InternalServerErrorResponse": {"type": "object", "properties": {"statusCode": {"type": "number", "example": 500, "description": "HTTP status code"}, "message": {"type": "string", "description": "Error message", "example": "Internal Server Error"}}, "required": ["statusCode", "message"]}, "TokenListItem": {"type": "object", "properties": {"name": {"type": "string", "description": "An API token name. This name is used as a user name when logging in to Swagger", "example": "token-name"}, "createdAt": {"format": "date-time", "type": "string", "description": "The date and time when an API token was created", "example": "2025-01-01T00:00:00.000Z"}}, "required": ["name", "createdAt"]}, "TokensList": {"type": "object", "properties": {"tokens": {"description": "A currently available to a client a list of API tokens that can be used in Swagger", "type": "array", "items": {"$ref": "#/components/schemas/TokenListItem"}}, "total": {"type": "number", "description": "A total number of API tokens available to a client", "example": 1}}, "required": ["tokens", "total"]}, "AddTokenDto": {"type": "object", "properties": {"name": {"type": "string", "description": "A unique API token name. It will be used as a username when logging in to Swagger", "example": "token-name"}}, "required": ["name"]}, "NewToken": {"type": "object", "properties": {"name": {"type": "string", "description": "An API token name, it is used as a username when logging in to Swagger", "example": "token-name"}, "key": {"type": "string", "description": "An API token key, it is used as a password when logging in to Swagger and as an authorization in Swagger", "example": "d04166e1cbb34c1f26dbcd1b248ca2a2395b826e"}, "createdAt": {"format": "date-time", "type": "string", "description": "An API token creation date", "example": "2025-01-15T12:00:00.000Z"}}, "required": ["name", "key", "createdAt"]}}}}