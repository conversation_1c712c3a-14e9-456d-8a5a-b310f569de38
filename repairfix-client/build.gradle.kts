import com.diffplug.spotless.LineEnding
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

evaluationDependsOn(":shared")
val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    api(libs.api.dms)

    implementation(project(":shared"))
    implementation(project(":damage-file"))
    implementation(libs.bundles.base)
    implementation(libs.bundles.webclient)

    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(libs.bundles.tests)
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
        targetExclude("**/*generated*/**")
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

val repairfixGeneratedSourcesPath = "src/generated/"

sourceSets {
    main { kotlin.srcDir("$repairfixGeneratedSourcesPath/kotlin") }
    test { kotlin { kotlin.srcDir(sharedTestSrcSetsOutput) } }
    test { resources { resources.srcDir(project(":shared").sourceSets.test.get().resources) } }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }

tasks.withType<KotlinCompile> { dependsOn("setupGeneratedSources") }

tasks.register<GenerateTask>("generateRepairfixClient") {
    outputs.upToDateWhen { false }
    val sourceGeneratedFolder = "$projectDir/generated"
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/api/repairfix_api.json")
    outputDir.set(sourceGeneratedFolder)
    apiPackage.set("com.emh.damagemanagement.repairfix.generated.adapter.out.rest")
    modelPackage.set("com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model")
    // models use 'Dto' suffix for requests and do without for responses
    // we will skip the additional suffix for now
    modelNameSuffix.set("")
    /*
     * provided spec is not valid ('String' instead of 'string', 'examples' as additionalProperties)
     * generated result looks fine though
     */
    validateSpec.set(true)
    templateDir.set(
        "${project(":shared").projectDir.absolutePath}/src/main/resources/generator-templates"
    )
    additionalProperties.set(
        mapOf(
            "useSpringBoot3" to true,
            "dateLibrary" to "java8",
            "library" to "jvm-spring-webclient",
            "apiSuffix" to "Api",
            "serializationLibrary" to "jackson",
            "enumPropertyNaming" to "original",
            "omitGradleWrapper" to "true"
        )
    )
}

tasks.register<Copy>("setupGeneratedSources") {
    doFirst { delete(repairfixGeneratedSourcesPath) }
    from("generated/src/main") { exclude("resources", "**/Serializer.kt") }
    into(repairfixGeneratedSourcesPath)
    includeEmptyDirs = false
    dependsOn("generateRepairfixClient")
    doLast { delete("generated/") }
}
