/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix.adapter

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CompoundDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponseDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ExteriorDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.InteriorDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.MechanicalDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ServiceLightsReport
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue

/**
 * Will convert DamageReportResponseDamageReport to a map. User this before converting to actual typed damage report.
 * THis is done to fix repairfix polymorphism issues, that we are mitigating by using custom data class that extends map
 * for additional properties.
 */
private fun DamageReportResponseDamageReport.toMap(objectMapper: ObjectMapper): Map<Any?, Any?> =
    objectMapper.convertValue(this, MutableMap::class.java).plus(mapOf("type" to this.type))

fun DamageReportResponse.toExteriorDamageReport(objectMapper: ObjectMapper): ExteriorDamageReport =
    objectMapper.convertValue(this.damageReport.toMap(objectMapper))

fun DamageReportResponse.toInteriorDamageReport(objectMapper: ObjectMapper): InteriorDamageReport =
    objectMapper.convertValue(this.damageReport.toMap(objectMapper))

fun DamageReportResponse.toMechanicalDamageReport(objectMapper: ObjectMapper): MechanicalDamageReport =
    objectMapper.convertValue(this.damageReport.toMap(objectMapper))

fun DamageReportResponse.toCompoundDamageReport(objectMapper: ObjectMapper): CompoundDamageReport =
    objectMapper.convertValue(this.damageReport.toMap(objectMapper))

fun DamageReportResponse.toServiceLightsReport(objectMapper: ObjectMapper): ServiceLightsReport =
    objectMapper.convertValue(this.damageReport.toMap(objectMapper))

const val DAMAGE_REPORT_CLOSED_STATUS = "closed"
