/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix.adapter

open class RepairfixException(val type: Type, message: String? = null, cause: Throwable? = null) :
    RuntimeException(message, cause) {

    enum class Type {
        BUSINESS,
        TECHNICAL,
    }
}

class RepairfixUnreachableException(message: String? = null, cause: Throwable? = null) :
    RepairfixException(message = message, cause = cause, type = Type.TECHNICAL)

class UnhandledRepairfixRequestException(cause: Throwable) : RepairfixException(cause = cause, type = Type.TECHNICAL)

class RepairfixTooManyRequestsException(message: String, cause: Throwable?) :
    RepairfixException(message = message, cause = cause, type = Type.TECHNICAL)

class RepairfixRetriesExhaustedException(message: String, cause: Throwable?) :
    RepairfixException(message = message, cause = cause, type = Type.TECHNICAL)
