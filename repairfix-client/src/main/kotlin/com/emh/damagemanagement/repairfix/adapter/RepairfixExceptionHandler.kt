/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix.adapter

import com.emh.damagemanagement.repairfix.adapter.config.RepairfixRetryConfigurationProperties
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.BadRequestErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ConflictErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.InternalServerErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnauthorizedErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnprocessableEntityErrorResponse
import java.net.ConnectException
import java.time.Duration
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.http.HttpStatusCode
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFunction
import reactor.core.publisher.Mono
import reactor.util.retry.Retry
import reactor.util.retry.RetryBackoffSpec

@Component
@Qualifier("repairfix")
class RepairfixExceptionHandler(retryConfigurationProperties: RepairfixRetryConfigurationProperties) {

    private val retryTooManyRequests: RetryBackoffSpec =
        Retry.backoff(
                retryConfigurationProperties.maxAttempts,
                Duration.ofMillis(retryConfigurationProperties.backoffDurationInMillis),
            )
            .filter { it is RepairfixTooManyRequestsException }
            .doBeforeRetry { log.warn("Encountered 429 Too Many Request - retry attempt: ${it.totalRetries()+1} ...") }
            .onRetryExhaustedThrow { _, retrySignal ->
                RepairfixRetriesExhaustedException(
                    message = "Exhausted ${retrySignal.totalRetries()} retry attempts.",
                    cause = retrySignal.failure(),
                )
            }

    /** A request error processor. Deals with connection issues nad may handle automatic retry in the future. */
    fun clientErrorRequestProcessor(request: ClientRequest, next: ExchangeFunction): Mono<ClientResponse> =
        next
            .exchange(request)
            .flatMap {
                if (it.statusCode().isSameCodeAs(HttpStatusCode.valueOf(429))) {
                    Mono.error(RepairfixTooManyRequestsException(message = "Too Many Requests.", cause = null))
                } else Mono.just(it)
            }
            .retryWhen(retryTooManyRequests)
            .onErrorMap {
                when {
                    it.cause is ConnectException -> {
                        log.debug("Error during request. Repairfix not reachable.", it.cause)
                        RepairfixUnreachableException("Repairfix is not reachable. ${it.cause?.message}.", it)
                    }
                    it is RepairfixRetriesExhaustedException -> it
                    else -> UnhandledRepairfixRequestException(it)
                }
            }

    companion object {
        private val log = LoggerFactory.getLogger(RepairfixExceptionHandler::class.java)

        /**
         * A clients response processor that maps responses based on http status codes to internal exceptions. Also
         * decides if an error is technical or business and if a retry may change the request outcome.
         */
        fun clientErrorResponseProcessor(clientResponse: ClientResponse): Mono<ClientResponse> {
            val statusCode = clientResponse.statusCode()
            return when {
                statusCode.isSameCodeAs(HttpStatusCode.valueOf(400)) -> handle400(clientResponse)
                statusCode.isSameCodeAs(HttpStatusCode.valueOf(401)) -> handle401(clientResponse)
                statusCode.isSameCodeAs(HttpStatusCode.valueOf(404)) -> handle404(clientResponse)
                statusCode.isSameCodeAs(HttpStatusCode.valueOf(409)) -> handle409(clientResponse)
                statusCode.isSameCodeAs(HttpStatusCode.valueOf(422)) -> handle422(clientResponse)
                // do not handle 429 here as this would interfere with retry handling for 429 (see
                // [clientErrorRequestProcessor])
                statusCode.isSameCodeAs(HttpStatusCode.valueOf(500)) -> handle500(clientResponse)
                statusCode.isError -> {
                    logErrorWith(clientResponse.statusCode().toString())
                    Mono.just(clientResponse)
                }
                else -> Mono.just(clientResponse)
            }
        }

        private fun handle400(clientResponse: ClientResponse): Mono<ClientResponse> =
            clientResponse
                .bodyToMono(String::class.java)
                .onErrorMap {
                    RepairfixException(
                        message =
                            "Error during repairfix request. Error body could not be parsed to ${BadRequestErrorResponse::class.simpleName}.",
                        type = RepairfixException.Type.TECHNICAL,
                        cause = it,
                    )
                }
                .flatMap {
                    logErrorWith(it)
                    Mono.error(RepairfixException(type = RepairfixException.Type.TECHNICAL, message = it))
                }

        private fun handle401(clientResponse: ClientResponse): Mono<ClientResponse> =
            clientResponse
                .bodyToMono(String::class.java)
                .onErrorMap {
                    RepairfixException(
                        message =
                            "Error during repairfix request. Error body could not be parsed to ${UnauthorizedErrorResponse::class.simpleName}.",
                        type = RepairfixException.Type.TECHNICAL,
                        cause = it,
                    )
                }
                .flatMap {
                    logErrorWith(it)
                    Mono.error(RepairfixException(type = RepairfixException.Type.TECHNICAL, message = it))
                }

        private fun handle404(clientResponse: ClientResponse): Mono<ClientResponse> =
            clientResponse
                .bodyToMono(String::class.java)
                .onErrorMap {
                    RepairfixException(
                        message =
                            "Error during repairfix request. Error body could not be parsed to ${NotFoundErrorResponse::class.simpleName}.",
                        type = RepairfixException.Type.TECHNICAL,
                        cause = it,
                    )
                }
                .flatMap {
                    logErrorWith(it)
                    /**
                     * When using an "old" or "no-longer-valid" api key repairfix will respond with 404 instead of 401.
                     * 401 will only be returned when using an actual invalid api key, or none at all. Yeah ... it´s
                     * weird.
                     */
                    log.info(
                        "If you get this error repeatedly check API Key configuration. As repairfix will respond with 404 instead of 401 if you use an \"old\" API Key."
                    )
                    Mono.error(RepairfixException(type = RepairfixException.Type.TECHNICAL, message = it))
                }

        private fun handle409(clientResponse: ClientResponse): Mono<ClientResponse> =
            clientResponse
                .bodyToMono(String::class.java)
                .onErrorMap {
                    RepairfixException(
                        message =
                            "Error during repairfix request. Error body could not be parsed to ${ConflictErrorResponse::class.simpleName}.",
                        type = RepairfixException.Type.BUSINESS,
                        cause = it,
                    )
                }
                .flatMap {
                    logErrorWith(it)
                    Mono.error(RepairfixException(type = RepairfixException.Type.BUSINESS, message = it))
                }

        private fun handle422(clientResponse: ClientResponse): Mono<ClientResponse> =
            clientResponse
                .bodyToMono(String::class.java)
                .onErrorMap {
                    RepairfixException(
                        message =
                            "Error during repairfix request. Error body could not be parsed to ${UnprocessableEntityErrorResponse::class.simpleName}.",
                        type = RepairfixException.Type.BUSINESS,
                        cause = it,
                    )
                }
                .flatMap {
                    logErrorWith(it)
                    Mono.error(RepairfixException(type = RepairfixException.Type.BUSINESS, message = it))
                }

        private fun handle500(clientResponse: ClientResponse): Mono<ClientResponse> =
            clientResponse
                .bodyToMono(String::class.java)
                .onErrorMap {
                    RepairfixException(
                        message =
                            "Error during repairfix request. Error body could not be parsed to ${InternalServerErrorResponse::class.simpleName}.",
                        type = RepairfixException.Type.TECHNICAL,
                        cause = it,
                    )
                }
                .flatMap {
                    logErrorWith(it)
                    Mono.error(RepairfixException(type = RepairfixException.Type.TECHNICAL, message = it))
                }

        private fun logErrorWith(message: String?) {
            log.error("Error during repairfix request. $message")
        }
    }
}
