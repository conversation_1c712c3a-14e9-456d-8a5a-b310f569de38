/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix.adapter.config

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.CarsApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.DamageReportsApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.FleetsApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.InvoicesApi
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.UserApi
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class RepairfixApiConfig {
    @Bean fun carsApi(@Qualifier("repairfix") webClient: WebClient) = Cars<PERSON><PERSON>(webClient)

    @Bean fun fleetsApi(@Qualifier("repairfix") webClient: WebClient) = Fleets<PERSON>pi(webClient)

    @Bean fun usersApi(@Qualifier("repairfix") webClient: WebClient) = UserApi(webClient)

    @Bean fun damageReportApi(@Qualifier("repairfix") webClient: WebClient) = DamageReportsApi(webClient)

    @Bean fun invoiceApi(@Qualifier("repairfix") webClient: WebClient) = InvoicesApi(webClient)
}
