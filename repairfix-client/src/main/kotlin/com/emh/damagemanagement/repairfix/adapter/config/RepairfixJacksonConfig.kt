/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix.adapter.config

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarDto
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonSerializer
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.SerializerProvider
import java.time.ZoneId
import java.util.*
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@Configuration
class RepairfixJacksonConfig {

    /** This configuration will automatically be picked up by spring. */
    @Bean
    @Qualifier("repairfix")
    fun repairfixJackson2ObjectMapperBuilder(): Jackson2ObjectMapperBuilder =
        Jackson2ObjectMapperBuilder()
            .featuresToEnable(
                DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY,
                DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS,
                JsonParser.Feature.ALLOW_COMMENTS,
                MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS,
            )
            .featuresToDisable(
                DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
            )
            .serializers(UpdateCarDtoSerializer())
            /**
             * 2024-02-14 we changed from ALL to NON_NULL as repairfix API was crashing with null values. If we need
             * null values for other use cases, then we will need to split this builder and have a dedicated one for the
             * repairfix api clients
             */
            .serializationInclusion(JsonInclude.Include.NON_NULL)
            .timeZone(TimeZone.getTimeZone(DEFAULT_ZONE_ID))
            .createXmlMapper(false)

    companion object {
        private val DEFAULT_ZONE_ID = ZoneId.of("UTC")
    }
}

/**
 * A custom serializer used for [UpdateCarDto]s, to ensure that null values are being serialized when we need them,
 * without providing unnecessary nulls for all values (which fail during PATCH)
 */
class UpdateCarDtoSerializer : JsonSerializer<UpdateCarDto>() {

    override fun serialize(value: UpdateCarDto, jsonGenerator: JsonGenerator, serializers: SerializerProvider) {
        jsonGenerator.writeStartObject()
        // vin -- only include if set, as VIN can not be removed
        value.vin?.also { jsonGenerator.writeStringField("vin", it) }
        // carPartnerId -- only include if not null, as vGUID can not be removed
        value.carPartnerId?.also { jsonGenerator.writeStringField("carPartnerId", it) }
        // isPool -- only include it, if it is set
        value.isPool?.also { jsonGenerator.writeBooleanField("isPool", it) }
        // licensePlate - always set, explicit null if we want to "remove" license plate
        if (null == value.licensePlate) {
            jsonGenerator.writeNullField("licensePlate")
        } else {
            jsonGenerator.writeStringField("licensePlate", value.licensePlate)
        }
        // fleetIdentifier -- only include it, if it is set
        value.fleetIdentifier?.also { jsonGenerator.writeStringField("fleetIdentifier", it) }
        // driver - always set, explicit null if we want to "remove" driver
        if (null == value.driver) {
            jsonGenerator.writeNullField("driver")
        } else {
            jsonGenerator.writeStringField("driver", value.driver)
        }
        // insurance
        value.insurance?.also { serializers.defaultSerializeField("insurance", it, jsonGenerator) }
        // isActive flag (used especially during deactivation of vehicles)
        value.isActive?.also { jsonGenerator.writeBooleanField("isActive", it) }
        jsonGenerator.writeEndObject()
    }

    override fun handledType(): Class<UpdateCarDto> = UpdateCarDto::class.java
}
