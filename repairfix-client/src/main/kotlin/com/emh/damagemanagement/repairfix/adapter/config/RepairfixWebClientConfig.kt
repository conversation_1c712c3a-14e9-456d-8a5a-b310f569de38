/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix.adapter.config

import com.emh.damagemanagement.repairfix.adapter.RepairfixExceptionHandler
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.codec.ClientCodecConfigurer
import org.springframework.http.codec.json.Jackson2JsonDecoder
import org.springframework.http.codec.json.Jackson2JsonEncoder
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class RepairfixWebClientConfig(private val repairfixExceptionHandler: RepairfixExceptionHandler) {

    @Bean
    @Qualifier("repairfix")
    fun repairfixExchangeStrategies(
        @Qualifier("repairfix") jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder
    ): ExchangeStrategies =
        ExchangeStrategies.builder()
            .codecs { clientDefaultCodecsConfigurer: ClientCodecConfigurer ->
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonEncoder(
                        Jackson2JsonEncoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonDecoder(
                        Jackson2JsonDecoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
            }
            .build()

    @Bean
    @Qualifier("repairfix")
    fun repairfixWebClient(
        repairfixWebClientProperties: RepairfixWebClientProperties,
        @Qualifier("repairfix") exchangeStrategies: ExchangeStrategies,
    ): WebClient =
        WebClient.builder()
            .baseUrl(repairfixWebClientProperties.baseUrl)
            .defaultHeader("Authorization", "Bearer " + repairfixWebClientProperties.apiKey)
            .exchangeStrategies(exchangeStrategies)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .filter(ExchangeFilterFunction.ofResponseProcessor(RepairfixExceptionHandler::clientErrorResponseProcessor))
            .filter(repairfixExceptionHandler::clientErrorRequestProcessor)
            .build()
}

@ConfigurationProperties("repairfix") data class RepairfixWebClientProperties(val baseUrl: String, val apiKey: String)

@ConfigurationProperties("repairfix.requests")
data class RepairfixRequestsProperties(val pageSize: Int, val pageDelaySeconds: Int)
