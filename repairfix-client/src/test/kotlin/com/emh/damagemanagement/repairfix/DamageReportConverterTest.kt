/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix

import com.emh.damagemanagement.repairfix.adapter.config.RepairfixJacksonConfig
import com.emh.damagemanagement.repairfix.adapter.toExteriorDamageReport
import com.emh.damagemanagement.repairfix.adapter.toInteriorDamageReport
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.DamageReportResponse
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.util.*
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.Test
import org.springframework.core.io.ClassPathResource

class DamageReportConverterTest {

    private val exteriorDamageReport = ClassPathResource("damage-reports/exterior-damage-report.json")
    private val interiorDamageReport = ClassPathResource("damage-reports/interior-damage-report.json")

    private val objectMapper: ObjectMapper = RepairfixJacksonConfig().repairfixJackson2ObjectMapperBuilder().build()

    @Test
    fun `should successfully map exterior DamageReportResponseDamageReport to DamageReportResponse`() {
        assertThatNoException().isThrownBy {
            val damageReportResponse: DamageReportResponse = objectMapper.readValue(exteriorDamageReport.inputStream)
            damageReportResponse.toExteriorDamageReport(objectMapper)
        }
    }

    @Test
    fun `should successfully map interior DamageReportResponseDamageReport to DamageReportResponse`() {
        assertThatNoException().isThrownBy {
            val damageReportResponse: DamageReportResponse = objectMapper.readValue(interiorDamageReport.inputStream)
            damageReportResponse.toInteriorDamageReport(objectMapper)
        }
    }
}
