/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.repairfix.adapter.config

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarInsuranceDto
import com.fasterxml.jackson.databind.ObjectMapper
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class UpdateCarDtoSerializerTest {

    private val objectMapper: ObjectMapper = RepairfixJacksonConfig().repairfixJackson2ObjectMapperBuilder().build()

    @Test
    fun `should only serialize values managed by the custom serializer`() {
        val updateDto =
            UpdateCarDto(
                location = "you cant see me",
                licensePlate = null,
                insurance =
                    UpdateCarInsuranceDto(
                        insuranceBroker = "some broker",
                        insuranceCompany = "some company",
                        costCenter = "some cost-center",
                    ),
            )

        val serializedResult = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(updateDto)

        assertThat(serializedResult).doesNotContain("location")
        assertThat(serializedResult).doesNotContain("vin")
        assertThat(serializedResult).doesNotContain("carPartnerId")
        assertThat(serializedResult).contains("licensePlate")
    }

    @Test
    fun `should always serialize licensePlate and driver`() {
        val updateDto =
            UpdateCarDto(
                location = "you cant see me",
                licensePlate = null,
                driver = "driver",
                insurance =
                    UpdateCarInsuranceDto(
                        insuranceBroker = "some broker",
                        insuranceCompany = "some company",
                        costCenter = "some cost-center",
                    ),
            )

        val serializedResult = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(updateDto)

        assertThat(serializedResult).contains("\"licensePlate\" : null")
        assertThat(serializedResult).contains("\"driver\" : \"driver\"")
    }
}
