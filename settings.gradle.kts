plugins { id("org.gradle.toolchains.foojay-resolver-convention") version "0.5.0" }

rootProject.name = "damage-management-service"

dependencyResolutionManagement {
    versionCatalogs { create("libs") { from(files("./libs.versions.toml")) } }
}


include("configuration-default")
include("damage-file")
include("damage-file-gobd")
include("damage-file-kafka-out-adapter")
include("damage-file-migration")
include("employee-client")
include("hcm-p08-damage-report-adapter")
include("legal-hold")
include("legal-hold-in-adapter")
include("legal-hold-out-adapter")
include("outbox-message-relay")
include("pace-p40-internal-order-number-adapter")
include("pvcc-p40-monetary-benefit-adapter")
include("repairfix-client")
include("shared")
include("vehicle-adapter")
include("excel-vehicle-migration")
include("wiremock")
include("vehicle-migration-data-in-adapter")
include("vehicle-migration")
include("vehicle-migration-message-retry")
