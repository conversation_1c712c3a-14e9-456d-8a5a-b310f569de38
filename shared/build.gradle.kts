import com.diffplug.spotless.LineEnding
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jpa)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

dependencies {
    api(libs.api.dms)

    implementation(libs.bundles.base)
    implementation(libs.bundles.databases)
    implementation(libs.bundles.spring.security)
    implementation(libs.cloudwatch)
    implementation(libs.micrometer.core)
    implementation(libs.spring.boot.cache)

    runtimeOnly(libs.bundles.runtime.databases)

    testImplementation(libs.bundles.tests)
}

val sharedModelsSourcesPath = "src/generated/"

sourceSets { main { kotlin.srcDir("$sharedModelsSourcesPath/kotlin") } }

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
        targetExclude("**/generated/**")
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }

tasks.withType<KotlinCompile> { dependsOn("setupGeneratedSources") }

val apiFolder: String by rootProject.extra

tasks.register<GenerateTask>("extractSharedModels") {
    outputs.upToDateWhen { false }
    validateSpec.set(false)
    generatorName.set("kotlin-spring")
    inputSpec.set("$projectDir/$apiFolder/shared.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.emh.damagemanagement.generated.shared.client.http")
    modelPackage.set("com.emh.damagemanagement.generated.shared.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
        mapOf(
            "serviceInterface" to true,
            "serviceImplementation" to false,
            "exceptionHandler" to false,
            "apiSuffix" to "",
            "useBeanValidation" to false,
            "serializationLibrary" to "jackson",
            "annotationLibrary" to "none",
            "documentationProvider" to "none",
            "useSwaggerUI" to false,
        ),
    )
    dependsOn("unpackApi")
}

tasks.register<Copy>("setupGeneratedSources") {
    doFirst { delete(sharedModelsSourcesPath) }
    from("generated/src/main") {
        exclude("*/org")
        exclude("resources")
        exclude("**/ApiUtil*")
    }
    into(sharedModelsSourcesPath)
    includeEmptyDirs = false
    doLast { delete("$projectDir/generated/") }
    dependsOn("extractSharedModels")
}
