### auth
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
    &client_secret={{oauth_m2m_api_client_secret}}
    &grant_type=client_credentials
    &scope={{api_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

### Get application metrics
GET /api/damage/actuator/metrics
Host: localhost:8081


### Post job trigger
POST /api/jobs/trigger?jobName=DamageFileArchiveJobDetail
Authorization: Bearer {{auth_token}}
Host: localhost:8081
accept: application/json
Content-Type: application/json