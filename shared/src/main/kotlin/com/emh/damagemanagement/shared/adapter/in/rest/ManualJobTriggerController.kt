/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.adapter.`in`.rest

import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.SchedulerException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/jobs")
class ManualJobTriggerController(@Autowired private val scheduler: Scheduler) {

    @PostMapping("/trigger")
    fun triggerJob(@RequestParam jobName: String, @RequestParam jobGroup: String = "DEFAULT"): ResponseEntity<String> {
        log.info("starting ${jobName}...")
        return try {
            val jobKey = JobKey.jobKey(jobName, jobGroup)
            scheduler.triggerJob(jobKey)
            ResponseEntity("Job triggered successfully.", HttpStatus.OK)
        } catch (e: SchedulerException) {
            ResponseEntity("Error triggering job: ${e.message}", HttpStatus.INTERNAL_SERVER_ERROR)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(ManualJobTriggerController::class.java)
    }
}
