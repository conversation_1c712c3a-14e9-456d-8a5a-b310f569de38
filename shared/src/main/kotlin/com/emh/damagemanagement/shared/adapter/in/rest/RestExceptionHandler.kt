/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.adapter.`in`.rest

import com.emh.damagemanagement.generated.shared.rest.model.ErrorDto
import com.emh.damagemanagement.generated.shared.rest.model.ErrorTypeDto
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler

@ControllerAdvice
class RestExceptionHandler {

    @ExceptionHandler(ConcurrentModificationException::class)
    private fun handleConcurrentModificationException(
        exception: ConcurrentModificationException
    ): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message = exception.message ?: "Server version does not match client version.",
                type = ErrorTypeDto.TECHNICAL,
            ),
            HttpStatus.PRECONDITION_FAILED,
        )

    @ExceptionHandler(IllegalArgumentException::class)
    private fun handleIllegalArgumentException(exception: IllegalArgumentException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(message = exception.message ?: "Invalid arguments supplied", type = ErrorTypeDto.BUSINESS),
            HttpStatus.CONFLICT,
        )

    @ExceptionHandler(NoSuchElementException::class)
    private fun handleNoSuchElementException(exception: NoSuchElementException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(message = exception.message ?: "Element could not be found.", type = ErrorTypeDto.BUSINESS),
            HttpStatus.NOT_FOUND,
        )
}
