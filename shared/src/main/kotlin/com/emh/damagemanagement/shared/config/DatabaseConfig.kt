/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.config

import com.zaxxer.hikari.HikariDataSource
import com.zaxxer.hikari.util.Credentials
import jakarta.annotation.PostConstruct
import java.net.URI
import javax.sql.DataSource
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.autoconfigure.quartz.QuartzDataSource
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.rds.RdsUtilities

@Configuration
class DatabaseConfig {

    @Bean
    @Primary
    @QuartzDataSource
    @ConfigurationProperties(prefix = "spring.datasource.hikari")
    @ConditionalOnProperty("database.rds", havingValue = "true", matchIfMissing = false)
    fun customHikariDataSource(): DataSource {
        return AuthTokenDataSource()
    }
}

class AuthTokenDataSource : HikariDataSource() {
    @Value("\${database.region}") private lateinit var region: String
    private lateinit var rdsUtilities: RdsUtilities
    private lateinit var uri: URI

    @PostConstruct
    fun initialize() {

        rdsUtilities =
            RdsUtilities.builder()
                .region(Region.of(region))
                .credentialsProvider(DefaultCredentialsProvider.create())
                .build()

        uri = URI.create(jdbcUrl.removePrefix("jdbc:"))
    }

    // overriding getPassword no longer works
    // https://github.com/brettwooldridge/HikariCP/issues/2256#issuecomment-**********
    override fun getCredentials(): Credentials {
        if (this.jdbcUrl == null || this.username == null) {
            return super.getCredentials()
        }
        return Credentials.of(this.username, generateNewAuthToken())
    }

    private fun generateNewAuthToken(): String {
        log.info("Getting new password for ${this.poolName}. Current connections will be lost.")
        return rdsUtilities.generateAuthenticationToken { builder ->
            builder.hostname(uri.host).port(uri.port).username(username)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(AuthTokenDataSource::class.java)
    }
}
