/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.config

import io.micrometer.cloudwatch2.CloudWatchConfig
import io.micrometer.cloudwatch2.CloudWatchMeterRegistry
import io.micrometer.core.instrument.Clock
import io.micrometer.core.instrument.MeterRegistry
import io.micrometer.core.instrument.logging.LoggingMeterRegistry
import java.time.Duration
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.cloudwatch.CloudWatchAsyncClient

@Configuration
class MicroMeterConfig {
    @Bean
    @ConditionalOnProperty(name = ["feature-flags.metrics-registry"], havingValue = "logMetrics")
    fun getLocalMeterRegistry(): MeterRegistry {
        return LoggingMeterRegistry()
    }

    @Bean
    @ConditionalOnProperty(name = ["feature-flags.metrics-registry"], havingValue = "cloudwatch")
    fun getMeterRegistry(
        @Value("\${management.metrics.export.cloudwatch.namespace}") cloudwatchNamespace: String,
        @Value("\${management.metrics.export.cloudwatch.region}") awsRegion: String,
        @Value("\${management.metrics.export.cloudwatch.duration}") duration: Long,
    ): CloudWatchMeterRegistry {
        return CloudWatchMeterRegistry(
            cloudwatchConfiguration(cloudwatchNamespace, duration),
            Clock.SYSTEM,
            cloudWatchAsyncClient(awsRegion),
        )
    }

    private fun cloudWatchAsyncClient(awsRegion: String): CloudWatchAsyncClient {
        val credentials = DefaultCredentialsProvider.create()
        return CloudWatchAsyncClient.builder().region(Region.of(awsRegion)).credentialsProvider(credentials).build()
    }

    private fun cloudwatchConfiguration(cloudwatchNamespace: String, duration: Long) = CloudWatchConfig { key ->
        mapOf(
            "cloudwatch.namespace" to cloudwatchNamespace,
            "cloudwatch.step" to Duration.ofMinutes(duration).toString(),
        )[key]
    }
}
