/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.shared.converter

import com.emh.damagemanagement.shared.config.JacksonConfig
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.stereotype.Component

@Component
class JsonConverter(jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder) {
    init {
        _objectMapper = jackson2ObjectMapperBuilder.build()
    }

    val objectMapper: ObjectMapper = _objectMapper

    companion object {
        private var _objectMapper: ObjectMapper = JacksonConfig.jackson2ObjectMapperBuilder.build()

        fun Any.toJsonNode(): JsonNode = _objectMapper.convertValue(this)
    }
}
