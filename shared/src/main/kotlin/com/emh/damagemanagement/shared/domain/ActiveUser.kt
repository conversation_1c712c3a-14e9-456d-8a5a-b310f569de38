/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.domain

interface ActiveUser {
    /** The unique identification of the currently active user (e.g. the JWT-Subject) */
    val userIdentification: String

    /** A human-readable name for the current user. Not necessarily unique. */
    val userDisplayName: String
}

class ServiceUser(override val userIdentification: String, override val userDisplayName: String) : ActiveUser
