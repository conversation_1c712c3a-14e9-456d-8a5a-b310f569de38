/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.domain

import jakarta.persistence.Column
import jakarta.persistence.EntityListeners
import jakarta.persistence.MappedSuperclass
import java.time.OffsetDateTime
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.CreatedDate
import org.springframework.data.annotation.LastModifiedBy
import org.springframework.data.annotation.LastModifiedDate
import org.springframework.data.jpa.domain.support.AuditingEntityListener

@Suppress("unused")
@MappedSuperclass
@EntityListeners(AuditingEntityListener::class)
abstract class Auditable {

    @CreatedBy @Column(name = "created_by") var createdBy: String = ""

    @CreatedDate @Column(name = "created_date") var created: OffsetDateTime = OffsetDateTime.now()

    @LastModifiedBy @Column(name = "last_modified_by") var lastModifiedBy: String = ""

    @LastModifiedDate @Column(name = "last_modified_date") var lastModified: OffsetDateTime = OffsetDateTime.now()
}
