/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.domain

import java.io.Serializable
import java.time.OffsetDateTime
import java.util.UUID

/**
 * An internal domain event within this service. Will be thrown by respective domain services and may be listened to by
 * interested parties.
 */
interface DomainEvent : Serializable {
    val occurredOn: OffsetDateTime
    val id: UUID
}
