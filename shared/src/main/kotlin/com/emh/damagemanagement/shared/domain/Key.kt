/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.domain

import java.io.Serializable

/**
 * The 'key' of an aggregate root. This property may be used to uniquely identify an aggregate root. The key is
 * considered public knowledge and may be shared with other modules and/or systems.
 */
interface Key : Serializable {
    val value: String
}
