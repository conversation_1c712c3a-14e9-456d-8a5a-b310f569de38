/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.domain

import java.util.*
import java.util.zip.CRC32C
import kotlin.ConcurrentModificationException

/** Will mark an entity susceptible to version conflict during optimisticLockingChecks */
interface WithVersion {
    /**
     * The version field for this entity. Value will be managed by Hibernate/the Database. Will also be used to
     * calculate an eTag-Hash for precondition checking.
     */
    val version: Int

    /**
     * A hash used to provide an eTag unique to this entity and version. Most entities will provide a CRC32C for a
     * combination of id and version. It is critical to ensure, that this function will provide the same value for the
     * same version (other properties may be included if deemed relevant).
     */
    val etag: String
}

/**
 * Checks for mismatch in etag values, used during precondition check for [WithVersion] on application service level.
 *
 * @param entity the domain entity to be checked
 * @param providedEtag the version provided by the client
 * @return returns true in case of a version/etag mismatch
 * @throws ConcurrentModificationException in case of etag mismatch
 */
fun checkForEtagMismatch(entity: WithVersion, providedEtag: String) {
    if (entity.etag != providedEtag) {
        throw ConcurrentModificationException(
            "Current server-etag (${entity.etag}) for this entity did not match client etag ($providedEtag)."
        )
    }
}

/**
 * Convenience method to create a "standardized" etag for a [WithVersion]-entity
 *
 * @param id the UUID id value of the entity id
 * @param version the current version of the entity to be checked
 * @return an etag value to be checked against the if-match header
 */
fun createCRC32CForEntity(id: UUID, version: Int): String {
    val crc32c = CRC32C().apply { update((id.toString() + version.toString()).toByteArray()) }
    return "%x".format(crc32c.value)
}
