/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.logging

import jakarta.servlet.FilterChain
import jakarta.servlet.http.HttpServletRequest
import jakarta.servlet.http.HttpServletResponse
import java.util.*
import org.slf4j.MDC
import org.springframework.stereotype.Component
import org.springframework.web.filter.OncePerRequestFilter

/** Mapped Diagnostic Context Filter for requests. */
@Component
class MDCFilter : OncePerRequestFilter() {

    override fun doFilterInternal(
        request: HttpServletRequest,
        response: HttpServletResponse,
        filterChain: FilterChain,
    ) {
        val requestId = createOrSetRequestIdInMDC(request)
        response.setHeader(X_TRACE_ID, requestId)
        filterChain.doFilter(request, response)
        MDC.clear()
    }

    private fun createOrSetRequestIdInMDC(request: HttpServletRequest): String {
        val requestId: String = request.getHeader(X_TRACE_ID) ?: "${UUID.randomUUID()}"
        MDC.put(X_TRACE_ID, requestId)
        return requestId
    }

    companion object {
        private const val X_TRACE_ID = "x-trace-id"
    }
}
