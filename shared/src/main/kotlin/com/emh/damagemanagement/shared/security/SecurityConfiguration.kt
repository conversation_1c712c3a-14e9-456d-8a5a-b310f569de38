/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.security

import org.slf4j.LoggerFactory
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.cache.CacheManager
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.concurrent.ConcurrentMapCacheManager
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.oauth2.core.DelegatingOAuth2TokenValidator
import org.springframework.security.oauth2.core.OAuth2Error
import org.springframework.security.oauth2.core.OAuth2TokenValidator
import org.springframework.security.oauth2.core.OAuth2TokenValidatorResult
import org.springframework.security.oauth2.jwt.Jwt
import org.springframework.security.oauth2.jwt.JwtDecoder
import org.springframework.security.oauth2.jwt.JwtIssuerValidator
import org.springframework.security.oauth2.jwt.NimbusJwtDecoder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.HttpStatusEntryPoint
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@Configuration
class SecurityConfiguration(private val jwtProperties: JwtProperties) {
    @Bean
    fun filterChain(http: HttpSecurity, jwtDecoder: JwtDecoder): SecurityFilterChain {
        http
            .exceptionHandling { it.authenticationEntryPoint(HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)) }
            .csrf { it.disable() }
            .authorizeHttpRequests { authorizeRequests ->
                authorizeRequests.requestMatchers("/damage/actuator/**").permitAll().anyRequest().authenticated()
            }
            .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
            .oauth2ResourceServer { it.jwt { jwt -> jwt.decoder(jwtDecoder) } }
        return http.build()
    }

    @Bean
    fun corsConfigurationSource(): CorsConfigurationSource {
        val corsConfiguration =
            CorsConfiguration().apply {
                applyPermitDefaultValues()
                addAllowedMethod(HttpMethod.PUT)
                addAllowedMethod(HttpMethod.DELETE)
            }
        corsConfiguration.validateAllowCredentials()
        val urlBasedConfiguration = UrlBasedCorsConfigurationSource()
        urlBasedConfiguration.registerCorsConfiguration("/**", corsConfiguration)
        return urlBasedConfiguration
    }

    @Bean
    fun cacheManager(): CacheManager {
        return ConcurrentMapCacheManager("jwks")
    }

    @Bean
    fun jwtDecoder(cacheManager: CacheManager): JwtDecoder {
        val withAudience =
            DelegatingOAuth2TokenValidator(
                JwtIssuerValidator(jwtProperties.issuerUri),
                AudienceValidator(jwtProperties.audiences),
            )
        return NimbusJwtDecoder.withJwkSetUri(jwtProperties.jwkSetUri)
            .cache(cacheManager.getCache("jwks"))
            .build()
            .apply { setJwtValidator(withAudience) }
    }

    @CacheEvict(value = ["jwks"], allEntries = true)
    @Scheduled(fixedRateString = "\${caching.jwksTTL}")
    fun clearJwksCache() {
        log.debug("Clearing jwks cache.")
    }

    class AudienceValidator(private val audiences: Set<String>) : OAuth2TokenValidator<Jwt> {

        override fun validate(jwt: Jwt): OAuth2TokenValidatorResult {
            return if (audiences.none { jwt.audience.contains(it) }) {
                OAuth2TokenValidatorResult.failure(
                    OAuth2Error(
                        "invalid_token",
                        "None of the provided audiences [${jwt.audience.joinToString()}] match expected ones [${audiences.joinToString()}].",
                        null,
                    )
                )
            } else OAuth2TokenValidatorResult.success()
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(SecurityConfiguration::class.java)
    }
}

@ConfigurationProperties("spring.security.oauth2.resourceserver.jwt")
class JwtProperties(val jwkSetUri: String, val audiences: Set<String>, val issuerUri: String)
