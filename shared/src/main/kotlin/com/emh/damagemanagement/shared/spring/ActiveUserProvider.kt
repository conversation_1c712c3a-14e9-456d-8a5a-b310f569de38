/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.spring

import com.emh.damagemanagement.shared.domain.ActiveUser
import com.emh.damagemanagement.shared.domain.ServiceUser
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class ActiveUserProvider {

    /** We currently only support ServiceUser, as no have no public APIs. */
    @Bean
    fun currentUser(): ActiveUser =
        ServiceUser(userIdentification = SERVICE_USER_IDENTIFICATION, userDisplayName = SERVICE_USER_DISPLAY_NAME)

    companion object {
        private const val SERVICE_USER_IDENTIFICATION = "DamageManagementService"
        private const val SERVICE_USER_DISPLAY_NAME = "Damage Management Service"
    }
}
