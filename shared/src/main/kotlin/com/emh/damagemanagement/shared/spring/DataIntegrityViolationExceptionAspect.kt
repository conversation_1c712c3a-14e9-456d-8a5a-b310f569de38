/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.spring

import org.aspectj.lang.annotation.AfterThrowing
import org.aspectj.lang.annotation.Aspect
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.stereotype.Component

/**
 * Aspect to catch [DataIntegrityViolationException] and transform it to [IllegalArgumentException]. This is done to
 * catch ConstrainViolations that are 'leaving' the Transactional-Context unmapped.
 */
@Aspect
@Component
class DataIntegrityViolationExceptionAspect {

    @AfterThrowing(
        pointcut =
            """
            @within(org.springframework.web.bind.annotation.RestController) &&
            (@annotation(org.springframework.web.bind.annotation.PatchMapping) 
            || @annotation(org.springframework.web.bind.annotation.PutMapping) 
            || @annotation(org.springframework.web.bind.annotation.PostMapping))""",
        throwing = "exception",
    )
    fun transformDataIntegrityViolationException(exception: DataIntegrityViolationException) {
        throw IllegalArgumentException("Constraint violation during server operation.", exception)
    }
}
