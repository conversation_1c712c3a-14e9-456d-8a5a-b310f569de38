/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.spring

import jakarta.persistence.OptimisticLockException
import org.aspectj.lang.annotation.AfterThrowing
import org.aspectj.lang.annotation.Aspect
import org.hibernate.StaleStateException
import org.springframework.dao.OptimisticLockingFailureException
import org.springframework.stereotype.Component

private const val SERVER_VERSION_DOES_NOT_MATCH_CLIENT_VERSION_ = "Server version does not match client version."

@Aspect
@Component
class OptimisticLockingFailureExceptionAspect {

    @AfterThrowing(
        pointcut =
            """
            (@within(org.springframework.stereotype.Service) && 
            @annotation(org.springframework.transaction.annotation.Transactional)) || 
            (@within(org.springframework.stereotype.Component) && 
            @annotation(org.springframework.transaction.annotation.Transactional)) || 
                @within(org.springframework.transaction.annotation.Transactional)""",
        throwing = "exception",
    )
    fun transform(exception: OptimisticLockingFailureException) {

        throw ConcurrentModificationException(SERVER_VERSION_DOES_NOT_MATCH_CLIENT_VERSION_, exception)
    }
}

@Aspect
@Component
class OptimisticLockExceptionAspect {

    @AfterThrowing(
        pointcut =
            """
            (@within(org.springframework.stereotype.Service) && 
            @annotation(org.springframework.transaction.annotation.Transactional)) || 
            (@within(org.springframework.stereotype.Component) && 
            @annotation(org.springframework.transaction.annotation.Transactional)) || 
                @within(org.springframework.transaction.annotation.Transactional)""",
        throwing = "exception",
    )
    fun transform(exception: OptimisticLockException) {

        throw ConcurrentModificationException(SERVER_VERSION_DOES_NOT_MATCH_CLIENT_VERSION_, exception)
    }
}

@Aspect
@Component
class StaleStateExceptionAspect {

    @AfterThrowing(
        pointcut =
            """
            (@within(org.springframework.stereotype.Service) && 
            @annotation(org.springframework.transaction.annotation.Transactional)) || 
            (@within(org.springframework.stereotype.Component) && 
            @annotation(org.springframework.transaction.annotation.Transactional)) || 
                @within(org.springframework.transaction.annotation.Transactional)""",
        throwing = "exception",
    )
    fun transform(exception: StaleStateException) {

        throw ConcurrentModificationException(SERVER_VERSION_DOES_NOT_MATCH_CLIENT_VERSION_, exception)
    }
}
