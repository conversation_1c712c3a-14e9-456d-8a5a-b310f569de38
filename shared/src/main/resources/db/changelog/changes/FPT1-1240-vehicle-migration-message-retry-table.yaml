databaseChangeLog:
  - changeSet:
      id: sbalodi7-create-vehicle-migration-message-retry-table
      author: <EMAIL>
      changes:
        - createTable:
            tableName: vehicle_migration_message_retry
            schemaName: emh_damage_management
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: payload
                  type: json
                  constraints:
                    nullable: false
              - column:
                  name: vin
                  type: varchar
                  constraints:
                    nullable: false
                    unique: true
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
      rollback:
        - dropTable:
            tableName: vehicle_migration_message_retry
            schemaName: emh_damage_management
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE
