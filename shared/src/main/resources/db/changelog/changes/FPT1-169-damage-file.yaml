databaseChangeLog:
  - changeSet:
      id: mjany1-create-damage-file-table
      author: <EMAIL>
      changes:
        - createTable:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: damage_file_key
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: version
                  type: int
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
              - column:
                  name: employee_to_be_charged
                  type: varchar
              - column:
                  name: deductible_amount
                  type: number(16,9)
              - column:
                  name: deductible_currency_unit
                  type: varchar
              - column:
                  name: claim_towards_employee_amount
                  type: number(16,9)
              - column:
                  name: claim_towards_employee_currency_unit
                  type: varchar
              - column:
                  name: state
                  type: varchar
              - column:
                  name: damage_report_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: internal_order_number
                  type: varchar
              - column:
                  name: external_reference
                  type: varchar
      rollback:
        - dropTable:
            tableName: damage_file
            schemaName: emh_damage_management
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE

  - changeSet:
      id: mjany2-create-damage-file-key-unique-constraint
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: uk_df_df_key
            unique: true
            columns:
              - column:
                  name: damage_file_key
      rollback:
        - dropIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: uk_df_df_key

  - changeSet:
      id: mjany3-create-damage-file-internal-order-number-index
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_ion
            columns:
              - column:
                  name: internal_order_number
      rollback:
        - dropIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_ion

  - changeSet:
      id: mjany4-create-damage-file-external-reference-unique-constraint
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: uk_df_er
            unique: true
            columns:
              - column:
                  name: external_reference
      rollback:
        - dropIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_er

  - changeSet:
      id: mjany5-create-damage-report-table
      author: <EMAIL>
      changes:
        - createTable:
            tableName: damage_report
            schemaName: emh_damage_management
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
      rollback:
        - dropTable:
            tableName: damage_report
            schemaName: emh_damage_management

  - changeSet:
      id: mjany6-create-damage-file-damage-report-fk
      author: <EMAIL>
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: damage_report_id
            baseTableName: damage_file
            baseTableSchemaName: emh_damage_management
            constraintName: fk_df_dr_id
            referencedColumnNames: id
            referencedTableName: damage_report
            referencedTableSchemaName: emh_damage_management
      rollback:
        - dropForeignKeyConstraint:
            baseTableName: damage_file
            baseTableSchemaName: emh_damage_management
            constraintName: fk_df_dr_id
