databaseChangeLog:
  - changeSet:
      id: jhentschel1-create-event-outbox-table
      author: extern.jan.he<PERSON><PERSON>@porsche.de
      changes:
        - createTable:
            tableName: event_outbox
            schemaName: emh_damage_management
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: event_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: event_type
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: payload
                  type: json
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
              - column:
                  name: state
                  type: varchar
              - column:
                  name: published_at
                  type: timestamp

      rollback:
        - dropTable:
            tableName: event_outbox
            schemaName: emh_damage_management
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE
