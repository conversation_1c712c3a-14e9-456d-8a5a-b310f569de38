databaseChangeLog:
  - changeSet:
      id: mjany10-add-creation-date-column-to-damage-file
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: creation_date
                  type: timestamp
                  constraints:
                    nullable: false
                  valueComputed: "(SELECT created_date
                                  FROM emh_damage_management.damage_file df
                                  WHERE df.id = id)"
      rollback:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columnName: creation_date
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE

  - changeSet:
      id: mjany11-add-deductible-claim-update-to-damage-file
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: deductible_claim_update
                  type: timestamp
      rollback:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columnName: deductible_claim_update
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE

  - changeSet:
      id: mjany12-add-damage-file-deductible-claim-update-index
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_dcu
            columns:
              - column:
                  name: deductible_claim_update
      rollback:
        - dropIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_dcu