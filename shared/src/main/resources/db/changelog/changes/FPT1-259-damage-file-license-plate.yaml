databaseChangeLog:
  - changeSet:
      id: mjany14-damage-file-add-license-plate
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: license_plate
                  type: varchar
      rollback:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: license_plate

  - changeSet:
      id: mjany15-damage-file-add-closed-date
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: closed_date
                  type: timestamp
      rollback:
        - dropColumn:
            tableName: damage_file
            columns:
              - column:
                  name: closed_date
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE

  - changeSet:
      id: mjany16-damage-file-add-leasing-art
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: leasing_art
                  type: varchar
                  constraints:
                    nullable: false
                  value: ''
      rollback:
        - dropColumn:
            tableName: damage_file
            columns:
              - column:
                  name: leasing_art

  - changeSet:
      id: mjany17-damage-file-add-damage-claim-classification
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: damage_claim_classification
                  type: varchar
                  constraints:
                    nullable: false
                  value: ''
      rollback:
        - dropColumn:
            tableName: damage_file
            columns:
              - column:
                  name: damage_claim_classification

