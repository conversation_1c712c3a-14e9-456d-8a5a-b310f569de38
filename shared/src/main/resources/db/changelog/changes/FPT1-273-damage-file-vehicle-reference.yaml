databaseChangeLog:
  - changeSet:
      id: mjany7-alter-damage-file-add-vehicle-reference
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: vehicle_vin
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: vehicle_vguid
                  type: varchar
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: vehicle_vin
              - column:
                  name: vehicle_vguid

  - changeSet:
      id: mjany8-create-damage-file-vehicle-vin-index
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_vvin
            columns:
              - column:
                  name: vehicle_vin
      rollback:
        - dropIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_vvin

  - changeSet:
      id: mjany9-create-damage-file-vehicle-vguid-index
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_vvguid
            columns:
              - column:
                  name: vehicle_vin
      rollback:
        - dropIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: idx_df_vvguid