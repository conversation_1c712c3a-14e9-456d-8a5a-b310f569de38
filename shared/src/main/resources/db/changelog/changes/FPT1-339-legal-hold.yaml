databaseChangeLog:
  - changeSet:
      id: mjany18-create-legal-hold-table
      author: <EMAIL>
      changes:
        - createTable:
            tableName: legal_hold
            schemaName: emh_damage_management
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: legal_hold_key
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
              - column:
                  name: issuer
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: external_reference
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: domain_entities
                  type: varchar
              - column:
                  name: released
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: released_by
                  type: varchar
      rollback:
        - dropTable:
            tableName: damage_file
            schemaName: emh_damage_management
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIM<PERSON><PERSON><PERSON> WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE

  - changeSet:
      id: mjany19-create-legal-hold-key-unique-constraint
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: legal_hold
            schemaName: emh_damage_management
            indexName: uk_lh_lh_key
            unique: true
            columns:
              - column:
                  name: legal_hold_key
      rollback:
        - dropIndex:
            tableName: legal_hold
            schemaName: emh_damage_management
            indexName: uk_lh_lh_key

  - changeSet:
      id: mjany20-create-damage-file-legal-hold-table
      author: <EMAIL>
      changes:
        - createTable:
            tableName: damage_file_legal_hold
            schemaName: emh_damage_management
            columns:
              - column:
                  name: damage_file_id
                  type: uuid
                  constraints:
                    nullable: false
              - column:
                  name: legal_hold_key
                  type: varchar
                  constraints:
                    nullable: false
      rollback:
        - dropTable:
            tableName: damage_file_legal_hold
            schemaName: emh_damage_management

  - changeSet:
      id: mjany21-create-damage-file-legal-hold-damage-file-id-index
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file_legal_hold
            schemaName: emh_damage_management
            indexName: idx_dflh_df_id
            columns:
              - column:
                  name: damage_file_id
      rollback:
        - dropIndex:
            tableName: damage_file_legal_hold
            schemaName: emh_damage_management
            indexName: idx_dflh_df_id
