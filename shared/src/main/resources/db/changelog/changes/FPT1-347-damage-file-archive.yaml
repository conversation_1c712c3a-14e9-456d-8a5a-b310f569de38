databaseChangeLog:
  - changeSet:
      id: acohn1-create-archive-damage-file-indicator-table
      author: <EMAIL>
      changes:
        - createTable:
            tableName: damage_file_archive_indicator
            schemaName: emh_damage_management
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: damage_file_key
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: archive
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
      rollback:
        - dropTable:
            tableName: damage_file
            schemaName: emh_damage_management
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE

  - changeSet:
      id: mjany31-add-damage-file-key-unique-constraint-for-h2
      author: <EMAIL>
      preConditions:
        - dbms:
            type: h2
        - onFail: MARK_RAN
      changes:
        - addUniqueConstraint:
            tableName: damage_file
            schemaName: emh_damage_management
            constraintName: uc_df_dfk
            columnNames: damage_file_key

  - changeSet:
      id: acohn2-add-foreign-key-constraint
      author: <EMAIL>
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: damage_file_key
            baseTableName: damage_file_archive_indicator
            baseTableSchemaName: emh_damage_management
            constraintName: fk_dfai_df_key
            referencedColumnNames: damage_file_key
            referencedTableName: damage_file
            referencedTableSchemaName: emh_damage_management
            onDelete: CASCADE
      rollback:
        - dropForeignKeyConstraint:
            baseTableName: damage_file
            baseTableSchemaName: emh_damage_management
            constraintName: fk_df_dr_id

