databaseChangeLog:
  - changeSet:
      id: sbalodi1-remove-trip-reason-from-damage-file
      author: <EMAIL>
      changes:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columnName: trip_reason

      rollback:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: trip_reason
                  type: varchar
                  value: "UNKNOWN"
  - changeSet:
      id: sbalodi2-create-employee-to-be-charged-not-null-constraint
      author: <EMAIL>
      changes:
        - addNotNullConstraint:
            columnName: employee_to_be_charged
            constraintName: nnc_df_etbc
            schemaName: emh_damage_management
            tableName: damage_file
            defaultNullValue: 'UNKNOWN'
            validate: true

      rollback:
        - dropNotNullConstraint:
            columnName: employee_to_be_charged
            constraintName: nnc_df_etbc
            schemaName: emh_damage_management
            tableName: damage_file