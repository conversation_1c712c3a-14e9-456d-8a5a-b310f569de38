databaseChangeLog:
  - changeSet:
      id: iplese1-create-internal-order-number-sync-indicator-table
      author: <EMAIL>
      changes:
        - createTable:
            tableName: internal_order_number_sync_indicator
            schemaName: emh_damage_management
            columns:
              - column:
                  name: id
                  type: uuid
                  constraints:
                    primaryKey: true
              - column:
                  name: damage_file_key
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: internal_order_number
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: external_system_sync_done
                  type: boolean
                  constraints:
                    nullable: false
              - column:
                  name: created_by
                  type: varchar
                  constraints:
                    nullable: false
              - column:
                  name: created_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: last_modified_by
                  type: varchar
              - column:
                  name: last_modified_date
                  type: timestamp
      rollback:
        - dropTable:
            tableName: damage_file
            schemaName: emh_damage_management
      modifySql:
        - replace:
            dbms: postgresql
            replace: TIMESTAMP WITHOUT TIME ZONE
            with: TIMESTAMP WITH TIME ZONE

  - changeSet:
      id: iplese2-add-foreign-key-constraint
      author: <EMAIL>
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: damage_file_key
            baseTableName: internal_order_number_sync_indicator
            baseTableSchemaName: emh_damage_management
            constraintName: fk_ionsi_df_key
            referencedColumnNames: damage_file_key
            referencedTableName: damage_file
            referencedTableSchemaName: emh_damage_management
            onDelete: CASCADE
      rollback:
        - dropForeignKeyConstraint:
            baseTableName: damage_file
            baseTableSchemaName: emh_damage_management
            constraintName: fk_df_dr_id

