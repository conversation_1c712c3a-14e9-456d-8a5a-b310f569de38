databaseChangeLog:
  - changeSet:
      id: mjany22-create-damage-file-key-sequence
      author: <EMAIL>
      changes:
        - createSequence:
            cacheSize:  1
            cycle:  false
            dataType:  bigint
            incrementBy: 1
            maxValue:  9999999999
            minValue:  1
            schemaName:  emh_damage_management
            sequenceName:  seq_damage_file_key
            startValue:  1
      rollback:
        - dropSequence:
            sequenceName: seq_damage_file_key
            schemaName: emh_damage_management

  - changeSet:
      id: mjany23-add-new-damage-file-key
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: damage_file_key_new
                  valueSequenceNext: seq_damage_file_key
                  type: varchar
                  constraints:
                    nullable: false
      rollback:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columnName: damage_file_key_new

  - changeSet:
      id: mjany24-drop-damage-file-archive-damage-file-key-fk
      author: <EMAIL>
      changes:
        - dropForeignKeyConstraint:
            baseTableName: damage_file_archive_indicator
            baseTableSchemaName: emh_damage_management
            constraintName: fk_dfai_df_key
      rollback:
        - addForeignKeyConstraint:
            baseColumnNames: damage_file_key
            baseTableName: damage_file_archive_indicator
            baseTableSchemaName: emh_damage_management
            constraintName: fk_dfai_df_key
            referencedColumnNames: damage_file_key
            referencedTableName: damage_file
            referencedTableSchemaName: emh_damage_management
            onDelete: CASCADE

  - changeSet:
      id: mjany25-update-damage-file-archive-damage-file-keys
      author: <EMAIL>
      changes:
        - update:
            tableName: damage_file_archive_indicator
            schemaName: emh_damage_management
            columns:
              - column:
                  name: damage_file_key
                  valueComputed: "(select df.damage_file_key_new from emh_damage_management.damage_file_archive_indicator ai join emh_damage_management.damage_file df on ai.damage_file_key = df.damage_file_key WHERE df.damage_file_key = emh_damage_management.damage_file_archive_indicator.damage_file_key)"

  - changeSet:
      id: mjany26-drop-old-damage-file-key
      author: <EMAIL>
      changes:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columnName: damage_file_key
      rollback:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: damage_file_key_new
                  type: varchar
                  constraints:
                    nullable: false

  - changeSet:
      id: mjany27-rename-new-damage-file-key
      author: <EMAIL>
      changes:
        - renameColumn:
            newColumnName: damage_file_key
            oldColumnName: damage_file_key_new
            tableName: damage_file
            schemaName: emh_damage_management
      rollback:
        - renameColumn:
            newColumnName: damage_file_key_new
            oldColumnName: damage_file_key
            tableName: damage_file
            schemaName: emh_damage_management

  - changeSet:
      id: mjany28-recreate-damage-file-key-index
      author: <EMAIL>
      changes:
        - createIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: uk_df_df_key
            unique: true
            columns:
              - column:
                  name: damage_file_key
      rollback:
        - dropIndex:
            tableName: damage_file
            schemaName: emh_damage_management
            indexName: uk_df_df_key

  - changeSet:
      id: mjany29-reenable-damage-file-key-unique-constraint-for-h2
      author: <EMAIL>
      preConditions:
        - dbms:
            type: h2
        - onFail: MARK_RAN
      changes:
        - addUniqueConstraint:
            tableName: damage_file
            schemaName: emh_damage_management
            constraintName: uc_df_dfk
            columnNames: damage_file_key


  - changeSet:
      id: mjany30-reenable-damage-file-archive-damage-file-key-fk
      author: <EMAIL>
      changes:
        - addForeignKeyConstraint:
            baseColumnNames: damage_file_key
            baseTableName: damage_file_archive_indicator
            baseTableSchemaName: emh_damage_management
            constraintName: fk_dfai_df_key
            referencedColumnNames: damage_file_key
            referencedTableName: damage_file
            referencedTableSchemaName: emh_damage_management
            onDelete: CASCADE
      rollback:
        - dropForeignKeyConstraint:
            baseTableName: damage_file_archive_indicator
            baseTableSchemaName: emh_damage_management
            constraintName: fk_dfai_df_key