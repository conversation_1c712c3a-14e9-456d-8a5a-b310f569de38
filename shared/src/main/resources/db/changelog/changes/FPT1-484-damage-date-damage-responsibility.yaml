databaseChangeLog:
  - changeSet:
      id: sbalodi6-add-damage-date-damage-responsibility
      author: <EMAIL>
      changes:
        - addColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columns:
              - column:
                  name: damage_date
                  type: timestamp
                  constraints:
                    nullable: false
              - column:
                  name: damage_responsibility
                  type: varchar
                  value: "UNKNOWN"
      rollback:
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columnName: damage_date
        - dropColumn:
            tableName: damage_file
            schemaName: emh_damage_management
            columnName: damage_responsibility