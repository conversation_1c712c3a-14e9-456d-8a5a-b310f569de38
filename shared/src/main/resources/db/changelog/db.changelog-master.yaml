databaseChangeLog:
  - include:
      file: classpath:/db/changelog/changes/FPT1-169-damage-file.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-273-damage-file-vehicle-reference.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-255-damage-file-creation-date.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-169-event-outbox-table.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-155-trip-reason.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-259-damage-file-license-plate.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-361-remove-trip-reason.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-339-legal-hold.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-334-quartz-jobstore.xml
  - include:
      file: classpath:/db/changelog/changes/FPT1-347-damage-file-archive.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-448-damage-file-key-sequence.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-449-add-cost-center.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-403-insurance-coverage.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-468-damage-file-deductible-update.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-484-damage-date-damage-responsibility.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-514-fleet-identifier-for-vehicle.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-446-damage-file-synchronisation.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-930-add-external-reference-to-vehicle.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-986-using-cost-center.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-482-damage-file-external-flag.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-1240-vehicle-migration-message-retry-table.yaml
  - include:
      file: classpath:/db/changelog/changes/FPT1-1289-vguid-optional-in-damage-file.yaml
