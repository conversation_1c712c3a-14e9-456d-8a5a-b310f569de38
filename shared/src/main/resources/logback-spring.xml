<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <springProfile name="default">
        <root level="INFO">
            <appender-ref ref="jsonlogger"/>
        </root>
        <appender name="jsonlogger" class="ch.qos.logback.core.ConsoleAppender">
            <encoder class="ch.qos.logback.classic.encoder.JsonEncoder"/>
        </appender>
    </springProfile>

    <springProfile name="test,debug">
        <root level="INFO">
            <appender-ref ref="CONSOLE"/>
        </root>
        <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
            <encoder>
                <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n</pattern>
            </encoder>
        </appender>
        <logger name="org.springframework.boot.actuate.endpoint.jmx" level="warn"/>
        <logger name="org.springframework.orm.jpa" level="warn"/>
        <logger name="org.springframework.ws.client.MessageTracing" level="debug"/>
        <logger name="org.springframework.ws.server.MessageTracing" level="debug"/>
        <logger name="org.springframework.web.filter.CommonsRequestLoggingFilter" level="debug"/>
        <logger name="org.springframework.web.servlet.mvc" level="error"/>
        <logger name="org.apache.kafka.clients" level="error"/>
    </springProfile>

</configuration>
