package com.emh.damagemanagement.shared

import java.time.OffsetDateTime
import java.time.temporal.TemporalAccessor
import java.util.*
import org.mockito.internal.util.KotlinInlineClassUtil
import org.mockito.invocation.InvocationOnMock
import org.mockito.stubbing.Answer
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.cache.annotation.EnableCaching
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.data.auditing.DateTimeProvider
import org.springframework.data.domain.AuditorAware
import org.springframework.security.test.web.servlet.setup.SecurityMockMvcConfigurers.springSecurity
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.setup.DefaultMockMvcBuilder
import org.springframework.test.web.servlet.setup.MockMvcBuilders
import org.springframework.web.context.WebApplicationContext

@EnableCaching
@SpringBootApplication
@ConfigurationPropertiesScan
class TestSharedApplication {

    fun main(args: Array<String>) {
        runApplication<TestSharedApplication>(*args)
    }
}

@TestConfiguration
class WebTestClientConfiguration {

    @Bean
    @Qualifier("secureMockMvc")
    fun secureMockMvc(applicationContext: WebApplicationContext): MockMvc {
        return MockMvcBuilders.webAppContextSetup(applicationContext)
            .apply<DefaultMockMvcBuilder>(springSecurity())
            .build()
    }

    @Bean
    @Qualifier("mockMvc")
    fun mockMvc(applicationContext: WebApplicationContext): MockMvc {
        return MockMvcBuilders.webAppContextSetup(applicationContext).build()
    }
}

const val TEST_USER = "TestUser"

@TestConfiguration
class TestJpaAuditingConfiguration {

    @Bean @Primary fun testSecurityAuditorProvider() = AuditorAware { Optional.of(TEST_USER) }

    @Bean @Primary fun auditingDateTimeProvider(): DateTimeProvider = TestAuditingDateTimeProvider()

    companion object {
        @Suppress("unused")
        val AUDIT_FIELDS = arrayOf("createdBy", "created", "lastModifiedBy", "lastModified", "version", "etag")
    }
}

/**
 * Custom DateTimeProvider used during JPAAuditing. You can inject this one and set your desired time. Time will be
 * picked up during flush and used for createdAt/lastModifiedAt. Do not forget to call reset() in @AfterEach
 */
class TestAuditingDateTimeProvider : DateTimeProvider {
    private var now: Optional<TemporalAccessor> = defaultNow

    private val defaultNow: Optional<TemporalAccessor>
        get() = Optional.of(OffsetDateTime.now())

    @Suppress("unused")
    fun reset() {
        now = this.defaultNow
    }

    @Suppress("unused")
    fun setNow(now: OffsetDateTime) {
        this.now = Optional.of(now)
    }

    override fun getNow(): Optional<TemporalAccessor> = now
}

/** Workaround for using value classes in mockito answer. See https://github.com/mockito/mockito/pull/2280 */
@Suppress("UNCHECKED_CAST")
class InlineClassesAnswer<T : Any>(private val defaultAnswer: Answer<T>) : Answer<T> {
    override fun answer(invocation: InvocationOnMock?): T {
        return KotlinInlineClassUtil.unboxUnderlyingValueIfNeeded(invocation, defaultAnswer.answer(invocation)) as T
    }
}
