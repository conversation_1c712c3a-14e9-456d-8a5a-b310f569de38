package com.emh.damagemanagement.shared

import java.time.OffsetDateTime
import java.util.*
import kotlin.random.Random

val alphanumericCharPool: List<Char> = ('a'..'z') + ('A'..'Z') + ('0'..'9')
val charPool: List<Char> = alphanumericCharPool + "äöüÄÖÜß?!.,:;_- ".map { it }
val locales =
    listOf(
        Locale.CANADA,
        Locale.ENGLISH,
        Locale.GERMAN,
        Locale.FRANCE,
        Locale.CHINESE,
        Locale.ITALIAN,
        Locale.JAPANESE,
        Locale.TRADITIONAL_CHINESE,
        Locale.UK,
        Locale.US,
    )

fun createRandomString(length: Int): String =
    (1..length).map { Random.nextInt(0, charPool.size) }.map(charPool::get).joinToString("")

fun createRandomAlphanumericString(length: Int): String =
    (1..length).map { Random.nextInt(0, alphanumericCharPool.size) }.map(alphanumericCharPool::get).joinToString("")

fun createRandomOffsetDate(): OffsetDateTime = OffsetDateTime.now().minusDays(Random.nextLong(0, 1000))
