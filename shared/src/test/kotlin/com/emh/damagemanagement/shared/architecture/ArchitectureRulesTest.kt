@file:Suppress("PropertyName", "unused")

package com.emh.damagemanagement.shared.architecture

import com.tngtech.archunit.base.DescribedPredicate.alwaysTrue
import com.tngtech.archunit.core.domain.JavaClass.Predicates.resideInAPackage
import com.tngtech.archunit.core.importer.ImportOption
import com.tngtech.archunit.junit.AnalyzeClasses
import com.tngtech.archunit.junit.ArchTest
import com.tngtech.archunit.lang.ArchRule
import com.tngtech.archunit.library.Architectures.onionArchitecture

@AnalyzeClasses(packages = [DEFAULT_PACKAGE], importOptions = [ImportOption.DoNotIncludeTests::class])
open class ArchitectureRulesTest {

    @ArchTest
    val `should respect onion architecture`: ArchRule =
        onionArchitecture()
            .ignoreDependency(resideInAPackage("com.emh.damagemanagement.shared.spring"), alwaysTrue())
            .domainModels(DOMAIN_PACKAGE)
            .domainServices("..domain.service..")
            .adapter("all", ADAPTER_PACKAGE)
            .applicationServices(APPLICATION_PACKAGE)
            .allowEmptyShould(true)
}

// Packages
const val DEFAULT_PACKAGE = "com.emh.damagemanagement"
const val REST_ADAPTER_PACKAGE = "..adapter.in.rest.."
const val ADAPTER_PACKAGE = "..adapter.."
const val APPLICATION_PACKAGE = "..application.."
const val DOMAIN_PACKAGE = "..domain.."

// Suffixes
const val SERVICE_SUFFIX = "Service"
const val FINDER_SUFFIX = "Finder"
const val CONVERTER_SUFFIX = "Converter"
