package com.emh.damagemanagement.shared.config

import io.micrometer.cloudwatch2.CloudWatchMeterRegistry
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.MockitoAnnotations

class MicroMeterConfigTest {
    private lateinit var microMeterConfig: MicroMeterConfig

    @BeforeEach
    fun setup() {
        MockitoAnnotations.openMocks(this)
        microMeterConfig = MicroMeterConfig()
    }

    @Test
    fun `can successfully create micrometer registry for cloudwatch`() {
        val awsRegion = "test-region"
        val cloudwatchNamespace = "test-namespace"
        val duration: Long = 2
        val meterRegistry = microMeterConfig.getMeterRegistry(cloudwatchNamespace, awsRegion, duration)

        assertThat(meterRegistry).isInstanceOf(CloudWatchMeterRegistry::class.java)
    }
}
