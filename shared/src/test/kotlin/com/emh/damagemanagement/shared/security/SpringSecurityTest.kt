package com.emh.damagemanagement.shared.security

import com.emh.damagemanagement.shared.WebServiceTest
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.ContextConfiguration
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestMethod
import org.springframework.web.bind.annotation.RestController

@WebServiceTest
@ContextConfiguration(classes = [TestConfiguration::class])
class SpringSecurityTest {

    @Autowired @Qualifier("secureMockMvc") private lateinit var mockMvc: MockMvc

    private val tokenWithDifferentSigner =
        "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************.dyt0CoTl4WoVjAHI9Q_CwSKhl6d_9rhM3NrXuJttkao"

    @Test
    fun `should return unauthorized when authentication is missing`() {
        mockMvc.perform(get("/test")).andExpect(status().isUnauthorized)
    }

    @Test
    fun `should allow unauthenticated access to actuators`() {
        mockMvc.perform(get("/damage/actuator/health")).andExpect(status().isOk)
    }

    @Test
    fun `should fail authentication with invalid token`() {
        mockMvc
            .perform(get("/test").header("Authorization", "Bearer $tokenWithDifferentSigner"))
            .andExpect(status().isUnauthorized)
    }
}

class TestConfiguration {
    @RestController
    class TestController {
        @RequestMapping(path = ["/test"], method = [RequestMethod.GET])
        fun test(): String {
            return "authorized!"
        }

        @RequestMapping(path = ["/damage/actuator/health"], method = [RequestMethod.GET])
        fun actuatorMock(): String {
            return "healthy"
        }
    }
}
