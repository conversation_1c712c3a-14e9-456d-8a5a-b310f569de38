/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.spring

import com.emh.damagemanagement.shared.IntegrationTest
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.stereotype.Service
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RestController

@IntegrationTest
class DataIntegrityViolationExceptionAspectTest {

    @Autowired private lateinit var testNonRestControllerService: TestNonRestControllerService

    @Autowired private lateinit var testRestController: TestRestController

    @Test
    fun `should not convert DataIntegrityViolationException due to missing controller annotation`() {
        assertThatExceptionOfType(DataIntegrityViolationException::class.java).isThrownBy {
            testNonRestControllerService.throwDataIntegrityViolationException()
        }
    }

    @Test
    fun `should not convert DataIntegrityViolationException due to missing post or update annotation`() {
        assertThatExceptionOfType(DataIntegrityViolationException::class.java).isThrownBy {
            testRestController.throwDataIntegrityViolationExceptionWithoutAnnotation()
        }
    }

    @Test
    fun `should not convert DataIntegrityViolationException due to get annotation`() {
        assertThatExceptionOfType(DataIntegrityViolationException::class.java).isThrownBy {
            testRestController.throwDataIntegrityViolationExceptionWithGetMapping()
        }
    }

    @Test
    fun `should convert DataIntegrityViolationException to IllegalArgumentException`() {
        assertThatExceptionOfType(IllegalArgumentException::class.java).isThrownBy {
            testRestController.throwDataIntegrityViolationExceptionWithPostMapping()
        }
    }
}

@RestController
internal class TestRestController {

    fun throwDataIntegrityViolationExceptionWithoutAnnotation(): Unit =
        throw DataIntegrityViolationException("cookie constraint violation")

    @PostMapping(value = ["test"])
    fun throwDataIntegrityViolationExceptionWithPostMapping(): Unit =
        throw DataIntegrityViolationException("cookie constraint violation")

    @GetMapping
    fun throwDataIntegrityViolationExceptionWithGetMapping(): Unit =
        throw DataIntegrityViolationException("cookie constraint violation")
}

@Service
internal class TestNonRestControllerService {

    fun throwDataIntegrityViolationException(): Unit =
        throw DataIntegrityViolationException("serious cookie constraint violation")
}
