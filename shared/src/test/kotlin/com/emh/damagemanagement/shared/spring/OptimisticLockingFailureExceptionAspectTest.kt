/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.shared.spring

import com.emh.damagemanagement.shared.IntegrationTest
import jakarta.persistence.OptimisticLockException
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.hibernate.StaleStateException
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.dao.OptimisticLockingFailureException
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@IntegrationTest
class OptimisticLockingFailureExceptionAspectTest {

    @Autowired private lateinit var testTransactionalService: TestTransactionalService

    @Autowired private lateinit var testTransactionalComponent: TestTransactionalComponent

    @Autowired private lateinit var testNonTransactionalService: TestNonTransactionalService

    @Test
    fun `should not convert OptimisticLockingFailureException due to missing transactional annotation`() {
        assertThatExceptionOfType(OptimisticLockingFailureException::class.java).isThrownBy {
            testNonTransactionalService.throwOptimisticLockingFailureException()
        }
    }

    @Test
    fun `should convert OptimisticLockingFailureException to ConcurrentModificationException for Services`() {
        assertThatExceptionOfType(ConcurrentModificationException::class.java).isThrownBy {
            testTransactionalService.throwOptimisticLockingFailureException()
        }
    }

    @Test
    fun `should convert OptimisticLockException to ConcurrentModificationException for Services`() {
        assertThatExceptionOfType(ConcurrentModificationException::class.java).isThrownBy {
            testTransactionalService.throwOptimisticLockException()
        }
    }

    @Test
    fun `should convert StaleStateException to ConcurrentModificationException for Services`() {
        assertThatExceptionOfType(ConcurrentModificationException::class.java).isThrownBy {
            testTransactionalService.throwStaleStateException()
        }
    }

    @Test
    fun `should convert OptimisticLockingFailureException to ConcurrentModificationException for Components`() {
        assertThatExceptionOfType(ConcurrentModificationException::class.java).isThrownBy {
            testTransactionalComponent.throwOptimisticLockingFailureException()
        }
    }

    @Test
    fun `should convert OptimisticLockException to ConcurrentModificationException for Components`() {
        assertThatExceptionOfType(ConcurrentModificationException::class.java).isThrownBy {
            testTransactionalComponent.throwOptimisticLockException()
        }
    }

    @Test
    fun `should convert StaleStateException to ConcurrentModificationException for Components`() {
        assertThatExceptionOfType(ConcurrentModificationException::class.java).isThrownBy {
            testTransactionalComponent.throwStaleStateException()
        }
    }
}

@Service
@Transactional
internal class TestTransactionalService {

    fun throwOptimisticLockingFailureException(): Unit = throw OptimisticLockingFailureException("failed again")

    fun throwOptimisticLockException(): Unit = throw OptimisticLockException("failed again")

    fun throwStaleStateException(): Unit = throw StaleStateException("failed again")
}

@Component
@Transactional
internal class TestTransactionalComponent {

    fun throwOptimisticLockingFailureException(): Unit = throw OptimisticLockingFailureException("failed again")

    fun throwOptimisticLockException(): Unit = throw OptimisticLockException("failed again")

    fun throwStaleStateException(): Unit = throw StaleStateException("failed again")
}

@Service
internal class TestNonTransactionalService {

    fun throwOptimisticLockingFailureException(): Unit =
        throw OptimisticLockingFailureException("failed again and again")
}
