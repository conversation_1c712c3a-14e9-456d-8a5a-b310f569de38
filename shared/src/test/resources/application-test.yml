spring:
  kafka:
    bootstrap-servers: ${spring.embedded.kafka.brokers}
    producer:
      key-serializer: org.apache.kafka.common.serialization.StringSerializer
      value-serializer: org.springframework.kafka.support.serializer.JsonSerializer
    consumer:
      auto-offset-reset: earliest
    properties:
      sasl:
        mechanism: PLAINTEXT
        jaas:
          config: null
      security:
        protocol: PLAINTEXT
      session:
        timeout:
          ms: 45000

  main:
    allow-bean-definition-overriding: true
  security:
    oauth2:
      resourceserver:
        jwt:
          audiences: ${OAUTH2_DM_AUDIENCE:de2c475e-e393-425d-a89e-a1f394c90ae1}
          jwk-set-uri: ${OAUTH2_JWKS_URI:https://login.microsoftonline.com/common/discovery/keys}
          issuer-uri: https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/v2.0
      client:
        registration:
          employee:
            authorization-grant-type: client_credentials
            client-id: TEST_CLIENT_ID
            client-secret: TEST_CLIENT_SECRET
            scope:
              - TEST_SCOPE
          vehicles:
            authorization-grant-type: client_credentials
            client-id: TEST_CLIENT_ID
            client-secret: TEST_CLIENT_SECRET
            scope:
              - TEST_SCOPE
          pace:
            authorization-grant-type: client_credentials
            client-id: TEST_CLIENT_ID
            client-secret: TEST_CLIENT_SECRET
            client-authentication-method: client_secret_post
        provider:
          employee:
            token-uri: http://localhost:8090/oauth/token
          vehicles:
            token-uri: http://localhost:8091/oauth/token
          pace:
            token-uri: http://localhost:8092/oauth/token
  cloud:
    aws:
      s3:
        path-style-access-enabled: true
      # requires localstack
      endpoint: ${TEST_S3_ENDPOINT:http://localhost:4566}
      region:
        static: us-east-1
      credentials:
        access-key: foo
        secret-key: bar
  datasource:
    url: ${database.url}
    username: ${database.user}
    password: ${database.pass}
    hikari:
      jdbc-url: ${database.url}
      username: ${database.user}
      maximum-pool-size: ${database.maximumPoolSize}
      minimum-idle: ${database.minimumIdle}
      connection-timeout: 300
      driver-class-name: ${database.driver-class-name}
  jpa:
    open-in-view: false
    properties:
      hibernate:
        ddl-auto: none
        jdbc:
          batch_size: 100
          time_zone: UTC
        dialect: ${database.dialect}
        default_schema: ${database.schema}
  sql:
    init:
      mode: never
  liquibase:
    change-log: classpath:/db/changelog/db.changelog-master.yaml
    liquibase-schema: ${database.schema}
    default-schema: ${database.schema}
    enabled: true
  quartz:
    auto-startup: false

caching:
  jwksTTL: 86400000

logging:
  level:
    org:
      springframework:
        web:
          filter:
            CommonsRequestLoggingFilter: ERROR
    org.springframework.web: ERROR
    root: INFO

database:
  rds: false
  user: postgres
  pass: postgres
  url: ${DATASOURCE_URL:******************************************************}
  dialect: org.hibernate.dialect.PostgreSQLDialect
  schema: emh_damage_management
  driver-class-name: org.postgresql.Driver
  maximumPoolSize: 4
  minimumIdle: 2

server:
  forward-headers-strategy: framework
  servlet:
    context-path: /api

repairfix:
  base-url: http://localhost:8080
  api-key: apiKey
  retry:
    max-attempts: 3
    backoff-duration-in-millis: 1000
  fleet-groups:
    dienst-leasing: porsche-dienst-leasing
    pool: porsche-pool
    root: porsche
  requests:
    page-size: 10
    page-delay-seconds: 0
  damage-file-migration:
    # date format yyyy-mm-dd
    from-date: "2024-08-01"
    thresholdInDays: 2


employees:
  base-url: http://localhost:8090

vehicles:
  base-url: http://localhost:8091

number-of-damages:
  kafka:
    producer:
      topic: ${NUMBER_OF_DAMAGES_TOPIC:FRA_emhs_dms_numer_of_damages_topic_dev}

pace:
  base-url: http://localhost:8092

storage:
  sse: aws:kms
  sse-kms-key: alias/valid-kms-key
  vehicle-migration:
    # has to match the one configured (or created) in localstack
    bucket: ${STORAGE_VEHICLE_MIGRATION_BUCKET:test-vehicle-migration}
  legal-hold:
    # has to match the one configured (or created) in localstack
    bucket: ${STORAGE_LEGAL_HOLD_BUCKET:test-legal-hold}
  archive:
    # has to match the one configured (or created) in localstack
    bucket: ${STORAGE_ARCHIVE_BUCKET:test-archive}

outbox-message-relay:
  scheduler:
    cron: 0 0 0 1 1 ?

monetary-benefit:
  accountable:
    kafka:
      listener:
        id: "monetary-benefit.accountable.consumer"
        group-id: "dispatch.monetary-benefit.accountable.consumer"
        topic: "monetary-benefit.accountable"
      producer:
        topic: "monetary-benefit.accountable"

kafka:
  retry:
    interval: 10
    maxAttempts: 1

feature-flags:
  vehicle-responsible-person-migration-uses-user-service: false

damage-file-migration:
  scheduler:
    cron: 0 0 0 1 1 ? 2099

internal-order-number-update:
  scheduler:
    cron: 0 0 0 1 1 ? 2099

internal-oder-number-sync:
  scheduler:
    cron: 0 0 0 1 1 ? 2099

vehicle-migration:
  scheduler:
    cron: 0 0 0 1 1 ? 2099

damage-file-gobd:
  scheduler:
    cron: 0 0 0 1 1 ? 2099
  number-of-days-for-closed: 3
  number-of-days-for-scraped-or-sold: 2

damage-file-archive:
  scheduler:
    # SECOND MINUTE HOUR DAY_OF_MONTH MONTH DAY_OF_WEEK
    cron: 0 0 0 1 1 ? 2099

vehicle-migration-data:
  kafka:
    listener:
      group-id: "dms-vehicle-migration.consumer"
      topic: "dms-vehicle-migration"
  vehicle-migration-kafka-integration-enabled: "true"

vehicle-migration-message-retry:
  scheduler:
    cron: 0 0 0 1 1 ? 2099
