# Required metadata
sonar.projectKey=FP20-dms-24099
sonar.projectName=damage-management-service
sonar.qualitygate.wait=true

# This property is used by SonarQube to track the evolution of your project's
# code quality over time. It helps you identify which version of your
# project a specific analysis belongs to.
# Another way do define this property is via command-line:
#   sonar:sonar -Dsonar.projectVersion=1.0.0
# sonar.projectVersion=1.0.0

# Comma-separated paths to directories with sources (required)
sonar.sources=shared/src/main/kotlin,\
  hcm-p08-damage-report-adapter/src/main/kotlin,\
  damage-file-gobd/src/main/kotlin,\
  damage-file-kafka-out-adapter/src/main/kotlin,\
  damage-file/src/main/kotlin,\
  damage-file-migration/src/main/kotlin,\
  employee-client/src/main/kotlin,\
  excel-vehicle-migration/src/main/kotlin,\
  repairfix-client/src/main/kotlin,\
  outbox-message-relay/src/main/kotlin,\
  legal-hold/src/main/kotlin,\
  legal-hold-in-adapter/src/main/kotlin,\
  legal-hold-out-adapter/src/main/kotlin,\
  vehicle-adapter/src/main/kotlin,\
  pvcc-p40-monetary-benefit-adapter/src/main/kotlin,\
  pace-p40-internal-order-number-adapter/src/main/kotlin, \
  vehicle-migration-data-in-adapter/src/main/kotlin, \
  vehicle-migration/src/main/kotlin

# Comma-separated paths to directories with tests
sonar.tests=shared/src/test/kotlin,\
  hcm-p08-damage-report-adapter/src/test/kotlin,\
  damage-file/src/test/kotlin,\
  damage-file-gobd/src/test/kotlin,\
  damage-file-kafka-out-adapter/src/test/kotlin,\
  damage-file-migration/src/test/kotlin,\
  employee-client/src/test/kotlin,\
  excel-vehicle-migration/src/test/kotlin,\
  outbox-message-relay/src/test/kotlin,\
  legal-hold/src/test/kotlin,\
  legal-hold-in-adapter/src/test/kotlin,\
  legal-hold-out-adapter/src/test/kotlin,\
  vehicle-adapter/src/test/kotlin,\
  pvcc-p40-monetary-benefit-adapter/src/test/kotlin,\
  pace-p40-internal-order-number-adapter/src/test/kotlin, \
  vehicle-migration-data-in-adapter/src/test/kotlin, \
  vehicle-migration/src/test/kotlin

# path to (aggregated) report
sonar.coverage.jacoco.xmlReportPaths = build/reports/jacoco/testCodeCoverageReport/testCodeCoverageReport.xml

# Language-specific properties
# sonar.java.source=
# sonar.java.target=
sonar.java.binaries=shared/build/classes/kotlin/main,\
  hcm-p08-damage-report-adapter/build/classes/kotlin/main,\
  damage-file/build/classes/kotlin/main,\
  damage-file-gobd/build/classes/kotlin/main,\
  damage-file-kafka-out-adapter/build/classes/kotlin/main,\
  damage-file-migration/build/classes/kotlin/main,\
  employee-client/build/classes/kotlin/main,\
  repairfix-client/build/classes/kotlin/main,\
  excel-vehicle-migration/build/classes/kotlin/main,\
  legal-hold/build/classes/kotlin/main,\
  legal-hold-in-adapter/build/classes/kotlin/main,\
  legal-hold-out-adapter/build/classes/kotlin/main,\
  vehicle-adapter/build/classes/kotlin/main,\
  pvcc-p40-monetary-benefit-adapter/build/classes/kotlin/main,\
  pace-p40-internal-order-number-adapter/build/classes/kotlin/main, \
  vehicle-migration-data-in-adapter/build/classes/kotlin/main. \
  vehicle-migration/build/classes/kotlin/main
# sonar.java.libraries=target/dependency/*.jar
# sonar.java.test.binaries=target/test-classes
# sonar.java.test.libraries=target/dependency/*.jar

# Optional
# Excluding specific files or directories
# sonar.exclusions=src/main/java/com/example/legacy/**/*.java
# sonar.test.exclusions=src/test/java/com/example/integrationtests/**/*.java

# Optional
# Set the encoding of the source files
sonar.sourceEncoding=UTF-8

# Optional
# Set the path to the Maven installation directory if not in the PATH
# sonar.maven.home=/path/to/maven/installation

# Ignored SONAR rules
sonar.issue.ignore.multicriteria=e1,e2,e3

sonar.issue.ignore.multicriteria.e1.ruleKey=fb-contrib:NAB_NEEDLESS_BOOLEAN_CONSTANT_CONVERSION
sonar.issue.ignore.multicriteria.e1.resourceKey=**/*.kt

sonar.issue.ignore.multicriteria.e2.ruleKey=fb-contrib:NAB_NEEDLESS_BOXING_VALUEOF
sonar.issue.ignore.multicriteria.e2.resourceKey=**/*.kt

sonar.issue.ignore.multicriteria.e3.ruleKey=fb-contrib:PDP_POORLY_DEFINED_PARAMETER
sonar.issue.ignore.multicriteria.e3.resourceKey=**/*.kt

