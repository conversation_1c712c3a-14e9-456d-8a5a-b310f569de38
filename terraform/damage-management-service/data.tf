data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_vpc" "vpc" {
  tags = {
    Name = "emh-infrastructure-fleet-management"
  }
}

data "aws_rds_cluster" "aurora_postgres_cluster" {
  cluster_identifier = "emh-fleet-management-database-cluster"
}

data "aws_subnets" "private" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.vpc.id]
  }
  tags = {
    Tier = "private-fleet-management"
  }
}

data "aws_subnet" "subnets_private_cidr" {
  for_each = toset(data.aws_subnets.private.ids)
  id       = each.value
}

data "aws_subnets" "public" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.vpc.id]
  }
  tags = {
    Tier = "public-fleet-management"
  }
}

data "aws_ecs_cluster" "ecs_cluster" {
  cluster_name = "emh-fleet-management"
}

data "aws_iam_roles" "roles" {
  name_regex  = "AWSReservedSSO_ProductOwner_.*"
  path_prefix = "/aws-reserved/sso.amazonaws.com/"
}

data "aws_iam_role" "product_owner_role" {
  name = tolist(data.aws_iam_roles.roles.names)[0]
}

data "aws_iam_roles" "auditor_roles" {
  name_regex  = "AWSReservedSSO_Auditor_.*"
  path_prefix = "/aws-reserved/sso.amazonaws.com/"
}

data "aws_iam_role" "auditor_role" {
  name = tolist(data.aws_iam_roles.auditor_roles.names)[0]
}

data "aws_iam_role" "gitlab_role" {
  name = local.role_name
}

data "aws_iam_role" "ecs_task_role" {
  name = "${local.repo}-ecs-task-role"
}

data "aws_iam_policy_document" "product_owner_bucket_permissions" {
  statement {
    sid    = "AllowFullAccessToRootAndProductOwner"
    effect = "Allow"

    actions = [
      "s3:*"
    ]
    resources = [
      module.s3_bucket_damage_report.bucket_arn,
      "${module.s3_bucket_damage_report.bucket_arn}/*"
    ]
  }
}

data "aws_iam_policy_document" "ecs_task_permissions" {

  statement {
    sid    = "AllowIamLogin"
    effect = "Allow"

    actions = [
      "rds-db:connect",
    ]
    resources = [
      "arn:aws:rds-db:${data.aws_region.current.name}:${local.account_id}:dbuser:${data.aws_rds_cluster.aurora_postgres_cluster.cluster_resource_id}/${var.datasource_username}"
    ]
  }

  statement {
    sid    = "S3BucketAccess"
    effect = "Allow"

    actions = [
      "s3:DeleteObject",
      "s3:GetObject",
      "s3:PutObject",
      "s3:ListBucket"
    ]

    resources = [
      module.s3_bucket_damage_report.bucket_arn,
      "${module.s3_bucket_damage_report.bucket_arn}/*"
    ]
  }

  statement {
    sid    = "S3BucketAccessGobdLegalHold"
    effect = "Allow"
    actions = [
      "s3:GetObject",
      "s3:DeleteObject",
      "s3:PutObject",
      "s3:PutObjectLegalHold",
      "s3:GetObjectLegalHold",
      "s3:PutObjectRetention",
      "s3:GetObjectRetention",
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::${local.legal_hold_bucket_name}/*",
      "arn:aws:s3:::${local.gobd_bucket_name}/*"
    ]
  }

  statement {
    sid    = "S3BucketAccessGobdLegalHoldConf"
    effect = "Allow"
    actions = [
      "s3:ListBucket"
    ]
    resources = [
      "arn:aws:s3:::${local.legal_hold_bucket_name}",
      "arn:aws:s3:::${local.gobd_bucket_name}"

    ]
  }

  statement {
    sid    = "KMSKey"
    effect = "Allow"

    actions = [
      "kms:GenerateDataKey",
      "kms:Describe",
      "kms:Encrypt",
      "kms:Decrypt",
      "kms:ReEncrypt",
      "kms:DescribeCustomKeyStores",
      "kms:DescribeKey",
      "kms:GetKeyPolicy",
      "kms:GetKeyRotationStatus"
    ]
    resources = [module.kms.key_arn, data.aws_kms_key.ecs_cluster_insights_key.arn]
  }
}

data "aws_kms_key" "ecs_cluster_insights_key" {
  key_id = "alias/emh-fleet-management/ecs_cluster_container_insights_key"
}

data "aws_iam_role" "glue_role" {
  name = local.gobd_glue_role_name
}

data "aws_lambda_function" "postgres_secrets_rotation" {
  function_name = "postgres_secrets_rotation"
}