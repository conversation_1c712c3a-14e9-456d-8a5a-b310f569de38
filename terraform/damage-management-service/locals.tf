locals {
  repo                   = "damage-management-service"
  role_name              = "emh-${local.repo}-cicd"
  cluster_name           = data.aws_ecs_cluster.ecs_cluster.cluster_name
  account_id             = data.aws_caller_identity.current.account_id
  legal_hold_bucket_name = var.s3_bucket_legal_hold
  gobd_bucket_name       = var.s3_bucket_archive
  gobd_glue_role_name    = "${local.repo}-crawler-glue-role"

  params = {
    DATASOURCE_PASSWORD = {
      category    = "internal"
      component   = "database"
      param_name  = "datasource_password"
      description = "Database password"
      repo        = local.repo
      type        = "SecureString"
    }

    REPAIRFIX_API_KEY = {
      category    = "external"
      component   = "repairfix"
      param_name  = "api-key"
      description = "api key for authenticating against repairfix api"
      repo        = local.repo
      type        = "SecureString"
    }

    KAFKA_USER = {
      category    = "external"
      component   = "streamzilla"
      param_name  = "kafka_user"
      description = "Streamzilla User"
      repo        = local.repo
      type        = "SecureString"
    }

    KAFKA_PASSWORD = {
      category    = "external"
      component   = "streamzilla"
      param_name  = "kafka_password"
      description = "Streamzilla Password"
      repo        = local.repo
      type        = "SecureString"
    }

    KAFKA_REGISTRY_PASSWORD = {
      category    = "external"
      component   = "streamzilla"
      param_name  = "kafka_registry_password"
      description = "Streamzilla registry password to connect and fetch schemas"

      repo = local.repo
      type = "SecureString"
    }

    OAUTH2_CLIENT_SECRET_FOR_EMPLOYEE = {
      category    = "external"
      component   = "oauth2"
      param_name  = "oauth2_dms_client_secret_for_employee"
      description = "OAuth2 DamageManagementService Client Secret for Employee API"
      repo        = local.repo
      type        = "SecureString"
    }

    OAUTH2_CLIENT_SECRET_FOR_VEHICLES = {
      category    = "external"
      component   = "oauth2"
      param_name  = "oauth2_dms_client_secret_for_vehicles"
      description = "OAuth2 DamageManagementService Client Secret for Vehicles API"
      repo        = local.repo
      type        = "SecureString"
    }

    OAUTH2_CLIENT_SECRET_FOR_PACE = {
      category    = "external"
      component   = "oauth2"
      param_name  = "oauth2_dms_client_secret_for_pace"
      description = "OAuth2 DamageManagementService Client Secret for PACE InternalOrderNumber API"
      repo        = local.repo
      type        = "SecureString"
    }
  }

  alb = {
    security_groups = [
      {
        name = "${local.repo}-alb-sg"

        ingress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure inbound traffic within VPC"
            cidr_blocks = [for subnet in data.aws_subnet.subnets_private_cidr : subnet.cidr_block]
          }
        ]
        egress_rules = [
          {
            from_port   = 8080
            to_port     = 8080
            protocol    = "TCP"
            description = "Allow outbound traffic to Target Instances"
            cidr_blocks = [for subnet in data.aws_subnet.subnets_private_cidr : subnet.cidr_block]
          }
        ]
      }
    ]

    listeners = [
      {
        port         = 443
        protocol     = "HTTPS"
        target_group = "${local.repo}-alb-tg"
        ssl_policy   = "ELBSecurityPolicy-FS-1-2-Res-2020-10"
      }
    ]

    target_group = {
      name        = "${local.repo}-alb-tg"
      port        = 8080
      protocol    = "HTTP"
      target_type = "ip"

      health_check = {
        healthy_threshold   = 2
        unhealthy_threshold = 10
        protocol            = "HTTP"
        path                = "/api/damage/actuator/health"
        timeout             = 5
        interval            = 10
        matcher             = "200"
      }
    }
  }

  nlb = {
    security_groups = [
      {
        name = "${local.repo}-nlb-sg"

        ingress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure inbound with in our subnet"
            cidr_blocks = ["0.0.0.0/0"]
          }
        ]
        egress_rules = [
          {
            from_port       = 443
            to_port         = 443
            protocol        = "TCP"
            description     = "Allow outbound traffic to ALB"
            security_groups = module.alb.security_group_ids
          }
        ]
      }
    ]

    target_group = {
      name        = "${local.repo}-nlb-tg"
      port        = 443
      protocol    = "TCP"
      target_type = "alb"

      health_check = {
        healthy_threshold   = 2
        unhealthy_threshold = 5
        protocol            = "HTTPS"
        path                = "/api/damage/actuator/health"
        timeout             = 5
        interval            = 10
        matcher             = "200"
      }
    }

  }

  ecs = {
    security_groups = [
      {
        name = "${local.repo}-ecs-task-sg"

        ingress_rules = [
          {
            from_port   = 8080
            to_port     = 8080
            protocol    = "TCP"
            description = "Allow inbound traffic from VPC as we have ALB in public subnet and NLB is in private subnet"
            cidr_blocks = [data.aws_vpc.vpc.cidr_block]
          }
        ]

        egress_rules = [
          {
            from_port   = 443
            to_port     = 443
            protocol    = "TCP"
            description = "Allow secure outbound traffic"
            cidr_blocks = ["0.0.0.0/0"]
          },
          {
            from_port   = 4317
            to_port     = 4317
            protocol    = "TCP"
            description = "allow to sending data from agent for open telemetry"
            cidr_blocks = [data.aws_vpc.vpc.cidr_block]
          },
          {
            from_port       = 443
            to_port         = 443
            protocol        = "TCP"
            description     = "allows pulling docker images layers from s3"
            prefix_list_ids = ["pl-6da54004"]
          },
          {
            from_port   = 5432
            to_port     = 5432
            protocol    = "TCP"
            description = "Allow outbound traffic to database"
            cidr_blocks = [data.aws_vpc.vpc.cidr_block]
          },
          {
            from_port   = 9092
            to_port     = 9092
            protocol    = "TCP"
            description = "Allow outbound traffic to Streamzilla"
            cidr_blocks = ["0.0.0.0/0"]
          }
        ]
      }
    ]

    secrets = [
      for key, secret in module.ssm_parameters_ecs : {
        name = key
        arn  = secret.param_arn
      }
    ]

    environments = [
      {
        name  = "OTEL_EXPORTER_OTLP_ENDPOINT"
        value = "http://aws-distro-opentelemetry-service.${local.cluster_name}:4317"
      },
      {
        name  = "OTEL_RESOURCE_ATTRIBUTES"
        value = "service.namespace=${local.cluster_name},service.name=${local.repo},aws.log.group.names=/aws/ecs/${local.repo},aws.log.group.arns=arn:aws:logs:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:log-group:/aws/ecs/${local.repo}"
      },
      {
        name  = "OTEL_METRICS_EXPORTER"
        value = "none"
      },
      {
        name  = "OAUTH2_JWKS_URI"
        value = var.oauth2_jwks_uri
      },
      {
        name  = "KAFKA_BOOTSTRAP_SERVERS"
        value = var.kafka_bootstrap_server
      },
      {
        name  = "OAUTH2_DM_AUDIENCE"
        value = var.oauth2_dm_audience
      },
      {
        name  = "REPAIRFIX_BASE_URL"
        value = var.repairfix_base_url
      },
      {
        name  = "REPAIRFIX_FLEET_GROUP_DIENST_LEASING"
        value = var.repairfix_fleet_group_dienst_leasing
      },
      {
        name  = "REPAIRFIX_FLEET_GROUP_POOL"
        value = var.repairfix_fleet_group_pool
      },
      {
        name  = "REPAIRFIX_FLEET_GROUP_ROOT"
        value = var.repairfix_fleet_group_root
      },
      {
        name  = "STORAGE_VEHICLE_MIGRATION_BUCKET"
        value = module.s3_bucket_damage_report.bucket_name
      },
      {
        name  = "STORAGE_LEGAL_HOLD_BUCKET"
        value = local.legal_hold_bucket_name
      },
      {
        name  = "WIREMOCK_ENABLED"
        value = var.wiremock_enabled
      },
      {
        name  = "KMS_KEY_ID"
        value = module.kms.key_id
      },
      {
        name  = "MONETARY_BENEFIT_ACCOUNTABLE_TOPIC"
        value = var.monetary_benefit_accountable_topic
      },
      {
        name  = "MONETARY_BENEFIT_ACCOUNTABLE_CONSUMER_GROUP_ID"
        value = var.monetary_benefit_accountable_consumer_group_id
      },
      {
        name  = "VEHICLE_MIGRATION_SCHEDULER_CRON"
        value = var.vehicle_migration_scheduler_cron
      },
      {
        name  = "DAMAGE_FILE_MIGRATION_SCHEDULER_CRON"
        value = var.damage_file_migration_scheduler_cron
      },
      {
        name  = "OUTBOX_MESSAGE_RELAY_SCHEDULER_CRON"
        value = var.outbox_message_relay_scheduler_cron
      },
      {
        name  = "INTERNAL_ORDER_NUMBER_UPDATE_SCHEDULER_CRON"
        value = var.internal_order_number_update_scheduler_cron
      },
      {
        name  = "INTERNAL_ORDER_NUMBER_SYNC_SCHEDULER_CRON"
        value = var.internal_order_number_sync_scheduler_cron
      },
      {
        name  = "DATASOURCE_USERNAME"
        value = var.datasource_username
      },
      {
        name  = "DATASOURCE_URL"
        value = var.datasource_url
      },
      {
        name  = "DATASOURCE_DRIVER"
        value = var.datasource_driver
      },
      {
        name  = "DATASOURCE_DIALECT"
        value = var.datasource_dialect
      },
      {
        name  = "RDS_ENABLED"
        value = var.enabled_rds
      },
      {
        name  = "AWS_REGION"
        value = data.aws_region.current.name
      },
      {
        name  = "OAUTH2_CLIENT_ID_FOR_EMPLOYEE",
        value = var.oauth2_client_id_for_employee
      },
      {
        name  = "OAUTH2_CLIENT_ID_FOR_VEHICLES",
        value = var.oauth2_client_id_for_vehicles
      },
      {
        name  = "OAUTH2_CLIENT_ID_FOR_PACE",
        value = var.oauth2_client_id_for_pace
      },
      {
        name  = "OAUTH2_EMPLOYEE_SCOPE",
        value = var.oauth2_employee_scope
      },
      {
        name  = "OAUTH2_VEHICLES_SCOPE",
        value = var.oauth2_vehicle_scope
      },
      {
        name  = "OAUTH2_AZURE_TENANT_ID_FOR_EMPLOYEE",
        value = var.oauth2_azure_tenant_id_for_employee
      },
      {
        name  = "OAUTH2_AZURE_TENANT_ID_FOR_VEHICLES",
        value = var.oauth2_azure_tenant_id_for_vehicles
      },
      {
        name  = "OAUTH2_HOST_PACE",
        value = var.oauth2_host_pace
      },
      {
        name  = "EMPLOYEES_BASE_URL",
        value = var.employees_base_url
      },
      {
        name  = "VEHICLES_BASE_URL",
        value = var.vehicles_base_url
      },
      {
        name  = "PACE_BASE_URL",
        value = var.pace_base_url
      },
      {
        name  = "VEHICLE_RESPONSIBLE_PERSON_MIGRATION_USES_USER_SERVICE",
        value = var.use_user_service
      },
      {
        name  = "DAMAGE_FILE_DELETION_SCHEDULER_CRON",
        value = var.damage_file_gobd_deletion_scheduler_cron
      },
      {
        name  = "NUMBER_OF_DAYS_FOR_CLOSED",
        value = var.number_of_days_for_closed
      },
      {
        name  = "NUMBER_OF_DAYS_FOR_SCRAPED_OR_SOLD",
        value = var.number_of_days_for_scraped_or_sold
      },
      {
        name  = "STORAGE_ARCHIVE_BUCKET",
        value = local.gobd_bucket_name
      },
      {
        name  = "ARCHIVE_SCHEDULER_CRON",
        value = var.archive_scheduler_cron
      },
      {
        name  = "BACK_OFFICE_GO_LIVE_DATE",
        value = var.back_office_go_live_date
      },
      {
        name  = "DAMAGE_FILE_MIGRATION_THRESHOLD",
        value = var.damage_file_migration_threshold
      },
      {
        name  = "NUMBER_OF_DAMAGES_TOPIC",
        value = var.numer_of_damages_topic
      },
      {
        name  = "VEHICLE_MIGRATION_DATA_CONSUMER_GROUP_ID",
        value = var.vehicle_migration_data_consumer_group_id
      },
      {
        name  = "VEHICLE_MIGRATION_DATA_TOPIC",
        value = var.vehicle_migration_data_topic
      },
      {
        name  = "VEHICLE_MIGRATION_KAFKA_INTEGRATION_ENABLED",
        value = var.vehicle_migration_kafka_integration_enabled
      },
      {
        name  = "REPAIRFIX_RETRY_MAX_ATTEMPTS"
        value = var.repairfix_max_retry_attempts
      },
      {
        name  = "REPAIRFIX_RETRY_BACKOFF_DURATION_IN_MILLIS"
        value = var.repairfix_retry_backoff_duration
      },
      {
        name  = "REPAIRFIX_REQUEST_PAGE_SIZE_KEY"
        value = var.repairfix_request_page_size
      },
      {
        name  = "REPAIRFIX_REQUEST_PAGE_DELAY_SECONDS"
        value = var.repairfix_request_page_delay
      },
      {
        name  = "VEHICLE_MIGRATION_MESSAGE_RETRY_CRON"
        value = var.vehicle_migration_message_retry_cron
      }
    ]

    port_mapping = [
      {
        name          = local.repo
        containerPort = 8080
        hostPort      = 8080
      }
    ]

    load_balancer_config = [
      {
        target_group_arn = module.alb.target_group_arn
        container_name   = local.repo
        container_port   = 8080
      }
    ]

    container_image = "${local.account_id}.dkr.ecr.${data.aws_region.current.name}.amazonaws.com/${local.repo}:${var.commit_hash}"
  }

  dashboards = [

    {
      name = "Damage-File-Migration-From-Repairfix"
      source = jsonencode({
        "widgets" : [
          {
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /Created new damage file with key|Updated damage file with key|Closed damage file with key/\n| parse message \"Updated damage file with key*\" as updatedDamageFileId\n| parse message \"Created new damage file with key*\" as createdDamageFileId\n| parse message \"Closed damage file with key*\" as closedDamageFileId\n| stats count_distinct(updatedDamageFileId) as UpdatedDamageFiles, count_distinct(createdDamageFileId) as CreatedDamageFiles, count_distinct(closedDamageFileId) as ClosedDamageFiles by bin(1h)",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "Damage Files Migrated",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 12,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter level = \"ERROR\"\n| filter loggerName =\"com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.RepairfixDamageReportAdapter\"\n| parse message \"invoices for the damage report Id:*.\" as missingInvoiceReportId\n| parse message \"damage report details with Id:*.\" as missingDetailsReportId\n| parse message \"car with id [*]\" as carId\n| stats count_distinct(carId) as carsNotFound, count_distinct(missingDetailsReportId) as missingReportDetails, count_distinct(missingInvoiceReportId) as missingInvoices, missingInvoices+missingReportDetails+carsNotFound as total by bin(1h)",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "Errors during Damage File Migration",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 9,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields arguments.0 as Reason\n | filter loggerName = \"com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.RepairfixDamageReportAdapter\"\n | parse Reason \"damage report with id [*]\" as reportId\n | dedup Reason",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "Damage Files with Errors",
              "view" : "event_table"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 9,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp, throwable.cause.message as Reason\n | filter level = \"ERROR\"\n | filter loggerName =\"com.emh.damagemanagement.damagefilemigration.adapter.out.repairfix.RepairfixDamageReportAdapter\"\n | parse message \"invoices for the damage report Id:*.\" as `Damage Report with missing Invoices`\n | parse message \"damage report details with Id:*.\" as `Damage Report with missing details`\n | parse message \"car with id [*]\" as `Car Id that could not be found`\n | dedup `Damage Report with missing details`,`Damage Report with missing Invoices`",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "Damage File Migration Failed due to Technical Reasons",
              "view" : "event_table"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 18,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /Provided employee number/\n| parse message \"Provided employee number [*] does not match the one from matched employee [*]\" as reportedPNumber, matchedEmployeePNumber\n| parse message \" for damage file with id *\" as damageFileId\n| display reportedPNumber, matchedEmployeePNumber, damageFileId\n|dedup damageFileId",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "Mismatch PNumber",
              "view" : "event_table"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 18,
            "x" : 12,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /No employee found for the provided email/\n| parse message \"No employee found for the provided email  *\" as email\n| parse message \" for damage file with id *\" as damageFileId\n| display email, damageFileId\n|dedup damageFileId",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "No employee found for email",
              "view" : "event_table"
            }
          },

        ]
      })
    },
    {
      name = "Vehicle-Migration-From-FMS"
      source = jsonencode({
        "widgets" : [
          {
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 12,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /New vehicles in excel/\n| parse message \"New vehicles in excel: *, skipped due to missing vehicle responsible person: *, vehicles to be m: *, successful: *, failed: *\" as new, skipped, tobemigrated, success, failed\n| stats sum(new) as New_Vehicles,sum(skipped) as Skipped_Vehicles, sum(success) as Success, sum(failed) as Failed by bin(1h)",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "New Vehicles Migrated to Repair Fix",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /Total (pool|leasing) vehicles parsed from excel/\n| parse message \"Total * vehicles parsed from excel: *, already migrated: *, new: *\" as type, total, existing, new\n| stats sum(total) as Total_Vehicles_In_Excel,sum(new) as New_Vehicles, sum(existing) as Exists_In_RepairFix by bin(1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Vehicles Parsed From FMS Excel",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 9,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /Total unique vehicle responsible/\n| parse message \"Total unique vehicle responsible parsed from excel: *, already migrated: *, new: *\" as total, existing, new\n| stats sum(total) as Total_Vehicle_Responsible_In_Excel, sum(new) as New_Vehicle_Responsible, sum(existing) as Exists_In_Repair_Fix by bin(1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Vehicle Responsible Parsed from Excel",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 9,
            "x" : 12,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /New vehicle responsible person/\n| parse message \"New vehicle responsible person in excel to be migrated: *, successful: *,failed: *\" as new, success, failed\n| stats sum(new) as New_Vehicle_Responsible, sum(success) as Success, sum(failed) as Failed by bin(1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "New Vehicle Responsible Migrated To Repair Fix",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 18,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /Potential Vehicle responsible person/\n| parse message \"Potential Vehicle responsible person in excel to be updated: *, actual: *, successful: *,failed: *\" as potentialupdate, actualupdate, success, failed\n| stats sum(potentialupdate) as Exists_In_Repair_Fix, sum(actualupdate) as To_Update_In_RepairFix, sum(success) as Success, sum(failed) as Failed by bin(1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Updates of Vehicle Responsible in Repair Fix",
              "view" : "timeSeries"
            }
          }
        ]
      })
    },
    {
      name = "Vehicle-Migration-From-FVM"
      source = jsonencode({
        "widgets" : [
          {
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /Successfully (added|updated|deactivated) car with vin/\n| parse message \"Successfully added car with vin *\" as vin_added\n| parse message \"Successfully updated car with vin *\" as vin_updated\n| parse message \"Successfully deactivated car with vin *\" as vin_deactivated\n| stats count(vin_added) as `Vehicles added`, count(vin_updated) as `Vehicles updated`, count(vin_deactivated) as `Vehicles deactivated` by bin(1h)",
              "region" : "eu-west-1"
              "stacked" : false,
              "title" : "Vehicles Migrated to Repair Fix",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 0,
            "x" : 12,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n | filter message like /Error while trying to (add|update|deactivate) car with vin/\n | parse message \"Error while trying to added car with vin *\" as vin_added\n | parse message \"Error while trying to updated car with vin *\" as vin_updated\n | parse message \"Error while trying to deactivated car with vin *\" as vin_deactivated\n | stats count(vin_added) as `Errors adding vehicle`, count(vin_updated) as `Errors updating vehicle`, count(vin_deactivated) as `Errors deactivating vehicles` by bin(1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Errors Migrating Vehicles to Repairfix",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 9,
            "x" : 0,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n| filter message like /Successfully (added|updated) (user|fleet manager) with userPartnerId/\n| parse message \"Successfully added user with userPartnerId *\" as user_added\n| parse message \"Successfully added fleet manager with userPartnerId *\" as manager_added\n| parse message \"Successfully updated user with userPartnerId *\" as user_updated\n| parse message \"Successfully updated fleet manager with userPartnerId *\" as manager_updated\n| stats count(user_added) as `User added`, count(user_updated) as `User updated`, count(manager_added) as `Fleet Manager added`, count(manager_updated) as `Fleet Manager updated` by bin(1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Users Migrated to Repairfix",
              "view" : "timeSeries"
            }
          },
          {
            "height" : 9,
            "width" : 12,
            "y" : 9,
            "x" : 12,
            "type" : "log",
            "properties" : {
              "query" : "SOURCE '/aws/ecs/damage-management-service' | fields @timestamp\n | filter message like /Error while trying to (add|update) (user|fleet manager) with userPartnerId/\n | parse message \"Error while trying to add user with userPartnerId *\" as user_added\n | parse message \"Error while trying to add fleet manager with userPartnerId *\" as manager_added\n | parse message \"Error while trying to update user with userPartnerId *\" as user_updated\n | parse message \"Error while trying to update fleet manager with userPartnerId *\" as manager_updated\n | stats count(user_added) as `Errors adding user`, count(user_updated) as `Errors upating user`, count(manager_added) as `Errors adding Fleet Manager`, count(manager_updated) as `Errors updating Fleet Manager` by bin(1h)",
              "region" : "eu-west-1",
              "stacked" : false,
              "title" : "Errors Migrationg Users to Repairfix",
              "view" : "timeSeries"
            }
          },
        ]
      })
    }
  ]

  vehicle_migration_expiration_rule = [
    {
      id     = "ExpireObjectsInSuccess"
      prefix = "vehicle-migration/success/"
    },
    {
      id     = "ExpireObjectsInSuccessWithWarning"
      prefix = "vehicle-migration/successWithWarning/"
    },
    {
      id     = "ExpireObjectsInFailure"
      prefix = "vehicle-migration/failure/"
    }
  ]
}
