module "kms" {
  source         = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-kms?ref=1.1.0"
  key_alias_name = local.repo
  role_name      = local.role_name
  services       = ["logs.amazonaws.com"]
  role_arns = [
    data.aws_iam_role.product_owner_role.arn,
    data.aws_iam_role.ecs_task_role.arn,
    data.aws_iam_role.glue_role.arn,
    data.aws_iam_role.auditor_role.arn,
  ]
}

module "ssm_parameters_ecs" {
  for_each = local.params
  source   = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ssm-parameter?ref=1.0.0"

  category    = each.value.category
  component   = each.value.component
  description = each.value.description
  param_name  = each.value.param_name
  repo        = each.value.repo
  type        = each.value.type

  kms_key_arn = module.kms.key_arn
}

module "alb" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-alb?ref=1.0.0"

  fqdn                 = var.damage_management_domain
  dns_hosted_zone_name = var.dns_hosted_zone_name
  load_balancer_name   = "${local.repo}-alb"

  target_group    = local.alb.target_group
  listeners       = local.alb.listeners
  security_groups = local.alb.security_groups

  vpc_id  = data.aws_vpc.vpc.id
  subnets = data.aws_subnets.private.ids
}

module "ibm_api_gw_nlb" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-nlb?ref=1.0.0"

  alb_arn              = module.alb.alb_arn
  app                  = local.repo
  dns_hosted_zone_name = var.dns_hosted_zone_name
  private_dns_name     = var.damage_management_domain
  fqdn                 = var.damage_management_domain

  ibm_gateway_principal = "arn:aws:iam::599822918172:root"
  target_group          = local.nlb.target_group
  security_groups       = local.nlb.security_groups

  vpc_id  = data.aws_vpc.vpc.id
  subnets = data.aws_subnets.private.ids
}

module "s3_bucket_damage_report" {
  source      = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-s3?ref=1.3.0"
  bucket_name = var.s3_bucket_damage_report
  kms_key_arn = module.kms.key_arn
  content_policy_permission = [
    {
      sid        = "DenyAllArnExcept"
      effect     = "Deny"
      actions    = ["s3:*"],
      type       = "*"
      identifier = ["*"]
      condition = [{
        test     = "StringNotEquals"
        variable = "aws:PrincipalArn"
        values = [
          data.aws_iam_role.ecs_task_role.arn,
          data.aws_iam_role.product_owner_role.arn,
          data.aws_iam_role.gitlab_role.arn
        ]
      }]
    },
  ]
  configuration_policy_permission = [
    {
      sid        = "DenyAllArnExceptConf"
      effect     = "Deny"
      actions    = ["s3:*"],
      type       = "*"
      identifier = ["*"]
      condition = [{
        test     = "StringNotEquals"
        variable = "aws:PrincipalArn"
        values = [
          data.aws_iam_role.ecs_task_role.arn,
          data.aws_iam_role.product_owner_role.arn,
          data.aws_iam_role.gitlab_role.arn
        ]
      }]
    },
  ]
}

resource "aws_s3_bucket_lifecycle_configuration" "vehicle_migration_lifecycle_rule" {
  bucket = module.s3_bucket_damage_report.bucket_name
  dynamic "rule" {
    for_each = local.vehicle_migration_expiration_rule
    content {
      id     = rule.value.id
      status = "Enabled"
      filter {
        prefix = rule.value.prefix
      }
      expiration {
        days = var.retention_in_days_vehicle_migration
      }
    }
  }
  rule {
    # this rule deletes non-current object versions #days after they become non-current.
    id     = "PermanentDeleteInBucket"
    status = "Enabled"
    noncurrent_version_expiration {
      noncurrent_days = 1
    }
    # this rule removes the Delete Marker with no non-current versions
    expiration {
      expired_object_delete_marker = true
    }
    # this rule specifies the days since the initiation of an incomplete multipart upload that Amazon S3
    //will wait before permanently removing all parts of the upload
    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }
  }
}

module "s3_bucket_gobd" {
  source            = "../modules/gobd"
  ci_role_name      = local.role_name
  kms_key_arn       = module.kms.key_arn
  retention_in_days = var.retention_in_days
  s3_bucket_gobd    = local.gobd_bucket_name
  task_role_arn     = data.aws_iam_role.ecs_task_role.arn
  repo              = local.repo
  glue_role_name    = local.gobd_glue_role_name
  auditor_role_arn  = data.aws_iam_role.auditor_role.arn
}

module "s3_bucket_legalhold" {
  source              = "../modules/legalhold"
  ci_role_name        = local.role_name
  kms_key_arn         = module.kms.key_arn
  s3_bucket_legalhold = local.legal_hold_bucket_name
  task_role_arn       = data.aws_iam_role.ecs_task_role.arn
}

module "ecs_service" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ecs-service?ref=1.2.0"

  vpc_id      = data.aws_vpc.vpc.id
  subnets     = data.aws_subnets.private.ids
  repo        = local.repo
  role_name   = local.role_name
  cluster_arn = data.aws_ecs_cluster.ecs_cluster.arn

  container_cpu    = 1024
  container_memory = 2048
  container_image  = local.ecs.container_image

  desired_count       = var.container_desired_count
  healthcheck_command = "curl -f http://localhost:8080/api/damage/actuator/health || exit 1"

  env_variables = local.ecs.environments
  secrets       = local.ecs.secrets

  load_balancer_config = local.ecs.load_balancer_config
  security_groups      = local.ecs.security_groups
  port_mapping         = local.ecs.port_mapping

  firehose_cloudwatch_role_name = var.firehose_cloudwatch_role_name
  firehose_s3_role_name         = var.firehose_s3_role_name

  alarm_actions = "arn:aws:lambda:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:function:ms_teams_notifications"

  stream_logs_to_newrelic = var.stream_logs_to_newrelic
}

module "cloudwatch" {
  source     = "../modules/cloudwatch"
  dashboards = local.dashboards
}

module "gobd_z1_audit" {
  source                 = "../modules/audit"
  aws_subnets_ids        = data.aws_subnets.private.ids
  ci_role_name           = local.role_name
  database               = "damagemanagement"
  repo                   = local.repo
  vpc_id                 = data.aws_vpc.vpc.id
  account_id             = local.account_id
  s3_athena_spill_bucket = var.s3_athena_spill_bucket
}

module "postgres_secrets_kms" {
  source         = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-kms?ref=1.1.0"
  key_alias_name = "${local.repo}/athena_log_group"
  role_name      = local.role_name

  services = ["logs.amazonaws.com", "lambda.amazonaws.com"]
}


module "rds_damage_management_db_secret" {
  source      = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-secrets?ref=1.0.0"
  repo        = local.repo
  category    = "password"
  component   = "rds"
  secret_name = "damage_management_user_creds"
  description = "Database user to connect via RDS query console"
  kms_key_arn = module.postgres_secrets_kms.key_arn
  lambda_arn  = data.aws_lambda_function.postgres_secrets_rotation.arn
  tags = {
    Rotation = "postgres"
  }
}