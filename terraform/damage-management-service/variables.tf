variable "commit_hash" {
  type        = string
  description = "The commit hash of the code to deploy"
}

variable "damage_management_domain" {
  type        = string
  description = "Domain for the damage management service"
}

variable "dns_hosted_zone_name" {
  type        = string
  description = "DNS hosted zone for the damage management service"
}
variable "kafka_bootstrap_server" {
  type        = string
  description = "Base URL of the Kafka server"
}
variable "oauth2_jwks_uri" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "oauth2_dm_audience" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "repairfix_base_url" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "repairfix_fleet_group_dienst_leasing" {
  type        = string
  description = "The parentFleetIdentifier for the leasing vehicle super-fleet in repairfix"
}

variable "repairfix_fleet_group_pool" {
  type        = string
  description = "The parentFleetIdentifier for the pool vehicle super-fleet in repairfix"
}

variable "repairfix_fleet_group_root" {
  type        = string
  description = "The fleetIdentifier for the root fleet group in repairfix"
}

variable "wiremock_enabled" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "internal_order_number_sync_scheduler_cron" {
  type        = string
  description = "how often do we update internalOrderNumber in external system"
}

variable "internal_order_number_update_scheduler_cron" {
  type        = string
  description = "how often do we get internalOrderNumber for damageFiles"
}

variable "vehicle_migration_scheduler_cron" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "damage_file_migration_scheduler_cron" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "outbox_message_relay_scheduler_cron" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "s3_bucket_damage_report" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "container_desired_count" {
  type        = number
  description = "Number of containers to run inside the ECS service"
  default     = 1
}

variable "firehose_cloudwatch_role_name" {
  type        = string
  description = "Role name to send logs from cloudwatch to firehose data stream"
}

variable "firehose_s3_role_name" {
  type        = string
  description = "Role name to ship logs from firehose to compliance s3 bucket"
}

variable "datasource_username" {
  type        = string
  description = "Username of the database"
}

variable "datasource_dialect" {
  type        = string
  description = "dialect of the database"
}

variable "datasource_driver" {
  type        = string
  description = "driver of the database"
}

variable "datasource_dbname" {
  type        = string
  description = "Database name"
}

variable "datasource_url" {
  type        = string
  description = "Url of the database"
}

variable "monetary_benefit_accountable_topic" {
  type        = string
  description = "Kafka topic for monetary benefit accountable event"
}

variable "monetary_benefit_accountable_consumer_group_id" {
  type        = string
  description = "Kafka consumer group id for monetary benefit accountable test consumer"
}

variable "oauth2_client_id_for_employee" {
  type        = string
  description = "The OAuth2 DamageManagementService clientId used with employee API"
}

variable "oauth2_client_id_for_vehicles" {
  type        = string
  description = "The OAuth2 DamageManagementService clientId used with vehicles API"
}

variable "oauth2_client_id_for_pace" {
  type        = string
  description = "The OAuth2 DamageManagementService clientId used with PACE InternalOrderNumber API"
}

variable "oauth2_employee_scope" {
  type        = string
  description = "The required scope when using employees API"
}

variable "oauth2_vehicle_scope" {
  type        = string
  description = "The required scope when using vehicles API"
}

variable "oauth2_azure_tenant_id_for_employee" {
  type        = string
  description = "The azure tenantId used by the OAuth2 client during authentication with employee API"
}

variable "oauth2_azure_tenant_id_for_vehicles" {
  type        = string
  description = "The azure tenantId used by the OAuth2 client during authentication with vehicles API"
}
variable "oauth2_host_pace" {
  type        = string
  description = "The host used by the OAuth2 client during authentication with PACE API"
}

variable "employees_base_url" {
  type        = string
  description = "Base url (including /api) pointing to the service that is currently providing employees API"
}

variable "vehicles_base_url" {
  type        = string
  description = "Base url (including /api) pointing to the service that is currently providing vehicles API"
}

variable "pace_base_url" {
  type        = string
  description = "Base url pointing to the service that is currently providing PACE InternalOrderNumber API"
}

variable "use_user_service" {
  type        = string
  description = "Temporary flag to indicate that user-service should be queried when performing vehicle-migration"
}

variable "retention_in_days" {
  description = "number of days to retain"
  type        = number
}

variable "retention_in_days_vehicle_migration" {
  description = "number of days to retain objects in vehicle migration bucket"
  type        = number
}

variable "damage_file_gobd_deletion_scheduler_cron" {
  description = "sprint boot cron expression for starting the GoBD deletion process"
  type        = string
}

variable "number_of_days_for_closed" {
  description = "number of days before a damage was closed that enable deletion"
  type        = number
}

variable "number_of_days_for_scraped_or_sold" {
  description = "number of days before a car was scraped or sold that enable deletion"
  type        = number
}

variable "back_office_go_live_date" {
  description = "date when back office will be live"
  type        = string
}

variable "damage_file_migration_threshold" {
  description = "threshold for damage file migration in days"
  type        = number
}

variable "s3_bucket_archive" {
  type        = string
  description = "bucket name"
}

variable "s3_bucket_legal_hold" {
  type        = string
  description = "bucket name"
}

variable "archive_scheduler_cron" {
  type        = string
  description = "sprint boot cron expression for starting the archive job"
}

variable "s3_athena_spill_bucket" {
  type        = string
  description = "bucket name"
}

variable "enabled_rds" {
  type        = bool
  description = "indicator if rds should be connected"
}

variable "numer_of_damages_topic" {
  type        = string
  description = "number of damages topic"
}

variable "vehicle_migration_data_consumer_group_id" {
  type        = string
  description = "consumer group id to consume dms vehicle migration topic"
}
variable "vehicle_migration_data_topic" {
  type        = string
  description = "dms vehicle migration topic"
}
variable "vehicle_migration_kafka_integration_enabled" {
  type        = string
  description = "enable/disable dms vehicle migration topic consumption"
}
variable "repairfix_max_retry_attempts" {
  type        = number
  description = "max number of retry attempts on technical repairfix communication errors"
}

variable "repairfix_retry_backoff_duration" {
  type        = number
  description = "retry backoff duration in milliseconds"
}

variable "repairfix_request_page_size" {
  type        = number
  description = "page size for repairfix requests"
}


variable "repairfix_request_page_delay" {
  type        = number
  description = "request delay in between page request in seconds"
}
variable "vehicle_migration_message_retry_cron" {
  type        = string
  description = "scheduler cron to retry sending vehicle-migration message to repair fix"
}

variable "stream_logs_to_newrelic" {
  type        = bool
  description = "Flag to enable streaming logs to New Relic"
  default     = false
}




