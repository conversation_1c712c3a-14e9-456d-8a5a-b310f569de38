damage_management_domain             = "dm.mobilityservicesdev.aws.platform.porsche-preview.cloud"
dns_hosted_zone_name                 = "mobilityservicesdev.aws.platform.porsche-preview.cloud"
oauth2_jwks_uri                      = "https://login.microsoftonline.com/common/discovery/keys"
oauth2_dm_audience                   = "de2c475e-e393-425d-a89e-a1f394c90ae1"
repairfix_base_url                   = "http://localhost:8888"
repairfix_fleet_group_dienst_leasing = "porsche-dienst-leasing"
repairfix_fleet_group_pool           = "porsche-pool"
repairfix_fleet_group_root           = "porsche"
wiremock_enabled                     = "true"
repairfix_max_retry_attempts         = 3
repairfix_retry_backoff_duration     = 2000
repairfix_request_page_size          = 10
repairfix_request_page_delay         = 1
# picks up excel files from s3
vehicle_migration_scheduler_cron = "0 0 1 * * ?"
# send internal order number to repair fix
internal_order_number_sync_scheduler_cron = "0 0 1 * * ?"
# fetch internal order number from pace
internal_order_number_update_scheduler_cron = "0 0 1 * * ?"
# sync damages from repair fix to dms
damage_file_migration_scheduler_cron           = "0 0 1 * * ?"
outbox_message_relay_scheduler_cron            = "0 0 1 * * ?"
damage_file_gobd_deletion_scheduler_cron       = "0 0 1 * * ?"
archive_scheduler_cron                         = "0 0 1 * * ?"
s3_bucket_damage_report                        = "damage-management-service-938255451121"
s3_bucket_archive                              = "damage-management-service-archive-938255451121"
s3_bucket_legal_hold                           = "damage-management-service-legal-hold-938255451121"
s3_athena_spill_bucket                         = "damage-management-service-athena-spill-bucket-938255451121"
container_desired_count                        = 1
firehose_cloudwatch_role_name                  = "damage-management-service-firehose-cloudwatch-role"
firehose_s3_role_name                          = "damage-management-service-firehose-s3-role"
datasource_url                                 = "**********************************************************************************************************************************************"
datasource_driver                              = "org.postgresql.Driver"
datasource_dialect                             = "org.hibernate.dialect.PostgreSQLDialect"
datasource_username                            = "damage_management"
datasource_dbname                              = "/damagemanagement"
kafka_bootstrap_server                         = "pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092"
monetary_benefit_accountable_topic             = "FRA_emhs_dms_monetary_benefit_accountable_dev"
monetary_benefit_accountable_consumer_group_id = "FRA_emhs_dms_monetary_benefit_accountable.dev.consumer"
retention_in_days                              = 1
retention_in_days_vehicle_migration            = 1
number_of_days_for_closed                      = 3
number_of_days_for_scraped_or_sold             = 2
back_office_go_live_date                       = "2024-08-01"
damage_file_migration_threshold                = 2

/* EMPLOYEE API CLIENT CONFIG */
oauth2_client_id_for_employee       = "575b41a7-052a-4265-a6a3-4f8cf64257bf"          # prod DMS client (as user-services uses prod)
oauth2_employee_scope               = "1a6442c4-e25f-4459-9a7a-8e7a14386bae/.default" # dev user service scope
oauth2_azure_tenant_id_for_employee = "56564e0f-83d3-4b52-92e8-a6bb9ea36564"          # prod azure tenant
employees_base_url                  = "https://user.mobilityservicesdev.aws.platform.porsche-preview.cloud/api"
use_user_service                    = "true"

/* VEHICLES API CLIENT CONFIG */
oauth2_client_id_for_vehicles       = "da6d503a-6101-4d6d-9677-4e8b1c6a9870"          # qa DMS client (as vehicle-services uses qa)
oauth2_vehicle_scope                = "1d612e2b-317e-4d59-80f5-f80b3e3a33c0/.default" # qa vehicle-service appId/scope
oauth2_azure_tenant_id_for_vehicles = "07f8138c-e3e4-4b45-842b-9ecc3ba58cf4"          # qa azure tenant (as vehicle-services uses qa)
vehicles_base_url                   = "http://localhost:8888"

/* PACE INTERNAL ORDER NUMBER  API CLIENT CONFIG */
oauth2_client_id_for_pace = "sb-f39ece74-12bd-4f49-87c3-8eb9e20d37fe!b139374|it-rt-porsche-qas-spine-integration!b117912"
oauth2_host_pace          = "porsche-qas-spine-integration.authentication.eu10.hana.ondemand.com"
pace_base_url             = "https://porsche-qas-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com"

enabled_rds = true

numer_of_damages_topic                      = "FRA_emhs_dms_numer_of_damages_topic_dev"
vehicle_migration_data_consumer_group_id    = "FRA_emhs_dms_vehicle_migration_dev.consumer"
vehicle_migration_data_topic                = "FRA_emhs_dms_vehicle_migration_dev"
vehicle_migration_kafka_integration_enabled = true
vehicle_migration_message_retry_cron        = "0 0 0 1 1 ? 2099"
stream_logs_to_newrelic                     = true


