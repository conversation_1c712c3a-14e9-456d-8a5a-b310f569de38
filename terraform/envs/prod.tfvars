damage_management_domain             = "dm.mobilityservicesprod.aws.platform.porsche.cloud"
dns_hosted_zone_name                 = "mobilityservicesprod.aws.platform.porsche.cloud"
oauth2_jwks_uri                      = "https://login.microsoftonline.com/common/discovery/keys"
oauth2_dm_audience                   = "51b2b817-bd6b-4c1a-9067-aab2356b602d"
repairfix_base_url                   = "https://api.b2b.motum.eu"
repairfix_fleet_group_dienst_leasing = "porsche-dienst-leasing"
repairfix_fleet_group_pool           = "porsche-pool"
repairfix_fleet_group_root           = "porsche"
repairfix_max_retry_attempts         = 1
repairfix_retry_backoff_duration     = 5000
repairfix_request_page_size          = 10
repairfix_request_page_delay         = 10
wiremock_enabled                     = "false"
# picks up excel files from s3
vehicle_migration_scheduler_cron = "0 */20 * * * ?"
# send internal order number to repair fix
internal_order_number_sync_scheduler_cron = "0 4-59/5 * * * ?"
# fetch internal order number from pace
internal_order_number_update_scheduler_cron = "0 2-59/5 * * * ?"
# sync damages from repair fix to dms
damage_file_migration_scheduler_cron           = "0 0 0 1 1 ? 2099"
outbox_message_relay_scheduler_cron            = "0 0 0 1 1 ? 2099"
damage_file_gobd_deletion_scheduler_cron       = "0 0 0 1 1 ? 2099"
archive_scheduler_cron                         = "0 0 1 * * ?"
s3_bucket_damage_report                        = "damage-management-service-906037121137"
s3_bucket_archive                              = "damage-management-service-archive-906037121137"
s3_bucket_legal_hold                           = "damage-management-service-legal-hold-906037121137"
s3_athena_spill_bucket                         = "damage-management-service-athena-spill-bucket-906037121137"
container_desired_count                        = 2
firehose_cloudwatch_role_name                  = "damage-management-service-firehose-cloudwatch-role"
firehose_s3_role_name                          = "damage-management-service-firehose-s3-role"
datasource_url                                 = "**********************************************************************************************************************************************"
datasource_driver                              = "org.postgresql.Driver"
datasource_dialect                             = "org.hibernate.dialect.PostgreSQLDialect"
datasource_username                            = "damage_management"
datasource_dbname                              = "/damagemanagement"
kafka_bootstrap_server                         = "pkc-zxm13.eu-west-1.aws.confluent.cloud:9092"
monetary_benefit_accountable_topic             = "FRA_emhs_dms_monetary_benefit_accountable"
monetary_benefit_accountable_consumer_group_id = "FRA_emhs_dms_monetary_benefit_accountable.consumer"
retention_in_days                              = 5475
retention_in_days_vehicle_migration            = 30
number_of_days_for_closed                      = 5475
number_of_days_for_scraped_or_sold             = 3 * 356
back_office_go_live_date                       = "2024-10-01"
damage_file_migration_threshold                = 2

/* EMPLOYEE API CLIENT CONFIG */
oauth2_client_id_for_employee       = "bd818647-a548-403a-b7c1-188e056c5b38"          # prod DMS client
oauth2_employee_scope               = "a49eb9f7-36ef-4fdf-8105-378f64b55985/.default" # prod user service scope
oauth2_azure_tenant_id_for_employee = "56564e0f-83d3-4b52-92e8-a6bb9ea36564"          # prod azure tenant
employees_base_url                  = "https://user.mobilityservicesprod.aws.platform.porsche.cloud/api"
use_user_service                    = "false"

/* VEHICLES API CLIENT CONFIG */
oauth2_client_id_for_vehicles       = "1837dca9-6a44-4388-af24-1691fa24936b"          # prod DMS client
oauth2_vehicle_scope                = "43fcdeae-751c-49f9-8961-6be3ed238f85/.default" # prod vehicle-service appId/scope
oauth2_azure_tenant_id_for_vehicles = "56564e0f-83d3-4b52-92e8-a6bb9ea36564"          # prod azure tenant
vehicles_base_url                   = "https://vs.mobilityservices.aws.platform.porsche.cloud/api/vs"

/* PACE INTERNAL ORDER NUMBER  API CLIENT CONFIG */
oauth2_client_id_for_pace = "sb-bdaa5f00-b8a2-4beb-85e6-b0384243f7eb!b140663|it-rt-porsche-prd-spine-integration!b117912"
oauth2_host_pace          = "porsche-prd-spine-integration.authentication.eu10.hana.ondemand.com"
pace_base_url             = "https://porsche-prd-spine-integration.it-cpi018-rt.cfapps.eu10-003.hana.ondemand.com"

enabled_rds = true

numer_of_damages_topic                      = "FRA_emhs_dms_numer_of_damages_topic"
vehicle_migration_data_consumer_group_id    = "FRA_emhs_dms_vehicle_migration_prod.consumer"
vehicle_migration_data_topic                = "FRA_emhs_dms_vehicle_migration"
vehicle_migration_kafka_integration_enabled = false
vehicle_migration_message_retry_cron        = "0 0 0 1 1 ? 2099"
stream_logs_to_newrelic                     = true