terraform {
  required_version = "1.5.7"

  backend "s3" {
    encrypt        = true
    key            = "damage-management-service/iam.tfstate"
    dynamodb_table = "emh-tfstate-a-team-locks"
    region         = "eu-west-1"
  }

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "= 5.70.0"
    }
  }
}

provider "aws" {
  region = "eu-west-1"

  default_tags {
    tags = {
      Repo      = "damage-management-service"
      Repo_Path = "https://cicd.skyway.porsche.com/FP20/damage-management/damage-management-service.git"
      OwnedBy   = "a-team"
    }
  }
}
