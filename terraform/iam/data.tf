data "aws_caller_identity" "current" {}

data "aws_region" "current" {}

data "aws_iam_policy_document" "additional_permissions" {

  statement {
    sid    = "ECSRead"
    effect = "Allow"

    actions = ["ecs:DescribeClusters"]

    resources = [
      "arn:aws:ecs:${data.aws_region.current.name}:${local.account_id}:cluster/emh-fleet-management"
    ]
  }

  statement {
    sid    = "S3LockandLifecycleConfiguration"
    effect = "Allow"

    actions = [
      "s3:PutBucketLifecycleConfiguration",
      "s3:PutLifecycleConfiguration",
      "s3:GetLifecycleConfiguration",
      "s3:PutObjectLockConfiguration",
      "s3:GetObjectLockConfiguration",
      "s3:GetBucketObjectLockConfiguration",
      "s3:PutBucketObjectLockConfiguration",
      "s3:DeleteBucketLifecycle",
    ]
    //tag is not working
    resources = [
      "arn:aws:s3:::${var.s3_bucket_archive}",
      "arn:aws:s3:::${var.s3_bucket_legal_hold}",
      "arn:aws:s3:::${var.s3_bucket_damage_report}"
    ]
  }



  statement {
    sid    = "IAMPolicy"
    effect = "Allow"

    actions = [
      "iam:PutRolePolicy",
      "iam:GetRolePolicy",
      "iam:AttachRolePolicy",
      "iam:DeletePolicy",
      "iam:DeletePolicyVersion",
      "iam:DetachRolePolicy",
      "iam:DeleteRolePolicy",
    ]

    resources = [
      module.ecs_service_iam.ecs_task_role_arn,
      module.ecs_service_iam.ecs_task_execution_role_arn
    ]
  }

  statement {
    sid    = "IAMPolicySSOUsers"
    effect = "Allow"

    actions = [
      "iam:DeletePolicy",
      "iam:UntagPolicy"
    ]

    resources = ["arn:aws:iam::${local.account_id}:policy/custom-u-permission-set-productowner-1"]
  }

  statement {
    sid    = "IAMProductOwner"
    effect = "Allow"

    actions = ["iam:GetRole", "iam:ListRoles"]

    resources = [
      "arn:aws:iam::${local.account_id}:role/aws-reserved/sso.amazonaws.com/*"
    ]
  }

  statement {
    sid    = "DescribeClusterInsights"
    effect = "Allow"

    actions = ["kms:DescribeKey"]

    resources = [
      "arn:aws:kms:${data.aws_region.current.name}:${local.account_id}:key/*"
    ]
  }

  statement {
    sid    = "AmazonGetCloudWatchDashboard"
    effect = "Allow"
    actions = [
      "cloudwatch:ListMetrics",
      "cloudwatch:GetMetricData",
      "cloudwatch:DescribeAlarmsForMetric",
      "cloudwatch:ListMetricStreams",
      "cloudwatch:ListDashboards",
      "cloudwatch:ListServices"
    ]
    resources = ["*"]
  }

  statement {
    sid    = "AmazonCloudWatchDashboard"
    effect = "Allow"
    actions = [
      "cloudwatch:GetDashboard",
      "cloudwatch:PutDashboard",
      "cloudwatch:DeleteDashboards"
    ]
    resources = [
      "arn:aws:cloudwatch::${data.aws_caller_identity.current.account_id}:dashboard/*"
    ]
  }

  statement {
    sid    = "AmazonCloudWatchAlarm"
    effect = "Allow"
    actions = [
      "cloudwatch:DescribeAlarms",
      "cloudwatch:ListTagsForResource",
      "cloudwatch:TagResource",
      "cloudwatch:UntagResource",
    ]
    resources = [
      "arn:aws:cloudwatch:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:alarm/*"
    ]
  }

  statement {
    sid    = "AmazonCloudWatchMetricStreams"
    effect = "Allow"
    actions = [
      "cloudwatch:GetMetricStream",
    ]
    resources = [
      "arn:aws:cloudwatch:${data.aws_region.current.name}:${data.aws_caller_identity.current.account_id}:metric-stream/*"
    ]
  }

  statement {
    sid    = "RDSClusterDescribe"
    effect = "Allow"
    actions = [
      "rds:DescribeDBClusters",
    ]
    resources = ["*"]
  }

}
