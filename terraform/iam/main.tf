module "ecr_iam" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ecr//iam?ref=1.0.0"

  ecr_repo_name = local.repo
  repo          = local.repo
  role_name     = local.role_name
}

module "kms_iam" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-kms//iam?ref=1.1.0"

  repo      = local.repo
  role_name = local.role_name
}

module "glue_iam" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-glue-crawler//iam?ref=1.0.0"

  repo      = local.repo
  role_name = local.role_name
}


module "ssm_parameter_iam" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ssm-parameter//iam?ref=1.0.0"

  repo           = local.repo
  role_name      = local.role_name
  key_alias_name = local.repo
}

module "alb_iam" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-alb//iam?ref=1.0.0"

  repo      = local.repo
  role_name = local.role_name
}

module "s3_bucket_damage_report_iam" {
  source       = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-s3//iam?ref=1.3.0"
  bucket_names = [var.s3_bucket_damage_report, var.s3_bucket_archive, var.s3_bucket_legal_hold, var.s3_athena_spill_bucket]
  role_name    = local.role_name
}

module "ecs_service_iam" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-ecs-service//iam?ref=1.1.0"

  repo                          = local.repo
  role_name                     = local.role_name
  firehose_cloudwatch_role_name = var.firehose_cloudwatch_role_name
  firehose_s3_role_name         = var.firehose_s3_role_name
}

module "nlb_iam" {
  source = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-nlb//iam?ref=1.0.0"

  repo      = local.repo
  role_name = local.role_name
}


module "iam_secret" {
  source    = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-secrets//iam?ref=1.0.0"
  repo      = local.repo
  role_name = local.role_name
}

module "lambda_iam" {
  source    = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-lambda//iam?ref=1.1.0"
  role_name = local.role_name
  repo      = local.repo
}

module "iam_athena" {
  source              = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-athena-postgres-connector//iam?ref=1.0.0"
  repo                = local.repo
  role_name           = local.role_name
  athena_spill_bucket = "${local.repo}-athena-spill-bucket-${local.account_id}"
}


resource "aws_iam_policy" "add_permissions" {
  name   = "iam-general-policies"
  policy = data.aws_iam_policy_document.additional_permissions.json
}

resource "aws_iam_policy_attachment" "add_permissions" {
  name       = "${local.repo}-iam"
  policy_arn = aws_iam_policy.add_permissions.arn
  roles      = [local.role_name]
}
