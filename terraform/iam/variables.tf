variable "s3_bucket_damage_report" {
  type = string
  #TODO: Please add description where this variable is used
  description = ""
}

variable "firehose_cloudwatch_role_name" {
  type        = string
  description = "Role name to send logs from cloudwatch to firehose data stream"
}

variable "firehose_s3_role_name" {
  type        = string
  description = "Role name to ship logs from firehose to compliance s3 bucket"
}

variable "s3_bucket_archive" {
  type        = string
  description = "bucket name"
}

variable "s3_bucket_legal_hold" {
  type        = string
  description = "bucket name"
}

variable "s3_athena_spill_bucket" {
  type        = string
  description = "bucket name"
}

