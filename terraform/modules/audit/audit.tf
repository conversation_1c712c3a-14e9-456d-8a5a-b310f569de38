
module "postgres_secrets_kms" {
  source         = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-kms?ref=1.1.0"
  key_alias_name = "${var.repo}/athena"
  services       = ["logs.amazonaws.com", "lambda.amazonaws.com"]
  role_name      = var.ci_role_name
}

module "db_secret" {
  source      = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-secrets?ref=1.0.0"
  repo        = var.repo
  category    = "password"
  component   = "athena-rds"
  secret_name = "athena_${var.database}_user"
  description = "${var.repo} database read only user for athena postgres connector"
  kms_key_arn = module.postgres_secrets_kms.key_arn
  lambda_arn  = data.aws_lambda_function.lambda_athena_connector.arn
  tags = {
    Rotation = "postgres"
  }
}

module "athena_connector" {

  source        = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-athena-postgres-connector?ref=1.0.0"
  repo          = var.repo
  ci_role_name  = var.ci_role_name
  function_name = "${var.repo}-emh-athena-postgres-connector"
  kms_key_arn   = module.postgres_secrets_kms.key_arn

  postgres = {
    host        = data.aws_rds_cluster.aurora_postgres_cluster.endpoint
    port        = data.aws_rds_cluster.aurora_postgres_cluster.port
    database    = var.database
    secret_name = module.db_secret.secret_name
  }

  vpc_id              = var.vpc_id
  subnet_ids          = var.aws_subnets_ids
  security_groups     = local.security_groups
  athena_spill_bucket = local.spill_bucket
}