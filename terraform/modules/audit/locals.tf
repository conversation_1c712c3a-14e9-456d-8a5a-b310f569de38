locals {
  spill_bucket = var.s3_athena_spill_bucket
  security_groups = [
    {
      name = "${var.repo}-athena-sg"

      egress_rules = [
        {
          from_port   = 443
          to_port     = 443
          protocol    = "TCP"
          description = "Allow outbound traffic to Target Instances"
          cidr_blocks = ["0.0.0.0/0"]
        },
        {
          from_port       = 5432
          to_port         = 5432
          protocol        = "TCP"
          description     = "Allow outbound traffic to Target Instances"
          security_groups = [data.aws_security_group.aurora_postgres.id]
        }
      ]
    }
  ]
}
