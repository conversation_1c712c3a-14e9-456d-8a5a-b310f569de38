variable "ci_role_name" {
  description = "ci role name"
  type        = string
}

variable "repo" {
  description = "repo name"
  type        = string
}

variable "database" {
  description = "the database name to connect "
  type        = string
}

variable "aws_subnets_ids" {
  description = "the database subnets"
  type        = list(string)
}
variable "vpc_id" {
  description = "the database vpc"
  type        = string
}

variable "account_id" {
  type = string
}

variable "s3_athena_spill_bucket" {
  type        = string
  description = "bucket name"
}
