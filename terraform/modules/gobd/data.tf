data "aws_iam_roles" "roles" {
  name_regex  = "AWSReservedSSO_Administrator_.*"
  path_prefix = "/aws-reserved/sso.amazonaws.com/"
}

data "aws_iam_role" "administrator" {
  name = tolist(data.aws_iam_roles.roles.names)[0]
}

data "aws_iam_role" "gitlab_role" {
  name = var.ci_role_name
}

data "aws_caller_identity" "current" {}

data "aws_iam_policy_document" "s3_glue_policy" {

  dynamic "statement" {
    for_each = [module.s3_bucket_gobd.bucket_name]
    content {
      sid    = "s3Bucket${replace(statement.value, "-", "")}"
      effect = "Allow"
      actions = [
        "s3:GetObject",
        "s3:ListBucket",

      ]
      resources = ["arn:aws:s3:::${statement.value}/*", "arn:aws:s3:::${statement.value}"]
    }
  }
}


data "aws_iam_role" "glue_role" {
  name = var.glue_role_name
}