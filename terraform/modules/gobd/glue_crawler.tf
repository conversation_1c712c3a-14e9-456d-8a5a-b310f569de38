module "archive_bucket_crawler" {
  source          = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-glue-crawler?ref=1.0.0"
  crawler_buckets = [module.s3_bucket_gobd.bucket_name]
  repo            = var.repo
  database_name   = "${var.repo}-archive"
  kms_key         = var.kms_key_arn
  policy_permission = [
    {
      sid    = "allowGet"
      effect = "Allow"
      actions = [
        "glue:GetClassifier",
        "glue:GetCrawler",
        "glue:GetDatabase",
        "glue:GetTable",
        "glue:GetTableVersion"
      ]
      not_actions = []
      type        = "AWS"
      identifier  = [data.aws_caller_identity.current.account_id]
      condition   = []
    },
  ]
}


resource "aws_iam_role_policy" "add_permissions_glue_role" {
  name   = "${var.repo}-glue-bucket-permissions"
  policy = data.aws_iam_policy_document.s3_glue_policy.json
  role   = var.glue_role_name
}