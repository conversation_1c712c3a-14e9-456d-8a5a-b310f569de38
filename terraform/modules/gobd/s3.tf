module "s3_bucket_gobd" {
  source      = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-s3?ref=1.3.0"
  bucket_name = var.s3_bucket_gobd
  kms_key_arn = var.kms_key_arn
  configuration_policy_permission = [
    {
      sid        = "DenyAllArnExceptConfig"
      effect     = "Deny"
      actions    = ["s3:*"],
      type       = "*"
      identifier = ["*"]
      condition = [{
        test     = "StringNotEquals"
        variable = "aws:PrincipalArn"
        values = [data.aws_iam_role.administrator.arn, data.aws_iam_role.gitlab_role.arn, var.task_role_arn,
        data.aws_iam_role.glue_role.arn, var.auditor_role_arn]
      }]
    },
    {
      sid    = "DenyAllActionExceptConf"
      effect = "Deny"
      not_actions = [
        "s3:ListBucket",
      ]
      type       = "AWS"
      identifier = [var.task_role_arn, data.aws_iam_role.glue_role.arn]
      condition  = []
    },
  ]

  content_policy_permission = [
    {
      sid    = "DenyAllActionExcept"
      effect = "Deny"
      not_actions = [
        "s3:GetObject",
        "s3:PutObject",
        "s3:PutObjectRetention",
        "s3:GetObjectRetention",
        "s3:ListObject",
      ]
      type = "AWS"
      identifier = [data.aws_iam_role.administrator.arn, var.task_role_arn,
      ]
      condition = []
    },
    {
      sid    = "DenyAllActionExceptForCrawler"
      effect = "Deny"
      not_actions = [
        "s3:GetObject",
        "s3:ListObject",
      ]
      type       = "AWS"
      identifier = [data.aws_iam_role.glue_role.arn]
      condition  = []
    },
    {
      sid        = "DenyAllArnExcept"
      effect     = "Deny"
      actions    = ["s3:*"],
      type       = "*"
      identifier = ["*"]
      condition = [{
        test     = "StringNotEquals"
        variable = "aws:PrincipalArn"
        values = [data.aws_iam_role.administrator.arn,
          var.task_role_arn,
          data.aws_iam_role.glue_role.arn,
          var.auditor_role_arn
        ]
      }]
    },
  ]

}

resource "aws_s3_bucket_lifecycle_configuration" "years_config" {
  bucket = module.s3_bucket_gobd.bucket_name
  rule {
    id     = "TransitionAndDeletion"
    status = "Enabled"
    transition {
      storage_class = "GLACIER" # moves current object to glacier at midnight after upload
    }
    expiration {
      days = var.retention_in_days # Puts a Delete Marker on current version after #days.
    }
    noncurrent_version_expiration {
      noncurrent_days = 1 # Deletes non-current object versions  #days after they become non-current.
      # ideally we should not have any second version
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }
  }
  rule {
    # this rule should delete the Delete Marker version
    id     = "PermanentDelete"
    status = "Enabled"
    expiration {
      expired_object_delete_marker = true
    }
  }
}

resource "aws_s3_bucket_object_lock_configuration" "governance_lock" {
  bucket = module.s3_bucket_gobd.bucket_name

  rule {
    default_retention {
      mode = "GOVERNANCE"
      days = var.retention_in_days
    }
  }
}
