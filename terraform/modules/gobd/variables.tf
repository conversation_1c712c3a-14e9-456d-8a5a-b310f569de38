variable "s3_bucket_gobd" {
  description = "bucket name"
  type        = string

}
variable "kms_key_arn" {
  description = "kms to encrypt the bucket"
  type        = string
}

variable "ci_role_name" {
  description = "ci role name"
  type        = string
}

variable "glue_role_name" {
  description = "glue role name"
  type        = string
}


variable "retention_in_days" {
  description = "number of days to retain"
  type        = number
}

variable "task_role_arn" {
  description = "ecs task role arn"
  type        = string
}

variable "repo" {
  description = "repo name"
  type        = string
}

variable "auditor_role_arn" {
  description = "arn of the auditor role"
  type        = string
}