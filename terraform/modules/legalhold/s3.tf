module "s3_bucket_legalhold" {
  source      = "git::ssh://***************************/FP20/infrastructure/terraform-modules/emh-aws-s3?ref=1.3.0"
  bucket_name = var.s3_bucket_legalhold
  kms_key_arn = var.kms_key_arn
  configuration_policy_permission = [
    {
      sid        = "DenyAllArnExceptConfig"
      effect     = "Deny"
      actions    = ["s3:*"],
      type       = "*"
      identifier = ["*"]
      condition = [
        {
          test     = "StringNotEquals"
          variable = "aws:PrincipalArn"
          values   = [data.aws_iam_role.administrator.arn, data.aws_iam_role.gitlab_role.arn, var.task_role_arn]
        }
      ]
    },
    {
      sid         = "DenyAllActionExceptConf"
      effect      = "Deny"
      not_actions = ["s3:ListBucket"],
      type        = "AWS"
      identifier  = [var.task_role_arn]
      condition   = []
    },
  ]

  content_policy_permission = [
    {
      sid = "DenyAllActionExcept"
      #      actions = []
      effect = "Deny"
      not_actions = [
        "s3:ListBucket",
        "s3:GetObject",
        "s3:DeleteObject",
        "s3:PutObject",
        "s3:PutObjectLegalHold",
        "s3:GetObjectLegalHold",
        "s3:PutObjectRetention",
        "s3:GetObjectRetention",
        "s3:ListBucket"
      ]
      type       = "AWS"
      identifier = [var.task_role_arn]
      condition  = []
    },
    {
      sid        = "DenyAllArnExcept"
      effect     = "Deny"
      actions    = ["s3:*"],
      type       = "*"
      identifier = ["*"]
      condition = [
        {
          test     = "StringNotEquals"
          variable = "aws:PrincipalArn"
          values   = [data.aws_iam_role.administrator.arn, var.task_role_arn]
        }
      ]
    },
  ]
}

resource "aws_s3_bucket_object_lock_configuration" "enable_object_lock" {
  bucket = module.s3_bucket_legalhold.bucket_name
}

resource "aws_s3_bucket_lifecycle_configuration" "delete_unlock" {
  bucket = module.s3_bucket_legalhold.bucket_name
  rule {
    id     = "ExpireNonCurrent"
    status = "Enabled"
    noncurrent_version_expiration {
      noncurrent_days = 1 # Deletes non-current object versions  #days after they become non-current.
      # ideally we should not have any second version
    }
    abort_incomplete_multipart_upload {
      days_after_initiation = 1
    }
  }
  rule {
    # this rule should delete the Delete Marker version
    id     = "PermanentDelete"
    status = "Enabled"
    expiration {
      expired_object_delete_marker = true
    }
  }
}