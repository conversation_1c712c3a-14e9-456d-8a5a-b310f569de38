:toc:
:numbered:
= Vehicle-Migration

== Module description

A simple adapter module to connect to `vehicles API` currently provided by vehicle-service.

The module implements the `VehicleOutPort` from the `dammage-file-gobd` module, as this is currently the only usage for vehicles API.

We expect, that once vehicle-service is able to provide us with proper vehicle data, as well as the connection of vehicles to their respective vehicle responsible person, we will either implement another port here or split the client move adapter logic into their respective business-modules, which will cause this module to become a `vehicle-client` module.
