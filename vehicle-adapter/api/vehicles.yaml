openapi: 3.0.0
info:
  version: 1.5.0
  title: vehicle-service
  x-ibm-name: vehicle-service
servers:
  - url: /
x-ibm-configuration:
  properties:
    target-url:
      value: https://vs.mobilityservicesdev.aws.platform.porsche-preview.cloud/api/vs
      description: URL of the proxy policy
      encoded: false
  catalogs:
    prod:
      properties:
        target-url: https://vs.mobilityservices.aws.platform.porsche.cloud/api/vs
    dev:
      properties:
        target-url: https://vs.mobilityservicesdev.aws.platform.porsche-preview.cloud/api/vs
    test:
      properties:
        target-url: https://vs.mobilityservicesstaging.aws.platform.porsche-preview.cloud/api/vs
  gateway: datapower-api-gateway
  type: rest
  phase: realized
  enforced: true
  testable: true
  assembly:
    execute:
      - porsche-auth:
          version: 2.0.0
          title: porsche-auth
          internal_only: 'false'
          type: m2m
          cidr: ''
          audience: ''
      - porsche-token:
          version: 2.0.0
          title: porsche-token
          type: custom-authorization-header
          audience: ''
          destination: ''
          custom_authorization_header_name: Authorization-FleetVehicle
      - invoke:
          title: invoke
          version: 2.0.0
          verb: keep
          target-url: $(target-url)$(api.operation.path)
          persistent-connection: true
          header-control:
            type: blocklist
            values: []
          parameter-control:
            type: blocklist
            values: []
          description: ''
          keep-payload: true
          backend-type: json
          follow-redirects: false
          cache-response: no-cache
  cors:
    enabled: true
  activity-log:
    enabled: true
    success-content: activity
    error-content: payload
paths:
  "/vehicles/vguid":
    post:
      tags:
        - vehicle-read-controller
      operationId: byVguids
      requestBody:
        content:
          application/json:
            schema:
              type: array
              items:
                type: string
        required: true
      responses:
        '200':
          description: OK
          content:
            "*/*":
              schema:
                type: array
                items:
                  "$ref": "#/components/schemas/VehicleFullRepresentation"
  "/vehicles/vin/{vin}":
    get:
      tags:
        - vehicle-read-controller
      operationId: byVinNumber
      parameters:
        - name: vin
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            "*/*":
              schema:
                "$ref": "#/components/schemas/VehicleFullRepresentation"
  "/vehicles/vguid/{vguid}":
    get:
      tags:
        - vehicle-read-controller
      operationId: byVguid
      parameters:
        - name: vguid
          in: path
          required: true
          schema:
            type: string
      responses:
        '200':
          description: OK
          content:
            "*/*":
              schema:
                "$ref": "#/components/schemas/VehicleFullRepresentation"
  "/vehicles/albheaders":
    get:
      tags:
        - alb-header-exposing-controller
      operationId: getAllHeaders
      responses:
        '200':
          description: OK
          content:
            "*/*":
              schema:
                type: object
                additionalProperties:
                  type: string
components:
  securitySchemes:
    clientID:
      type: apiKey
      in: header
      name: X-Porsche-Client-Id
      x-key-type: client_id
    clientSecret:
      type: apiKey
      in: header
      x-key-type: client_secret
      name: X-Porsche-Client-Secret
    AzureADIdp:
      type: oauth2
      x-ibm-oauth-provider: azure-ad-qa
      flows:
        clientCredentials:
          tokenUrl: >-
            https://login.microsoftonline.com/56564e0f-83d3-4b52-92e8-a6bb9ea36564/oauth2/v2.0/token
          scopes:
            email: email
            openid: openid
            profile: profile
            offline_access: offline_access
  schemas:
    JsonNode:
      type: object
    VehicleFullRepresentation:
      required:
        - modelInfo
        - orderInfo
        - registrationInfo
        - vguid
      type: object
      properties:
        vin:
          type: string
        vguid:
          type: string
        modelInfo:
          "$ref": "#/components/schemas/VehicleModelInfo"
        orderInfo:
          "$ref": "#/components/schemas/VehicleOrderInfo"
        registrationInfo:
          "$ref": "#/components/schemas/VehicleRegistrationInfo"
        vehicleScrappedOrSoldDate:
          type: string
          format: date-time
        options:
          "$ref": "#/components/schemas/JsonNode"
    VehicleModelInfo:
      type: object
      properties:
        modelDescription:
          type: string
        driveType:
          type: string
    VehicleOrderInfo:
      type: object
      properties:
        department:
          type: string
        leasingType:
          type: string
    VehicleRegistrationInfo:
      type: object
      properties:
        licensePlate:
          type: string
  responses:
    4xxError:
      description: Client Error
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
    5xxError:
      description: Internal server error
      content:
        application/json:
          schema:
            type: object
            properties:
              message:
                type: string
                description: Error message indicating an internal server error
security:
  - clientID: []
    clientSecret: []
    AzureADIdp:
      - openid
