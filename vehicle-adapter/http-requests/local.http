### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
&client_secret={{oauth_m2m_api_client_secret}}
&grant_type=client_credentials
&scope={{api_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Get health check
GET /api/damage/actuator/health
Host: localhost:8081
accept: application/json
Content-Type: application/json

### Get vehcile status
GET /api/vehicles/i-am-a-vin
Authorization: Bearer {{auth_token}}
Host: localhost:8081
accept: application/json
Content-Type: application/json

### get vehicle-service token ( wirenock)
POST /oauth2/v2.0/token
Host: localhost:8888
accept: application/json
Content-Type: application/json