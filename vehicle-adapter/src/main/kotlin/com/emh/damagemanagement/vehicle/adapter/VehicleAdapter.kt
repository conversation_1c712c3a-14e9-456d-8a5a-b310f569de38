/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehicle.adapter

import com.emh.damagemanagement.damagefile.gobd.application.port.VehicleScrappedOrSold
import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPort
import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPortException
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.VehicleReadControllerApi
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.model.VehicleFullRepresentationDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClientException
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

@Component
class VehicleAdapter(private val vehiclesApi: VehicleReadControllerApi) : VehiclesOutPort {
    override fun getVehicleScrappedOrSoldStatus(vins: Collection<String>): List<VehicleScrappedOrSold> {
        return Flux.fromIterable(vins.chunked(DEFAULT_CHUNK_SIZE))
            .flatMap(::getVehiclesChunked)
            .flatMapIterable { vehicles -> vehicles }
            .filter { !it.vin.isNullOrBlank() }
            .map(VehicleFullRepresentationDto::toVehicleScrappedOrSold)
            .collectList()
            .block()
            ?.toList() ?: emptyList()
    }

    private fun getVehiclesChunked(vins: Collection<String>): Mono<List<VehicleFullRepresentationDto>> {
        return Flux.fromIterable(vins)
            .concatMap { vin ->
                vehiclesApi.byVinNumber(vin).onErrorResume { error ->
                    when (error) {
                        is WebClientResponseException.NotFound -> {
                            log.debug("Vehicle with VIN $vin not found, skipping.")
                            Mono.empty()
                        }

                        is WebClientException -> {
                            log.warn("Error while trying to load vehicle with VIN $vin.", error)
                            Mono.error(VehiclesOutPortException(error.message, error))
                        }

                        else -> {
                            log.warn("Unexpected error while trying to load vehicle with VIN $vin.", error)
                            Mono.error(error)
                        }
                    }
                }
            }
            .collectList()
    }

    companion object {
        private const val DEFAULT_CHUNK_SIZE = 100
        private val log = LoggerFactory.getLogger(VehicleAdapter::class.java)
    }
}
