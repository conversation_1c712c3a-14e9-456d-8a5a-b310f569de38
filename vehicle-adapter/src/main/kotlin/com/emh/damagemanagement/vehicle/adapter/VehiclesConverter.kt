/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehicle.adapter

import com.emh.damagemanagement.damagefile.gobd.application.port.VehicleScrappedOrSold
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.model.VehicleFullRepresentationDto

fun VehicleFullRepresentationDto.toVehicleScrappedOrSold(): VehicleScrappedOrSold =
    VehicleScrappedOrSold(vin = this.vin!!, scrappedOrSoldDate = this.vehicleScrappedOrSoldDate)
