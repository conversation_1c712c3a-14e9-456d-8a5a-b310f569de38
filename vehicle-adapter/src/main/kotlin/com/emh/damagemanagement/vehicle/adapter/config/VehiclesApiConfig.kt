/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehicle.adapter.config

import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.VehicleReadControllerApi
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class VehiclesApiConfig {
    @Bean fun vehiclesApi(@Qualifier("vehicles") webClient: WebClient) = VehicleReadControllerApi(webClient)
}
