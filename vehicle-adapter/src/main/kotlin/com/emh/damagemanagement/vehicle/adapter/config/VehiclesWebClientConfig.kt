/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehicle.adapter.config

import com.emh.damagemanagement.vehicle.adapter.VehiclesWebClientExceptionHandler
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.codec.ClientCodecConfigurer
import org.springframework.http.codec.json.Jackson2JsonDecoder
import org.springframework.http.codec.json.Jackson2JsonEncoder
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.security.oauth2.client.AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ClientCredentialsReactiveOAuth2AuthorizedClientProvider
import org.springframework.security.oauth2.client.InMemoryReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.security.oauth2.client.web.server.AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository
import org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient

@Configuration
class VehiclesWebClientConfig {

    @Bean
    @Qualifier("vehicles")
    fun vehiclesExchangeStrategies(jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder): ExchangeStrategies =
        ExchangeStrategies.builder()
            .codecs { clientDefaultCodecsConfigurer: ClientCodecConfigurer ->
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonEncoder(
                        Jackson2JsonEncoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonDecoder(
                        Jackson2JsonDecoder(jackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON)
                    )
            }
            .build()

    @Bean
    @Qualifier("vehicles")
    fun reactiveVehiclesClientRegistrationRepository(
        clientRegistrationRepository: ClientRegistrationRepository
    ): ReactiveClientRegistrationRepository {
        return InMemoryReactiveClientRegistrationRepository(
            clientRegistrationRepository.findByRegistrationId(CLIENT_REGISTRATION_ID)
        )
    }

    @Bean
    @Qualifier("vehicles")
    fun reactiveVehiclesAuthorizedClientService(
        @Qualifier("vehicles") clientRegistrationRepository: ReactiveClientRegistrationRepository
    ): ReactiveOAuth2AuthorizedClientService {
        return InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrationRepository)
    }

    @Bean
    @Qualifier("vehicles")
    fun reactiveVehiclesAuthorizedClientRepository(
        @Qualifier("vehicles") authorizedClientService: ReactiveOAuth2AuthorizedClientService
    ): ServerOAuth2AuthorizedClientRepository {
        return AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository(authorizedClientService)
    }

    @Bean
    @Qualifier("vehicles")
    fun reactiveVehiclesAuthorizedClientManager(
        @Qualifier("vehicles") reactiveRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("vehicles") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ReactiveOAuth2AuthorizedClientManager {
        return AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(
                reactiveRegistrationRepository,
                authorizedClientService,
            )
            .apply { setAuthorizedClientProvider(ClientCredentialsReactiveOAuth2AuthorizedClientProvider()) }
    }

    @Bean
    @Qualifier("vehicles")
    fun vehiclesWebClient(
        @Qualifier("vehicles") reactiveOAuth2AuthorizedClientManager: ReactiveOAuth2AuthorizedClientManager,
        vehiclesWebClientProperties: VehiclesWebClientProperties,
        @Qualifier("vehicles") exchangeStrategies: ExchangeStrategies,
        vehiclesWebClientExceptionHandler: VehiclesWebClientExceptionHandler,
    ): WebClient {
        val oauth2Client = ServerOAuth2AuthorizedClientExchangeFilterFunction(reactiveOAuth2AuthorizedClientManager)
        // ExchangeFilterFunction already has an apply function, so we do it the imperative way
        oauth2Client.setDefaultClientRegistrationId(CLIENT_REGISTRATION_ID)
        return WebClient.builder()
            .baseUrl(vehiclesWebClientProperties.baseUrl)
            .exchangeStrategies(exchangeStrategies)
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .filter(oauth2Client)
            .filter(vehiclesWebClientExceptionHandler::clientErrorRequestProcessor)
            .build()
    }

    companion object {
        const val CLIENT_REGISTRATION_ID = "vehicles"
    }
}

@ConfigurationProperties("vehicles") data class VehiclesWebClientProperties(val baseUrl: String)
