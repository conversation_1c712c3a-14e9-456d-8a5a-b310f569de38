/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehicle

import com.emh.damagemanagement.shared.createRandomString
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.model.VehicleFullRepresentationDto
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.model.VehicleModelInfoDto
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.model.VehicleOrderInfoDto
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.model.VehicleRegistrationInfoDto
import java.time.OffsetDateTime
import kotlin.random.Random

class VehicleFullRepresentationDtoBuilder {

    private var scrappedOrSoldDate: OffsetDateTime? = if (Random.nextBoolean()) OffsetDateTime.now() else null
    private var vin: String = createRandomString(Random.nextInt(4, 23))

    fun vin(vin: String) = apply { this.vin = vin }

    fun scrappedOrSold(scrappedOrSoldDate: OffsetDateTime) = apply { this.scrappedOrSoldDate = scrappedOrSoldDate }

    fun inUse() = apply { this.scrappedOrSoldDate = null }

    fun build(): VehicleFullRepresentationDto {
        return VehicleFullRepresentationDto(
            vin = createRandomString(Random.nextInt(4, 23)),
            vguid = createRandomString(Random.nextInt(4, 23)),
            modelInfo = VehicleModelInfoDto(),
            orderInfo = VehicleOrderInfoDto(),
            registrationInfo = VehicleRegistrationInfoDto(),
            vehicleScrappedOrSoldDate = scrappedOrSoldDate,
            options = null,
        )
    }

    companion object {

        fun buildSingle(): VehicleFullRepresentationDto = VehicleFullRepresentationDtoBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleFullRepresentationDto> =
            (1..count).map { VehicleFullRepresentationDtoBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
