/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehicle.adapter

import com.emh.damagemanagement.damagefile.gobd.application.port.VehicleScrappedOrSold
import com.emh.damagemanagement.vehicle.VehicleFullRepresentationDtoBuilder
import com.emh.damagemanagement.vehicle.generated.adapter.out.rest.model.VehicleFullRepresentationDto
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class VehicleAdapterConverterTest {

    @ParameterizedTest
    @MethodSource("vehicleFullRepresentationDtoProvider")
    fun `should convert VehicleFullRepresentationDto to VehicleScrappedOrSold`(
        vehicleFullRepresentationDto: VehicleFullRepresentationDto
    ) {
        val vehicleScrappedOrSold = vehicleFullRepresentationDto.toVehicleScrappedOrSold()

        assertThat(vehicleScrappedOrSold)
            .satisfies(vehicleFullRepresentationDtoRequirements(vehicleFullRepresentationDto))
    }

    companion object {

        private fun vehicleFullRepresentationDtoRequirements(
            vehicleFullRepresentationDto: VehicleFullRepresentationDto
        ) =
            Consumer<VehicleScrappedOrSold> { vehicleScrappedOrSold: VehicleScrappedOrSold ->
                assertThat(vehicleScrappedOrSold.vin).isEqualTo(vehicleFullRepresentationDto.vin)
                assertThat(vehicleScrappedOrSold.scrappedOrSoldDate)
                    .isEqualTo(vehicleFullRepresentationDto.vehicleScrappedOrSoldDate)
            }

        @JvmStatic
        fun vehicleFullRepresentationDtoProvider(): Stream<Arguments> =
            VehicleFullRepresentationDtoBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
