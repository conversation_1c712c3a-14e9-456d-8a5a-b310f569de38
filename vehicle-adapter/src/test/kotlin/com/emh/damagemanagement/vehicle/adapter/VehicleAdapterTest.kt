/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehicle.adapter

import com.emh.damagemanagement.damagefile.gobd.application.port.VehiclesOutPortException
import com.emh.damagemanagement.vehicle.IntegrationTest
import com.emh.damagemanagement.vehicle.VehicleFullRepresentationDtoBuilder
import com.fasterxml.jackson.databind.ObjectMapper
import java.io.BufferedReader
import java.time.OffsetDateTime
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.core.io.ClassPathResource
import org.springframework.security.oauth2.client.ClientAuthorizationException
import org.springframework.test.annotation.DirtiesContext
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@TestPropertySource(properties = ["vehicles.base-url=http://localhost:8091"])
class VehicleAdapterTest {

    @Autowired private lateinit var vehicleAdapter: VehicleAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    private val tokenResponse =
        ClassPathResource("token_response.json").inputStream.bufferedReader().use(BufferedReader::readText)

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8091)
    }

    private val validTokenResponse =
        MockResponse.Builder().body(tokenResponse).addHeader("Content-Type", "application/json").code(200).build()

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    @Test
    @DirtiesContext
    fun `should request oauth token and set jwt in authorization header`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        else ->
                            MockResponse.Builder()
                                .body(
                                    objectMapper.writeValueAsString(
                                        VehicleFullRepresentationDtoBuilder().inUse().build()
                                    )
                                )
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                    }
                }
            }

        assertThatNoException().isThrownBy {
            vehicleAdapter.getVehicleScrappedOrSoldStatus(setOf("vins"))
            vehicleAdapter.getVehicleScrappedOrSoldStatus(setOf("vins"))
        }
        val tokenRequest: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val vehicleRequest: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val vehicleRequest2: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        val jwt = objectMapper.readTree(tokenResponse)["access_token"].textValue()
        assertThat(tokenRequest.url.toString()).contains("oauth/token")
        assertThat(vehicleRequest.headers["Authorization"]).isEqualTo("Bearer $jwt")
        assertThat(vehicleRequest2.headers["Authorization"]).isEqualTo("Bearer $jwt")
        assertThat(vehicleRequest2.url.toString()).contains("/vehicles/vin")
        // should be true, as we assume that token get properly cached
        assertThat(mockWebServer.requestCount).isEqualTo(3)
    }

    @Test
    @DirtiesContext
    fun `should throw ClientAuthorizationException when token could not be obtained`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder().code(401).build()
                }
            }

        assertThatExceptionOfType(ClientAuthorizationException::class.java).isThrownBy {
            vehicleAdapter.getVehicleScrappedOrSoldStatus(setOf("vins"))
        }
    }

    @Test
    @DirtiesContext
    fun `should return vehicleScrappedOrSold for vins`() {
        val scrappedOrSoldVehicles =
            (0..2).map { VehicleFullRepresentationDtoBuilder().scrappedOrSold(OffsetDateTime.now()).build() }
        val vehiclesInUse = (0..1).map { VehicleFullRepresentationDtoBuilder().inUse().build() }
        val allVehicles = scrappedOrSoldVehicles + vehiclesInUse
        var requestCount = 0

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        else -> {
                            val vehicle = allVehicles[requestCount % allVehicles.size]
                            requestCount++
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(vehicle))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        }
                    }
                }
            }

        val vehicleScrappedOrSoldFromAdapter =
            vehicleAdapter.getVehicleScrappedOrSoldStatus(setOf("vin1", "vin2", "vin3", "vin4", "vin5"))
        assertThat(vehicleScrappedOrSoldFromAdapter)
            .extracting("vin")
            .containsExactlyInAnyOrderElementsOf(allVehicles.map { it.vin })
        assertThat(vehicleScrappedOrSoldFromAdapter.filter { it.isScrappedOrSold }).allSatisfy {
            assertThat(scrappedOrSoldVehicles.map { vehicle -> vehicle.vin }).contains(it.vin)
            assertThat(it.scrappedOrSoldDate).isNotNull()
        }
    }

    @Test
    @DirtiesContext
    fun `should throw  VehiclesOutPortException in case of 5xx errror`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        else -> MockResponse.Builder().addHeader("Content-Type", "application/json").code(500).build()
                    }
                }
            }

        assertThatExceptionOfType(VehiclesOutPortException::class.java).isThrownBy {
            vehicleAdapter.getVehicleScrappedOrSoldStatus(setOf("some invalid vin"))
        }
    }

    @Test
    @DirtiesContext
    fun `should skip vehicles that are not found (404) and continue processing`() {
        val existingVehicle = VehicleFullRepresentationDtoBuilder().inUse().build()
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("oauth/token") -> validTokenResponse
                        request.url.toString().contains("vin2") -> {
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(existingVehicle))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        }
                        else -> MockResponse.Builder().code(404).build()
                    }
                }
            }

        val result = vehicleAdapter.getVehicleScrappedOrSoldStatus(setOf("vin1", "vin2", "vin3"))

        assertThat(result).hasSize(1)
        assertThat(result[0].vin).isEqualTo(existingVehicle.vin)
    }
}
