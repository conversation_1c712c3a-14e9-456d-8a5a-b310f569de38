import com.diffplug.spotless.LineEnding
import org.jetbrains.kotlin.gradle.tasks.KotlinCompile
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    api(libs.api.fuhrpark)

    implementation(project(":shared"))
    implementation(project(":vehicle-migration"))
    implementation(project(":vehicle-migration-message-retry"))

    implementation(libs.bundles.base)
    implementation(libs.spring.kafka)

    testImplementation(libs.test.spring.kafka)
    testImplementation(files(sharedTestSrcSetsOutput))
    testImplementation(libs.bundles.tests)
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

val vehicleMigrationGeneratedSourcesPath = "src/generated/"

sourceSets {
    main { kotlin.srcDir("$vehicleMigrationGeneratedSourcesPath/kotlin") }
    test { kotlin { kotlin.srcDir(sharedTestSrcSetsOutput) } }
    test { resources { resources.srcDir(project(":shared").sourceSets.test.get().resources) } }
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }

tasks.withType<KotlinCompile> { dependsOn("setupGeneratedSources") }

val apiFolder: String by rootProject.extra
tasks.register<GenerateTask>("generateVehicleMigrationDataDtos") {
    outputs.upToDateWhen { false }
    val sourceGeneratedFolder = "$projectDir/generated"
    generatorName.set("kotlin-spring")
    inputSpec.set("$projectDir/api/dms-vehicle-migration.yaml")
    outputDir.set(sourceGeneratedFolder)
    apiPackage.set("com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka")
    modelPackage.set("com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model")
    modelNameSuffix.set("Event")
    validateSpec.set(true)
    templateDir.set(
        "${project(":shared").projectDir.absolutePath}/src/main/resources/generator-templates",
    )
    additionalProperties.set(
        mapOf(
            "serviceInterface" to true,
            "serviceImplementation" to false,
            "exceptionHandler" to false,
            "useBeanValidation" to false,
            "serializationLibrary" to "jackson",
            "annotationLibrary" to "none",
            "documentationProvider" to "none",
            "useSwaggerUI" to false,
        ),
    )
    dependsOn("unpackApi")
}

tasks.register<Copy>("setupGeneratedSources") {
    doFirst { delete(vehicleMigrationGeneratedSourcesPath) }
    from("generated/src/main") {
        exclude("*/org")
        exclude("resources")
        exclude("**/ApiUtil*")
    }
    into(vehicleMigrationGeneratedSourcesPath)
    includeEmptyDirs = false
    dependsOn("generateVehicleMigrationDataDtos")
    doLast { delete("generated/") }
}
