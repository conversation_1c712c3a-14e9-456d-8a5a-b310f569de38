/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigrationdata

import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Configuration

@Configuration
@ConditionalOnProperty(
    "vehicle-migration-data.vehicle-migration-kafka-integration-enabled",
    havingValue = "true",
    matchIfMissing = false,
)
class VehicleMigrationDataAdapterConfiguration
