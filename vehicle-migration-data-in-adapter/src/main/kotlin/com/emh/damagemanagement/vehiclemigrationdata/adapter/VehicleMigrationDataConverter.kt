/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigrationdata.adapter

import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.FleetInformation
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.LicensePlate
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry
import com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model.VehicleMigrationEvent
import com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model.VehicleMigrationVehicleEvent

fun VehicleMigrationEvent.toVehicleMigrationData(): VehicleMigrationData {
    val fleetInformation = this.vehicle.fleetInformation.toFleetInformation()
    val leasingArt = LeasingArt.of(vehicle.leasingArt)
    val fleet =
        Fleet.determineFleet(
            fleetInformation = fleetInformation,
            firstName = this.vehicleResponsiblePerson?.firstName,
            lastName = this.vehicleResponsiblePerson?.lastName,
            employeeNumber = this.vehicleResponsiblePerson?.employeeNumber,
            leasingArt = leasingArt,
        )
    return VehicleMigrationData(
        vehicle =
            Vehicle(
                vin = this.vehicle.vin,
                vGuid = this.vehicle.vguid,
                model = this.vehicle.model,
                depreciationRelevantCostCenter = this.vehicle.depreciationRelevantCostCenter,
                usingCostCenter = this.vehicle.usingCostCenter,
                isActive = this.vehicle.isActive,
                firstRegistrationDate = this.vehicle.firstRegistrationDate,
                licensePlate = this.vehicle.licensePlate?.let { LicensePlate(it) },
                responsiblePerson = this.vehicleResponsiblePerson?.employeeNumber?.let { EmployeeNumber(it) },
                manufacturer = this.vehicle.manufacturer,
                fleet = fleet,
                leasingArt = leasingArt,
            ),
        fleetInformation = fleetInformation,
        vehicleResponsiblePerson =
            this.vehicleResponsiblePerson?.let { vehicleResponsiblePerson ->
                VehicleResponsiblePerson(
                    employeeNumber = EmployeeNumber(vehicleResponsiblePerson.employeeNumber),
                    firstName = vehicleResponsiblePerson.firstName,
                    lastName = vehicleResponsiblePerson.lastName,
                    email = vehicleResponsiblePerson.email,
                    // if you are responsible for a pool vehicle, you get to be a fleet manager
                    isFleetManager = leasingArt.isPool,
                )
            },
    )
}

fun VehicleMigrationVehicleEvent.FleetInformation.toFleetInformation() =
    when (this) {
        VehicleMigrationVehicleEvent.FleetInformation.PRE_USAGE -> FleetInformation.PRE_USAGE
        VehicleMigrationVehicleEvent.FleetInformation.POST_USAGE -> FleetInformation.POST_USAGE
        VehicleMigrationVehicleEvent.FleetInformation.USAGE -> FleetInformation.USAGE
        VehicleMigrationVehicleEvent.FleetInformation.NONE -> FleetInformation.NONE
    }

fun VehicleMigrationData.toVehicleMigrationMessageRetry(): VehicleMigrationMessageRetry =
    VehicleMigrationMessageRetry(vin = this.vehicle.vin, payload = this.toJsonNode())
