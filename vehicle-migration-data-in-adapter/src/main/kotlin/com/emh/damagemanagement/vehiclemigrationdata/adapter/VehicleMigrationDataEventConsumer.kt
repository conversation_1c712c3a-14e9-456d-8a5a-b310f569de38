/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigrationdata.adapter

import com.emh.damagemanagement.vehiclemigration.application.port.MigrateVehicleDataUseCase
import com.emh.damagemanagement.vehiclemigration.messageretry.application.port.CreateVehicleMigrationMessageRetryUseCase
import com.emh.damagemanagement.vehiclemigrationdata.VehicleMigrationDataAdapterConfiguration
import com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model.VehicleMigrationEvent
import com.fasterxml.jackson.databind.ObjectMapper
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(VehicleMigrationDataAdapterConfiguration::class)
class VehicleMigrationDataEventConsumer(
    private val objectMapper: ObjectMapper,
    private val migrateVehicleDataUseCase: MigrateVehicleDataUseCase,
    private val createVehicleMigrationMessageRetry: CreateVehicleMigrationMessageRetryUseCase,
) {
    @KafkaListener(
        topics = ["\${vehicle-migration-data.kafka.listener.topic}"],
        groupId = "\${vehicle-migration-data.kafka.listener.group-id}",
    )
    fun listen(message: String) {
        try {
            val vehicleMigrationEvent = parseMessage(message)
            logger.info("Received DMS Vehicle Migration message from Kafka ${vehicleMigrationEvent.vehicle.vin}")
            val vehicleMigrationData = vehicleMigrationEvent.toVehicleMigrationData()
            val migrationResult = migrateVehicleDataUseCase.migrateVehicleData(vehicleMigrationData)
            if (migrationResult.canRetry) {
                createVehicleMigrationMessageRetry.create(vehicleMigrationData.toVehicleMigrationMessageRetry())
            }
        } catch (e: Exception) {
            logger.error("Failed to process message $message", e)
        }
    }

    private fun parseMessage(message: String): VehicleMigrationEvent =
        objectMapper.readValue(message, VehicleMigrationEvent::class.java)

    companion object {
        private val logger = LoggerFactory.getLogger(VehicleMigrationDataEventConsumer::class.java)
    }
}
