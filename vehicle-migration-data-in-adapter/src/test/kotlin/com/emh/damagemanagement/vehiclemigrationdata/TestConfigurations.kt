package com.emh.damagemanagement.vehiclemigrationdata

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.autoconfigure.domain.EntityScan
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication

@SpringBootApplication(
    scanBasePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.vehiclemigrationdata*",
            "com.emh.damagemanagement.vehiclemigration*",
            "com.emh.damagemanagement.repairfix.*",
            "com.emh.damagemanagement.vehiclemigration.messageretry.*",
        ]
)
@ConfigurationPropertiesScan(
    basePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.vehiclemigrationdata*",
            "com.emh.damagemanagement.vehiclemigration*",
            "com.emh.damagemanagement.repairfix.*",
            "com.emh.damagemanagement.vehiclemigration.messageretry.*",
        ]
)
@EntityScan("com.emh.damagemanagement.vehiclemigration.messageretry.*")
class TestVehicleMigrationDataAdapterApplication {
    fun main(args: Array<String>) {
        runApplication<TestVehicleMigrationDataAdapterApplication>(*args)
    }
}
