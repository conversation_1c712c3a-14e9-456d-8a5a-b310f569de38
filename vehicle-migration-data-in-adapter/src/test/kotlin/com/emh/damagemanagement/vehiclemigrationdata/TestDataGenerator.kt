/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigrationdata

import com.emh.damagemanagement.shared.createRandomOffsetDate
import com.emh.damagemanagement.shared.createRandomString
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.FleetInformation
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.LicensePlate
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.vehiclemigration.domain.toFleet
import com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model.VehicleMigrationEvent
import com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model.VehicleMigrationVehicleEvent
import com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model.VehicleMigrationVehicleResponsiblePersonEvent
import java.time.OffsetDateTime
import kotlin.random.Random
import net.datafaker.Faker

class VehicleMigrationEventBuilder {
    val faker = Faker()

    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var vguid: String = createRandomString(Random.nextInt(4, 23))
    private var model: String = createRandomString(Random.nextInt(4, 23))
    private var depreciationRelevantCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var usingCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var vehicleUsage: String = createRandomString(Random.nextInt(4, 23))
    private var isActive: Boolean = Random.nextBoolean()
    private var firstRegistrationDate: OffsetDateTime = createRandomOffsetDate()
    private var licensePlate: String = faker.vehicle().licensePlate()
    private var fleetInformation = VehicleMigrationVehicleEvent.FleetInformation.entries.random()
    private var employeeNumber = faker.numerify("00######")
    private var firstName: String = faker.name().firstName()
    private var lastName: String = faker.name().lastName()
    private var email: String = faker.internet().emailAddress()
    private var vehicleType: String = createRandomString(Random.nextInt(4, 23))
    private var manufacturer: String = createRandomString(Random.nextInt(4, 23))
    private var leasingArt: String = LeasingArt.entries.random().id

    fun build(): VehicleMigrationEvent {
        return VehicleMigrationEvent(
            vehicle =
                VehicleMigrationVehicleEvent(
                    vin = this.vin,
                    vguid = this.vguid,
                    model = this.model,
                    depreciationRelevantCostCenter = this.depreciationRelevantCostCenter,
                    usingCostCenter = this.usingCostCenter,
                    vehicleUsage = this.vehicleUsage,
                    isActive = this.isActive,
                    fleetInformation = this.fleetInformation,
                    firstRegistrationDate = this.firstRegistrationDate,
                    licensePlate = this.licensePlate,
                    vehicleType = this.vehicleType,
                    manufacturer = this.manufacturer,
                    leasingArt = this.leasingArt,
                ),
            vehicleResponsiblePerson =
                VehicleMigrationVehicleResponsiblePersonEvent(
                    employeeNumber = this.employeeNumber,
                    firstName = this.firstName,
                    lastName = this.lastName,
                    email = this.email,
                ),
        )
    }

    companion object {
        fun buildSingle() = VehicleMigrationEventBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleMigrationEvent> =
            (1..count).map { VehicleMigrationEventBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class VehicleMigrationDataBuilder {
    private val faker = Faker()
    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var vguid: String = createRandomString(Random.nextInt(4, 23))
    private var model: String = createRandomString(Random.nextInt(4, 23))
    private var depreciationRelevantCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var usingCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var isActive: Boolean = Random.nextBoolean()
    private var firstRegistrationDate: OffsetDateTime = createRandomOffsetDate()
    private var licensePlate: LicensePlate = LicensePlate(faker.vehicle().licensePlate())
    private var responsiblePerson: EmployeeNumber = EmployeeNumber(createRandomString(8))
    private var leasingArt: LeasingArt = LeasingArt.entries.random()
    private var manufacturer: String = createRandomString(23)

    private var fleetInformation = FleetInformation.entries.random()

    private var employeeNumber = EmployeeNumber(faker.numerify("00######"))
    private var firstName = faker.name().firstName()
    private var lastName = faker.name().lastName()
    private var email = faker.internet().emailAddress()

    private var vehicleResponsiblePerson: VehicleResponsiblePerson? =
        VehicleResponsiblePerson(
            employeeNumber = this.employeeNumber,
            firstName = this.firstName,
            lastName = this.lastName,
            email = this.email,
            isFleetManager = leasingArt.isPool,
        )

    private fun buildVehicle() =
        Vehicle(
            vin = this.vin,
            vGuid = this.vguid,
            model = this.model,
            depreciationRelevantCostCenter = this.depreciationRelevantCostCenter,
            usingCostCenter = this.usingCostCenter,
            isActive = this.isActive,
            firstRegistrationDate = this.firstRegistrationDate,
            licensePlate = this.licensePlate,
            responsiblePerson = this.responsiblePerson,
            manufacturer = this.manufacturer,
            leasingArt = leasingArt,
            fleet =
                vehicleResponsiblePerson?.let {
                    Fleet.determineFleet(
                        fleetInformation = fleetInformation,
                        firstName = it.firstName,
                        lastName = it.lastName,
                        leasingArt = leasingArt,
                        employeeNumber = it.employeeNumber.value,
                    )
                } ?: leasingArt.toFleet(),
        )

    private var vehicle = buildVehicle()

    fun fleetInformation(fleetInformation: FleetInformation) = apply {
        this.fleetInformation = fleetInformation
        this.vehicle = buildVehicle()
    }

    fun leasingArt(leasingArt: LeasingArt) = apply {
        this.leasingArt = leasingArt
        this.vehicle = buildVehicle()
    }

    fun vehicle(vehicle: Vehicle) = apply { this.vehicle = vehicle }

    fun vehicleResponsiblePerson(vehicleResponsiblePerson: VehicleResponsiblePerson?) = apply {
        this.vehicleResponsiblePerson = vehicleResponsiblePerson
        this.vehicle = buildVehicle()
    }

    fun build(): VehicleMigrationData {
        return VehicleMigrationData(
            vehicle = vehicle,
            fleetInformation = this.fleetInformation,
            vehicleResponsiblePerson = this.vehicleResponsiblePerson,
        )
    }

    companion object {
        fun buildSingle() = VehicleMigrationDataBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleMigrationData> =
            (1..count).map { VehicleMigrationDataBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
