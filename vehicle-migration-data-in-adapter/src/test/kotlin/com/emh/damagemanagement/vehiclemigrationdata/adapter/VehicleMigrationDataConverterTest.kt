/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigrationdata.adapter

import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigrationdata.VehicleMigrationDataBuilder
import com.emh.damagemanagement.vehiclemigrationdata.VehicleMigrationEventBuilder
import com.emh.damagemanagement.vehiclemigrationdata.generated.adapter.kafka.model.VehicleMigrationEvent
import java.util.function.Consumer
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class VehicleMigrationDataConverterTest {

    @ParameterizedTest
    @MethodSource("vehicleMigrationEventProvider")
    fun `should convert vehicleMigrationEvent to vehicleMigrationData`(vehicleMigrationEvent: VehicleMigrationEvent) {
        val vehicleMigrationData = vehicleMigrationEvent.toVehicleMigrationData()
        assertThat(vehicleMigrationData).satisfies(vehicleMigrationDataRequirements(vehicleMigrationEvent))
    }

    @Test
    fun `should convert vehicleMigrationData to VehicleMigrationMessageRetry`() {
        val vehicleMigrationData = VehicleMigrationDataBuilder.buildSingle()
        val vehicleMigrationMessageRetry = vehicleMigrationData.toVehicleMigrationMessageRetry()
        val vehicleResponsiblePerson = vehicleMigrationData.vehicleResponsiblePerson!!
        val expectedPayload =
            mapOf(
                    "vehicle" to
                        mapOf(
                            "vin" to vehicleMigrationData.vehicle.vin,
                            "model" to vehicleMigrationData.vehicle.model,
                            "depreciationRelevantCostCenter" to
                                vehicleMigrationData.vehicle.depreciationRelevantCostCenter,
                            "usingCostCenter" to vehicleMigrationData.vehicle.usingCostCenter,
                            "firstRegistrationDate" to vehicleMigrationData.vehicle.firstRegistrationDate,
                            "licensePlate" to vehicleMigrationData.vehicle.licensePlate,
                            "manufacturer" to vehicleMigrationData.vehicle.manufacturer,
                            "fleet" to vehicleMigrationData.vehicle.fleet,
                            "leasingArt" to vehicleMigrationData.vehicle.leasingArt,
                            "responsiblePerson" to vehicleMigrationData.vehicle.responsiblePerson,
                            "vGuid" to vehicleMigrationData.vehicle.vGuid,
                            "isActive" to vehicleMigrationData.vehicle.isActive,
                            "isPoolVehicle" to vehicleMigrationData.vehicle.isPoolVehicle,
                        ),
                    "vehicleResponsiblePerson" to
                        mapOf(
                            "employeeNumber" to vehicleResponsiblePerson.employeeNumber,
                            "firstName" to vehicleResponsiblePerson.firstName,
                            "lastName" to vehicleResponsiblePerson.lastName,
                            "email" to vehicleResponsiblePerson.email,
                            "isFleetManager" to vehicleResponsiblePerson.isFleetManager,
                            "isGlobalFleetManager" to vehicleResponsiblePerson.isGlobalFleetManager,
                        ),
                    "fleetInformation" to vehicleMigrationData.fleetInformation,
                )
                .toJsonNode()

        assertEquals(expectedPayload, vehicleMigrationMessageRetry.payload)
        assertEquals(vehicleMigrationData.vehicle.vin, vehicleMigrationMessageRetry.vin)
    }

    companion object {
        fun vehicleMigrationDataRequirements(vehicleMigrationEvent: VehicleMigrationEvent) =
            Consumer<VehicleMigrationData> { vehicleMigrationData: VehicleMigrationData ->
                assertThat(vehicleMigrationData.vehicle).isNotNull
                assertThat(vehicleMigrationData.vehicle.vin).isEqualTo(vehicleMigrationEvent.vehicle.vin)
                assertThat(vehicleMigrationData.vehicle.vGuid).isEqualTo(vehicleMigrationEvent.vehicle.vguid)
                assertThat(vehicleMigrationData.vehicle.model).isEqualTo(vehicleMigrationEvent.vehicle.model)
                assertThat(vehicleMigrationData.vehicle.depreciationRelevantCostCenter)
                    .isEqualTo(vehicleMigrationEvent.vehicle.depreciationRelevantCostCenter)
                assertThat(vehicleMigrationData.vehicle.usingCostCenter)
                    .isEqualTo(vehicleMigrationEvent.vehicle.usingCostCenter)
                assertThat(vehicleMigrationData.vehicle.isActive).isEqualTo(vehicleMigrationEvent.vehicle.isActive)
                assertThat(vehicleMigrationData.vehicle.firstRegistrationDate)
                    .isEqualTo(vehicleMigrationEvent.vehicle.firstRegistrationDate)
                assertThat(vehicleMigrationData.vehicle.licensePlate?.value)
                    .isEqualTo(vehicleMigrationEvent.vehicle.licensePlate)
                assertThat(vehicleMigrationData.vehicle.manufacturer)
                    .isEqualTo(vehicleMigrationEvent.vehicle.manufacturer)
                assertThat(vehicleMigrationData.fleetInformation.name)
                    .isEqualTo(vehicleMigrationEvent.vehicle.fleetInformation.name)

                val actualVehicleResponsiblePerson = requireNotNull(vehicleMigrationData.vehicleResponsiblePerson)
                val expectedVehicleResponsiblePerson = vehicleMigrationEvent.vehicleResponsiblePerson!!

                assertThat(actualVehicleResponsiblePerson.employeeNumber.value)
                    .isEqualTo(expectedVehicleResponsiblePerson.employeeNumber)
                assertThat(actualVehicleResponsiblePerson.firstName)
                    .isEqualTo(expectedVehicleResponsiblePerson.firstName)
                assertThat(actualVehicleResponsiblePerson.lastName).isEqualTo(expectedVehicleResponsiblePerson.lastName)
                assertThat(actualVehicleResponsiblePerson.email).isEqualTo(expectedVehicleResponsiblePerson.email)
            }

        @JvmStatic
        fun vehicleMigrationEventProvider(): Set<VehicleMigrationEvent> =
            VehicleMigrationEventBuilder.buildMultiple(10).toSet()
    }
}
