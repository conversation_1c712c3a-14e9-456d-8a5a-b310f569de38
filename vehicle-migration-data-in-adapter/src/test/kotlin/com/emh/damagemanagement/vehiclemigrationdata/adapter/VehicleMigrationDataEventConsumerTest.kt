/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigrationdata.adapter

import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.application.port.MigrateVehicleDataUseCase
import com.emh.damagemanagement.vehiclemigration.application.port.MigrationResult
import com.emh.damagemanagement.vehiclemigration.messageretry.application.VehicleMigrationMessageRetryService
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry
import com.emh.damagemanagement.vehiclemigrationdata.IntegrationTest
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@IntegrationTest
@EmbeddedKafka(controlledShutdown = true, partitions = 1, topics = ["dms-vehicle-migration"])
class VehicleMigrationDataEventConsumerTest() {
    @Autowired private lateinit var kafkaTemplate: KafkaTemplate<String, JsonNode>
    @MockitoSpyBean private lateinit var vehicleMigrationDataEventConsumer: VehicleMigrationDataEventConsumer
    @MockitoBean private lateinit var migrateVehicleDataUseCase: MigrateVehicleDataUseCase
    @MockitoBean private lateinit var vehicleMigrationMessageRetryService: VehicleMigrationMessageRetryService

    @Test
    fun `should consume dms vehicle migration topic event and migrate vehicle migration data`() {
        val vin = "1N4AA5AP6EC448282"
        val vguid = "some-vguid"
        val model = "Macan"
        val depreciationRelevantCostCenter = "00H0015131"
        val usingCostCenter = "********"
        val vehicleUsage = "Zweitleasing"
        val isActive = true
        val fleetInformation = "POST_USAGE"
        val firstRegistrationDate = "2025-02-20T08:00Z"
        val licensePlate = "BE-1235"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val leasingArt = "L1.1"
        val employeeNumber = "00121212"
        val firstName = "Max"
        val lastName = "Mustermann"
        val email = "<EMAIL>"
        val payload =
            mapOf(
                    "vehicle" to
                        mapOf(
                            "vin" to vin,
                            "vguid" to vguid,
                            "model" to model,
                            "depreciationRelevantCostCenter" to depreciationRelevantCostCenter,
                            "usingCostCenter" to usingCostCenter,
                            "vehicleUsage" to vehicleUsage,
                            "isActive" to isActive,
                            "fleetInformation" to fleetInformation,
                            "firstRegistrationDate" to firstRegistrationDate,
                            "licensePlate" to licensePlate,
                            "vehicleType" to vehicleType,
                            "manufacturer" to manufacturer,
                            "leasingArt" to leasingArt,
                        ),
                    "vehicleResponsiblePerson" to
                        mapOf(
                            "employeeNumber" to employeeNumber,
                            "firstName" to firstName,
                            "lastName" to lastName,
                            "email" to email,
                        ),
                )
                .toJsonNode()

        whenever(migrateVehicleDataUseCase.migrateVehicleData(any())).thenReturn(MigrationResult(success = true))

        kafkaTemplate.send("dms-vehicle-migration", payload)
        val vehicleMigrationDataCaptor = argumentCaptor<VehicleMigrationData>()

        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(vehicleMigrationDataEventConsumer, times(1))
                    .listen(jacksonObjectMapper().writeValueAsString(payload))
                verify(migrateVehicleDataUseCase, times(1)).migrateVehicleData(vehicleMigrationDataCaptor.capture())
                val actualVehicleMigrationData = vehicleMigrationDataCaptor.firstValue
                assertEquals(vin, actualVehicleMigrationData.vehicle.vin)
                verify(vehicleMigrationMessageRetryService, times(0)).create(any())
            }
    }

    @Test
    fun `should consume dms vehicle migration topic event and create vehicle migration message retry`() {
        val payload =
            mapOf(
                    "vehicle" to
                        mapOf(
                            "vin" to "1N4AA5AP6EC448282",
                            "vguid" to "some-vguid",
                            "model" to "Macan",
                            "depreciationRelevantCostCenter" to "00H0015131",
                            "usingCostCenter" to "********",
                            "vehicleUsage" to "Zweitleasing",
                            "isActive" to true,
                            "fleetInformation" to "POST_USAGE",
                            "firstRegistrationDate" to "2025-02-20T08:00Z",
                            "licensePlate" to "BE-1235",
                            "vehicleType" to "PKW",
                            "manufacturer" to "Porsche",
                            "leasingArt" to "L1.1",
                        ),
                    "vehicleResponsiblePerson" to
                        mapOf(
                            "employeeNumber" to "00121212",
                            "firstName" to "Max",
                            "lastName" to "Mustermann",
                            "email" to "<EMAIL>",
                        ),
                )
                .toJsonNode()

        whenever(migrateVehicleDataUseCase.migrateVehicleData(any()))
            .thenReturn(
                MigrationResult(
                    success = false,
                    errorType = setOf(ErrorType.TECHNICAL),
                    errors = setOf("Something went wrong"),
                )
            )

        kafkaTemplate.send("dms-vehicle-migration", payload)

        val vehicleMigrationMessageRetryCaptor = argumentCaptor<VehicleMigrationMessageRetry>()

        await atMost
            Durations.TWO_SECONDS withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(vehicleMigrationDataEventConsumer, times(1))
                    .listen(jacksonObjectMapper().writeValueAsString(payload))
                verify(migrateVehicleDataUseCase, times(1)).migrateVehicleData(any())
                verify(vehicleMigrationMessageRetryService, times(1))
                    .create(vehicleMigrationMessageRetryCaptor.capture())
                val actualVehicleMigrationMessageRetry = vehicleMigrationMessageRetryCaptor.firstValue
                assertEquals("1N4AA5AP6EC448282", actualVehicleMigrationMessageRetry.vin)
            }
    }
}
