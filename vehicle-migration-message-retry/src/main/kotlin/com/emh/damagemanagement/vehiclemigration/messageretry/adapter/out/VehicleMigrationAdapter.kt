/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.adapter.out

import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.application.port.MigrateVehicleDataUseCase
import com.emh.damagemanagement.vehiclemigration.application.port.MigrationResult
import com.emh.damagemanagement.vehiclemigration.messageretry.application.port.MigrateVehicleMigrationMessageRetryUseCase
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry
import com.fasterxml.jackson.databind.ObjectMapper
import org.springframework.stereotype.Component

@Component
class VehicleMigrationAdapter(
    private val migrateVehicleDataUseCase: MigrateVehicleDataUseCase,
    private val objectMapper: ObjectMapper,
) : MigrateVehicleMigrationMessageRetryUseCase {
    override fun migrate(message: VehicleMigrationMessageRetry): MigrationResult {
        val vehicleMigrationData = objectMapper.treeToValue(message.payload, VehicleMigrationData::class.java)
        return migrateVehicleDataUseCase.migrateVehicleData(vehicleMigrationData)
    }
}
