package com.emh.damagemanagement.vehiclemigration.messageretry.application

import com.emh.damagemanagement.vehiclemigration.messageretry.application.port.CreateVehicleMigrationMessageRetryUseCase
import com.emh.damagemanagement.vehiclemigration.messageretry.application.port.DeleteVehicleMigrationMessageRetryUseCase
import com.emh.damagemanagement.vehiclemigration.messageretry.application.port.MigrateVehicleMigrationMessageRetryUseCase
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetryId
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetryRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class VehicleMigrationMessageRetryService(
    private val repository: VehicleMigrationMessageRetryRepository,
    private val syncVehicleMigrationMessageRetryService: SyncVehicleMigrationMessageRetryService,
) : CreateVehicleMigrationMessageRetryUseCase, DeleteVehicleMigrationMessageRetryUseCase {
    @Transactional
    override fun create(message: VehicleMigrationMessageRetry) {
        val existingVehicleMigrationMessageRetry = repository.findByVin(message.vin)
        if (null == existingVehicleMigrationMessageRetry) {
            repository.save(message)
            log.info("Successfully persisted vehicle migration message retry: {}", message.vin)
        } else {
            existingVehicleMigrationMessageRetry.updatePayload(message.payload)
            log.info("Successfully updated vehicle migration message retry: {}", message.vin)
        }
    }

    @Transactional
    override fun delete(id: VehicleMigrationMessageRetryId) {
        repository.deleteById(id)
    }

    fun syncVehicleMigrationMessageRetry() {
        val messages = repository.findAll()
        log.info("Found: {} vehicle migration retry messages", messages.size)
        messages.forEach { message ->
            syncVehicleMigrationMessageRetryService.syncVehicleMigrationMessageRetry(message)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleMigrationMessageRetryService::class.java)
    }
}

@Component
class SyncVehicleMigrationMessageRetryService(
    private val repository: VehicleMigrationMessageRetryRepository,
    private val migrateVehicleMigrationMessageRetryUseCase: MigrateVehicleMigrationMessageRetryUseCase,
) {
    @Transactional
    fun syncVehicleMigrationMessageRetry(message: VehicleMigrationMessageRetry) {
        val migrationResult = migrateVehicleMigrationMessageRetryUseCase.migrate(message)
        if (migrationResult.success || !migrationResult.canRetry) {
            repository.deleteById(message.id)
            log.info("Successfully Migrated vehicle migration message retry: {}", message.vin)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(SyncVehicleMigrationMessageRetryService::class.java)
    }
}
