/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.application.job

import com.emh.damagemanagement.vehiclemigration.messageretry.application.VehicleMigrationMessageRetryService
import java.util.TimeZone
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class SyncVehicleMigrationMessageRetryJob(
    private val vehicleMigrationMessageRetryService: VehicleMigrationMessageRetryService
) : Job {
    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled vehicle migration message retry job")
        vehicleMigrationMessageRetryService.syncVehicleMigrationMessageRetry()
        log.info("Finished scheduled vehicle migration message retry job")
    }

    companion object {
        private val log = LoggerFactory.getLogger(SyncVehicleMigrationMessageRetryJob::class.java)
    }
}

@Configuration
class SyncVehicleMigrationMessageRetryJobConfig {

    @Bean
    @Qualifier("syncVehicleMigrationMessageRetryJob")
    fun syncVehicleMigrationMessageRetryJobDetail(): JobDetail {
        return JobBuilder.newJob()
            .ofType(SyncVehicleMigrationMessageRetryJob::class.java)
            .storeDurably()
            .withIdentity("SyncVehicleMigrationMessageRetryJobDetail")
            .withDescription("SyncVehicleMigrationMessageRetryJob Job")
            .build()
    }

    @Bean
    fun syncVehicleMigrationMessageRetryJobTrigger(
        @Qualifier("syncVehicleMigrationMessageRetryJob") job: JobDetail,
        @Value("\${vehicle-migration-message-retry.scheduler.cron}") cron: String,
    ): Trigger {
        return TriggerBuilder.newTrigger()
            .forJob(job)
            .withIdentity("SyncVehicleMigrationMessageRetryJobTrigger")
            .withDescription("SyncVehicleMigrationMessageRetryJob trigger")
            .withSchedule(CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZONE_UTC)))
            .build()
    }
}

private const val ZONE_UTC = "UTC"
