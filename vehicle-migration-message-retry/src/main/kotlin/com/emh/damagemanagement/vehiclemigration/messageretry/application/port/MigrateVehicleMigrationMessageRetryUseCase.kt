/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */ package com.emh.damagemanagement.vehiclemigration.messageretry.application.port

import com.emh.damagemanagement.vehiclemigration.application.port.MigrationResult
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry

fun interface MigrateVehicleMigrationMessageRetryUseCase {
    fun migrate(message: VehicleMigrationMessageRetry): MigrationResult
}
