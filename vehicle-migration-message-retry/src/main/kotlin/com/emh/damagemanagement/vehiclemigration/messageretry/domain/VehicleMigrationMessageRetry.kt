/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.domain

import com.emh.damagemanagement.shared.domain.Auditable
import com.fasterxml.jackson.databind.JsonNode
import io.hypersistence.utils.hibernate.type.json.JsonType
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import java.io.Serializable
import java.util.UUID
import org.hibernate.annotations.Type

@Entity
@Table(name = "vehicle_migration_message_retry")
class VehicleMigrationMessageRetry(
    @Column(name = "payload", columnDefinition = "json") @Type(JsonType::class) var payload: JsonNode,
    @Column(name = "vin") val vin: String,
) : Auditable() {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: VehicleMigrationMessageRetryId = VehicleMigrationMessageRetryId()

    internal fun updatePayload(payload: JsonNode) {
        this.payload = payload
    }
}

@Embeddable data class VehicleMigrationMessageRetryId(@Basic val value: UUID = UUID.randomUUID()) : Serializable
