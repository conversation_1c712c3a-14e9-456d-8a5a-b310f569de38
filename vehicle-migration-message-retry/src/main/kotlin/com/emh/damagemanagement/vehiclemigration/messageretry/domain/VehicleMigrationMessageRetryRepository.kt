/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.domain

import org.springframework.data.repository.Repository

interface VehicleMigrationMessageRetryRepository :
    Repository<VehicleMigrationMessageRetry, VehicleMigrationMessageRetryId> {

    fun save(message: VehicleMigrationMessageRetry)

    fun findAll(): List<VehicleMigrationMessageRetry>

    fun deleteById(id: VehicleMigrationMessageRetryId)

    fun findByVin(vin: String): VehicleMigrationMessageRetry?
}
