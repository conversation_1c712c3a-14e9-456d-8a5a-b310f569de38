package com.emh.damagemanagement.vehiclemigration.messageretry

import com.emh.damagemanagement.shared.TestJpaAuditingConfiguration
import java.lang.annotation.Inherited
import org.junit.jupiter.api.Tag
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.TestPropertySource
import org.springframework.transaction.annotation.Transactional

@Tag("integration")
@Transactional
@SpringBootTest(classes = [TestVehicleMigrationMessageRetryApplication::class, TestJpaAuditingConfiguration::class])
@ActiveProfiles("test")
@TestPropertySource("classpath:application-test.yml")
@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Inherited
annotation class IntegrationTest
