package com.emh.damagemanagement.vehiclemigration.messageretry

import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication

@SpringBootApplication(
    scanBasePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.vehiclemigration.messageretry.*",
            "com.emh.damagemanagement.vehiclemigration.*",
            "com.emh.damagemanagement.repairfix.*",
        ]
)
@ConfigurationPropertiesScan(
    basePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.vehiclemigration.messageretry.*",
            "com.emh.damagemanagement.vehiclemigration.*",
            "com.emh.damagemanagement.repairfix.*",
        ]
)
class TestVehicleMigrationMessageRetryApplication {

    fun main(args: Array<String>) {
        runApplication<TestVehicleMigrationMessageRetryApplication>(*args)
    }
}
