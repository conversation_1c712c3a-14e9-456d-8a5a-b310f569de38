/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.messageretry

import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.emh.damagemanagement.shared.createRandomOffsetDate
import com.emh.damagemanagement.shared.createRandomString
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.FleetInformation
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.LicensePlate
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.vehiclemigration.domain.toFleet
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry
import com.fasterxml.jackson.databind.JsonNode
import java.time.OffsetDateTime
import kotlin.random.Random
import net.datafaker.Faker

class VehicleMigrationDataBuilder {
    private val faker = Faker()
    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var vguid: String = createRandomString(Random.nextInt(4, 23))
    private var model: String = createRandomString(Random.nextInt(4, 23))
    private var depreciationRelevantCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var usingCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var isActive: Boolean = Random.nextBoolean()
    private var firstRegistrationDate: OffsetDateTime = createRandomOffsetDate()
    private var licensePlate: LicensePlate = LicensePlate(faker.vehicle().licensePlate())
    private var responsiblePerson: EmployeeNumber = EmployeeNumber(createRandomString(8))
    private var leasingArt: LeasingArt = LeasingArt.entries.random()
    private var manufacturer: String = createRandomString(23)

    private var fleetInformation = FleetInformation.entries.random()

    private var employeeNumber = EmployeeNumber(faker.numerify("00######"))
    private var firstName = faker.name().firstName()
    private var lastName = faker.name().lastName()
    private var email = faker.internet().emailAddress()

    private var vehicleResponsiblePerson: VehicleResponsiblePerson? =
        VehicleResponsiblePerson(
            employeeNumber = this.employeeNumber,
            firstName = this.firstName,
            lastName = this.lastName,
            email = this.email,
            isFleetManager = leasingArt.isPool,
        )

    private fun buildVehicle() =
        Vehicle(
            vin = this.vin,
            vGuid = this.vguid,
            model = this.model,
            depreciationRelevantCostCenter = this.depreciationRelevantCostCenter,
            usingCostCenter = this.usingCostCenter,
            isActive = this.isActive,
            firstRegistrationDate = this.firstRegistrationDate,
            licensePlate = this.licensePlate,
            responsiblePerson = this.responsiblePerson,
            manufacturer = this.manufacturer,
            leasingArt = leasingArt,
            fleet =
                vehicleResponsiblePerson?.let {
                    Fleet.determineFleet(
                        fleetInformation = fleetInformation,
                        firstName = it.firstName,
                        lastName = it.lastName,
                        leasingArt = leasingArt,
                        employeeNumber = it.employeeNumber.value,
                    )
                } ?: leasingArt.toFleet(),
        )

    private var vehicle = buildVehicle()

    fun fleetInformation(fleetInformation: FleetInformation) = apply {
        this.fleetInformation = fleetInformation
        this.vehicle = buildVehicle()
    }

    fun leasingArt(leasingArt: LeasingArt) = apply {
        this.leasingArt = leasingArt
        this.vehicle = buildVehicle()
    }

    fun vehicle(vehicle: Vehicle) = apply { this.vehicle = vehicle }

    fun vehicleResponsiblePerson(vehicleResponsiblePerson: VehicleResponsiblePerson?) = apply {
        this.vehicleResponsiblePerson = vehicleResponsiblePerson
        this.vehicle = buildVehicle()
    }

    fun build(): VehicleMigrationData {
        return VehicleMigrationData(
            vehicle = vehicle,
            fleetInformation = this.fleetInformation,
            vehicleResponsiblePerson = this.vehicleResponsiblePerson,
        )
    }

    companion object {
        fun buildSingle() = VehicleMigrationDataBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleMigrationData> =
            (1..count).map { VehicleMigrationDataBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class VehicleMigrationMessageRetryBuilder {
    val vehicleMigrationData = VehicleMigrationDataBuilder().build()
    val payload: JsonNode = vehicleMigrationData.toJsonNode()
    var vin: String = createRandomString(Random.nextInt(4, 23))

    fun vin(vin: String) = apply { this.vin = vin }

    fun build(): VehicleMigrationMessageRetry = VehicleMigrationMessageRetry(vin = vin, payload = payload)

    companion object {
        fun buildSingle() = VehicleMigrationMessageRetryBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleMigrationMessageRetry> =
            (1..count).map { VehicleMigrationMessageRetryBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}
