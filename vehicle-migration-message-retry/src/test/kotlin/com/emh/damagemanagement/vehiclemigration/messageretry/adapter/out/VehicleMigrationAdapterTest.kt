/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.adapter.out

import com.emh.damagemanagement.shared.config.JacksonConfig
import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.application.port.MigrateVehicleDataUseCase
import com.emh.damagemanagement.vehiclemigration.application.port.MigrationResult
import com.emh.damagemanagement.vehiclemigration.messageretry.VehicleMigrationDataBuilder
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry
import com.fasterxml.jackson.databind.ObjectMapper
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class VehicleMigrationAdapterTest {

    private val migrateVehicleDataUseCase: MigrateVehicleDataUseCase = mock()
    private val objectMapper: ObjectMapper = JacksonConfig.jackson2ObjectMapperBuilder.build()

    private val vehicleMigrationAdapter: VehicleMigrationAdapter =
        VehicleMigrationAdapter(migrateVehicleDataUseCase, objectMapper)

    @Test
    fun `should call migrateVehicleDataUseCase`() {
        val vehicleMigrationData = VehicleMigrationDataBuilder().build()
        val vehicleMigrationMessageRetry =
            VehicleMigrationMessageRetry(
                vin = vehicleMigrationData.vehicle.vin,
                payload = vehicleMigrationData.toJsonNode(),
            )
        val vehicleMigrationDataCaptor = argumentCaptor<VehicleMigrationData>()

        whenever(migrateVehicleDataUseCase.migrateVehicleData(any())).thenReturn(MigrationResult(success = true))

        vehicleMigrationAdapter.migrate(vehicleMigrationMessageRetry)

        verify(migrateVehicleDataUseCase).migrateVehicleData(vehicleMigrationDataCaptor.capture())
        val actualVehicleMigrationData = vehicleMigrationDataCaptor.firstValue

        assertThat(actualVehicleMigrationData.vehicle.vin).isEqualTo(vehicleMigrationData.vehicle.vin)
        assertThat(actualVehicleMigrationData.vehicle.vGuid).isEqualTo(vehicleMigrationData.vehicle.vGuid)
        assertThat(actualVehicleMigrationData.vehicle.model).isEqualTo(vehicleMigrationData.vehicle.model)
        assertThat(actualVehicleMigrationData.vehicle.depreciationRelevantCostCenter)
            .isEqualTo(vehicleMigrationData.vehicle.depreciationRelevantCostCenter)
        assertThat(actualVehicleMigrationData.vehicle.usingCostCenter)
            .isEqualTo(vehicleMigrationData.vehicle.usingCostCenter)
        assertThat(actualVehicleMigrationData.vehicle.isActive).isEqualTo(vehicleMigrationData.vehicle.isActive)
        assertThat(actualVehicleMigrationData.vehicle.firstRegistrationDate)
            .isEqualTo(vehicleMigrationData.vehicle.firstRegistrationDate)
        assertThat(actualVehicleMigrationData.vehicle.licensePlate?.value)
            .isEqualTo(vehicleMigrationData.vehicle.licensePlate?.value)
        assertThat(actualVehicleMigrationData.vehicle.manufacturer).isEqualTo(vehicleMigrationData.vehicle.manufacturer)
        assertThat(actualVehicleMigrationData.vehicle.fleet.id).isEqualTo(vehicleMigrationData.vehicle.fleet.id)
        assertThat(actualVehicleMigrationData.vehicle.fleet.description)
            .isEqualTo(vehicleMigrationData.vehicle.fleet.description)
        assertThat(actualVehicleMigrationData.vehicle.leasingArt).isEqualTo(vehicleMigrationData.vehicle.leasingArt)

        assertThat(actualVehicleMigrationData.fleetInformation.name)
            .isEqualTo(vehicleMigrationData.fleetInformation.name)

        val actualVehicleResponsiblePerson = requireNotNull(actualVehicleMigrationData.vehicleResponsiblePerson)
        val expectedVehicleResponsiblePerson = vehicleMigrationData.vehicleResponsiblePerson!!

        assertThat(actualVehicleResponsiblePerson.employeeNumber.value)
            .isEqualTo(expectedVehicleResponsiblePerson.employeeNumber.value)
        assertThat(actualVehicleResponsiblePerson.firstName).isEqualTo(expectedVehicleResponsiblePerson.firstName)
        assertThat(actualVehicleResponsiblePerson.lastName).isEqualTo(expectedVehicleResponsiblePerson.lastName)
        assertThat(actualVehicleResponsiblePerson.email).isEqualTo(expectedVehicleResponsiblePerson.email)
        assertThat(actualVehicleResponsiblePerson.isFleetManager)
            .isEqualTo(expectedVehicleResponsiblePerson.isFleetManager)
        assertThat(actualVehicleResponsiblePerson.isGlobalFleetManager)
            .isEqualTo(expectedVehicleResponsiblePerson.isGlobalFleetManager)
    }
}
