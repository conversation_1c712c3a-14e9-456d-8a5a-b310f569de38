/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.application

import com.emh.damagemanagement.shared.TEST_USER
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.MigrationResult
import com.emh.damagemanagement.vehiclemigration.messageretry.IntegrationTest
import com.emh.damagemanagement.vehiclemigration.messageretry.VehicleMigrationMessageRetryBuilder
import com.emh.damagemanagement.vehiclemigration.messageretry.application.port.MigrateVehicleMigrationMessageRetryUseCase
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetry
import com.emh.damagemanagement.vehiclemigration.messageretry.domain.VehicleMigrationMessageRetryRepository
import com.fasterxml.jackson.databind.JsonNode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Test
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.bean.override.mockito.MockitoBean

@IntegrationTest
class VehicleMigrationMessageRetryServiceTest {
    @Autowired private lateinit var vehicleMigrationMessageRetryService: VehicleMigrationMessageRetryService

    @Autowired private lateinit var vehicleMigrationMessageRetryRepository: VehicleMigrationMessageRetryRepository

    @MockitoBean
    private lateinit var migrateVehicleMigrationMessageRetryUseCase: MigrateVehicleMigrationMessageRetryUseCase

    @AfterEach
    fun cleanup() {
        reset(migrateVehicleMigrationMessageRetryUseCase)
    }

    @Test
    fun `should create vehicle migration message retry`() {
        val vehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(vehicleMigrationMessageRetry)

        val listOfSavedVehicleMigrationMessageRetry = vehicleMigrationMessageRetryRepository.findAll()

        assertThat(listOfSavedVehicleMigrationMessageRetry).hasSize(1)
        val saved = listOfSavedVehicleMigrationMessageRetry[0]
        assertThat(saved.id).isNotNull
        assertThat(saved.payload).isEqualTo(vehicleMigrationMessageRetry.payload)
        assertThat(saved.vin).isEqualTo(vehicleMigrationMessageRetry.vin)
        assertThat(saved.createdBy).isEqualTo(TEST_USER)
        assertThat(saved.created).isNotNull()
    }

    @Test
    fun `should update vehicle migration message retry`() {
        val vehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(vehicleMigrationMessageRetry)
        val listOfSavedVehicleMigrationMessageRetry = vehicleMigrationMessageRetryRepository.findAll()
        assertThat(listOfSavedVehicleMigrationMessageRetry).hasSize(1)
        val initialSaved = listOfSavedVehicleMigrationMessageRetry[0]

        val anotherVehicleMigrationMessageRetry =
            VehicleMigrationMessageRetryBuilder().vin(vehicleMigrationMessageRetry.vin).build()
        vehicleMigrationMessageRetryService.create(anotherVehicleMigrationMessageRetry)

        val updatedListOfSavedVehicleMigrationMessageRetry = vehicleMigrationMessageRetryRepository.findAll()
        assertThat(updatedListOfSavedVehicleMigrationMessageRetry).hasSize(1)
        val updated = updatedListOfSavedVehicleMigrationMessageRetry[0]

        assertThat(updated.id).isEqualTo(initialSaved.id)
        assertThat(updated.payload).isEqualTo(anotherVehicleMigrationMessageRetry.payload)
        assertThat(updated.vin).isEqualTo(initialSaved.vin)
        assertThat(updated.lastModifiedBy).isEqualTo(TEST_USER)
        assertThat(updated.lastModified).isNotNull()
    }

    @Test
    fun `should delete vehicle migration message retry`() {
        val vehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(vehicleMigrationMessageRetry)
        val listOfSavedVehicleMigrationMessageRetry = vehicleMigrationMessageRetryRepository.findAll()
        assertThat(listOfSavedVehicleMigrationMessageRetry).hasSize(1)

        vehicleMigrationMessageRetryService.delete(listOfSavedVehicleMigrationMessageRetry[0].id)
        val listOfVehicleMigrationMessageRetry = vehicleMigrationMessageRetryRepository.findAll()
        assertThat(listOfVehicleMigrationMessageRetry).hasSize(0)
    }

    @Test
    fun `should sync vehicle migration message retry`() {
        val vehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(vehicleMigrationMessageRetry)

        val anotherVehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(anotherVehicleMigrationMessageRetry)

        whenever(migrateVehicleMigrationMessageRetryUseCase.migrate(any())).thenReturn(MigrationResult(success = true))

        val vehicleMigrationMessageRetryCapture = argumentCaptor<VehicleMigrationMessageRetry>()

        vehicleMigrationMessageRetryService.syncVehicleMigrationMessageRetry()

        verify(migrateVehicleMigrationMessageRetryUseCase, times(2))
            .migrate(vehicleMigrationMessageRetryCapture.capture())

        val allValues = vehicleMigrationMessageRetryCapture.allValues
        assertThat(allValues)
            .extracting<JsonNode> { it.payload }
            .containsExactlyInAnyOrderElementsOf(
                listOf(vehicleMigrationMessageRetry.payload, anotherVehicleMigrationMessageRetry.payload)
            )
        val saved = vehicleMigrationMessageRetryRepository.findAll()
        assertThat(saved).hasSize(0)
    }

    @Test
    fun `should skip deletion of vehicle migration message retry message that failed migration`() {
        val vehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(vehicleMigrationMessageRetry)

        val anotherVehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(anotherVehicleMigrationMessageRetry)

        whenever(migrateVehicleMigrationMessageRetryUseCase.migrate(any())).thenAnswer { invocation ->
            val input = invocation.arguments[0] as VehicleMigrationMessageRetry

            when (input.id) {
                vehicleMigrationMessageRetry.id ->
                    MigrationResult(success = false, errorType = setOf(ErrorType.TECHNICAL))

                anotherVehicleMigrationMessageRetry.id -> MigrationResult(success = true)
                else -> throw IllegalStateException()
            }
        }

        vehicleMigrationMessageRetryService.syncVehicleMigrationMessageRetry()

        verify(migrateVehicleMigrationMessageRetryUseCase, times(2)).migrate(any())
        val saved = vehicleMigrationMessageRetryRepository.findAll()
        assertThat(saved).hasSize(1)
        assertThat(saved[0].id).isEqualTo(vehicleMigrationMessageRetry.id)
    }

    @Test
    fun `should still delete vehicle migration message retry message when migration failed due to business error`() {
        val vehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(vehicleMigrationMessageRetry)

        val anotherVehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder.buildSingle()
        vehicleMigrationMessageRetryService.create(anotherVehicleMigrationMessageRetry)

        whenever(migrateVehicleMigrationMessageRetryUseCase.migrate(any())).thenAnswer { invocation ->
            val input = invocation.arguments[0] as VehicleMigrationMessageRetry

            when (input.id) {
                vehicleMigrationMessageRetry.id ->
                    MigrationResult(success = false, errorType = setOf(ErrorType.BUSINESS))

                anotherVehicleMigrationMessageRetry.id -> MigrationResult(success = true)
                else -> throw IllegalStateException()
            }
        }

        vehicleMigrationMessageRetryService.syncVehicleMigrationMessageRetry()

        verify(migrateVehicleMigrationMessageRetryUseCase, times(2)).migrate(any())
        val saved = vehicleMigrationMessageRetryRepository.findAll()
        assertThat(saved).hasSize(0)
    }
}
