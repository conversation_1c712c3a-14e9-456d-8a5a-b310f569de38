/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.application.job

import com.emh.damagemanagement.vehiclemigration.messageretry.application.VehicleMigrationMessageRetryService
import org.junit.jupiter.api.Test
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify

class SyncVehicleMigrationMessageRetryJobTest() {
    private val vehicleMigrationMessageRetryService: VehicleMigrationMessageRetryService = mock()
    private val syncVehicleMigrationMessageRetryJob: SyncVehicleMigrationMessageRetryJob =
        SyncVehicleMigrationMessageRetryJob(vehicleMigrationMessageRetryService)

    @Test
    fun `should sync VehicleMigrationMessageRetry when scheduler is called`() {

        syncVehicleMigrationMessageRetryJob.execute(null)
        verify(vehicleMigrationMessageRetryService).syncVehicleMigrationMessageRetry()
    }
}
