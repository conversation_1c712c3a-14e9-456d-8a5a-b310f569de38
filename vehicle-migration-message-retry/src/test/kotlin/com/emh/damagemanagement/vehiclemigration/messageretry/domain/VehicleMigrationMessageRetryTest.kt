/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.messageretry.domain

import com.emh.damagemanagement.shared.converter.JsonConverter.Companion.toJsonNode
import com.emh.damagemanagement.vehiclemigration.messageretry.VehicleMigrationDataBuilder
import com.emh.damagemanagement.vehiclemigration.messageretry.VehicleMigrationMessageRetryBuilder
import com.fasterxml.jackson.databind.JsonNode
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class VehicleMigrationMessageRetryTest {

    @Test
    fun `should update payload`() {
        val vehicleMigrationEvent = VehicleMigrationDataBuilder().build()
        val payload: JsonNode = vehicleMigrationEvent.toJsonNode()

        val vehicleMigrationMessageRetry = VehicleMigrationMessageRetryBuilder().build()

        vehicleMigrationMessageRetry.updatePayload(payload)

        assertThat(vehicleMigrationMessageRetry.payload).isEqualTo(payload)
    }
}
