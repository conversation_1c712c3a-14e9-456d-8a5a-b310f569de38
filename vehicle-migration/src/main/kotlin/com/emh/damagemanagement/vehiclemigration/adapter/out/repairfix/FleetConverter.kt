/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetOnboardingDto
import com.emh.damagemanagement.vehiclemigration.domain.Fleet

fun Fleet.toFleetOnboardingDto(parentFleetId: String): FleetOnboardingDto =
    FleetOnboardingDto(name = requireNotNull(this.description), fleetPartnerId = this.id, parentFleetId = parentFleetId)
