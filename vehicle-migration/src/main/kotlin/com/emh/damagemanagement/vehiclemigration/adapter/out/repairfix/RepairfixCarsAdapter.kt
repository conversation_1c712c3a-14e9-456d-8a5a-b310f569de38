/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.CarsApi
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono

@Component
class RepairfixCarsAdapter(private val carsApi: CarsApi) : VehicleOutPort {

    override fun addVehicle(vehicle: Vehicle): AddVehicleResult {
        log.info("Adding car with vin [${vehicle.vin}] to repairfix.")

        val result =
            carsApi
                .porscheCarsControllerAdd(listOf(vehicle.toAddCarDto()))
                .map { operationResult -> AddVehicleResult(success = operationResult.success) }
                .onErrorResume(RepairfixException::class.java) { repairfixException ->
                    Mono.just(
                        AddVehicleResult(
                            success = false,
                            error = repairfixException.message,
                            errorType = repairfixException.type.toErrorType(),
                        )
                    )
                }
                .block()
                ?: AddVehicleResult(
                    success = false,
                    error = "No result returned when trying to add vehicle with vin [${vehicle.vin}] to repairfix.",
                    errorType = ErrorType.TECHNICAL,
                )

        if (result.success) {
            log.info("Successfully added car with vin [${vehicle.vin}] to repairfix.")
        } else {
            log.warn("Error while trying to add car with vin [${vehicle.vin}] to repairfix. ${result.error}")
        }
        return result
    }

    override fun findVehicle(vin: String): Vehicle? {
        return carsApi
            .porscheCarsControllerGetCar(vin)
            .map { carDetails -> carDetails.car.toVehicle() }
            .onErrorResume(RepairfixException::class.java) { repairfixException ->
                log.warn(
                    "Error while trying to fetch car with vin [$vin] from repairfix. ${repairfixException.message}"
                )
                Mono.empty()
            }
            .block()
    }

    override fun updateVehicle(vehicle: Vehicle): UpdateVehicleResult {
        log.info("Updating car with VIN [${vehicle.vin}] in repairfix.")

        val result =
            carsApi
                .porscheCarsControllerUpdate(
                    carIdentifier = vehicle.externalId.toString(),
                    updateCarDto = vehicle.toUpdateCarDto(),
                )
                .map { _ -> UpdateVehicleResult(success = true) }
                .onErrorResume(RepairfixException::class.java) { repairfixException ->
                    Mono.just(
                        UpdateVehicleResult(
                            success = false,
                            error = repairfixException.message,
                            errorType = repairfixException.type.toErrorType(),
                        )
                    )
                }
                .block()
                ?: UpdateVehicleResult(
                    success = false,
                    error = "No result returned when trying to update vehicle with vin [${vehicle.vin}] in repairfix.",
                    errorType = ErrorType.TECHNICAL,
                )

        if (result.success) {
            log.info("Successfully updated car with vin [${vehicle.vin}] in repairfix.")
        } else {
            log.warn("Error while trying to update car with vin [${vehicle.vin}] in repairfix. ${result.error}")
        }
        return result
    }

    override fun deactivateVehicle(vehicle: Vehicle): UpdateVehicleResult {
        log.info("Deactivating car with VIN [${vehicle.vin}] in repairfix.")

        val result =
            carsApi
                .porscheCarsControllerUpdate(
                    carIdentifier = vehicle.externalId.toString(),
                    updateCarDto = vehicle.toUpdateCarDto(deactivate = true),
                )
                .map { _ -> UpdateVehicleResult(success = true) }
                .onErrorResume(RepairfixException::class.java) { repairfixException ->
                    Mono.just(
                        UpdateVehicleResult(
                            success = false,
                            error = repairfixException.message,
                            errorType = repairfixException.type.toErrorType(),
                        )
                    )
                }
                .block()
                ?: UpdateVehicleResult(
                    success = false,
                    error =
                        "No result returned when trying to deactivate vehicle with vin [${vehicle.vin}] to repairfix.",
                    errorType = ErrorType.TECHNICAL,
                )

        if (result.success) {
            log.info("Successfully deactivated car with vin [${vehicle.vin}] in repairfix.")
        } else {
            log.warn("Error while trying to deactivated car with vin [${vehicle.vin}] in repairfix. ${result.error}")
        }
        return result
    }

    companion object {
        private val log = LoggerFactory.getLogger(RepairfixCarsAdapter::class.java)
    }
}
