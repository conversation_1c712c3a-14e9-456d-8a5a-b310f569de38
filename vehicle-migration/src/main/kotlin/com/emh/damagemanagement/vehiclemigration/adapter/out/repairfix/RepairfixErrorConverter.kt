/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.FleetMigrationException
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationException
import com.emh.damagemanagement.vehiclemigration.application.VehicleResponsiblePersonMigrationException

fun RepairfixException.toVehicleMigrationException(): VehicleMigrationException =
    VehicleMigrationException(
        message = this.message,
        canRetry =
            when (this.type) {
                RepairfixException.Type.BUSINESS -> false
                RepairfixException.Type.TECHNICAL -> true
            },
        cause = this.cause,
        errorType = this.type.toErrorType(),
    )

fun RepairfixException.toVehicleResponsiblePersonMigrationException(): VehicleResponsiblePersonMigrationException =
    VehicleResponsiblePersonMigrationException(
        message = this.message,
        canRetry =
            when (this.type) {
                RepairfixException.Type.BUSINESS -> false
                RepairfixException.Type.TECHNICAL -> true
            },
        cause = this.cause,
        errorType = this.type.toErrorType(),
    )

fun RepairfixException.toFleetMigrationException(): FleetMigrationException =
    FleetMigrationException(message = this.message, cause = this.cause, errorType = this.type.toErrorType())

fun RepairfixException.Type.toErrorType() =
    when (this) {
        RepairfixException.Type.TECHNICAL -> ErrorType.TECHNICAL
        RepairfixException.Type.BUSINESS -> ErrorType.BUSINESS
    }
