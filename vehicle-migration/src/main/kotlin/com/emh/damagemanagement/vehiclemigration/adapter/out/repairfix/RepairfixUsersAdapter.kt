/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.UserApi
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import reactor.core.publisher.Mono

@Component
class RepairfixUsersAdapter(private val userApi: UserApi) : VehicleResponsiblePersonOutPort {

    override fun findVehicleResponsiblePerson(employeeNumber: String): Pair<VehicleResponsiblePerson, Set<String>>? {
        return userApi
            .porscheUsersControllerGetUserInfo(userPartnerId = employeeNumber)
            .map { it.toVehicleResponsiblePersonAndFleetIds() }
            .onErrorResume(RepairfixException::class.java) { repairfixException ->
                log.warn(
                    "Error while trying to fetch user with userPartnerId [$employeeNumber] from repairfix. ${repairfixException.message}"
                )
                Mono.empty()
            }
            .block()
    }

    override fun addVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson
    ): AddVehicleResponsiblePersonResult {
        log.info("Adding user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] to repairfix.")
        val result =
            userApi
                .porscheUsersControllerAdd(listOf(vehicleResponsiblePerson.toCreateUserDto()))
                .map { operationResult -> AddVehicleResponsiblePersonResult(success = operationResult.success) }
                .onErrorResume(RepairfixException::class.java) { repairfixException ->
                    Mono.just(
                        AddVehicleResponsiblePersonResult(
                            success = false,
                            error = repairfixException.message,
                            errorType = repairfixException.type.toErrorType(),
                        )
                    )
                }
                .block()
                ?: AddVehicleResponsiblePersonResult(
                    success = false,
                    error =
                        "No result returned when trying to add user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] to repairfix.",
                    errorType = ErrorType.TECHNICAL,
                )

        if (result.success) {
            log.info(
                "Successfully added user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] to repairfix."
            )
        } else {
            log.warn(
                "Error while trying to add user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] to repairfix. ${result.error}"
            )
        }
        return result
    }

    override fun addFleetManager(
        fleetManagerAndFleetIds: Pair<VehicleResponsiblePerson, Set<Fleet>>
    ): AddVehicleResponsiblePersonResult {
        log.info(
            "Adding fleet manager with userPartnerId [${fleetManagerAndFleetIds.first.employeeNumber.value}] to repairfix."
        )
        val result =
            userApi
                .porscheUsersControllerAdd(listOf(fleetManagerAndFleetIds.toCreateUserDto()))
                .map { operationResult -> AddVehicleResponsiblePersonResult(success = operationResult.success) }
                .onErrorResume(RepairfixException::class.java) { repairfixException ->
                    Mono.just(
                        AddVehicleResponsiblePersonResult(
                            success = false,
                            error = repairfixException.message,
                            errorType = repairfixException.type.toErrorType(),
                        )
                    )
                }
                .block()
                ?: AddVehicleResponsiblePersonResult(
                    success = false,
                    error =
                        "No result returned when trying to add fleet manager with userPartnerId [${fleetManagerAndFleetIds.first.employeeNumber.value}] to repairfix.",
                    errorType = ErrorType.TECHNICAL,
                )

        if (result.success) {
            log.info(
                "Successfully added fleet manager with userPartnerId [${fleetManagerAndFleetIds.first.employeeNumber.value}] to repairfix."
            )
        } else {
            log.warn(
                "Error while trying to add fleet manager with userPartnerId [${fleetManagerAndFleetIds.first.employeeNumber.value}] to repairfix. ${result.error}"
            )
        }
        return result
    }

    override fun updateVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson
    ): UpdateVehicleResponsiblePersonResult {
        log.info("Updating user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] in repairfix.")
        val result =
            userApi
                .porscheUsersControllerUpdate(
                    userPartnerId = vehicleResponsiblePerson.employeeNumber.value,
                    updateUserDto = vehicleResponsiblePerson.toUpdateUserDto(),
                )
                .map { _ -> UpdateVehicleResponsiblePersonResult(success = true) }
                .onErrorResume(RepairfixException::class.java) { repairfixException ->
                    Mono.just(
                        UpdateVehicleResponsiblePersonResult(
                            success = false,
                            error = repairfixException.message,
                            errorType = repairfixException.type.toErrorType(),
                        )
                    )
                }
                .block()
                ?: UpdateVehicleResponsiblePersonResult(
                    success = false,
                    error =
                        "No result returned when trying to update user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] in repairfix.",
                    errorType = ErrorType.TECHNICAL,
                )

        if (result.success) {
            log.info(
                "Successfully updated user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] in repairfix."
            )
        } else {
            log.warn(
                "Error while trying to update user with userPartnerId [${vehicleResponsiblePerson.employeeNumber.value}] in repairfix. ${result.error}"
            )
        }
        return result
    }

    override fun updateFleetManager(
        fleetManagerAndFleet: Pair<VehicleResponsiblePerson, Set<Fleet>>
    ): UpdateVehicleResponsiblePersonResult {
        log.info(
            "Updating fleet manager with userPartnerId [${fleetManagerAndFleet.first.employeeNumber.value}] in repairfix."
        )
        val result =
            userApi
                .porscheUsersControllerUpdate(
                    userPartnerId = fleetManagerAndFleet.first.employeeNumber.value,
                    updateUserDto = fleetManagerAndFleet.toUpdateUserDto(),
                )
                .map { _ -> UpdateVehicleResponsiblePersonResult(success = true) }
                .onErrorResume(RepairfixException::class.java) { repairfixException ->
                    Mono.just(
                        UpdateVehicleResponsiblePersonResult(
                            success = false,
                            error = repairfixException.message,
                            errorType = repairfixException.type.toErrorType(),
                        )
                    )
                }
                .block()
                ?: UpdateVehicleResponsiblePersonResult(
                    success = false,
                    error =
                        "No result returned when trying to update fleet manager with userPartnerId [${fleetManagerAndFleet.first.employeeNumber.value}] in repairfix.",
                    errorType = ErrorType.TECHNICAL,
                )

        if (result.success) {
            log.info(
                "Successfully updated fleet manager with userPartnerId [${fleetManagerAndFleet.first.employeeNumber.value}] in repairfix."
            )
        } else {
            log.warn(
                "Error while trying to update fleet manager with userPartnerId [${fleetManagerAndFleet.first.employeeNumber.value}] in repairfix. ${result.error}"
            )
        }
        return result
    }

    companion object {
        private val log = LoggerFactory.getLogger(RepairfixUsersAdapter::class.java)
    }
}
