/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CreateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.User
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.VisibilityScopeDto
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson

fun VehicleResponsiblePerson.toCreateUserDto(): CreateUserDto =
    CreateUserDto(
        userPartnerId = this.employeeNumber.value,
        firstName = this.firstName,
        lastName = this.lastName,
        email = this.email,
        role = CreateUserDto.Role.driver,
    )

fun Pair<VehicleResponsiblePerson, Set<Fleet>>.toCreateUserDto(): CreateUserDto =
    CreateUserDto(
        userPartnerId = this.first.employeeNumber.value,
        firstName = this.first.firstName,
        lastName = this.first.lastName,
        email = this.first.email,
        role = CreateUserDto.Role.local_fleet_manager,
        visibilityScope =
            if (this.second.isEmpty()) null else this.second.map { VisibilityScopeDto(fleetPartnerId = it.id) },
    )

fun VehicleResponsiblePerson.toUpdateUserDto(): UpdateUserDto =
    UpdateUserDto(
        userPartnerId = this.employeeNumber.value,
        firstName = this.firstName,
        lastName = this.lastName,
        email = this.email,
        role = UpdateUserDto.Role.driver,
        visibilityScope = listOf(VisibilityScopeDto(fleetPartnerId = null, location = null)),
    )

fun Pair<VehicleResponsiblePerson, Set<Fleet>>.toUpdateUserDto(): UpdateUserDto =
    UpdateUserDto(
        userPartnerId = this.first.employeeNumber.value,
        firstName = this.first.firstName,
        lastName = this.first.lastName,
        email = this.first.email,
        // do not downgrade to local fleet manager if this user was originally a global fleet manager
        role =
            if (this.first.isGlobalFleetManager) UpdateUserDto.Role.fleet_manager
            else UpdateUserDto.Role.local_fleet_manager,
        /* be aware, that current serializer for repairfix does not send null-values (as we ran into issues when using explicit null for filter)
         * so using null here might "not work" as expected
         */
        visibilityScope =
            if (this.second.isEmpty()) null else this.second.map { VisibilityScopeDto(fleetPartnerId = it.id) },
    )

fun User.toVehicleResponsiblePersonAndFleetIds(): Pair<VehicleResponsiblePerson, Set<String>> =
    Pair(
        VehicleResponsiblePerson(
                employeeNumber = EmployeeNumber(requireNotNull(this.userPartnerId) { "User is missing partnerId." }),
                email = this.email,
                firstName = requireNotNull(this.firstName) { "User is missing first name." },
                lastName = requireNotNull(this.lastName) { "User is missing last name." },
                isFleetManager = setOf(User.Role.local_fleet_manager, User.Role.fleet_manager).contains(this.role),
            )
            .apply {
                updateGlobalFleetManager(
                    isGlobalFleetManager = User.Role.fleet_manager == <EMAIL>
                )
            },
        visibilityScope?.mapNotNull { it.fleetPartnerId }?.toSet() ?: emptySet(),
    )
