/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AddCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarInsuranceDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarInsuranceDto
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.LicensePlate
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import java.time.format.DateTimeFormatter

fun Vehicle.toAddCarDto(): AddCarDto =
    AddCarDto(
        vin = this.vin,
        carPartnerId = this.vGuid,
        make = this.manufacturer,
        model = this.model,
        isPool = this.isPoolVehicle,
        licensePlate = this.licensePlate?.value?.ifBlank { null },
        /**
         * 2024-02-07 motum/repairfix told us, that fleetIdentifier can be either repairfix-internal fleetId or
         * fleetPartnerId. So we will use fleetPartnerId (fleetId within our domain), which saves us some calls and
         * mapping.
         *
         * FPT1-1331 do not set NONE fleet (as it is unknown in repairfix)
         */
        fleetIdentifier = if (Fleet.NO_FLEET == this.fleet) null else this.fleet.id,
        /**
         * 2024-02-07 motum/repairfix told us, that driver can be either repairfix-internal userId or userPartnerId. So
         * we will use userPartnerId (employeeNumber within our domain), which saves us some calls and mapping.
         */
        driver = this.responsiblePerson?.value,
        firstRegistration = this.firstRegistrationDate?.format(FIRST_REGISTRATION_DATE_OUTPUT_FORMATTER),
        insurance =
            CarInsuranceDto(
                /**
                 * FPT1-514 With the changes in fleetId for pool vehicles we lost access to leasingArt for leasing
                 * vehicles. This is a problem downstream for PVCC, as this information is required there. As
                 * VehicleService is not ready, yet, we have to find a place within repairfix to store and later
                 * retrieve leasingArt value. We are misusing insuranceBroker for now, as no proper field is available
                 * for this purpose.
                 */
                insuranceBroker = "$INSURANCE_BROKER_LEASING_PREFIX${this.leasingArt.id}",
                insuranceCompany = INSURANCE_COMPANY_DEFAULT,
                costCenter = this.depreciationRelevantCostCenter,
                /**
                 * FPT1-986 PACE requires additional using cost center information. As we have no proper place for this
                 * information within repairfix we, once again, misuse and existing, unused property of the insurance to
                 * save this information in repairfix and fetch it again later, during damage file migration.
                 *
                 * All of this will no longer be necessary, once VehicleService is able to provide vehicle data (and
                 * updates) to DamageManagementService.
                 *
                 * FPT1-1151 switched to address, as phone got validation from repairfix and is no longer working
                 */
                insurerAddress = this.usingCostCenter?.let { "$INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX${it}" },
            ),
    )

fun Car.toVehicle(): Vehicle =
    Vehicle(
            vin = this.vin,
            vGuid = this.carPartnerId,
            model = this.model,
            licensePlate = LicensePlate(this.licensePlate ?: ""),
            // as we did not migrate fleet description we go without here
            fleet = Fleet(id = requireNotNull(this.fleetPartnerId) { "FleetId is missing." }, description = null),
            responsiblePerson = this.driver?.partnerId?.let { EmployeeNumber(it) },
            /**
             * For some unique reason we can get a list of insurances back from repairfix, but can only provide one
             * during onboarding.
             */
            depreciationRelevantCostCenter =
                this.insurances?.firstOrNull()?.costCenter
                    ?: throw IllegalArgumentException("Depreciation relevant CostCenter is missing"),
            /**
             * FPT1-514 With the changes in fleetId for pool vehicles we lost access to leasingArt for leasing vehicles.
             * This is a problem downstream for PVCC, as this information is required there. As VehicleService is not
             * ready yet, we have to find a place within repairfix to store and later retrieve leasingArt value. We are
             * misusing insuranceBroker for now, as no proper field is available for this purpose.
             */
            leasingArt =
                this.insurances?.firstOrNull()?.insuranceBroker?.replace(INSURANCE_BROKER_LEASING_PREFIX, "")?.let {
                    LeasingArt.asLeasingArt(it)
                } ?: throw IllegalArgumentException("LeasingArt is missing."),
            isActive = this.isActive,
            /**
             * FPT1-986 PACE requires additional using cost center information. As we have no proper place for this
             * information within repairfix we, once again, misuse and existing, unused property of the insurance to
             * save this information in repairfix and fetch it again later, during damage file migration.
             *
             * All of this will no longer be necessary, once VehicleService is able to provide vehicle data (and
             * updates) to DamageManagementService.
             *
             * FPT1-1151 switched to address, as phone got validation from repairfix and is no longer working
             */
            usingCostCenter =
                this.insurances?.firstOrNull()?.insurerAddress?.replace(INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX, ""),
            manufacturer = this.make,
            firstRegistrationDate = this.firstRegistration,
        )
        .apply { this.updateExternalId(<EMAIL>) }

/**
 * @param deactivate special flag to indicate, that we want to 'deactivate' a vehicle (only set in context of FTP1-530
 *   deactivation of vehicles not in Excel)
 */
fun Vehicle.toUpdateCarDto(deactivate: Boolean = false): UpdateCarDto =
    /**
     * FPT1-543 UpdateCarDto is now using a custom serializer, to ensure that we are only serializing the values that we
     * are actually using/providing and also ensuring that we are sending explicit null-values for the properties we
     * explicitly want to null (like licensePlate).
     *
     * So if you are changing UpdateDto here, or any of the values ALWAYS have an additional look into
     * 'com.emh.damagemanagement.repairfix.adapter.config.UpdateCarDtoSerializer' to match your changes.
     */
    UpdateCarDto(
        vin = this.vin,
        carPartnerId = this.vGuid,
        isPool = this.isPoolVehicle,
        licensePlate = this.licensePlate?.value,
        // FPT1-1331 do not set NONE fleet (as it is unknown in repairfix)
        fleetIdentifier = if (Fleet.NO_FLEET == this.fleet) null else this.fleet.id,
        driver = this.responsiblePerson?.value,
        isActive = if (deactivate) false else this.isActive,
        insurance =
            UpdateCarInsuranceDto(
                /**
                 * FPT1-514 With the changes in fleetId for pool vehicles we lost access to leasingArt for leasing
                 * vehicles. This is a problem downstream for PVCC, as this information is required there. As
                 * VehicleService is not ready yet, we have to find a place within repairfix to store and later retrieve
                 * leasingArt value. We are misusing insuranceBroker for now, as no proper field is available for this
                 * purpose.
                 */
                insuranceBroker = "$INSURANCE_BROKER_LEASING_PREFIX${this.leasingArt.id}",
                insuranceCompany = INSURANCE_COMPANY_DEFAULT,
                costCenter = this.depreciationRelevantCostCenter,
                // FPT1-1151 phone stopped working, when repairfix implemented validation
                insurerAddress = "$INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX${this.usingCostCenter}",
            ),
    )

fun List<Vehicle>.toAddCarDtos(): List<AddCarDto> = this.map { it.toAddCarDto() }.toList()

/**
 * There is a difference in insurance providers for PAG and MHP Leasing, but the differentiation can (no longer) not be
 * made based on LeasingArt.
 *
 * 2024-07-22 Luisa: The default insurance name to be used until we can get something "better" or a dedicated value can
 * be provided by excel/vehicle-provider.
 *
 * 2025-01-20 | FPT1-772 proper value provided
 */
private const val INSURANCE_COMPANY_DEFAULT = "Allianz SE"

/** Custom prefix to be able to use insuranceBroker field, which requires 3 or more characters */
private const val INSURANCE_BROKER_LEASING_PREFIX = "LeasingArt:"
private const val INSURANCE_ADDRESS_USING_COST_CENTER_PREFIX = "UsingCostCenter:"
/** Custom formatter for first registration date, repairfix API requires format dd-MM-yyyy for that field */
private val FIRST_REGISTRATION_DATE_OUTPUT_FORMATTER = DateTimeFormatter.ofPattern("dd-MM-yyyy")
