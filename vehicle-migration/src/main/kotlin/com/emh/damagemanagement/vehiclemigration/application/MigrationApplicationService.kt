/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.application

import com.emh.damagemanagement.vehiclemigration.application.fleet.FleetMigrationService
import com.emh.damagemanagement.vehiclemigration.application.port.FleetMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.MigrateVehicleDataUseCase
import com.emh.damagemanagement.vehiclemigration.application.port.MigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.combineWith
import com.emh.damagemanagement.vehiclemigration.application.port.toMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.vehicle.VehicleDeactivationService
import com.emh.damagemanagement.vehiclemigration.application.vehicle.VehicleMigrationService
import com.emh.damagemanagement.vehiclemigration.application.vehicleresponsible.VehicleResponsiblePersonMigrationService
import com.emh.damagemanagement.vehiclemigration.domain.*
import org.slf4j.LoggerFactory
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
class MigrationApplicationService(
    private val fleetMigrationService: FleetMigrationService,
    private val vehicleDeactivationService: VehicleDeactivationService,
    private val vehicleMigrationService: VehicleMigrationService,
    private val vehicleResponsiblePersonMigrationService: VehicleResponsiblePersonMigrationService,
) : MigrateVehicleDataUseCase {
    override fun migrateVehicleData(vehicleMigrationData: VehicleMigrationData): MigrationResult {
        log.info("Received DMS Vehicle Migration message from Kafka ${vehicleMigrationData.vehicle.vin}")

        /** Check if the vehicle needs to be deactivated. If so, deactivate and skip further migration. */
        if (FleetInformation.NONE == vehicleMigrationData.fleetInformation) {
            log.info("Vehicle with vin [${vehicleMigrationData.vehicle.vin}] is marked inactive. Deactivating...")
            return vehicleDeactivationService
                .deactivateVehicle(vehicleToBeDeactivated = vehicleMigrationData.vehicle)
                .toMigrationResult()
        }

        if (null == vehicleMigrationData.vehicleResponsiblePerson) {
            log.warn(
                "No vehicle responsible person provided for non-deactivation migration. vehicle : [${vehicleMigrationData.vehicle.vin}]"
            )
            return MigrationResult(
                success = false,
                errors = setOf("Vehicle responsible person is missing."),
                errorType = setOf(ErrorType.BUSINESS),
            )
        }

        /** if we have a pool vehicle, ensure that we have a matching pool-fleet on repairfix side */
        val isPoolVehicleMigration = vehicleMigrationData.vehicle.isPoolVehicle
        var fleetMigrationResult: FleetMigrationResult? = null
        if (isPoolVehicleMigration) {
            fleetMigrationResult = fleetMigrationService.migratePoolFleet(vehicleMigrationData.vehicle.fleet)
        }

        /**
         * Migrate vehicle-responsible person or fleet-manager. Has to be done first, otherwise drivers cannot be
         * properly set during vehicle migration
         */
        val vehicleResponsiblePersonMigrationResult =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                vehicleResponsiblePerson = vehicleMigrationData.vehicleResponsiblePerson,
                fleet = vehicleMigrationData.vehicle.fleet,
            )

        /** migrate vehicle */
        val vehicleMigrationResult = vehicleMigrationService.migrateVehicle(vehicleMigrationData = vehicleMigrationData)

        return if (fleetMigrationResult == null) {
            vehicleMigrationResult.combineWith(vehicleResponsiblePersonMigrationResult)
        } else {
            vehicleMigrationResult.combineWith(vehicleResponsiblePersonMigrationResult, fleetMigrationResult)
        }
    }

    /** migrates all managed fleets - leasing, pre-usage and post-usage fleets */
    @EventListener(ApplicationReadyEvent::class)
    fun migrateManagedFleets() {
        log.info("Starting managed fleets migration.")
        try {
            val managedFleetsAlreadyMigrated = fleetMigrationService.allManagedFleetsHaveBeenMigrated()
            if (!managedFleetsAlreadyMigrated) {
                log.info("Starting managed fleet migration.")
                fleetMigrationService.migrateManagedFleets()
                log.info("Finished managed fleet migration.")
            } else {
                log.info("All managed fleets have already been migrated. Skipping fleet migration.")
            }
        } catch (exception: FleetMigrationException) {
            log.warn("Fleet migration failed.", exception)
        } catch (exception: VehicleMigrationException) {
            // this will happen, if repairfix is not reachable or on DEV as wiremock is not started yet
            log.warn("Fleet migration failed.", exception)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(MigrationApplicationService::class.java)
    }
}

data class VehicleMigrationData(
    val vehicle: Vehicle,
    val vehicleResponsiblePerson: VehicleResponsiblePerson?,
    val fleetInformation: FleetInformation,
)
