/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application

class AddFleetFailedException(val fleetId: String, cause: Throwable?) : RuntimeException(cause)

class VehicleMigrationException(
    val canRetry: <PERSON><PERSON>an,
    @Suppress("unused") val errorType: ErrorType,
    message: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

class VehicleResponsiblePersonMigrationException(
    val canRetry: <PERSON>olean,
    val errorType: ErrorType,
    message: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

enum class ErrorType {
    BUSINESS,
    TECHNICAL,
}

class FleetMigrationException(val errorType: ErrorType, message: String? = null, cause: Throwable? = null) :
    RuntimeException(message, cause)
