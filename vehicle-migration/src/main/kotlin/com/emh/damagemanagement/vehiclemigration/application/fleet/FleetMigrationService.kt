/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.fleet

import com.emh.damagemanagement.vehiclemigration.application.port.FleetMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.FleetOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.toFleetMigrationResult
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class FleetMigrationService(private val fleetOutPort: FleetOutPort) {

    /** migrates all managed fleets. These include all leasing fleets, as well as pre- and post-usage fleets. */
    fun migrateManagedFleets() {
        log.info("Migrating leasing fleets.")
        fleetOutPort.migrateLeasingFleets(fleetsToBeMigrated = Fleet.leasingFleets)

        log.info("Migrating pre-/post-usage fleets.")
        fleetOutPort.migratePoolFleets(fleetsToBeMigrated = setOf(Fleet.PRE_USAGE_FLEET, Fleet.POST_USAGE_FLEET))

        log.info("Finished migrating fleets.")
    }

    /** migrates pool fleet */
    fun migratePoolFleet(poolFleet: Fleet): FleetMigrationResult {
        log.info("Migrating pool fleet. Fleet: [${poolFleet.id}]")
        val fleetMigrationResult = fleetOutPort.migratePoolFleet(fleetToBeMigrated = poolFleet).toFleetMigrationResult()
        if (fleetMigrationResult.success) {
            log.info("Finished migrating pool fleet. Fleet: [${poolFleet.id}]")
        } else {
            log.info("Could not migrate pool fleet. Fleet: [${poolFleet.id}], error: ${fleetMigrationResult.error}")
        }
        return fleetMigrationResult
    }

    /**
     * Checks if [Fleet.managedFleets] are already migrate to 3rd party system
     *
     * @return return true if all fleets have been migrated successfully.
     */
    fun allManagedFleetsHaveBeenMigrated(): Boolean = fleetOutPort.allFleetsExist(Fleet.managedFleets)

    companion object {
        private val log = LoggerFactory.getLogger(FleetMigrationService::class.java)
    }
}
