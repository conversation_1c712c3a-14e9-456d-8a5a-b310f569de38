/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.port

import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.domain.Fleet

interface FleetOutPort {

    /** Will migrate given list of managed fleets. Only missing fleets will be created. No update will be performed. */
    fun migrateLeasingFleets(fleetsToBeMigrated: Set<Fleet>): AddFleetResult

    /** Will migrate given list of pool fleets. Only missing fleets will be created. No update will be performed. */
    fun migratePoolFleets(fleetsToBeMigrated: Set<Fleet>): AddFleetResult

    /** Will migrate given pool fleet. Only missing fleets will be created. No update will be performed. */
    fun migratePoolFleet(fleetToBeMigrated: Fleet): AddFleetResult

    /** Will check if all provided fleets exist in 3rd party system. */
    fun allFleetsExist(fleetsToCompare: Set<Fleet>): Boolean
}

data class AddFleetResult(val success: Boolean, val error: String? = null, var errorType: ErrorType? = null)

data class FleetMigrationResult(val success: Boolean, val error: String? = null, var errorType: ErrorType? = null)

fun AddFleetResult.toFleetMigrationResult(): FleetMigrationResult =
    FleetMigrationResult(success = this.success, error = this.error, errorType = this.errorType)
