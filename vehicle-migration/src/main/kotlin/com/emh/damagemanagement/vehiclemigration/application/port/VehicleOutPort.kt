/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.port

import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle

interface VehicleOutPort {

    fun addVehicle(vehicle: Vehicle): AddVehicleResult

    fun findVehicle(vin: String): Vehicle?

    fun updateVehicle(vehicle: Vehicle): UpdateVehicleResult

    fun deactivateVehicle(vehicle: Vehicle): UpdateVehicleResult
}

data class VehicleMigrationResult(val success: Boolean, val error: String? = null, var errorType: ErrorType? = null)

data class AddVehicleResult(val success: Boolean, val error: String? = null, var errorType: ErrorType? = null)

fun AddVehicleResult.toVehicleMigrationResult() =
    VehicleMigrationResult(success = this.success, error = this.error, errorType = this.errorType)

data class UpdateVehicleResult(val success: Boolean, val error: String? = null, var errorType: ErrorType? = null)

fun UpdateVehicleResult.toVehicleMigrationResult() =
    VehicleMigrationResult(success = this.success, error = this.error, errorType = this.errorType)
