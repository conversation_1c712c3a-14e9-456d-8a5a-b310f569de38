/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.port

import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson

interface VehicleResponsiblePersonOutPort {

    fun findVehicleResponsiblePerson(employeeNumber: String): Pair<VehicleResponsiblePerson, Set<String>>?

    fun addFleetManager(
        fleetManagerAndFleetIds: Pair<VehicleResponsiblePerson, Set<Fleet>>
    ): AddVehicleResponsiblePersonResult

    fun addVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson
    ): AddVehicleResponsiblePersonResult

    fun updateVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson
    ): UpdateVehicleResponsiblePersonResult

    fun updateFleetManager(
        fleetManagerAndFleet: Pair<VehicleResponsiblePerson, Set<Fleet>>
    ): UpdateVehicleResponsiblePersonResult
}

data class VehicleResponsiblePersonMigrationResult(
    val success: Boolean,
    val error: String? = null,
    var errorType: ErrorType? = null,
)

data class MigrationResult(
    val success: Boolean,
    val errors: Set<String> = emptySet(),
    val errorType: Set<ErrorType> = emptySet(),
) {
    val canRetry: Boolean = !this.success && this.errorType.contains(ErrorType.TECHNICAL)
}

data class AddVehicleResponsiblePersonResult(
    val success: Boolean,
    val error: String? = null,
    var errorType: ErrorType? = null,
)

fun AddVehicleResponsiblePersonResult.toVehicleResponsiblePersonMigrationResult() =
    VehicleResponsiblePersonMigrationResult(success = this.success, error = this.error, errorType = this.errorType)

data class UpdateVehicleResponsiblePersonResult(
    val success: Boolean,
    val error: String? = null,
    var errorType: ErrorType? = null,
)

fun UpdateVehicleResponsiblePersonResult.toVehicleResponsiblePersonMigrationResult() =
    VehicleResponsiblePersonMigrationResult(success = this.success, error = this.error, errorType = this.errorType)

fun VehicleMigrationResult.combineWith(
    vehicleResponsiblePersonMigrationResult: VehicleResponsiblePersonMigrationResult
): MigrationResult =
    MigrationResult(
        success = this.success.and(vehicleResponsiblePersonMigrationResult.success),
        errors = setOfNotNull(this.error, vehicleResponsiblePersonMigrationResult.error),
        errorType = setOfNotNull(this.errorType, vehicleResponsiblePersonMigrationResult.errorType),
    )

fun VehicleMigrationResult.combineWith(
    vehicleResponsiblePersonMigrationResult: VehicleResponsiblePersonMigrationResult,
    fleetMigrationResult: FleetMigrationResult,
): MigrationResult =
    MigrationResult(
        success = this.success.and(vehicleResponsiblePersonMigrationResult.success).and(fleetMigrationResult.success),
        errors = setOfNotNull(this.error, vehicleResponsiblePersonMigrationResult.error, fleetMigrationResult.error),
        errorType =
            setOfNotNull(
                this.errorType,
                vehicleResponsiblePersonMigrationResult.errorType,
                fleetMigrationResult.errorType,
            ),
    )

fun VehicleMigrationResult.toMigrationResult() =
    MigrationResult(success = this.success, errors = setOfNotNull(this.error), errorType = setOfNotNull(this.errorType))
