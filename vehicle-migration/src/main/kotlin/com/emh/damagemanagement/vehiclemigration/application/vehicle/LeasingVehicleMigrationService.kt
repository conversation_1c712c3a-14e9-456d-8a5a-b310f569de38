/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.toVehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class LeasingVehicleMigrationService(private val vehicleOutPort: VehicleOutPort) {

    fun migrateLeasingVehicle(leasingVehicle: Vehicle, existingVehicle: Vehicle?): VehicleMigrationResult {
        log.info("Starting migration of leasing vehicle with vin [${leasingVehicle.vin}]...")

        val migrationResult =
            // add a new leasing vehicle
            if (null == existingVehicle) {
                return addNewLeasingVehicle(leasingVehicle = leasingVehicle).toVehicleMigrationResult()
            } else if (vehicleMustBeUpdated(vehicleToBeMigrated = leasingVehicle, existingVehicle = existingVehicle)) {
                // update existing leasing vehicle if changes are detected
                updateLeasingVehicle(leasingVehicle = leasingVehicle).toVehicleMigrationResult()
            } else {
                // the vehicle does not need to be updated
                log.info("Vehicle with vin [${leasingVehicle.vin}] does not need to be updated.")
                VehicleMigrationResult(success = true)
            }

        log.info("Successfully finished migration of vehicle with vin [${leasingVehicle.vin}].")
        return migrationResult
    }

    private fun addNewLeasingVehicle(leasingVehicle: Vehicle): AddVehicleResult {
        return vehicleOutPort.addVehicle(leasingVehicle)
    }

    private fun updateLeasingVehicle(leasingVehicle: Vehicle): UpdateVehicleResult {
        return vehicleOutPort.updateVehicle(vehicle = leasingVehicle)
    }

    /** Checks if given vehicle needs to be updated. */
    private fun vehicleMustBeUpdated(vehicleToBeMigrated: Vehicle, existingVehicle: Vehicle): Boolean {
        val vehicleToBeMigratedHash = buildComparisonHash(vehicleToBeMigrated)
        val existingVehicleHash = buildComparisonHash(existingVehicle)
        return vehicleToBeMigratedHash != existingVehicleHash
    }

    private fun buildComparisonHash(vehicle: Vehicle) =
        /**
         * We are using .lower() to account for repairfix sided transformation of our inputs (which is at least done to
         * emails)
         */
        Objects.hash(
            vehicle.licensePlate?.value?.lowercase().orEmpty(),
            vehicle.responsiblePerson?.value.orEmpty(),
            vehicle.fleet.id.lowercase(),
            vehicle.depreciationRelevantCostCenter.lowercase(),
            vehicle.usingCostCenter?.lowercase().orEmpty(),
            vehicle.leasingArt.id.lowercase(),
        )

    companion object {
        private val log = LoggerFactory.getLogger(LeasingVehicleMigrationService::class.java)
    }
}
