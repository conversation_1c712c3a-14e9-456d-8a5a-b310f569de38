/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.toVehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class PoolVehicleMigrationService(private val vehicleOutPort: VehicleOutPort) {

    fun migratePoolVehicle(poolVehicle: Vehicle, existingVehicle: Vehicle?): VehicleMigrationResult {
        log.info("Starting migration of pool vehicle with vin [${poolVehicle.vin}]...")

        val migrationResult =
            // add a new pool vehicle
            if (null == existingVehicle) {
                return addNewPoolVehicle(poolVehicle = poolVehicle).toVehicleMigrationResult()
            } else if (vehicleMustBeUpdated(vehicleToBeMigrated = poolVehicle, existingVehicle = existingVehicle)) {
                // update existing pool vehicle if changes are detected
                updatePoolVehicle(poolVehicle = poolVehicle).toVehicleMigrationResult()
            } else {
                // the vehicle does not need to be updated
                log.info("Vehicle with vin [${poolVehicle.vin}] does not need to be updated.")
                VehicleMigrationResult(success = true)
            }
        log.info("Successfully finished migration of pool with vin [${poolVehicle.vin}].")
        return migrationResult
    }

    private fun addNewPoolVehicle(poolVehicle: Vehicle): AddVehicleResult {
        return vehicleOutPort.addVehicle(poolVehicle)
    }

    private fun updatePoolVehicle(poolVehicle: Vehicle): UpdateVehicleResult {
        return vehicleOutPort.updateVehicle(vehicle = poolVehicle)
    }

    /**
     * Checks if given vehicle needs to be updated. VehicleResponsiblePerson will not be compared (as opposed to leasing
     * vehicle migration), as no information is maintained for pool vehicles in repairfix.
     */
    private fun vehicleMustBeUpdated(vehicleToBeMigrated: Vehicle, existingVehicle: Vehicle): Boolean {
        val vehicleToBeMigratedHash = buildComparisonHash(vehicleToBeMigrated)
        val existingVehicleHash = buildComparisonHash(existingVehicle)
        return vehicleToBeMigratedHash != existingVehicleHash
    }

    private fun buildComparisonHash(vehicle: Vehicle) =
        /**
         * We are using .lower() to account for repairfix sided transformation of our inputs (which is at least done to
         * emails)
         */
        Objects.hash(
            vehicle.licensePlate?.value?.lowercase().orEmpty(),
            vehicle.fleet.id.lowercase(),
            vehicle.depreciationRelevantCostCenter.lowercase(),
            vehicle.usingCostCenter?.lowercase().orEmpty(),
            vehicle.leasingArt.id.lowercase(),
        )

    companion object {
        private val log = LoggerFactory.getLogger(PoolVehicleMigrationService::class.java)
    }
}
