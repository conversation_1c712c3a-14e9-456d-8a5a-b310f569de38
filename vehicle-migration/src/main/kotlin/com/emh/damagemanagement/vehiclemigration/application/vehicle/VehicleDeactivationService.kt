/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.application.port.VehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.toVehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/** A service that deals with the deactivation of vehicles, that we consider to be 'inactive'. */
@Component
class VehicleDeactivationService(private val vehicleOutPort: VehicleOutPort) {

    fun deactivateVehicle(vehicleToBeDeactivated: Vehicle): VehicleMigrationResult {
        log.info("Starting deactivation of vehicle with vin [${vehicleToBeDeactivated.vin}]...")

        // fetch vehicle from repairfix first (we need repairfixId)
        val existingVehicle = vehicleOutPort.findVehicle(vehicleToBeDeactivated.vin)

        if (null == existingVehicle) {
            log.info(
                "Vehicle with vin [${vehicleToBeDeactivated.vin}] does not exist in repairfix, so it does not need to be deactivated. Skipping deactivation."
            )
            return VehicleMigrationResult(success = true)
        }

        // set externalId to properly perform deactivation
        vehicleToBeDeactivated.updateExternalId(existingVehicle.externalId)

        val deactivationResult = vehicleOutPort.deactivateVehicle(vehicleToBeDeactivated).toVehicleMigrationResult()

        if (deactivationResult.success) {
            log.info("Successfully finished deactivation of vehicle with vin [${vehicleToBeDeactivated.vin}].")
        } else {
            log.info("Finished deactivation of vehicle with vin [${vehicleToBeDeactivated.vin}].")
        }

        return deactivationResult
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleDeactivationService::class.java)
    }
}
