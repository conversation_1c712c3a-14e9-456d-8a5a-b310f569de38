/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

/** Entry point for migration of vehicles to repairfix. */
@Component
class VehicleMigrationService(
    private val poolVehicleMigrationService: PoolVehicleMigrationService,
    private val leasingVehicleMigrationService: LeasingVehicleMigrationService,
    private val vehicleOutPort: VehicleOutPort,
) {

    fun migrateVehicle(vehicleMigrationData: VehicleMigrationData): VehicleMigrationResult {
        log.info("Starting migration for vehicle with vin [${vehicleMigrationData.vehicle.vin}]...")

        /**
         * Determine if we are dealing with a leasing or pool vehicle and call the respective service to handle the
         * actual migration
         */
        val existingVehicle = vehicleOutPort.findVehicle(vin = vehicleMigrationData.vehicle.vin)

        /**
         * FPT1-526 map repairfix car id to vehicle to be used for vehicle update later as "Old" vehicles (like museum
         * cars) may have "/" in their VIN. As / is a delimiter in url paths, it cannot be used as a path parameter
         */
        val vehicleToBeMigrated =
            if (null != existingVehicle)
                vehicleMigrationData.vehicle.apply { this.updateExternalId(existingVehicle.externalId) }
            else vehicleMigrationData.vehicle

        val migrationResult =
            if (vehicleMigrationData.vehicle.isPoolVehicle) {
                poolVehicleMigrationService.migratePoolVehicle(
                    poolVehicle = vehicleToBeMigrated,
                    existingVehicle = existingVehicle,
                )
            } else {
                leasingVehicleMigrationService.migrateLeasingVehicle(
                    leasingVehicle = vehicleToBeMigrated,
                    existingVehicle = existingVehicle,
                )
            }

        log.info("Successfully finished migration for vehicle with vin [${vehicleMigrationData.vehicle.vin}].")
        return migrationResult
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleMigrationService::class.java)
    }
}
