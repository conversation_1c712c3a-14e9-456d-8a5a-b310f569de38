/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicleresponsible

import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleResponsiblePersonMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.toVehicleResponsiblePersonMigrationResult
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import java.util.*
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class VehicleResponsiblePersonMigrationService(
    private val vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort
) {

    fun migrateVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson,
        fleet: Fleet,
    ): VehicleResponsiblePersonMigrationResult {
        log.info(
            "Starting migration for vehicle responsible person with employee number [${vehicleResponsiblePerson.employeeNumber.value}] and fleet [${fleet.id}]..."
        )
        val existingVehicleResponsiblePerson =
            vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(
                employeeNumber = vehicleResponsiblePerson.employeeNumber.value
            )

        val migrationResult =
            if (vehicleResponsiblePerson.isFleetManager) {
                migrateFleetManager(
                    fleetManager = vehicleResponsiblePerson,
                    fleet = fleet,
                    existingFleetManagerAndFleetIds = existingVehicleResponsiblePerson,
                )
            } else {
                migrateVehicleResponsiblePerson(
                    vehicleResponsiblePerson = vehicleResponsiblePerson,
                    existingVehicleResponsiblePersonAndFleetIds = existingVehicleResponsiblePerson,
                )
            }

        log.info(
            "Successfully finished migration for vehicle responsible person with employee number [${vehicleResponsiblePerson.employeeNumber.value}] and fleet [${fleet.id}]..."
        )
        return migrationResult
    }

    private fun migrateVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson,
        existingVehicleResponsiblePersonAndFleetIds: Pair<VehicleResponsiblePerson, Set<String>>?,
    ): VehicleResponsiblePersonMigrationResult {

        return if (null == existingVehicleResponsiblePersonAndFleetIds) {
            addVehicleResponsiblePerson(vehicleResponsiblePerson = vehicleResponsiblePerson)
                .toVehicleResponsiblePersonMigrationResult()
        } else if (
            vehicleResponsiblePersonHasToBeUpdated(
                vehicleResponsiblePersonToBeMigrated = vehicleResponsiblePerson,
                existingVehicleResponsiblePerson = existingVehicleResponsiblePersonAndFleetIds.first,
            )
        ) {
            updateVehicleResponsiblePerson(
                    vehicleResponsiblePerson = vehicleResponsiblePerson,
                    existingVehicleResponsiblePersonAndFleetIds = existingVehicleResponsiblePersonAndFleetIds,
                )
                .toVehicleResponsiblePersonMigrationResult()
        } else {
            // the fleet manager does not need to be updated
            log.info(
                "Vehicle responsible person with employee number [${vehicleResponsiblePerson.employeeNumber.value}] does not need to be updated."
            )
            VehicleResponsiblePersonMigrationResult(success = true)
        }
    }

    private fun migrateFleetManager(
        fleetManager: VehicleResponsiblePerson,
        fleet: Fleet,
        existingFleetManagerAndFleetIds: Pair<VehicleResponsiblePerson, Set<String>>?,
    ): VehicleResponsiblePersonMigrationResult {
        /**
         * FPT1-516 pre- and post-usage fleet do not get fleet managers (or at least none that are maintained within the
         * excel). So we will ignore al person-fleet relation matching pre- or post-usage. This will exclude pre- and
         * post-usage from visibility scope and also ignore any managers that would only have visibility on pre- and/or
         * post-usage fleets.
         */
        if (setOf(Fleet.PRE_USAGE_FLEET, Fleet.POST_USAGE_FLEET).contains(fleet)) {
            log.info("Vehicle responsible person for pre- or post-usage fleets will not be migrated. Skipping.")
            return VehicleResponsiblePersonMigrationResult(success = true)
        }

        return if (null == existingFleetManagerAndFleetIds) {
            addFleetManager(fleetManager = fleetManager, fleet = fleet).toVehicleResponsiblePersonMigrationResult()
        } else if (
            fleetManagerHasToBeUpdated(
                vehicleResponsiblePersonToBeMigrated = fleetManager,
                newResponsibleFleet = fleet,
                existingVehicleResponsiblePerson = existingFleetManagerAndFleetIds,
            )
        ) {
            /**
             * If the existing fleet manager was considered a global fleet manager we have to keep this additional
             * permission intact (only relevant for fleet manager migration)
             */
            fleetManager.updateGlobalFleetManager(existingFleetManagerAndFleetIds.first.isGlobalFleetManager)
            updateFleetManager(
                    fleetManager = fleetManager,
                    fleet = fleet,
                    currentFleets = existingFleetManagerAndFleetIds.second.map { Fleet(id = it, description = null) },
                )
                .toVehicleResponsiblePersonMigrationResult()
        } else {
            // the fleet manager does not need to be updated
            log.info(
                "Fleet manager with employee number [${fleetManager.employeeNumber.value}] and fleet [${fleet.id}] does not need to be updated."
            )
            VehicleResponsiblePersonMigrationResult(success = true)
        }
    }

    private fun addFleetManager(
        fleetManager: VehicleResponsiblePerson,
        fleet: Fleet,
    ): AddVehicleResponsiblePersonResult {
        return vehicleResponsiblePersonOutPort.addFleetManager(
            fleetManagerAndFleetIds = Pair(fleetManager, setOf(fleet))
        )
    }

    private fun addVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson
    ): AddVehicleResponsiblePersonResult {
        return vehicleResponsiblePersonOutPort.addVehicleResponsiblePerson(
            vehicleResponsiblePerson = vehicleResponsiblePerson
        )
    }

    /**
     * Will update existing fleet manager and respective visibility scope. This will also "upgrade" a normal
     * vehicle-responsible person (driver) to local fleet manager.
     */
    private fun updateFleetManager(
        fleetManager: VehicleResponsiblePerson,
        fleet: Fleet?,
        currentFleets: Collection<Fleet>,
    ): UpdateVehicleResponsiblePersonResult {
        // add current fleet to existing ones if for some reason it was missing before
        val updatedFleets = (currentFleets + fleet).filterNotNull().distinctBy { it.id }.toSet()
        return vehicleResponsiblePersonOutPort.updateFleetManager(
            fleetManagerAndFleet = Pair(fleetManager, updatedFleets)
        )
    }

    /**
     * Will update existing fleet manager and respective visibility scope. This will also "upgrade" a normal
     * vehicle-responsible person (driver) to local fleet manager.
     */
    private fun updateVehicleResponsiblePerson(
        vehicleResponsiblePerson: VehicleResponsiblePerson,
        existingVehicleResponsiblePersonAndFleetIds: Pair<VehicleResponsiblePerson, Set<String>>,
    ): UpdateVehicleResponsiblePersonResult {
        /**
         * If we are updating a user, that was previously upgraded to (local) fleet manager, we cannot use "normal"
         * update, as this would downgrade him to driver. So we perform a fleetManager update, without changing fleet
         * instead.
         */
        return if (existingVehicleResponsiblePersonAndFleetIds.first.isFleetManager) {
            updateFleetManager(
                fleetManager = vehicleResponsiblePerson,
                fleet = null,
                currentFleets =
                    existingVehicleResponsiblePersonAndFleetIds.second.map { Fleet(id = it, description = null) },
            )
        } else {
            vehicleResponsiblePersonOutPort.updateVehicleResponsiblePerson(
                vehicleResponsiblePerson = vehicleResponsiblePerson
            )
        }
    }

    private fun fleetManagerHasToBeUpdated(
        vehicleResponsiblePersonToBeMigrated: VehicleResponsiblePerson,
        newResponsibleFleet: Fleet,
        existingVehicleResponsiblePerson: Pair<VehicleResponsiblePerson, Set<String>>,
    ): Boolean =
        vehicleResponsiblePersonHasToBeUpdated(
            vehicleResponsiblePersonToBeMigrated = vehicleResponsiblePersonToBeMigrated,
            existingVehicleResponsiblePerson = existingVehicleResponsiblePerson.first,
        ) ||
            responsibleFleetsChanged(
                newResponsibleFleet = newResponsibleFleet,
                currentResponsibleFleetIds = existingVehicleResponsiblePerson.second,
            )

    private fun vehicleResponsiblePersonHasToBeUpdated(
        vehicleResponsiblePersonToBeMigrated: VehicleResponsiblePerson,
        existingVehicleResponsiblePerson: VehicleResponsiblePerson,
    ): Boolean {
        /**
         * We are using .lower() to account for repairfix sided transformation of our inputs (which is at least done to
         * emails)
         */
        val vehicleResponsiblePersonToBeMigratedHash =
            Objects.hash(
                vehicleResponsiblePersonToBeMigrated.email.lowercase(),
                vehicleResponsiblePersonToBeMigrated.lastName.lowercase(),
                vehicleResponsiblePersonToBeMigrated.firstName.lowercase(),
            )
        val existingVehicleResponsiblePersonHash =
            Objects.hash(
                existingVehicleResponsiblePerson.email.lowercase(),
                existingVehicleResponsiblePerson.lastName.lowercase(),
                existingVehicleResponsiblePerson.firstName.lowercase(),
            )
        return vehicleResponsiblePersonToBeMigratedHash != existingVehicleResponsiblePersonHash
    }

    private fun responsibleFleetsChanged(newResponsibleFleet: Fleet, currentResponsibleFleetIds: Set<String>): Boolean {
        return currentResponsibleFleetIds.none { it == newResponsibleFleet.id }
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleResponsiblePersonMigrationService::class.java)
    }
}
