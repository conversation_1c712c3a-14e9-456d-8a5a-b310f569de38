/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.domain

/** This class is the internal domain representation of the fleet. */
class Fleet(id: String, description: String?) {
    var id: String = id
        private set

    var description: String? = description
        private set

    constructor(leasingArt: LeasingArt) : this(id = leasingArt.id, description = leasingArt.description)

    companion object {

        /** Returns all leasing fleets */
        val leasingFleets: Set<Fleet> = LeasingArt.leasingArts.map { it.toFleet() }.toSet()

        val managedFleets: Set<Fleet> =
            leasingFleets + setOf(FleetInformation.PRE_USAGE.toFleet(), FleetInformation.POST_USAGE.toFleet())

        val PRE_USAGE_FLEET = FleetInformation.PRE_USAGE.toFleet()

        val POST_USAGE_FLEET = FleetInformation.POST_USAGE.toFleet()
        val NO_FLEET = FleetInformation.NONE.toFleet()

        /*
         * Will determine fleet based on PrimStatus.
         * Additional information like firstName, lastName and employee number are only need when dynamically creating a pool fleet, as description uses those value.
         * This is done because excel can not supply us with a proper fleet name (as those are kept in moves)
         */
        fun determineFleet(
            fleetInformation: FleetInformation,
            leasingArt: LeasingArt,
            firstName: String?,
            lastName: String?,
            employeeNumber: String?,
        ): Fleet {
            return when (fleetInformation) {
                FleetInformation.NONE -> NO_FLEET
                FleetInformation.PRE_USAGE -> PRE_USAGE_FLEET
                FleetInformation.POST_USAGE -> POST_USAGE_FLEET
                FleetInformation.USAGE -> {
                    if (leasingArt.isPool) {
                        requireNotNull(firstName) { "First name may not be null when creating a pool fleet." }
                        requireNotNull(lastName) { "Last name may not be null when creating a pool fleet." }
                        requireNotNull(employeeNumber) { "Employee number may not be null when creating a pool fleet." }
                        Fleet(
                            id =
                                poolFleetId(
                                    firstName = firstName,
                                    lastName = lastName,
                                    employeeNumber = employeeNumber,
                                ),
                            description =
                                poolFleetDescription(
                                    firstName = firstName,
                                    lastName = lastName,
                                    employeeNumber = employeeNumber,
                                ),
                        )
                    } else {
                        leasingArt.toFleet()
                    }
                }
            }
        }

        // 2024-07-30 Luise/Elena build description lastName+firstName+employeeNumber
        private fun poolFleetDescription(firstName: String, lastName: String, employeeNumber: String) =
            "$lastName, $firstName ($employeeNumber)"

        /**
         * We are constructing a unique fleetId for pool fleets, as provided leasingArt information is not unique for
         * pool fleets.
         */
        private fun poolFleetId(firstName: String, lastName: String, employeeNumber: String) =
            "${lastName}_${firstName}_${employeeNumber}".replace(" ", "")
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as Fleet

        return id == other.id
    }

    override fun hashCode(): Int {
        return id.hashCode()
    }
}

fun LeasingArt.toFleet() = Fleet(this)

fun FleetInformation.toFleet() = Fleet(this.id, this.description)

enum class FleetInformation(val id: String, val description: String) {
    PRE_USAGE("preUsage", "Vorlauf"),
    POST_USAGE("postUsage", "Nachlauf"),
    USAGE("usage", "Usage"),
    // for inactive vehicles, fleet is NONE
    NONE("none", "None");

    companion object {
        fun of(id: String): FleetInformation =
            try {
                FleetInformation.entries.single { id.equals(it.id, true) }
            } catch (exception: java.util.NoSuchElementException) {
                throw UnknownFleetException("Fleet with id: $id is not known by the system.", exception)
            }
    }
}

class UnknownFleetException(override val message: String, override val cause: Throwable?) : RuntimeException()
