/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.domain

/**
 * Outdated concept that merges types of leasing based on vehicleUsage, vehicleType and manufacturer. We maintain this
 * information and respective mappings to support some downstream systems, like PVCC, that are unable to adapt to
 * vehicleUsage in time.
 */
enum class LeasingArt(val id: String, val description: String, val isPool: Boolean) {
    // leasing types
    L1_1("L1.1", "ÜT2 Leasing", false),
    L1_2("L1.2", "Leasing ab P11", false),
    L1_3("L1.3", "Treueleasing 25 Jahre", false),
    L1_4("L1.4", "Rentner", false),
    L2("L2", "Zweitleasing", false),
    L9_1("L9.1", "Dienstwagen", false),
    // pool leasing types
    L9_0("L9.0", "Entwicklung", true),
    L9_2("L9.2", "Abteilungsfahrzeuge", true),
    L9_3("L9.3", "Fremd Fahrzeuge", true),
    L9_5("L9.5", "LKW", true),
    L9_6("L9.6", "Anhänger/Sonderfahrzeuge", true);

    companion object {
        val poolLeasingArts = LeasingArt.entries.filter { it.isPool }
        val leasingArts = LeasingArt.entries.filterNot { it.isPool }

        fun LeasingArt.isPool(): Boolean = poolLeasingArts.any { this.id == it.id }

        fun of(id: String): LeasingArt =
            try {
                entries.single { id.equals(it.id, true) }
            } catch (exception: NoSuchElementException) {
                throw UnknownLeasingArtException("LeasingArt with id [$id] is not supported.", exception)
            }

        fun asLeasingArt(id: String): LeasingArt =
            try {
                entries.single { id.equals(it.id, true) }
            } catch (exception: NoSuchElementException) {
                throw UnknownLeasingArtException("LeasingArt with id [$id] is not supported.", exception)
            }
    }
}

// NOSONAR
class UnknownLeasingArtException(override val message: String, override val cause: Throwable?) : RuntimeException()
