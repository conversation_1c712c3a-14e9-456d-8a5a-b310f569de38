/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.domain

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fasterxml.jackson.annotation.JsonProperty
import java.time.OffsetDateTime
import java.util.*

/**
 * This class is the internal domain representation of the vehicle consumed from dms vehicle migration (vehicle-service)
 * kafka topic.
 */
class Vehicle(
    /** Use this when trying to uniquely identify a vehicle */
    val vin: String,
    /** Globally unique vehicle identifier. Required for PACE */
    @get:JsonProperty("vGuid") val vGuid: String?,
    val model: String,
    val depreciationRelevantCostCenter: String,
    val usingCostCenter: String?,

    /** a flag indicating, if the vehicle is considered 'active' */
    @get:JsonProperty("isActive") val isActive: Boolean,
    val firstRegistrationDate: OffsetDateTime?,

    /** licensePlate is optional (to accommodate active and inactive vehicles) */
    val licensePlate: LicensePlate?,
    val manufacturer: String,
    val fleet: Fleet,
    val leasingArt: LeasingArt,

    /** A reference to the [VehicleResponsiblePerson] for this vehicle, optional in case of inactive vehicles */
    responsiblePerson: EmployeeNumber?,
) {

    init {
        require(vin.isNotBlank()) { "Vin may not be blank." }
        require(model.isNotBlank()) { "Model may not be blank." }
        require(depreciationRelevantCostCenter.isNotBlank()) { "DepreciationRelevantCostCenter may not be blank." }
    }

    @get:JsonProperty("isPoolVehicle") val isPoolVehicle = leasingArt.isPool

    /**
     * A reference to the [VehicleResponsiblePerson] for this vehicle, can be null for pool vehicles and inactive
     * vehicles
     */
    val responsiblePerson: EmployeeNumber? =
        if (isPoolVehicle) null
        else if (!isActive) responsiblePerson
        else requireNotNull(responsiblePerson) { "VehicleResponsiblePerson may not be null for a leasing vehicle" }

    /** vehicle identifier in repairfix (required for vehicle update later) */
    @JsonIgnore
    lateinit var externalId: UUID
        private set

    internal fun updateExternalId(externalId: UUID) {
        this.externalId = externalId
    }
}

/** License plate wrapper to ensure proper format. */
class LicensePlate(@JsonProperty("value") licensePlate: String) {

    val value: String =
        licensePlate.removeSuffix(ELECTRIC_VEHICLE_SUFFIX).removeSuffix(HISTORIC_VEHICLE_SUFFIX).trimEnd()

    companion object {
        private val ELECTRIC_VEHICLE_SUFFIX = "E"
        private val HISTORIC_VEHICLE_SUFFIX = "H"
    }
}
