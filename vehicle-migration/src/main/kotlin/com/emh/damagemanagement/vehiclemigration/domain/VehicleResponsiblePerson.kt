/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.domain

import com.fasterxml.jackson.annotation.JsonProperty

class VehicleResponsiblePerson(
    /** Uniquely identifies a person */
    val employeeNumber: EmployeeNumber,
    val firstName: String,
    val lastName: String,
    val email: String,
    @get:JsonProperty("isFleetManager") val isFleetManager: Boolean,
) {
    /**
     * A dedicated flag to indicate, if this VehicleResponsible<PERSON>erson was manually set as a global fleet manager within
     * repairfix. This information is only obtained when fetching existing users from repairfix and will be copied to
     * the migrating vehicle responsible person to ensure, that any update does not downgrade global permissions.
     */
    @get:JsonProperty("isGlobalFleetManager")
    var isGlobalFleetManager: Boolean = false
        private set

    init {
        require(firstName.isNotBlank()) { "First name may not be blank." }
        require(lastName.isNotBlank()) { "Last name may not be blank." }
        require(email.isNotBlank()) { "Email may not be blank." }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as VehicleResponsiblePerson

        return employeeNumber == other.employeeNumber
    }

    override fun hashCode(): Int {
        return employeeNumber.hashCode()
    }

    fun updateGlobalFleetManager(isGlobalFleetManager: Boolean) {
        this.isGlobalFleetManager = isGlobalFleetManager
    }
}

class EmployeeNumber(@JsonProperty("value") employeeNumber: String) {
    /** TODO remove once we solely depend on user service (as service will ensure employee number validity */
    val value: String = employeeNumber.padStart(8, '0')

    init {
        require(8 >= employeeNumber.length) { "Employee number may not exceed 8 characters." }
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EmployeeNumber

        return value == other.value
    }

    override fun hashCode(): Int {
        return value.hashCode()
    }
}
