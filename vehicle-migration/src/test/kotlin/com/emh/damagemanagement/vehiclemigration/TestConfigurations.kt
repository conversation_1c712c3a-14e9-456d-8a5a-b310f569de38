package com.emh.damagemanagement.vehiclemigration

import com.emh.damagemanagement.vehiclemigration.application.port.FleetOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.fasterxml.jackson.databind.ObjectMapper
import org.mockito.kotlin.mock
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.SpringBootApplication
import org.springframework.boot.context.properties.ConfigurationPropertiesScan
import org.springframework.boot.runApplication
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder

@SpringBootApplication(
    scanBasePackages =
        [
            "com.emh.damagemanagement.shared.*",
            "com.emh.damagemanagement.vehiclemigration.*",
            "com.emh.damagemanagement.repairfix.*",
        ]
)
@ConfigurationPropertiesScan(
    "com.emh.damagemanagement.shared.*",
    "com.emh.damagemanagement.vehiclemigration.*",
    "com.emh.damagemanagement.repairfix.*",
)
class TestVehicleMigrationApplication {
    fun main(args: Array<String>) {
        runApplication<TestVehicleMigrationApplication>(*args)
    }
}

@TestConfiguration
class TestObjectMapperConfiguration {
    @Bean
    @Primary
    @Suppress("SpringJavaInjectionPointsAutowiringInspection")
    fun objectMapper(@Qualifier("repairfix") jackson2ObjectMapperBuilder: Jackson2ObjectMapperBuilder): ObjectMapper =
        jackson2ObjectMapperBuilder.createXmlMapper(false).build()
}

@TestConfiguration
class TestAdapterMocks {
    @Bean @Primary fun fleetOutPort(): FleetOutPort = mock()

    @Bean @Primary fun vehicleResponsiblePersonOutPort(): VehicleResponsiblePersonOutPort = mock()

    @Bean @Primary fun vehicleOutPort(): VehicleOutPort = mock()
}
