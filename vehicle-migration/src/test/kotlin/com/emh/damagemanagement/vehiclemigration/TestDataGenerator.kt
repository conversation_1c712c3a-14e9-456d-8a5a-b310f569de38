/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarDriver
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarInsurance
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.User
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.VisibilityScope
import com.emh.damagemanagement.shared.createRandomOffsetDate
import com.emh.damagemanagement.shared.createRandomString
import com.emh.damagemanagement.vehiclemigration.application.VehicleMigrationData
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.FleetInformation
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.LicensePlate
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.vehiclemigration.domain.toFleet
import java.time.OffsetDateTime
import java.util.*
import kotlin.random.Random
import net.datafaker.Faker

class VehicleBuilder {
    private val faker = Faker()

    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var depreciationRelevantCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var usingCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var vGuid: String = createRandomString(22)
    private var model: String = createRandomString(23)
    private var leasingArt: LeasingArt = LeasingArt.entries.random()
    private var isActive: Boolean = true
    private val firstRegistrationDate: OffsetDateTime = createRandomOffsetDate()
    private var manufacturer: String = createRandomString(23)
    private var externalId: UUID? = null

    private var employeeNumber: EmployeeNumber? = if (leasingArt.isPool) null else EmployeeNumber(createRandomString(6))
    private var licensePlate: LicensePlate? =
        if (Random.nextBoolean()) LicensePlate(faker.vehicle().licensePlate()) else null
    private var fleet = Fleet.PRE_USAGE_FLEET

    fun vin(vin: String) = apply { this.vin = vin }

    fun externalId(externalId: UUID?) = apply { this.externalId = externalId }

    fun employeeNumber(employeeNumber: EmployeeNumber?) = apply { this.employeeNumber = employeeNumber }

    fun depreciationRelevantCostCenter(depreciationRelevantCostCenter: String) = apply {
        this.depreciationRelevantCostCenter = depreciationRelevantCostCenter
    }

    fun usingCostCenter(usingCostCenter: String) = apply { this.usingCostCenter = usingCostCenter }

    fun licensePlate(licensePlate: String?) = apply { this.licensePlate = licensePlate?.let { LicensePlate(it) } }

    fun leasingArt(leasingArt: LeasingArt) = apply { this.leasingArt = leasingArt }

    fun fleet(fleet: Fleet) = apply { this.fleet = fleet }

    fun active(active: Boolean = true) = apply { this.isActive = active }

    fun build(): Vehicle {
        return Vehicle(
                vin = this.vin,
                vGuid = this.vGuid,
                model = this.model,
                depreciationRelevantCostCenter = this.depreciationRelevantCostCenter,
                usingCostCenter = this.usingCostCenter,
                isActive = this.isActive,
                firstRegistrationDate = this.firstRegistrationDate,
                licensePlate = this.licensePlate,
                responsiblePerson = this.employeeNumber,
                manufacturer = this.manufacturer,
                leasingArt = leasingArt,
                fleet = fleet,
            )
            .apply { <EMAIL>?.also { updateExternalId(it) } }
    }

    companion object {
        fun buildSingle() = VehicleBuilder().build()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<Vehicle> = (1..count).map { VehicleBuilder().build() }.toSet()
    }
}

class VehicleMigrationDataBuilder {
    private val faker = Faker()
    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var vguid: String = createRandomString(Random.nextInt(4, 23))
    private var model: String = createRandomString(Random.nextInt(4, 23))
    private var depreciationRelevantCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var usingCostCenter: String = createRandomString(Random.nextInt(4, 23))
    private var isActive: Boolean = Random.nextBoolean()
    private var firstRegistrationDate: OffsetDateTime = createRandomOffsetDate()
    private var licensePlate: LicensePlate = LicensePlate(faker.vehicle().licensePlate())
    private var responsiblePerson: EmployeeNumber = EmployeeNumber(createRandomString(8))
    private var leasingArt: LeasingArt = LeasingArt.entries.random()
    private var manufacturer: String = createRandomString(23)

    private var fleetInformation = FleetInformation.entries.random()

    private var employeeNumber = EmployeeNumber(faker.numerify("00######"))
    private var firstName = faker.name().firstName()
    private var lastName = faker.name().lastName()
    private var email = faker.internet().emailAddress()

    private var vehicleResponsiblePerson: VehicleResponsiblePerson? =
        VehicleResponsiblePerson(
            employeeNumber = this.employeeNumber,
            firstName = this.firstName,
            lastName = this.lastName,
            email = this.email,
            isFleetManager = leasingArt.isPool,
        )

    private fun buildVehicle() =
        Vehicle(
            vin = this.vin,
            vGuid = this.vguid,
            model = this.model,
            depreciationRelevantCostCenter = this.depreciationRelevantCostCenter,
            usingCostCenter = this.usingCostCenter,
            isActive = this.isActive,
            firstRegistrationDate = this.firstRegistrationDate,
            licensePlate = this.licensePlate,
            responsiblePerson = this.responsiblePerson,
            manufacturer = this.manufacturer,
            leasingArt = leasingArt,
            fleet =
                vehicleResponsiblePerson?.let {
                    Fleet.determineFleet(
                        fleetInformation = fleetInformation,
                        firstName = it.firstName,
                        lastName = it.lastName,
                        leasingArt = leasingArt,
                        employeeNumber = it.employeeNumber.value,
                    )
                } ?: leasingArt.toFleet(),
        )

    private var vehicle = buildVehicle()

    fun fleetInformation(fleetInformation: FleetInformation) = apply {
        this.fleetInformation = fleetInformation
        this.vehicle = buildVehicle()
    }

    fun leasingArt(leasingArt: LeasingArt) = apply {
        this.leasingArt = leasingArt
        this.vehicle = buildVehicle()
    }

    fun vehicle(vehicle: Vehicle) = apply { this.vehicle = vehicle }

    fun vehicleResponsiblePerson(vehicleResponsiblePerson: VehicleResponsiblePerson?) = apply {
        this.vehicleResponsiblePerson = vehicleResponsiblePerson
        this.vehicle = buildVehicle()
    }

    fun build(): VehicleMigrationData {
        return VehicleMigrationData(
            vehicle = vehicle,
            fleetInformation = this.fleetInformation,
            vehicleResponsiblePerson = this.vehicleResponsiblePerson,
        )
    }

    companion object {
        fun buildSingle() = VehicleMigrationDataBuilder().build()

        fun buildMultiple(count: Int): Set<VehicleMigrationData> =
            (1..count).map { VehicleMigrationDataBuilder().build() }.toSet()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))
    }
}

class CarBuilder {
    val faker: Faker = Faker()

    private var vin: String = createRandomString(Random.nextInt(4, 23))
    private var userPartnerId: String = createRandomString(Random.nextInt(6, 8))

    fun vin(vin: String) = apply { this.vin = vin }

    fun userPartnerId(userPartnerId: String) = apply { this.userPartnerId = userPartnerId }

    fun build(): Car {
        val leasingArt = LeasingArt.entries.random()
        return Car(
            vin = vin,
            carPartnerId = createRandomString(22),
            make = createRandomString(Random.nextInt(4, 23)),
            model = createRandomString(Random.nextInt(4, 23)),
            isPool = leasingArt.isPool,
            licensePlate = faker.vehicle().licensePlate(),
            id = UUID.randomUUID(),
            availability = Car.Availability.AVAILABLE,
            createdAt = OffsetDateTime.now(),
            updatedAt = OffsetDateTime.now(),
            isActive = true,
            bodyType = Car.BodyType.sedan,
            fleetId = UUID.randomUUID().toString(),
            fleetPartnerId = leasingArt.toFleet().id,
            driver =
                CarDriver(
                    firstName = createRandomString(Random.nextInt(4, 23)),
                    lastName = createRandomString(Random.nextInt(4, 23)),
                    email = createRandomString(Random.nextInt(4, 23)),
                    partnerId = userPartnerId,
                    phoneNumber = createRandomString(Random.nextInt(4, 23)),
                ),
            insurances =
                listOf(
                    CarInsurance(
                        id = UUID.randomUUID(),
                        costCenter = createRandomString(Random.nextInt(4, 23)),
                        insuranceCompany = createRandomString(Random.nextInt(4, 23)),
                        insuranceBroker = "LeasingArt:${leasingArt.id}",
                        insurerAddress = "UsingCostCenter:${createRandomString(10)}",
                        createdAt = OffsetDateTime.now(),
                        updatedAt = OffsetDateTime.now(),
                    )
                ),
        )
    }

    companion object {
        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<Car> = (1..count).map { CarBuilder().build() }.toSet()

        fun buildSingle(): Car = CarBuilder().build()
    }
}

class UserBuilder {
    private var role: User.Role = User.Role.entries.random()
    private var userPartnerId: String = EmployeeNumber(createRandomString(Random.nextInt(6, 8))).value

    fun role(role: User.Role) = apply { this.role = role }

    fun userPartnerId(userPartnerId: String) = apply { this.userPartnerId = userPartnerId }

    fun build(): User {
        return User(
            id = UUID.randomUUID(),
            role = role,
            firstName = createRandomString(Random.nextInt(4, 23)),
            lastName = createRandomString(Random.nextInt(4, 23)),
            email = createRandomString(Random.nextInt(4, 23)),
            userPartnerId = userPartnerId,
            visibilityScope =
                (0..Random.nextInt(1, 5))
                    .map { VisibilityScope(fleetPartnerId = Fleet.leasingFleets.random().id) }
                    .distinctBy { it.fleetPartnerId },
        )
    }

    companion object {
        fun buildSingle() = UserBuilder().build()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<User> = (1..count).map { UserBuilder().build() }.toSet()
    }
}

class VehicleResponsiblePersonBuilder {
    private val faker = Faker()

    private var employeeNumber: EmployeeNumber = EmployeeNumber(faker.numerify("00######"))
    private var firstName: String = faker.name().firstName()
    private var lastName: String = faker.name().lastName()
    private var email: String = faker.internet().emailAddress()
    private var isFleetManager: Boolean = false
    private var isGlobalFleetManager: Boolean = Random.nextBoolean()

    fun employeeNumber(employeeNumber: EmployeeNumber) = apply { this.employeeNumber = employeeNumber }

    fun firstName(firstName: String) = apply { this.firstName = firstName }

    fun lastName(lastName: String) = apply { this.lastName = lastName }

    fun email(email: String) = apply { this.email = email }

    fun fleetManager() = apply { this.isFleetManager = true }

    fun globalFleetManager() = apply {
        this.isFleetManager = true
        this.isGlobalFleetManager = true
    }

    fun build(): VehicleResponsiblePerson {
        return VehicleResponsiblePerson(
                employeeNumber = employeeNumber,
                firstName = firstName,
                lastName = lastName,
                email = email,
                isFleetManager = isFleetManager,
            )
            .apply { updateGlobalFleetManager(<EMAIL>) }
    }

    companion object {
        fun buildSingle() = VehicleResponsiblePersonBuilder().build()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<VehicleResponsiblePerson> =
            (1..count).map { VehicleResponsiblePersonBuilder().build() }.toSet()
    }
}

class FleetBuilder {
    private val fleet = LeasingArt.entries.random()
    private val fleetId = fleet.id
    private val description = fleet.description

    fun build(): Fleet {
        return Fleet(id = fleetId, description = description)
    }

    companion object {
        fun buildSingle() = FleetBuilder().build()

        fun buildMultiple() = buildMultiple(count = Random.nextInt(1, 5))

        fun buildMultiple(count: Int): Set<Fleet> = (1..count).map { FleetBuilder().build() }.toSet()
    }
}
