/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetOnboardingDto
import com.emh.damagemanagement.shared.createRandomString
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class FleetConverterTest {
    @ParameterizedTest
    @MethodSource("fleetProvider")
    fun `should convert Fleet to FleetOnboardingDTO`(fleet: Fleet) {
        val parentFleetId = createRandomString(13)
        val fleetOnboardingDto = fleet.toFleetOnboardingDto(parentFleetId)

        assertThat(fleetOnboardingDto).satisfies(fleetRequirements(fleet, parentFleetId))
    }

    companion object {
        fun fleetRequirements(fleet: Fleet, parentFleetId: String) =
            Consumer<FleetOnboardingDto> { fleetOnboardingDto: FleetOnboardingDto ->
                assertThat(fleetOnboardingDto.name).isEqualTo(fleet.description)
                assertThat(fleetOnboardingDto.fleetPartnerId).isEqualTo(fleet.id)
                assertThat(fleetOnboardingDto.parentFleetId).isEqualTo(parentFleetId)
            }

        @JvmStatic fun fleetProvider(): Stream<Arguments> = Fleet.managedFleets.map { Arguments.of(it) }.stream()
    }
}
