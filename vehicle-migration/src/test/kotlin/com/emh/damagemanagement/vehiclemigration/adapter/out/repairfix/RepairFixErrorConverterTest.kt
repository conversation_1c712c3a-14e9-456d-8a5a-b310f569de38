/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.adapter.RepairfixException.Type as RepairFixExceptionType
import com.emh.damagemanagement.shared.createRandomString
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import java.util.function.Consumer
import java.util.stream.Stream
import kotlin.random.Random
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.web.reactive.function.client.WebClientResponseException

class RepairFixErrorConverterTest {

    @ParameterizedTest
    @MethodSource("repairFixExceptionProvider")
    fun `should convert RepairFixException to FleetMigrationException`(repairFixException: RepairfixException) {
        val fleetMigrationException = repairFixException.toFleetMigrationException()

        assertThat(fleetMigrationException.errorType)
            .satisfies(repairFixExceptionTypeRequirements(repairFixException.type))
        assertThat(fleetMigrationException.message).isEqualTo(repairFixException.message)
        assertThat(fleetMigrationException.cause).isEqualTo(repairFixException.cause)
    }

    @ParameterizedTest
    @MethodSource("repairFixExceptionProvider")
    fun `should convert RepairFixException to VehicleMigrationException`(repairFixException: RepairfixException) {
        val vehicleMigrationException = repairFixException.toVehicleMigrationException()

        assertThat(vehicleMigrationException.errorType)
            .satisfies(repairFixExceptionTypeRequirements(repairFixException.type))
        assertThat(vehicleMigrationException.message).isEqualTo(repairFixException.message)
        assertThat(vehicleMigrationException.cause).isEqualTo(repairFixException.cause)
        when (repairFixException.type) {
            RepairfixException.Type.BUSINESS -> assertThat(vehicleMigrationException.canRetry).isFalse()
            RepairfixException.Type.TECHNICAL -> assertThat(vehicleMigrationException.canRetry).isTrue()
        }
    }

    @ParameterizedTest
    @MethodSource("repairFixExceptionProvider")
    fun `should convert RepairFixException to VehicleResponsiblePersonMigrationException`(
        repairFixException: RepairfixException
    ) {
        val vehicleResponsiblePersonMigrationException =
            repairFixException.toVehicleResponsiblePersonMigrationException()

        assertThat(vehicleResponsiblePersonMigrationException.errorType)
            .satisfies(repairFixExceptionTypeRequirements(repairFixException.type))
        assertThat(vehicleResponsiblePersonMigrationException.message).isEqualTo(repairFixException.message)
        assertThat(vehicleResponsiblePersonMigrationException.cause).isEqualTo(repairFixException.cause)
        when (repairFixException.type) {
            RepairfixException.Type.BUSINESS ->
                assertThat(vehicleResponsiblePersonMigrationException.canRetry).isFalse()
            RepairfixException.Type.TECHNICAL ->
                assertThat(vehicleResponsiblePersonMigrationException.canRetry).isTrue()
        }
    }

    companion object {

        fun repairFixExceptionTypeRequirements(repairFixExceptionType: RepairFixExceptionType) =
            Consumer<ErrorType> { errorType: ErrorType ->
                when (repairFixExceptionType) {
                    RepairFixExceptionType.BUSINESS -> assertThat(errorType).isEqualTo(ErrorType.BUSINESS)
                    RepairFixExceptionType.TECHNICAL -> assertThat(errorType).isEqualTo(ErrorType.TECHNICAL)
                }
            }

        @JvmStatic
        fun repairFixExceptionProvider(): Stream<Arguments> =
            (1..10)
                .map {
                    RepairfixException(
                        type = RepairfixException.Type.entries.random(),
                        message = createRandomString(23),
                        cause =
                            if (Random.nextBoolean())
                                WebClientResponseException("error", 409, "status", null, null, null)
                            else null,
                    )
                }
                .map { Arguments.of(it) }
                .stream()
    }
}
