/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("HttpUrlsUsage")

package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.adapter.RepairfixException
import com.emh.damagemanagement.repairfix.adapter.RepairfixRetriesExhaustedException
import com.emh.damagemanagement.repairfix.adapter.RepairfixTooManyRequestsException
import com.emh.damagemanagement.repairfix.adapter.RepairfixUnreachableException
import com.emh.damagemanagement.repairfix.adapter.config.RepairfixRetryConfigurationProperties
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.BadRequestErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.ConflictErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetsList
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.InternalServerErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnauthorizedErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnprocessableEntityErrorResponse
import com.emh.damagemanagement.vehiclemigration.IntegrationTest
import com.emh.damagemanagement.vehiclemigration.application.MigrationApplicationService
import com.fasterxml.jackson.databind.ObjectMapper
import java.math.BigDecimal
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.test.context.TestPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoBean
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.WebClientResponseException

@IntegrationTest
@TestPropertySource(
    properties = ["repairfix.base-url=http://localhost:8083", "repairfix.retry.backoff-duration-in-millis=5"]
)
class RepairFixExceptionHandlerTest {
    @Autowired private lateinit var objectMapper: ObjectMapper
    @Autowired @Qualifier("repairfix") private lateinit var webClient: WebClient
    @Autowired private lateinit var retryConfigurationProperties: RepairfixRetryConfigurationProperties
    @MockitoBean private lateinit var migrationApplicationService: MigrationApplicationService

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8083)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")

    private val internalServerErrorResponse =
        InternalServerErrorResponse(statusCode = BigDecimal.valueOf(500), message = "Danger ahead.")

    private val unauthorizedErrorResponse =
        UnauthorizedErrorResponse(
            statusCode = BigDecimal.valueOf(401),
            message = "unauthorized",
            error = "api looks weird",
        )

    private val unprocessableEntityErrorResponse =
        UnprocessableEntityErrorResponse(status = BigDecimal.valueOf(422), message = "random business exception")

    private val conflictErrorResponse =
        ConflictErrorResponse(status = BigDecimal.valueOf(409), message = "hopefully a business conflict.")
    private val badRequestErrorResponse =
        BadRequestErrorResponse(
            statusCode = BigDecimal.valueOf(400),
            message = listOf("error1", "error2"),
            error = "error3",
        )

    @Test
    fun `should throw RepairfixException in case of 404 not found`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(notFoundResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(404)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining(notFoundResponse.message)
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 404 not found with invalid body`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("invalid response body")
                        .addHeader("Content-Type", "application/json")
                        .code(404)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining("invalid response body")
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 500 response`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(internalServerErrorResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(500)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining(internalServerErrorResponse.message)
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 500 not found with invalid body`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("invalid response body")
                        .addHeader("Content-Type", "application/json")
                        .code(500)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining("invalid response body")
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 401 response`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(unauthorizedErrorResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(401)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining(unauthorizedErrorResponse.message)
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 401 not found with invalid body`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("invalid response body")
                        .addHeader("Content-Type", "application/json")
                        .code(401)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining("invalid response body")
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 422 response`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(unprocessableEntityErrorResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(422)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining(unprocessableEntityErrorResponse.message)
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.BUSINESS)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 422 not found with invalid body`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("invalid response body")
                        .addHeader("Content-Type", "application/json")
                        .code(422)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining("invalid response body")
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.BUSINESS)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 409 response`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(conflictErrorResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(409)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining(conflictErrorResponse.message)
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.BUSINESS)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 409 not found with invalid body`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("invalid response body")
                        .addHeader("Content-Type", "application/json")
                        .code(409)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining("invalid response body")
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.BUSINESS)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 400 response`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(badRequestErrorResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(400)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining(badRequestErrorResponse.message.joinToString(",") { "\"$it\"" })
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should throw RepairfixException in case of 400 not found with invalid body`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("invalid response body")
                        .addHeader("Content-Type", "application/json")
                        .code(400)
                        .build()
                }
            }

        assertThatExceptionOfType(RepairfixException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining("invalid response body")
            .extracting(RepairfixException::type)
            .isEqualTo(RepairfixException.Type.TECHNICAL)
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should not map unhandled status codes`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("i am a teapot")
                        .addHeader("Content-Type", "application/json")
                        .code(418)
                        .build()
                }
            }

        assertThatExceptionOfType(WebClientResponseException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withMessageContaining("418 I'm a teapot from GET")
        assertThat(mockWebServer.requestCount).isEqualTo(1)
    }

    @Test
    fun `should not map non-error responses`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body("i am a happy response")
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        assertThatNoException().isThrownBy {
            webClient
                .get()
                .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                .retrieve()
                .toBodilessEntity()
                .block()
        }
    }

    @Test
    fun `should throw RepairfixUnreachableException if remote server is unreachable`() {
        mockWebServer.close()

        assertThatExceptionOfType(RepairfixUnreachableException::class.java).isThrownBy {
            webClient
                .get()
                .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                .retrieve()
                .toBodilessEntity()
                .block()
        }
    }

    @Test
    fun `should retry on 429`() {
        repeat(2) {
            mockWebServer.enqueue(
                MockResponse.Builder().addHeader("Content-Type", "application/json").code(429).build()
            )
        }
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(FleetsList(fleets = emptyList(), total = BigDecimal.ZERO)))
                .addHeader("Content-Type", "application/json")
                .code(200)
                .build()
        )

        assertThatNoException().isThrownBy {
            webClient
                .get()
                .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                .retrieve()
                .toBodilessEntity()
                .block()
        }
        assertThat(mockWebServer.requestCount).isEqualTo(3)
    }

    @Test
    fun `should throw RepairfixRetriesExhaustedException after exhausting all retry attempts when encountering 429`() {
        repeat(retryConfigurationProperties.maxAttempts.toInt() + 1) {
            mockWebServer.enqueue(
                MockResponse.Builder().addHeader("Content-Type", "application/json").code(429).build()
            )
        }

        assertThatExceptionOfType(RepairfixRetriesExhaustedException::class.java)
            .isThrownBy {
                webClient
                    .get()
                    .uri("http://${mockWebServer.hostName}:${mockWebServer.port}/v1/fleets")
                    .retrieve()
                    .toBodilessEntity()
                    .block()
            }
            .withCauseInstanceOf(RepairfixTooManyRequestsException::class.java)
            .withMessage("Exhausted ${retryConfigurationProperties.maxAttempts} retry attempts.")
        assertThat(mockWebServer.requestCount).isEqualTo(retryConfigurationProperties.maxAttempts + 1)
    }
}
