/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetListItem
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetOnboardingDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.FleetsList
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.InternalServerErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.OperationResult
import com.emh.damagemanagement.vehiclemigration.IntegrationTest
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.FleetMigrationException
import com.emh.damagemanagement.vehiclemigration.application.MigrationApplicationService
import com.emh.damagemanagement.vehiclemigration.application.port.AddFleetResult
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatExceptionOfType
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoBean

@IntegrationTest
@TestPropertySource(properties = ["repairfix.base-url=http://localhost:8083"])
class RepairFixFleetsAdapterTest {
    @Autowired private lateinit var repairFixFleetsAdapter: RepairFixFleetsAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper
    @MockitoBean private lateinit var migrationApplicationService: MigrationApplicationService

    private lateinit var mockWebServer: MockWebServer

    private val invalidFleets =
        """{
        "fleets": [
            {
                "id": "08373786-53c3-43ce-9e95-2805fd8acfe3",
                "title": "i am a fleet without partner"
            }
        ],
        "total": 1
    }"""

    private val porscheDienstLeasing =
        FleetListItem(
            id = UUID.randomUUID(),
            partnerId = null,
            title = "porsche-dienst-leasing",
            parentFleetId = null,
            name = "porsche-dienst-leasing",
            parentFleetName = null,
        )
    private val porschePool =
        FleetListItem(
            id = UUID.randomUUID(),
            partnerId = null,
            title = "porsche-pool",
            parentFleetId = null,
            name = "porsche-pool",
            parentFleetName = null,
        )

    private val fleetListFleetGroups =
        FleetsList(fleets = listOf(porscheDienstLeasing, porschePool), total = BigDecimal.valueOf(2L))

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8083)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")

    private val internalServerErrorResponse =
        InternalServerErrorResponse(statusCode = BigDecimal.valueOf(500), message = "Internal error something.")

    private val onboardNewFleetResponse = OperationResult(true)

    @Test
    fun `should not fail when repairfix supplies fleets without partnerId`() {
        repeat(2) {
            mockWebServer.enqueue(
                MockResponse.Builder()
                    .body(objectMapper.writeValueAsString(fleetListFleetGroups))
                    .addHeader("Content-Type", "application/json")
                    .code(200)
                    .build()
            )
        }
        mockWebServer.enqueue(
            MockResponse.Builder().body(invalidFleets).addHeader("Content-Type", "application/json").code(200).build()
        )

        assertThatNoException().isThrownBy {
            repairFixFleetsAdapter.allFleetsExist(fleetsToCompare = Fleet.leasingFleets)
        }
    }

    @Test
    fun `should migrate all leasing fleets`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when (request.method) {
                        "GET" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(fleetListFleetGroups))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        "POST" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(onboardNewFleetResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        var addFleetResult: AddFleetResult? = null
        assertThatNoException().isThrownBy {
            addFleetResult = repairFixFleetsAdapter.migrateLeasingFleets(fleetsToBeMigrated = Fleet.leasingFleets)
        }

        assertThat(mockWebServer.requestCount).isEqualTo(2 + Fleet.leasingFleets.size)
        // skip getTotalNumberOfFleets
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        // skip getFleetPage
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        (1..Fleet.leasingFleets.size).onEach {
            val addFleetRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val addFleetRequestPayload: FleetOnboardingDto = objectMapper.readValue(addFleetRequest!!.body!!.utf8())
            val matchingFleet = Fleet.leasingFleets.single { addFleetRequestPayload.fleetPartnerId == it.id }
            assertThat(addFleetRequestPayload.name).isEqualTo(matchingFleet.description)
            assertThat(addFleetRequestPayload.fleetPartnerId).isEqualTo(matchingFleet.id)
            assertThat(addFleetRequestPayload.parentFleetId).isEqualTo(porscheDienstLeasing.name)
        }
        assertThat(addFleetResult!!.success).isTrue()
        assertThat(addFleetResult.error).isNull()
        assertThat(addFleetResult.errorType).isNull()
    }

    @Test
    fun `should migrate all managed pool - pre and post usage fleets`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when (request.method) {
                        "GET" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(fleetListFleetGroups))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        "POST" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(onboardNewFleetResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }
        val managedPoolFleets = setOf(Fleet.PRE_USAGE_FLEET, Fleet.POST_USAGE_FLEET)
        var addFleetResult: AddFleetResult? = null
        assertThatNoException().isThrownBy {
            addFleetResult = repairFixFleetsAdapter.migratePoolFleets(fleetsToBeMigrated = managedPoolFleets)
        }

        assertThat(mockWebServer.requestCount).isEqualTo(4)
        // skip getTotalNumberOfFleets
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        // skip getFleetPage
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        (1..2).onEach {
            val addFleetRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
            val addFleetRequestPayload: FleetOnboardingDto = objectMapper.readValue(addFleetRequest!!.body!!.utf8())
            val matchingFleet = managedPoolFleets.single { addFleetRequestPayload.fleetPartnerId == it.id }
            assertThat(addFleetRequestPayload.name).isEqualTo(matchingFleet.description)
            assertThat(addFleetRequestPayload.fleetPartnerId).isEqualTo(matchingFleet.id)
            assertThat(addFleetRequestPayload.parentFleetId).isEqualTo(porschePool.name)
        }
        assertThat(addFleetResult!!.success).isTrue()
        assertThat(addFleetResult.error).isNull()
        assertThat(addFleetResult.errorType).isNull()
    }

    @Test
    fun `should migrate pool fleet`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when (request.method) {
                        "GET" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(fleetListFleetGroups))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        "POST" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(onboardNewFleetResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }
        val poolFleet = Fleet(id = "pool-fleet-id", description = "pool-fleet-description")
        var addFleetResult: AddFleetResult? = null
        assertThatNoException().isThrownBy {
            addFleetResult = repairFixFleetsAdapter.migratePoolFleet(fleetToBeMigrated = poolFleet)
        }

        assertThat(mockWebServer.requestCount).isEqualTo(3)
        // skip getTotalNumberOfFleets
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        // skip getFleetPage
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val addFleetRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val addFleetRequestPayload: FleetOnboardingDto = objectMapper.readValue(addFleetRequest!!.body!!.utf8())
        val matchingFleet = poolFleet
        assertThat(addFleetRequestPayload.name).isEqualTo(matchingFleet.description)
        assertThat(addFleetRequestPayload.fleetPartnerId).isEqualTo(matchingFleet.id)
        assertThat(addFleetRequestPayload.parentFleetId).isEqualTo(porschePool.name)
        assertThat(addFleetResult!!.success).isTrue()
        assertThat(addFleetResult.error).isNull()
        assertThat(addFleetResult.errorType).isNull()
    }

    @Test
    fun `should skip fleet on AddFleetFailedException`() {
        repeat(2) {
            mockWebServer.enqueue(
                MockResponse.Builder()
                    .body(objectMapper.writeValueAsString(fleetListFleetGroups))
                    .addHeader("Content-Type", "application/json")
                    .code(200)
                    .build()
            )
        }
        repeat(2) {
            mockWebServer.enqueue(
                MockResponse.Builder()
                    .body(objectMapper.writeValueAsString(onboardNewFleetResponse))
                    .addHeader("Content-Type", "application/json")
                    .code(200)
                    .build()
            )
        }
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(OperationResult(false)))
                .addHeader("Content-Type", "application/json")
                .code(200)
                .build()
        )
        repeat(4) {
            mockWebServer.enqueue(
                MockResponse.Builder()
                    .body(objectMapper.writeValueAsString(onboardNewFleetResponse))
                    .addHeader("Content-Type", "application/json")
                    .code(200)
                    .build()
            )
        }

        assertThatNoException().isThrownBy {
            repairFixFleetsAdapter.migrateLeasingFleets(fleetsToBeMigrated = Fleet.leasingFleets)
        }

        assertThat(mockWebServer.requestCount).isEqualTo(2 + Fleet.leasingFleets.size)
    }

    @Test
    fun `should migrate only missing fleets`() {
        val existingFleets =
            FleetsList(
                fleets =
                    listOf(
                        porscheDienstLeasing,
                        FleetListItem(
                            id = UUID.randomUUID(),
                            partnerId = LeasingArt.L1_1.id,
                            title = LeasingArt.L1_1.description,
                            name = LeasingArt.L1_1.description,
                            parentFleetId = porscheDienstLeasing.id.toString(),
                            parentFleetName = porscheDienstLeasing.name,
                        ),
                        FleetListItem(
                            id = UUID.randomUUID(),
                            partnerId = LeasingArt.L2.id,
                            title = LeasingArt.L2.description,
                            name = LeasingArt.L2.description,
                            parentFleetId = porscheDienstLeasing.id.toString(),
                            parentFleetName = porscheDienstLeasing.name,
                        ),
                    ),
                total = BigDecimal.valueOf(3L),
            )

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when (request.method) {
                        "GET" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(existingFleets))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        "POST" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(onboardNewFleetResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        var addFleetResult: AddFleetResult? = null
        assertThatNoException().isThrownBy {
            addFleetResult = repairFixFleetsAdapter.migrateLeasingFleets(fleetsToBeMigrated = Fleet.leasingFleets)
        }

        assertThat(mockWebServer.requestCount).isEqualTo(2 + Fleet.leasingFleets.size - 2)
        // skip getTotalNumberOfFleets
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        // skip getFleetPage
        mockWebServer.takeRequest(1, TimeUnit.SECONDS)

        val createdPartnerIds =
            (1..Fleet.leasingFleets.size - 2).map {
                val addFleetRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
                val addFleetRequestPayload: FleetOnboardingDto = objectMapper.readValue(addFleetRequest!!.body!!.utf8())
                addFleetRequestPayload.fleetPartnerId
            }

        assertThat(createdPartnerIds)
            .containsExactlyInAnyOrderElementsOf(
                (LeasingArt.leasingArts - setOf(LeasingArt.L2, LeasingArt.L1_1)).map { it.id }
            )

        assertThat(addFleetResult!!.success).isTrue()
        assertThat(addFleetResult.error).isNull()
        assertThat(addFleetResult.errorType).isNull()
    }

    @Test
    fun `should return false if not all fleets have been migrated`() {
        val existingFleets =
            FleetsList(
                fleets =
                    listOf(
                        porscheDienstLeasing,
                        FleetListItem(
                            id = UUID.randomUUID(),
                            partnerId = LeasingArt.L1_1.id,
                            title = LeasingArt.L1_1.description,
                            name = LeasingArt.L1_1.description,
                            parentFleetId = porscheDienstLeasing.id.toString(),
                            parentFleetName = porscheDienstLeasing.name,
                        ),
                        FleetListItem(
                            id = UUID.randomUUID(),
                            partnerId = LeasingArt.L2.id,
                            title = LeasingArt.L2.description,
                            name = LeasingArt.L2.description,
                            parentFleetId = porscheDienstLeasing.id.toString(),
                            parentFleetName = porscheDienstLeasing.name,
                        ),
                    ),
                total = BigDecimal.valueOf(3L),
            )

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when (request.method) {
                        "GET" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(existingFleets))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val migrationStatus = repairFixFleetsAdapter.allFleetsExist(Fleet.leasingFleets)
        assertThat(migrationStatus).isFalse()
    }

    @Test
    fun `should return true if all fleets have been migrated`() {
        val existingFleets =
            FleetsList(
                fleets =
                    listOf(porscheDienstLeasing) +
                        Fleet.leasingFleets.map {
                            FleetListItem(
                                id = UUID.randomUUID(),
                                partnerId = it.id,
                                title = requireNotNull(it.description),
                                name = requireNotNull(it.description),
                                parentFleetName = porscheDienstLeasing.name,
                                parentFleetId = porscheDienstLeasing.id.toString(),
                            )
                        },
                total = BigDecimal.valueOf(Fleet.leasingFleets.size.toLong() + 1),
            )

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when (request.method) {
                        "GET" ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(existingFleets))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val migrationStatus = repairFixFleetsAdapter.allFleetsExist(Fleet.leasingFleets)
        assertThat(migrationStatus).isTrue()
    }

    @Test
    fun `should throw FleetMigrationException on RepairfixException when checking that all fleets exist`() {
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(fleetListFleetGroups))
                .addHeader("Content-Type", "application/json")
                .code(200)
                .build()
        )
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(notFoundResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        assertThatExceptionOfType(FleetMigrationException::class.java).isThrownBy {
            repairFixFleetsAdapter.allFleetsExist(Fleet.leasingFleets)
        }
    }

    @Test
    fun `should return success as false on RepairfixException when migrating fleets`() {
        val existingFleets =
            FleetsList(
                fleets =
                    listOf(porscheDienstLeasing) +
                        Fleet.leasingFleets.map {
                            FleetListItem(
                                id = UUID.randomUUID(),
                                partnerId = it.id,
                                title = requireNotNull(it.description),
                                name = requireNotNull(it.description),
                                parentFleetName = porscheDienstLeasing.name,
                                parentFleetId = porscheDienstLeasing.id.toString(),
                            )
                        },
                total = BigDecimal.valueOf(Fleet.leasingFleets.size.toLong() + 1),
            )

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(existingFleets))
                .addHeader("Content-Type", "application/json")
                .code(200)
                .build()
        )

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(internalServerErrorResponse))
                .addHeader("Content-Type", "application/json")
                .code(500)
                .build()
        )

        val addFleetResult = repairFixFleetsAdapter.migrateLeasingFleets(fleetsToBeMigrated = Fleet.leasingFleets)
        assertThat(addFleetResult.success).isFalse()
        assertThat(addFleetResult.error).isNotNull()
        assertThat(addFleetResult.errorType).isEqualTo(ErrorType.TECHNICAL)
    }
}
