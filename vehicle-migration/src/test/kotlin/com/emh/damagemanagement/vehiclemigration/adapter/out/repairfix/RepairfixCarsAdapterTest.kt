/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AddCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CarDetails
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.OperationResult
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnprocessableEntityErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateCarDto
import com.emh.damagemanagement.vehiclemigration.CarBuilder
import com.emh.damagemanagement.vehiclemigration.IntegrationTest
import com.emh.damagemanagement.vehiclemigration.VehicleBuilder
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.math.BigDecimal
import java.util.*
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@TestPropertySource(properties = ["repairfix.base-url=http://localhost:8181"])
class RepairfixCarsAdapterTest {
    @Autowired private lateinit var repairfixCarsAdapter: RepairfixCarsAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8181)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")

    private val okResponse = OperationResult(true)

    private val unprocessableEntityResponse =
        UnprocessableEntityErrorResponse(status = BigDecimal.valueOf(422), message = "I am an error of business.")

    @Test
    fun `should set apikey in authorization header`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(okResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        assertThatNoException().isThrownBy { repairfixCarsAdapter.addVehicle(VehicleBuilder.buildSingle()) }
        val request: RecordedRequest = mockWebServer.takeRequest(1, TimeUnit.SECONDS)!!
        assertThat(request.headers["Authorization"]).isEqualTo("Bearer apiKey")
    }

    @Test
    fun `should add vehicle`() {
        val vehicle = VehicleBuilder.buildSingle()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/cars") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixCarsAdapter.addVehicle(vehicle)

        assertThat(mockWebServer.requestCount).isEqualTo(1)
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestPayload: List<AddCarDto> = objectMapper.readValue(request!!.body!!.utf8())
        assertThat(requestPayload.single()).isEqualTo(vehicle.toAddCarDto())
    }

    @Test
    fun `should return success false in case of RepairfixException when adding vehicle`() {
        val vehicle = VehicleBuilder.buildSingle()

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        val result = repairfixCarsAdapter.addVehicle(vehicle)

        assertThat(result.success).isFalse()
        assertThat(result.error).isNotNull()
        assertThat(result.errorType).isNotNull()
    }

    @Test
    fun `should find vehicle by vin`() {
        val resultCar = CarBuilder.buildSingle()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/cars") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(CarDetails(car = resultCar)))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val vehicleByVin = repairfixCarsAdapter.findVehicle("anyVin")

        assertThat(vehicleByVin).usingRecursiveComparison().isEqualTo(resultCar.toVehicle())
    }

    @Test
    fun `should return null if no vehicle can by found`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(notFoundResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(404)
                        .build()
                }
            }

        val noVehicle = repairfixCarsAdapter.findVehicle("anyVin")

        assertThat(noVehicle).isNull()
    }

    @Test
    fun `should update vehicle`() {
        val vehicle = VehicleBuilder.buildSingle().apply { this.updateExternalId(UUID.randomUUID()) }

        val carDetails = CarDetails(car = CarBuilder().build())

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/cars") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carDetails))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixCarsAdapter.updateVehicle(vehicle)

        val updateCarDto = vehicle.toUpdateCarDto()

        assertThat(mockWebServer.requestCount).isEqualTo(1)
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestPayload: UpdateCarDto = objectMapper.readValue(request!!.body!!.utf8())
        val pathParam: String = request.url.toString().split("/").last()
        assertThat(updateCarDto).isEqualTo(requestPayload)
        assertThat(requestPayload.isActive).isTrue()
        assertThat(pathParam).isEqualTo(vehicle.externalId.toString())
    }

    @Test
    fun `should return success false in case of RepairfixException when trying to update vehicle`() {
        val vehicle = VehicleBuilder.buildSingle().apply { this.updateExternalId(UUID.randomUUID()) }
        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        val result = repairfixCarsAdapter.updateVehicle(vehicle)

        assertThat(result.success).isFalse()
        assertThat(result.error).isNotNull()
        assertThat(result.errorType).isNotNull()
    }

    @Test
    fun `should deactivate vehicle`() {
        val vehicle = VehicleBuilder.buildSingle().apply { this.updateExternalId(UUID.randomUUID()) }

        val carDetails = CarDetails(car = CarBuilder().build())

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/cars") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(carDetails))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()
                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        repairfixCarsAdapter.deactivateVehicle(vehicle)

        val updateCarDto = vehicle.toUpdateCarDto(deactivate = true)

        assertThat(mockWebServer.requestCount).isEqualTo(1)
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestPayload: UpdateCarDto = objectMapper.readValue(request!!.body!!.utf8())
        val pathParam: String = request.url.toString().split("/").last()
        assertThat(updateCarDto).isEqualTo(requestPayload)
        assertThat(requestPayload.isActive).isFalse()
        assertThat(pathParam).isEqualTo(vehicle.externalId.toString())
    }

    @Test
    fun `should return success false in case of RepairfixException when deactivating vehicle`() {
        val vehicle = VehicleBuilder.buildSingle().apply { this.updateExternalId(UUID.randomUUID()) }

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        val result = repairfixCarsAdapter.deactivateVehicle(vehicle)

        assertThat(result.success).isFalse()
        assertThat(result.error).isNotNull()
        assertThat(result.errorType).isNotNull()
    }

    @Test
    fun `update payload should contain null values, even if serializer is set to NON_NULL`() {
        val vehicle =
            VehicleBuilder().leasingArt(LeasingArt.L9_3).licensePlate(null).employeeNumber(null).build().apply {
                updateExternalId(UUID.randomUUID())
            }
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(CarDetails(car = CarBuilder().build())))
                        .addHeader("Content-Type", "application/json")
                        .code(200)
                        .build()
                }
            }

        repairfixCarsAdapter.updateVehicle(vehicle)

        val updateCarDto = vehicle.toUpdateCarDto()
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestBody = request!!.body!!.utf8()
        val requestPayload: UpdateCarDto = objectMapper.readValue(requestBody)
        assertThat(updateCarDto).isEqualTo(requestPayload)
        assertThat(requestBody).contains("\"licensePlate\":null")
        assertThat(requestBody).contains("\"driver\":null")
    }
}
