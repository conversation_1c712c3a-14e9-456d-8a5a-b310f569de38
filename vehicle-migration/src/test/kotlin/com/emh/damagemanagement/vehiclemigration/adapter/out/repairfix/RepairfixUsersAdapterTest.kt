/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CreateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.NotFoundErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.OperationResult
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnauthorizedErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UnprocessableEntityErrorResponse
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateUserDto
import com.emh.damagemanagement.vehiclemigration.IntegrationTest
import com.emh.damagemanagement.vehiclemigration.UserBuilder
import com.emh.damagemanagement.vehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.toFleet
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import java.math.BigDecimal
import java.util.concurrent.TimeUnit
import mockwebserver3.Dispatcher
import mockwebserver3.MockResponse
import mockwebserver3.MockWebServer
import mockwebserver3.RecordedRequest
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.TestPropertySource

@IntegrationTest
@TestPropertySource(properties = ["repairfix.base-url=http://localhost:8081"])
class RepairfixUsersAdapterTest {
    @Autowired private lateinit var repairfixUsersAdapter: RepairfixUsersAdapter
    @Autowired private lateinit var objectMapper: ObjectMapper

    private lateinit var mockWebServer: MockWebServer

    @BeforeEach
    fun setupMockWebserver() {
        mockWebServer = MockWebServer()
        mockWebServer.start(8081)
    }

    @AfterEach
    fun cleanup() {
        mockWebServer.close()
    }

    private val notFoundResponse =
        NotFoundErrorResponse(statusCode = BigDecimal.valueOf(404), message = "Resource could not be found.")
    private val unprocessableEntityResponse =
        UnprocessableEntityErrorResponse(status = BigDecimal.valueOf(422), message = "I am an error of business.")
    private val unauthorizedResponse =
        UnauthorizedErrorResponse(
            statusCode = BigDecimal.valueOf(401),
            message = "You need to be authenticated for this operation.",
            error = "Unauthorized",
        )

    private val okResponse = OperationResult(true)
    private val successFalseResponse = OperationResult(false)

    @Test
    fun `should find vehicle responsible person by employee number`() {
        val resultUser = UserBuilder().userPartnerId("00123456").build()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(resultUser))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val vehicleResponsiblePersonFromAdapter =
            requireNotNull(repairfixUsersAdapter.findVehicleResponsiblePerson(employeeNumber = "0012345"))

        assertThat(vehicleResponsiblePersonFromAdapter.first.employeeNumber.value).isEqualTo(resultUser.userPartnerId)
        assertThat(vehicleResponsiblePersonFromAdapter.second)
            .containsExactlyInAnyOrderElementsOf(resultUser.visibilityScope?.map { it.fleetPartnerId })
    }

    @Test
    fun `should return null if vehicle responsible person could not be found`() {
        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(404)
                        .build()
                }
            }

        val vehicleResponsiblePersonFromAdapter =
            repairfixUsersAdapter.findVehicleResponsiblePerson(employeeNumber = "0012345")

        assertThat(vehicleResponsiblePersonFromAdapter).isNull()
    }

    @Test
    fun `should add fleet manager`() {
        val fleetManagerAndFleets =
            Pair(
                VehicleResponsiblePersonBuilder.buildSingle(),
                (1..3).map { LeasingArt.poolLeasingArts.random().toFleet() }.distinctBy { it.id }.toSet(),
            )

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val result = repairfixUsersAdapter.addFleetManager(fleetManagerAndFleetIds = fleetManagerAndFleets)

        val fleetManagersForComparison = fleetManagerAndFleets.toCreateUserDto()
        assertThat(mockWebServer.requestCount).isEqualTo(1)
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestPayload: List<CreateUserDto> = objectMapper.readValue(request!!.body!!.utf8())
        assertThat(requestPayload.single()).isEqualTo(fleetManagersForComparison)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should add vehicle responsible person`() {
        val vehicleResponsiblePerson = VehicleResponsiblePersonBuilder.buildSingle()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val result =
            repairfixUsersAdapter.addVehicleResponsiblePerson(vehicleResponsiblePerson = vehicleResponsiblePerson)

        val vehicleResponsiblePersonForComparison = vehicleResponsiblePerson.toCreateUserDto()
        assertThat(mockWebServer.requestCount).isEqualTo(1)
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestPayload: List<CreateUserDto> = objectMapper.readValue(request!!.body!!.utf8())
        assertThat(requestPayload.single()).isEqualTo(vehicleResponsiblePersonForComparison)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false in case of RepairfixException when adding vehicle responsible person`() {
        val vehicleResponsiblePerson = VehicleResponsiblePersonBuilder.buildSingle()

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        val result = repairfixUsersAdapter.addVehicleResponsiblePerson(vehicleResponsiblePerson)

        assertThat(result.success).isFalse()
        assertThat(result.error).isNotNull()
        assertThat(result.errorType).isNotNull()
    }

    @Test
    fun `should return success false in case of RepairfixException when adding fleet manager`() {
        val fleetManagerAndFleets =
            Pair(
                VehicleResponsiblePersonBuilder.buildSingle(),
                (1..3).map { LeasingArt.poolLeasingArts.random().toFleet() }.distinctBy { it.id }.toSet(),
            )

        mockWebServer.enqueue(
            MockResponse.Builder()
                .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                .addHeader("Content-Type", "application/json")
                .code(404)
                .build()
        )

        val result = repairfixUsersAdapter.addFleetManager(fleetManagerAndFleets)

        assertThat(result.success).isFalse()
        assertThat(result.error).isNotNull()
        assertThat(result.errorType).isNotNull()
    }

    @Test
    fun `should update vehicle responsible person`() {
        val person = VehicleResponsiblePersonBuilder.buildSingle()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateVehicleResponsiblePerson(vehicleResponsiblePerson = person)

        assertThat(mockWebServer.requestCount).isEqualTo(1)
        assertThat(updateResult.success).isTrue()
        assertThat(updateResult.error).isNull()
        assertThat(updateResult.errorType).isNull()
        val expectedUpdateUserDtoForComparison = person.toUpdateUserDto()
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestPayload: UpdateUserDto = objectMapper.readValue(request!!.body!!.utf8())
        assertThat(requestPayload).isEqualTo(expectedUpdateUserDtoForComparison)
    }

    @Test
    fun `should return success false in case of RepairfixException when trying to update vehicle responsible person`() {
        val person = VehicleResponsiblePersonBuilder.buildSingle()

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse =
                    MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(404)
                        .build()
            }

        val result = repairfixUsersAdapter.updateVehicleResponsiblePerson(person)

        assertThat(result.success).isFalse()
        assertThat(result.error).isNotNull()
        assertThat(result.errorType).isNotNull()
    }

    @Test
    fun `should update fleet manager`() {
        val fleetManagerAndFleets =
            Pair(
                VehicleResponsiblePersonBuilder.buildSingle(),
                (1..3).map { LeasingArt.poolLeasingArts.random().toFleet() }.distinctBy { it.id }.toSet(),
            )

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse {
                    return when {
                        request.url.toString().contains("/porsche/v1/users") ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(okResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(200)
                                .build()

                        else ->
                            MockResponse.Builder()
                                .body(objectMapper.writeValueAsString(notFoundResponse))
                                .addHeader("Content-Type", "application/json")
                                .code(404)
                                .build()
                    }
                }
            }

        val updateResult = repairfixUsersAdapter.updateFleetManager(fleetManagerAndFleet = fleetManagerAndFleets)

        assertThat(mockWebServer.requestCount).isEqualTo(1)
        assertThat(updateResult.success).isTrue()
        assertThat(updateResult.error).isNull()
        assertThat(updateResult.errorType).isNull()
        val expectedUpdateUserDtoForComparison = fleetManagerAndFleets.toUpdateUserDto()
        val request = mockWebServer.takeRequest(1, TimeUnit.SECONDS)
        val requestPayload: UpdateUserDto = objectMapper.readValue(request!!.body!!.utf8())
        assertThat(requestPayload).isEqualTo(expectedUpdateUserDtoForComparison)
    }

    @Test
    fun `should return success false in case of RepairfixException when trying to update fleet manager`() {
        val fleetManagerAndFleets =
            Pair(
                VehicleResponsiblePersonBuilder.buildSingle(),
                (1..3).map { LeasingArt.poolLeasingArts.random().toFleet() }.distinctBy { it.id }.toSet(),
            )

        mockWebServer.dispatcher =
            object : Dispatcher() {
                override fun dispatch(request: RecordedRequest): MockResponse =
                    MockResponse.Builder()
                        .body(objectMapper.writeValueAsString(unprocessableEntityResponse))
                        .addHeader("Content-Type", "application/json")
                        .code(404)
                        .build()
            }

        val result = repairfixUsersAdapter.updateFleetManager(fleetManagerAndFleets)

        assertThat(result.success).isFalse()
        assertThat(result.error).isNotNull()
        assertThat(result.errorType).isNotNull()
    }
}
