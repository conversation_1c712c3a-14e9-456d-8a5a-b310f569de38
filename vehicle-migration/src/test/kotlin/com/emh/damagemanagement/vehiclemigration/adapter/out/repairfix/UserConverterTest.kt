/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.CreateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.UpdateUserDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.User
import com.emh.damagemanagement.vehiclemigration.UserBuilder
import com.emh.damagemanagement.vehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import com.emh.damagemanagement.vehiclemigration.domain.toFleet
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class UserConverterTest {

    @ParameterizedTest
    @MethodSource("vehicleResponsiblePersonProvider")
    fun `should convert VehicleResponsiblePerson to CreateUserDto with role driver`(
        vehicleResponsiblePerson: VehicleResponsiblePerson
    ) {
        val createUserDto = vehicleResponsiblePerson.toCreateUserDto()

        assertThat(createUserDto).satisfies(createVehicleResponsiblePersonRequirements(vehicleResponsiblePerson))
    }

    @ParameterizedTest
    @MethodSource("fleetManagerAndFleetsProvider")
    fun `should convert FleetManager to CreateUserDto with role fleet_manager`(
        fleetManager: Pair<VehicleResponsiblePerson, Set<Fleet>>
    ) {
        val createUserDto = fleetManager.toCreateUserDto()

        assertThat(createUserDto).satisfies(createFleetManagerRequirements(fleetManager))
    }

    @ParameterizedTest
    @MethodSource("vehicleResponsiblePersonProvider")
    fun `should convert VehicleResponsiblePerson to UpdateUserDto`(vehicleResponsiblePerson: VehicleResponsiblePerson) {
        val updateUserDto = vehicleResponsiblePerson.toUpdateUserDto()

        assertThat(updateUserDto).satisfies(updateVehicleResponsiblePersonRequirements(vehicleResponsiblePerson))
    }

    @ParameterizedTest
    @MethodSource("fleetManagerAndFleetsProvider")
    fun `should convert FleetManager to UpdateUserDto`(fleetManager: Pair<VehicleResponsiblePerson, Set<Fleet>>) {
        val updateUserDto = fleetManager.toUpdateUserDto()

        assertThat(updateUserDto).satisfies(updateFleetManagerRequirements(fleetManager))
    }

    @ParameterizedTest
    @MethodSource("userProvider")
    fun `should convert User to VehicleResponsiblePerson`(user: User) {
        val vehicleResponsiblePersonAndFleetIds = user.toVehicleResponsiblePersonAndFleetIds()

        assertThat(vehicleResponsiblePersonAndFleetIds).satisfies(vehicleResponsiblePersonRequirements(user))
    }

    companion object {

        fun createVehicleResponsiblePersonRequirements(vehicleResponsiblePerson: VehicleResponsiblePerson) =
            Consumer<CreateUserDto> { createUserDto: CreateUserDto ->
                assertThat(createUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(vehicleResponsiblePerson)
                assertThat(createUserDto.userPartnerId).isEqualTo(vehicleResponsiblePerson.employeeNumber.value)
                assertThat(createUserDto.role).isEqualTo(CreateUserDto.Role.driver)
            }

        fun createFleetManagerRequirements(fleetManagerAndFleets: Pair<VehicleResponsiblePerson, Set<Fleet>>) =
            Consumer<CreateUserDto> { createUserDto: CreateUserDto ->
                assertThat(createUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(fleetManagerAndFleets.first)
                assertThat(createUserDto.userPartnerId).isEqualTo(fleetManagerAndFleets.first.employeeNumber.value)
                assertThat(createUserDto.role).isEqualTo(CreateUserDto.Role.local_fleet_manager)
                assertThat(createUserDto.visibilityScope?.map { it.fleetPartnerId })
                    .containsExactlyInAnyOrderElementsOf(fleetManagerAndFleets.second.map { it.id })
            }

        fun updateVehicleResponsiblePersonRequirements(vehicleResponsiblePerson: VehicleResponsiblePerson) =
            Consumer<UpdateUserDto> { updateUserDto: UpdateUserDto ->
                assertThat(updateUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(vehicleResponsiblePerson)
                assertThat(updateUserDto.userPartnerId).isEqualTo(vehicleResponsiblePerson.employeeNumber.value)
                assertThat(updateUserDto.role).isEqualTo(UpdateUserDto.Role.driver)
            }

        fun updateFleetManagerRequirements(fleetManagerAndFleets: Pair<VehicleResponsiblePerson, Set<Fleet>>) =
            Consumer<UpdateUserDto> { updateUserDto: UpdateUserDto ->
                assertThat(updateUserDto)
                    .usingRecursiveComparison()
                    .ignoringFields("role", "fleetPartnerId", "userPartnerId", "visibilityScope", "phone")
                    .isEqualTo(fleetManagerAndFleets.first)
                assertThat(updateUserDto.userPartnerId).isEqualTo(fleetManagerAndFleets.first.employeeNumber.value)
                if (fleetManagerAndFleets.first.isGlobalFleetManager) {
                    assertThat(updateUserDto.role).isEqualTo(UpdateUserDto.Role.fleet_manager)
                } else {
                    assertThat(updateUserDto.role).isEqualTo(UpdateUserDto.Role.local_fleet_manager)
                }
                assertThat(updateUserDto.visibilityScope?.map { it.fleetPartnerId })
                    .containsExactlyInAnyOrderElementsOf(fleetManagerAndFleets.second.map { it.id })
            }

        fun vehicleResponsiblePersonRequirements(user: User) =
            Consumer<Pair<VehicleResponsiblePerson, Set<String>>> {
                vehicleResponsiblePersonAndFleetIds: Pair<VehicleResponsiblePerson, Set<String>> ->
                val vehicleResponsiblePerson = vehicleResponsiblePersonAndFleetIds.first
                assertThat(vehicleResponsiblePerson.firstName).isEqualTo(user.firstName)
                assertThat(vehicleResponsiblePerson.lastName).isEqualTo(user.lastName)
                assertThat(vehicleResponsiblePerson.email).isEqualTo(user.email)
                assertThat(vehicleResponsiblePerson.employeeNumber.value).isEqualTo(user.userPartnerId)
                if (setOf(User.Role.local_fleet_manager, User.Role.fleet_manager).contains(user.role)) {
                    assertThat(vehicleResponsiblePerson.isFleetManager).isTrue()
                } else {
                    assertThat(vehicleResponsiblePerson.isFleetManager).isFalse()
                }
                if (User.Role.fleet_manager == user.role) {
                    assertThat(vehicleResponsiblePerson.isGlobalFleetManager).isTrue()
                } else {
                    assertThat(vehicleResponsiblePerson.isGlobalFleetManager).isFalse()
                }
                val fleetIds = vehicleResponsiblePersonAndFleetIds.second
                assertThat(fleetIds)
                    .containsExactlyInAnyOrderElementsOf(user.visibilityScope?.map { it.fleetPartnerId })
            }

        @JvmStatic
        fun vehicleResponsiblePersonProvider(): Stream<Arguments> =
            VehicleResponsiblePersonBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()

        @JvmStatic
        fun userProvider(): Stream<Arguments> = UserBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()

        @JvmStatic
        fun fleetManagerAndFleetsProvider(): Stream<Arguments> =
            (1..10)
                .map {
                    Pair(
                        VehicleResponsiblePersonBuilder.buildSingle(),
                        (0..5)
                            .map { LeasingArt.poolLeasingArts.random() }
                            .distinctBy { it.id }
                            .map { it.toFleet() }
                            .toSet(),
                    )
                }
                .map { Arguments.of(it) }
                .stream()
    }
}
