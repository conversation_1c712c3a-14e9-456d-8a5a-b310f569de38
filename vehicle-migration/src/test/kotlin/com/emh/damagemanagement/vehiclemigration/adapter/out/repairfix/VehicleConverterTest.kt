/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.adapter.out.repairfix

import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.AddCarDto
import com.emh.damagemanagement.repairfix.generated.adapter.out.rest.model.Car
import com.emh.damagemanagement.vehiclemigration.CarBuilder
import com.emh.damagemanagement.vehiclemigration.VehicleBuilder
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.function.Consumer
import java.util.stream.Stream
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class VehicleConverterTest {
    @ParameterizedTest
    @MethodSource("vehicleProvider")
    fun `should convert Vehicle to AddCarDto`(vehicle: Vehicle) {
        val addCarDto = vehicle.toAddCarDto()

        assertThat(addCarDto).satisfies(vehicleRequirements(vehicle))
    }

    @Test
    fun `should not map NONE fleet when converting Vehicle to AddCarDto`() {
        val vehicle = VehicleBuilder().fleet(Fleet.NO_FLEET).build()

        val addCarDto = vehicle.toAddCarDto()

        assertThat(addCarDto.fleetIdentifier).isNull()
    }

    @ParameterizedTest
    @MethodSource("carProvider")
    fun `should convert Car to Vehicle`(car: Car) {
        val vehicle = car.toVehicle()

        assertThat(vehicle).satisfies(carRequirements(car))
    }

    @ParameterizedTest
    @MethodSource("vehicleProvider")
    fun `should convert Vehicle to UpdateCarDto`(vehicle: Vehicle) {
        val updateCarDto = vehicle.toUpdateCarDto()

        assertThat(updateCarDto)
            .satisfies({
                assertThat(it.carPartnerId).isEqualTo(vehicle.vGuid)
                assertThat(it.vin).isEqualTo(vehicle.vin)
                assertThat(it.isPool).isEqualTo(vehicle.isPoolVehicle)
                assertThat(it.driver).isEqualTo(vehicle.responsiblePerson?.value)
                assertThat(it.fleetIdentifier).isEqualTo(vehicle.fleet.id)
                assertThat(it.licensePlate).isEqualTo(vehicle.licensePlate?.value)
                assertThat(it.insurance?.costCenter).isEqualTo(vehicle.depreciationRelevantCostCenter)
                assertThat(it.insurance?.insuranceBroker).isEqualTo("LeasingArt:${vehicle.leasingArt.id}")
                assertThat(it.insurance?.insurerAddress).isEqualTo("UsingCostCenter:${vehicle.usingCostCenter}")
            })
    }

    @Test
    fun `should not map NONE fleet when converting Vehicle to UpdateCarDto`() {
        val vehicle = VehicleBuilder().fleet(Fleet.NO_FLEET).build()

        val updateCarDto = vehicle.toUpdateCarDto()

        assertThat(updateCarDto.fleetIdentifier).isNull()
    }

    companion object {
        fun vehicleRequirements(vehicle: Vehicle) =
            Consumer<AddCarDto> { addCarDto: AddCarDto ->
                assertThat(addCarDto)
                    .usingRecursiveComparison()
                    .ignoringFields(
                        "vin",
                        "isPool",
                        "insurance",
                        "bodyType",
                        "firstRegistration",
                        "leasing",
                        "warrantyExpirationDate",
                        "nextInspectionDate",
                        "driver",
                        "carPartnerId",
                        "fleetIdentifier",
                        "lastInspectionDate",
                        "location",
                        "make",
                        "licensePlate",
                    )
                    .isEqualTo(vehicle)
                assertThat(addCarDto.vin).isEqualTo(vehicle.vin)
                assertThat(addCarDto.make).isEqualTo(vehicle.manufacturer)
                assertThat(addCarDto.carPartnerId).isEqualTo(vehicle.vGuid)
                assertThat(addCarDto.isPool).isEqualTo(vehicle.isPoolVehicle)
                assertThat(addCarDto.fleetIdentifier).isEqualTo(vehicle.fleet.id)
                if (vehicle.isPoolVehicle) {
                    assertThat(addCarDto.driver).isNull()
                } else {
                    assertThat(EmployeeNumber(addCarDto.driver!!)).isEqualTo(vehicle.responsiblePerson)
                }

                assertThat(addCarDto.licensePlate).isEqualTo(vehicle.licensePlate?.value)
                assertThat(addCarDto.insurance?.costCenter).isEqualTo(vehicle.depreciationRelevantCostCenter)
                assertThat(addCarDto.insurance?.insuranceBroker).isEqualTo("LeasingArt:${vehicle.leasingArt.id}")
                assertThat(addCarDto.insurance?.insurerAddress).isEqualTo("UsingCostCenter:${vehicle.usingCostCenter}")
                if (vehicle.firstRegistrationDate != null) {
                    val localDate = vehicle.firstRegistrationDate.toLocalDate()
                    assertThat(localDate)
                        .isEqualTo(
                            LocalDate.parse(addCarDto.firstRegistration, DateTimeFormatter.ofPattern("dd-MM-yyyy"))
                        )
                } else {
                    assertThat(addCarDto.firstRegistration).isNull()
                }
            }

        fun carRequirements(car: Car) =
            Consumer<Vehicle> { vehicle: Vehicle ->
                assertThat(vehicle)
                    .usingRecursiveComparison()
                    .ignoringFields(
                        "vin",
                        "vGuid",
                        "isPoolVehicle",
                        "fleet",
                        "fleetId",
                        "make",
                        "responsiblePerson",
                        "depreciationRelevantCostCenter",
                        "usingCostCenter",
                        "leasingArt",
                        "externalId",
                        "firstRegistrationDate",
                        "manufacturer",
                        "licensePlate",
                    )
                    .isEqualTo(car)
                assertThat(vehicle.vin).isEqualTo(car.vin)
                assertThat(vehicle.manufacturer).isEqualTo(car.make)
                assertThat(vehicle.vGuid).isEqualTo(car.carPartnerId)
                assertThat(vehicle.isPoolVehicle).isEqualTo(car.isPool)
                assertThat(vehicle.fleet.id).isEqualTo(car.fleetPartnerId)
                if (car.isPool) {
                    assertThat(vehicle.responsiblePerson).isNull()
                } else {
                    assertThat(vehicle.responsiblePerson).isEqualTo(EmployeeNumber(car.driver!!.partnerId!!))
                }
                assertThat(vehicle.depreciationRelevantCostCenter).isEqualTo(car.insurances?.firstOrNull()?.costCenter)
                val insuranceBroker = requireNotNull(car.insurances?.firstOrNull()?.insuranceBroker)
                assertThat(vehicle.leasingArt.id).isEqualTo(insuranceBroker.replace("LeasingArt:", ""))
                val insurerAddress = car.insurances?.firstOrNull()?.insurerAddress
                if (null == insurerAddress) {
                    assertThat(vehicle.usingCostCenter).isNull()
                } else {
                    assertThat(vehicle.usingCostCenter).isEqualTo(insurerAddress.replace("UsingCostCenter:", ""))
                }
                assertThat(vehicle.externalId).isEqualTo(car.id)
                assertThat(vehicle.firstRegistrationDate).isEqualTo(car.firstRegistration)
                assertThat(vehicle.licensePlate?.value).isEqualTo(car.licensePlate)
            }

        @JvmStatic
        fun vehicleProvider(): Stream<Arguments> = VehicleBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()

        @JvmStatic fun carProvider(): Stream<Arguments> = CarBuilder.buildMultiple(10).map { Arguments.of(it) }.stream()
    }
}
