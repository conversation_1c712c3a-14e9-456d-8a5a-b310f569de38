/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.emh.damagemanagement.vehiclemigration.application

import com.emh.damagemanagement.vehiclemigration.IntegrationTest
import com.emh.damagemanagement.vehiclemigration.VehicleBuilder
import com.emh.damagemanagement.vehiclemigration.VehicleMigrationDataBuilder
import com.emh.damagemanagement.vehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.vehiclemigration.application.fleet.FleetMigrationService
import com.emh.damagemanagement.vehiclemigration.application.port.AddFleetResult
import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.FleetOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.vehiclemigration.application.vehicle.VehicleDeactivationService
import com.emh.damagemanagement.vehiclemigration.application.vehicle.VehicleMigrationService
import com.emh.damagemanagement.vehiclemigration.application.vehicleresponsible.VehicleResponsiblePersonMigrationService
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.FleetInformation
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.reset
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean

@IntegrationTest
class MigrationApplicationServiceTest {
    @Autowired private lateinit var migrationApplicationService: MigrationApplicationService
    @Autowired private lateinit var fleetOutPort: FleetOutPort
    @Autowired private lateinit var vehicleOutPort: VehicleOutPort
    @Autowired private lateinit var vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort
    @MockitoSpyBean private lateinit var fleetMigrationService: FleetMigrationService
    @MockitoSpyBean private lateinit var vehicleDeactivationService: VehicleDeactivationService

    @MockitoSpyBean
    private lateinit var vehicleResponsiblePersonMigrationService: VehicleResponsiblePersonMigrationService
    @MockitoSpyBean private lateinit var vehicleMigrationService: VehicleMigrationService

    @AfterEach
    fun resetMocks() {
        reset(fleetOutPort)
        reset(vehicleResponsiblePersonOutPort)
        reset(vehicleOutPort)
    }

    @BeforeEach
    fun setup() {
        whenever(vehicleOutPort.deactivateVehicle(any())).thenReturn(UpdateVehicleResult(success = true))
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePerson(any()))
            .thenReturn(AddVehicleResponsiblePersonResult(success = true))
        whenever(vehicleResponsiblePersonOutPort.updateVehicleResponsiblePerson(any()))
            .thenReturn(UpdateVehicleResponsiblePersonResult(success = true))
        whenever(vehicleResponsiblePersonOutPort.addFleetManager(any()))
            .thenReturn(AddVehicleResponsiblePersonResult(success = true))
        whenever(vehicleResponsiblePersonOutPort.updateFleetManager(any()))
            .thenReturn(UpdateVehicleResponsiblePersonResult(success = true))
        whenever(vehicleOutPort.addVehicle(any())).thenReturn(AddVehicleResult(success = true))
        whenever(vehicleOutPort.updateVehicle(any())).thenReturn(UpdateVehicleResult(success = true))
    }

    @Test
    fun `should migrate managed fleets`() {
        `when`(fleetOutPort.allFleetsExist(any())).thenReturn(false)
        whenever(fleetOutPort.migrateLeasingFleets(any())).thenReturn(AddFleetResult(success = true))
        whenever(fleetOutPort.migratePoolFleets(any())).thenReturn(AddFleetResult(success = true))
        migrationApplicationService.migrateManagedFleets()
        verify(fleetOutPort, times(1)).migrateLeasingFleets(Fleet.leasingFleets)
        verify(fleetOutPort, times(1)).migratePoolFleets(setOf(Fleet.PRE_USAGE_FLEET, Fleet.POST_USAGE_FLEET))
    }

    @Test
    fun `should not migrate managed fleets if already exists`() {
        `when`(fleetOutPort.allFleetsExist(any())).thenReturn(true)
        migrationApplicationService.migrateManagedFleets()
        verify(fleetOutPort, times(0)).migrateLeasingFleets(any())
        verify(fleetOutPort, times(0)).migratePoolFleets(any())
    }

    @Test
    fun `should handle FleetMigrationException when fleet migration fails`() {
        `when`(fleetOutPort.allFleetsExist(any())).thenReturn(false)
        `when`(fleetOutPort.migrateLeasingFleets(any()))
            .thenThrow(FleetMigrationException(errorType = ErrorType.BUSINESS))
        assertThatNoException().isThrownBy { migrationApplicationService.migrateManagedFleets() }
    }

    @Test
    fun `should migrate fleet`() {
        val vehicleMigrationData =
            VehicleMigrationDataBuilder().fleetInformation(FleetInformation.USAGE).leasingArt(LeasingArt.L9_0).build()
        whenever(fleetOutPort.migratePoolFleet(any())).thenReturn(AddFleetResult(success = true))
        migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        val fleetArgumentCaptor = argumentCaptor<Fleet>()
        verify(fleetOutPort).migratePoolFleet(fleetArgumentCaptor.capture())

        val fleetArgument = fleetArgumentCaptor.firstValue
        assertThat(fleetArgument.id)
            .isEqualTo(
                "${vehicleMigrationData.vehicleResponsiblePerson!!.lastName}_${vehicleMigrationData.vehicleResponsiblePerson!!.firstName}_${vehicleMigrationData.vehicleResponsiblePerson!!.employeeNumber.value}"
            )
        assertThat(fleetArgument.description)
            .isEqualTo(
                "${vehicleMigrationData.vehicleResponsiblePerson!!.lastName}, ${vehicleMigrationData.vehicleResponsiblePerson!!.firstName} (${vehicleMigrationData.vehicleResponsiblePerson!!.employeeNumber.value})"
            )
    }

    @Test
    fun `should not migrate fleet if leasingArt is not Pool`() {
        val vehicleMigrationData =
            VehicleMigrationDataBuilder().fleetInformation(FleetInformation.USAGE).leasingArt(LeasingArt.L1_1).build()
        migrationApplicationService.migrateVehicleData(vehicleMigrationData)
        verify(fleetOutPort, times(0)).migratePoolFleet(any())
    }

    @Test
    fun `should not migrate fleet if vehicle responsible person is missing`() {
        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .fleetInformation(FleetInformation.USAGE)
                .vehicleResponsiblePerson(null)
                .build()
        migrationApplicationService.migrateVehicleData(vehicleMigrationData)
        verify(fleetOutPort, times(0)).migratePoolFleet(any())
    }

    @Test
    fun `should deactivate vehicle if fleet information is NONE`() {
        val vehicleMigrationData =
            VehicleMigrationDataBuilder().fleetInformation(FleetInformation.NONE).vehicleResponsiblePerson(null).build()
        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(vehicleDeactivationService).deactivateVehicle(vehicleToBeDeactivated = vehicleMigrationData.vehicle)
        verify(fleetMigrationService, times(0)).migratePoolFleet(any())
        verify(vehicleResponsiblePersonMigrationService, times(0)).migrateVehicleResponsiblePerson(any(), any())
        verify(vehicleMigrationService, times(0)).migrateVehicle(any())

        assertThat(migrationResult.success).isTrue()
        assertThat(migrationResult.errors).isEmpty()
        assertThat(migrationResult.errorType).isEmpty()
        assertThat(migrationResult.canRetry).isFalse()
    }

    @Test
    fun `should return success false when deactivating vehicle fails`() {
        val vehicleMigrationData =
            VehicleMigrationDataBuilder().fleetInformation(FleetInformation.NONE).vehicleResponsiblePerson(null).build()
        whenever(vehicleOutPort.findVehicle(any()))
            .thenReturn(VehicleBuilder().active(true).externalId(UUID.randomUUID()).build())
        whenever(vehicleOutPort.deactivateVehicle(any()))
            .thenReturn(
                UpdateVehicleResult(success = false, error = "something went wrong", errorType = ErrorType.TECHNICAL)
            )
        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(vehicleDeactivationService).deactivateVehicle(vehicleToBeDeactivated = vehicleMigrationData.vehicle)
        verify(fleetMigrationService, times(0)).migratePoolFleet(any())
        verify(vehicleResponsiblePersonMigrationService, times(0)).migrateVehicleResponsiblePerson(any(), any())
        verify(vehicleMigrationService, times(0)).migrateVehicle(any())

        assertThat(migrationResult.success).isFalse()
        assertThat(migrationResult.errors).contains("something went wrong")
        assertThat(migrationResult.errorType).contains(ErrorType.TECHNICAL)
        assertThat(migrationResult.canRetry).isTrue()
    }

    @Test
    fun `should successfully migrate new vehicle and new vehicle responsible person`() {
        val vehicleResponsiblePerson = VehicleResponsiblePersonBuilder.buildSingle()
        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .vehicleResponsiblePerson(vehicleResponsiblePerson)
                .fleetInformation(FleetInformation.USAGE)
                .build()
        val vehicleResponsiblePersonCaptor = argumentCaptor<VehicleResponsiblePerson>()
        val vehicleCaptor = argumentCaptor<Vehicle>()
        whenever(fleetOutPort.migratePoolFleet(any())).thenReturn(AddFleetResult(success = true))

        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(vehicleResponsiblePersonOutPort).addVehicleResponsiblePerson(vehicleResponsiblePersonCaptor.capture())
        verify(vehicleOutPort).addVehicle(vehicleCaptor.capture())
        val addedVehicleResponsiblePerson = vehicleResponsiblePersonCaptor.firstValue
        val addedVehicle = vehicleCaptor.firstValue
        assertThat(addedVehicleResponsiblePerson).isEqualTo(vehicleMigrationData.vehicleResponsiblePerson)
        assertThat(addedVehicle).isEqualTo(vehicleMigrationData.vehicle)
        assertThat(migrationResult.success).isTrue()
        assertThat(migrationResult.canRetry).isFalse()
    }

    @Test
    fun `should return false when migrating new vehicle and new vehicle responsible person fails`() {
        val vehicleResponsiblePerson = VehicleResponsiblePersonBuilder.buildSingle()
        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .vehicleResponsiblePerson(vehicleResponsiblePerson)
                .fleetInformation(FleetInformation.USAGE)
                .build()
        val vehicleResponsiblePersonCaptor = argumentCaptor<VehicleResponsiblePerson>()
        val vehicleCaptor = argumentCaptor<Vehicle>()
        whenever(fleetOutPort.migratePoolFleet(any())).thenReturn(AddFleetResult(success = true))
        whenever(vehicleOutPort.addVehicle(any()))
            .thenReturn(
                AddVehicleResult(success = false, error = "something went wrong", errorType = ErrorType.TECHNICAL)
            )
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePerson(any()))
            .thenReturn(
                AddVehicleResponsiblePersonResult(success = false, error = "some error", errorType = ErrorType.BUSINESS)
            )

        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(vehicleResponsiblePersonOutPort).addVehicleResponsiblePerson(vehicleResponsiblePersonCaptor.capture())
        verify(vehicleOutPort).addVehicle(vehicleCaptor.capture())
        val addedVehicleResponsiblePerson = vehicleResponsiblePersonCaptor.firstValue
        val addedVehicle = vehicleCaptor.firstValue
        assertThat(addedVehicleResponsiblePerson).isEqualTo(vehicleMigrationData.vehicleResponsiblePerson)
        assertThat(addedVehicle).isEqualTo(vehicleMigrationData.vehicle)
        assertThat(migrationResult.success).isFalse()
        assertThat(migrationResult.errors).contains("something went wrong", "some error")
        assertThat(migrationResult.errorType).contains(ErrorType.TECHNICAL, ErrorType.BUSINESS)
        assertThat(migrationResult.canRetry).isTrue()
    }

    @Test
    fun `should successfully migrate new fleet, new vehicle and new fleet manager`() {
        val fleetManager = VehicleResponsiblePersonBuilder().fleetManager().build()
        val leasingArt = LeasingArt.L9_3
        val fleet =
            Fleet.determineFleet(
                fleetInformation = FleetInformation.USAGE,
                firstName = fleetManager.firstName,
                lastName = fleetManager.lastName,
                employeeNumber = fleetManager.employeeNumber.value,
                leasingArt = leasingArt,
            )
        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .fleetInformation(FleetInformation.USAGE)
                .vehicleResponsiblePerson(VehicleResponsiblePersonBuilder().fleetManager().build())
                .vehicle(VehicleBuilder().fleet(fleet).leasingArt(leasingArt).build())
                .build()
        val fleetManagerCaptor = argumentCaptor<Pair<VehicleResponsiblePerson, Set<Fleet>>>()
        val vehicleCaptor = argumentCaptor<Vehicle>()
        val fleetCaptor = argumentCaptor<Fleet>()
        whenever(fleetOutPort.migratePoolFleet(any())).thenReturn(AddFleetResult(success = true))

        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(fleetOutPort).migratePoolFleet(fleetCaptor.capture())
        verify(vehicleResponsiblePersonOutPort).addFleetManager(fleetManagerCaptor.capture())
        verify(vehicleOutPort).addVehicle(vehicleCaptor.capture())
        val addedFleetManager = fleetManagerCaptor.firstValue
        val addedVehicle = vehicleCaptor.firstValue
        val addedFleet = fleetCaptor.firstValue
        assertThat(addedFleetManager.first).isEqualTo(vehicleMigrationData.vehicleResponsiblePerson)
        assertThat(addedFleetManager.second).containsExactlyInAnyOrder(fleet)
        assertThat(addedVehicle).isEqualTo(vehicleMigrationData.vehicle)
        assertThat(addedFleet).isEqualTo(fleet)
        assertThat(migrationResult.success).isTrue()
        assertThat(migrationResult.errors).isEmpty()
        assertThat(migrationResult.errorType).isEmpty()
    }

    @Test
    fun `should return false if migrating new fleet fails`() {
        val fleetManager = VehicleResponsiblePersonBuilder().fleetManager().build()
        val leasingArt = LeasingArt.L9_3
        val fleet =
            Fleet.determineFleet(
                fleetInformation = FleetInformation.USAGE,
                firstName = fleetManager.firstName,
                lastName = fleetManager.lastName,
                employeeNumber = fleetManager.employeeNumber.value,
                leasingArt = leasingArt,
            )
        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .fleetInformation(FleetInformation.USAGE)
                .vehicleResponsiblePerson(VehicleResponsiblePersonBuilder().fleetManager().build())
                .vehicle(VehicleBuilder().fleet(fleet).leasingArt(leasingArt).build())
                .build()
        whenever(fleetOutPort.migratePoolFleet(any()))
            .thenReturn(AddFleetResult(success = false, error = "some error", errorType = ErrorType.TECHNICAL))

        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(fleetOutPort).migratePoolFleet(any())
        verify(vehicleResponsiblePersonOutPort).addFleetManager(any())
        verify(vehicleOutPort).addVehicle(any())
        assertThat(migrationResult.success).isFalse()
        assertThat(migrationResult.errors).contains("some error")
        assertThat(migrationResult.errorType).contains(ErrorType.TECHNICAL)
        assertThat(migrationResult.canRetry).isTrue()
    }

    @Test
    fun `should successfully update vehicle and vehicle responsible person`() {
        val existingVehicleResponsiblePerson =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        val vehicleResponsiblePersonToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        val existingVehicle =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("UpdatedTest 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingVehicleResponsiblePerson, emptySet()))
        whenever(vehicleOutPort.findVehicle(any())).thenReturn(existingVehicle)
        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .fleetInformation(FleetInformation.USAGE)
                .vehicleResponsiblePerson(vehicleResponsiblePersonToBeMigrated)
                .vehicle(vehicleToBeMigrated)
                .build()

        val vehicleResponsiblePersonCaptor = argumentCaptor<VehicleResponsiblePerson>()
        val vehicleCaptor = argumentCaptor<Vehicle>()

        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(vehicleResponsiblePersonOutPort).updateVehicleResponsiblePerson(vehicleResponsiblePersonCaptor.capture())
        verify(vehicleOutPort).updateVehicle(vehicleCaptor.capture())
        val updatedVehicleResponsiblePerson = vehicleResponsiblePersonCaptor.firstValue
        val updatedVehicle = vehicleCaptor.firstValue
        assertThat(updatedVehicleResponsiblePerson).isEqualTo(vehicleResponsiblePersonToBeMigrated)
        assertThat(updatedVehicle).isEqualTo(vehicleToBeMigrated)
        assertThat(migrationResult.success).isTrue()
    }

    @Test
    fun `should successfully update vehicle and fleet manager`() {
        val existingFleetManager =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleetManagerToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val existingFleet = Fleet.PRE_USAGE_FLEET
        val fleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")
        val existingVehicle =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter2")
                .usingCostCenter("usingCostCenter")
                .fleet(fleet)
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingFleetManager, setOf(existingFleet.id)))
        whenever(vehicleOutPort.findVehicle(any())).thenReturn(existingVehicle)
        whenever(fleetOutPort.migratePoolFleet(any())).thenReturn(AddFleetResult(success = true))

        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .fleetInformation(FleetInformation.USAGE)
                .vehicleResponsiblePerson(fleetManagerToBeMigrated)
                .vehicle(vehicleToBeMigrated)
                .build()

        val fleetManagerCaptor = argumentCaptor<Pair<VehicleResponsiblePerson, Set<Fleet>>>()
        val vehicleCaptor = argumentCaptor<Vehicle>()

        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        verify(vehicleResponsiblePersonOutPort).updateFleetManager(fleetManagerCaptor.capture())
        verify(vehicleOutPort).updateVehicle(vehicleCaptor.capture())
        val updatedFleetManager = fleetManagerCaptor.firstValue
        val updatedVehicle = vehicleCaptor.firstValue
        assertThat(updatedFleetManager.first).isEqualTo(fleetManagerToBeMigrated)
        assertThat(updatedFleetManager.second.map { it.id }).containsExactlyInAnyOrder(fleet.id, existingFleet.id)
        assertThat(updatedVehicle).isEqualTo(vehicleToBeMigrated)
        assertThat(migrationResult.success).isTrue()
    }

    @Test
    fun `should return success false if vehicle responsible person is not provided`() {
        val vehicleMigrationData =
            VehicleMigrationDataBuilder()
                .vehicleResponsiblePerson(null)
                .fleetInformation(FleetInformation.USAGE)
                .build()

        val migrationResult = migrationApplicationService.migrateVehicleData(vehicleMigrationData)

        assertThat(migrationResult.success).isFalse()
        assertThat(migrationResult.errors).contains("Vehicle responsible person is missing.")
        assertThat(migrationResult.errorType).contains(ErrorType.BUSINESS)
        assertThat(migrationResult.canRetry).isFalse()
    }
}
