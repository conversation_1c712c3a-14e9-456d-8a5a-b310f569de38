/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.fleet

import com.emh.damagemanagement.vehiclemigration.IntegrationTest
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.AddFleetResult
import com.emh.damagemanagement.vehiclemigration.application.port.FleetOutPort
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import org.assertj.core.api.Assertions.assertThat
import org.assertj.core.api.Assertions.assertThatNoException
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.Mockito.`when`
import org.mockito.kotlin.any
import org.mockito.kotlin.argThat
import org.mockito.kotlin.eq
import org.mockito.kotlin.mock
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration

@IntegrationTest
@ContextConfiguration(classes = [FleetMigrationServiceTest.TestConfiguration::class])
class FleetMigrationServiceTest {
    @Autowired private lateinit var fleetMigrationService: FleetMigrationService
    @Autowired private lateinit var fleetOutPort: FleetOutPort

    @BeforeEach
    fun resetMocks() {
        Mockito.reset(fleetOutPort)
    }

    @Test
    fun `should call FleetOutPort when migrating managed fleets`() {
        assertThatNoException().isThrownBy { fleetMigrationService.migrateManagedFleets() }
        verify(fleetOutPort).migrateLeasingFleets(eq(Fleet.leasingFleets))
        verify(fleetOutPort).migratePoolFleets(eq(setOf(Fleet.PRE_USAGE_FLEET, Fleet.POST_USAGE_FLEET)))
    }

    @Test
    fun `should call FleetOutPort when migrating pool fleet`() {
        val poolFleet = Fleet(id = "L9.5", description = "some-description")
        whenever(fleetOutPort.migratePoolFleet(any())).thenReturn(AddFleetResult(success = true))
        val result = fleetMigrationService.migratePoolFleet(poolFleet)
        verify(fleetOutPort)
            .migratePoolFleet(
                argThat { argument -> argument.id == "L9.5" && argument.description == "some-description" }
            )
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success as false when migrating pool fleet fails`() {
        val poolFleet = Fleet(id = "L9.5", description = "some-description")
        whenever(fleetOutPort.migratePoolFleet(any()))
            .thenReturn(
                AddFleetResult(success = false, error = "something went wrong", errorType = ErrorType.TECHNICAL)
            )
        val result = fleetMigrationService.migratePoolFleet(poolFleet)
        verify(fleetOutPort)
            .migratePoolFleet(
                argThat { argument -> argument.id == "L9.5" && argument.description == "some-description" }
            )
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("something went wrong")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should call FleetOutPort when checking fleet migration status`() {
        `when`(fleetOutPort.allFleetsExist(fleetsToCompare = Fleet.managedFleets)).thenReturn(true)

        val fleetMigrationStatus = fleetMigrationService.allManagedFleetsHaveBeenMigrated()
        assertThat(fleetMigrationStatus).isTrue()
    }

    internal class TestConfiguration {

        @Bean @Primary fun fleetOutPort(): FleetOutPort = mock()
    }
}
