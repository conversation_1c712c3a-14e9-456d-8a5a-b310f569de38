/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.VehicleBuilder
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class LeasingVehicleMigrationServiceTest {
    private val vehicleOutPort: VehicleOutPort = mock()

    private val leasingVehicleMigrationService = LeasingVehicleMigrationService(vehicleOutPort)

    @BeforeEach
    fun setup() {
        whenever(vehicleOutPort.addVehicle(any())).thenReturn(AddVehicleResult(success = true))
        whenever(vehicleOutPort.updateVehicle(any())).thenReturn(UpdateVehicleResult(success = true))
    }

    @Test
    fun `should add leasing vehicle if it has not been added before`() {
        val vehicleToBeMigrated = VehicleBuilder.buildSingle()

        val result =
            leasingVehicleMigrationService.migrateLeasingVehicle(
                leasingVehicle = vehicleToBeMigrated,
                existingVehicle = null,
            )

        verify(vehicleOutPort).addVehicle(vehicleToBeMigrated)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false if adding leasing vehicle fails`() {
        val vehicleToBeMigrated = VehicleBuilder.buildSingle()
        whenever(vehicleOutPort.addVehicle(any()))
            .thenReturn(
                AddVehicleResult(success = false, error = "something went wrong", errorType = ErrorType.BUSINESS)
            )
        val result =
            leasingVehicleMigrationService.migrateLeasingVehicle(
                leasingVehicle = vehicleToBeMigrated,
                existingVehicle = null,
            )

        verify(vehicleOutPort).addVehicle(vehicleToBeMigrated)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("something went wrong")
        assertThat(result.errorType).isEqualTo(ErrorType.BUSINESS)
    }

    @Test
    fun `should update leasing vehicle if it has actual changes`() {
        val existingVehicleWithOldLicensePlate =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("UpdatedTest 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val result =
            leasingVehicleMigrationService.migrateLeasingVehicle(
                leasingVehicle = vehicleToBeMigrated,
                existingVehicle = existingVehicleWithOldLicensePlate,
            )

        verify(vehicleOutPort).updateVehicle(vehicleToBeMigrated)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false if updating leasing vehicle fails`() {
        val existingVehicleWithOldLicensePlate =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("UpdatedTest 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        whenever(vehicleOutPort.updateVehicle(any()))
            .thenReturn(
                UpdateVehicleResult(success = false, error = "something went wrong", errorType = ErrorType.BUSINESS)
            )
        val result =
            leasingVehicleMigrationService.migrateLeasingVehicle(
                leasingVehicle = vehicleToBeMigrated,
                existingVehicle = existingVehicleWithOldLicensePlate,
            )

        verify(vehicleOutPort).updateVehicle(vehicleToBeMigrated)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("something went wrong")
        assertThat(result.errorType).isEqualTo(ErrorType.BUSINESS)
    }

    @Test
    fun `should not update leasing vehicle if it has no actual changes`() {
        val existingVehicle =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        leasingVehicleMigrationService.migrateLeasingVehicle(
            leasingVehicle = vehicleToBeMigrated,
            existingVehicle = existingVehicle,
        )

        verify(vehicleOutPort, times(0)).updateVehicle(any())
    }

    @Test
    fun `should not update leasing vehicle if it has just case changes`() {
        val existingVehicleWithOnlyCaseChanges =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("pv-cc 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("PV-CC 0123")
                .leasingArt(LeasingArt.L1_1)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        leasingVehicleMigrationService.migrateLeasingVehicle(
            leasingVehicle = vehicleToBeMigrated,
            existingVehicle = existingVehicleWithOnlyCaseChanges,
        )

        verify(vehicleOutPort, times(0)).updateVehicle(any())
    }
}
