/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.VehicleBuilder
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class PoolVehicleMigrationServiceTest {
    private val vehicleOutPort: VehicleOutPort = mock()

    private val poolVehicleMigrationService = PoolVehicleMigrationService(vehicleOutPort)

    @BeforeEach
    fun setup() {
        whenever(vehicleOutPort.addVehicle(any())).thenReturn(AddVehicleResult(success = true))
        whenever(vehicleOutPort.updateVehicle(any())).thenReturn(UpdateVehicleResult(success = true))
    }

    @Test
    fun `should add pool vehicle if it has not been added before`() {
        val vehicleToBeMigrated = VehicleBuilder.buildSingle()
        val result =
            poolVehicleMigrationService.migratePoolVehicle(poolVehicle = vehicleToBeMigrated, existingVehicle = null)

        verify(vehicleOutPort).addVehicle(vehicleToBeMigrated)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false if adding pool vehicle fails`() {
        val vehicleToBeMigrated = VehicleBuilder.buildSingle()
        whenever(vehicleOutPort.addVehicle(any()))
            .thenReturn(AddVehicleResult(success = false, error = "some error", errorType = ErrorType.TECHNICAL))
        val result =
            poolVehicleMigrationService.migratePoolVehicle(poolVehicle = vehicleToBeMigrated, existingVehicle = null)

        verify(vehicleOutPort).addVehicle(vehicleToBeMigrated)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("some error")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should update pool vehicle if it has actual changes`() {
        val existingVehicleWithOldDepreciationRelevantCostCenter =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter2")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        val result =
            poolVehicleMigrationService.migratePoolVehicle(
                poolVehicle = vehicleToBeMigrated,
                existingVehicle = existingVehicleWithOldDepreciationRelevantCostCenter,
            )

        verify(vehicleOutPort).updateVehicle(vehicleToBeMigrated)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false if updating pool vehicle fails`() {
        val existingVehicleWithOldDepreciationRelevantCostCenter =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter2")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        whenever(vehicleOutPort.updateVehicle(any()))
            .thenReturn(UpdateVehicleResult(success = false, error = "some error", errorType = ErrorType.TECHNICAL))

        val result =
            poolVehicleMigrationService.migratePoolVehicle(
                poolVehicle = vehicleToBeMigrated,
                existingVehicle = existingVehicleWithOldDepreciationRelevantCostCenter,
            )

        verify(vehicleOutPort).updateVehicle(vehicleToBeMigrated)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("some error")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should not update leasing vehicle if it has no actual changes`() {
        val existingVehicle =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("Test 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        poolVehicleMigrationService.migratePoolVehicle(
            poolVehicle = vehicleToBeMigrated,
            existingVehicle = existingVehicle,
        )

        verify(vehicleOutPort, times(0)).updateVehicle(any())
    }

    @Test
    fun `should not update leasing vehicle if it has just case changes`() {
        val existingVehicleWithOnlyCaseChanges =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("pv-cc 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("CostCenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }
        val vehicleToBeMigrated =
            VehicleBuilder()
                .vin("WP0AC2A84P2240082")
                .licensePlate("pv-cc 0123")
                .leasingArt(LeasingArt.L9_3)
                .employeeNumber(EmployeeNumber("00355318"))
                .depreciationRelevantCostCenter("costcenter")
                .usingCostCenter("usingCostCenter")
                .build()
                .apply { this.updateExternalId(UUID.randomUUID()) }

        poolVehicleMigrationService.migratePoolVehicle(
            poolVehicle = vehicleToBeMigrated,
            existingVehicle = existingVehicleWithOnlyCaseChanges,
        )

        verify(vehicleOutPort, times(0)).updateVehicle(any())
    }
}
