/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.VehicleBuilder
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.verify
import org.mockito.kotlin.whenever

class VehicleDeactivationServiceTest {
    private val vehicleOutPort: VehicleOutPort = mock()

    private val vehicleDeactivationService = VehicleDeactivationService(vehicleOutPort)

    @Test
    fun `should deactivate vehicle`() {
        val existingVehicle = VehicleBuilder().active(true).externalId(UUID.randomUUID()).build()
        val vehicleToBeDeactivated = VehicleBuilder().active(false).build()
        whenever(vehicleOutPort.deactivateVehicle(any())).thenReturn(UpdateVehicleResult(success = true))
        whenever(vehicleOutPort.findVehicle(any())).thenReturn(existingVehicle)
        val vehicleDeactivationArgumentCaptor = argumentCaptor<Vehicle>()

        val result = vehicleDeactivationService.deactivateVehicle(vehicleToBeDeactivated = vehicleToBeDeactivated)

        verify(vehicleOutPort).deactivateVehicle(vehicleDeactivationArgumentCaptor.capture())
        assertThat(vehicleDeactivationArgumentCaptor.firstValue.externalId).isEqualTo(existingVehicle.externalId)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success as false as when deactivating vehicle fails`() {
        val vehicleToBeDeactivated = VehicleBuilder().active(false).build()
        whenever(vehicleOutPort.findVehicle(any()))
            .thenReturn(VehicleBuilder().active(true).externalId(UUID.randomUUID()).build())
        whenever(vehicleOutPort.deactivateVehicle(any()))
            .thenReturn(UpdateVehicleResult(success = false, error = "some error", errorType = ErrorType.TECHNICAL))
        val result = vehicleDeactivationService.deactivateVehicle(vehicleToBeDeactivated = vehicleToBeDeactivated)

        verify(vehicleOutPort).deactivateVehicle(vehicleToBeDeactivated)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("some error")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should skip deactivation if vehicle does not exist within repairfix`() {
        val vehicleToBeDeactivated = VehicleBuilder().active(false).build()
        whenever(vehicleOutPort.findVehicle(any())).thenReturn(null)

        val result = vehicleDeactivationService.deactivateVehicle(vehicleToBeDeactivated = vehicleToBeDeactivated)

        verify(vehicleOutPort, times(0)).deactivateVehicle(any())
        assertThat(result.success).isTrue()
    }
}
