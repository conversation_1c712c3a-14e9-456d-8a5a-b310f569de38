/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicle

import com.emh.damagemanagement.vehiclemigration.VehicleBuilder
import com.emh.damagemanagement.vehiclemigration.VehicleMigrationDataBuilder
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleMigrationResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleOutPort
import com.emh.damagemanagement.vehiclemigration.domain.FleetInformation
import com.emh.damagemanagement.vehiclemigration.domain.LeasingArt
import com.emh.damagemanagement.vehiclemigration.domain.Vehicle
import java.util.*
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.eq
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.anyOrNull
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.whenever

class VehicleMigrationServiceTest {

    private val leasingVehicleMigrationService: LeasingVehicleMigrationService = mock()
    private val poolVehicleMigrationService: PoolVehicleMigrationService = mock()
    private val vehicleOutPort: VehicleOutPort = mock()

    private val vehicleMigrationService =
        VehicleMigrationService(
            poolVehicleMigrationService = poolVehicleMigrationService,
            leasingVehicleMigrationService = leasingVehicleMigrationService,
            vehicleOutPort = vehicleOutPort,
        )

    @BeforeEach
    fun setup() {
        whenever(vehicleOutPort.findVehicle(any())).thenReturn(null)
        whenever(leasingVehicleMigrationService.migrateLeasingVehicle(any(), anyOrNull()))
            .thenReturn(VehicleMigrationResult(success = true))
        whenever(poolVehicleMigrationService.migratePoolVehicle(any(), anyOrNull()))
            .thenReturn(VehicleMigrationResult(success = true))
    }

    @Test
    fun `should migrate leasing vehicle if leasing art indicates leasing fleet`() {

        val vehicleMigrationData =
            VehicleMigrationDataBuilder().fleetInformation(FleetInformation.USAGE).leasingArt(LeasingArt.L1_1).build()

        val migrationResult = vehicleMigrationService.migrateVehicle(vehicleMigrationData = vehicleMigrationData)

        verify(leasingVehicleMigrationService)
            .migrateLeasingVehicle(leasingVehicle = vehicleMigrationData.vehicle, existingVehicle = null)

        assertThat(migrationResult.success).isTrue()
        assertThat(migrationResult.error).isNull()
        assertThat(migrationResult.errorType).isNull()
    }

    @Test
    fun `should migrate pool vehicle if leasing art indicates pool fleet`() {

        val vehicleMigrationData =
            VehicleMigrationDataBuilder().fleetInformation(FleetInformation.USAGE).leasingArt(LeasingArt.L9_3).build()

        val migrationResult = vehicleMigrationService.migrateVehicle(vehicleMigrationData = vehicleMigrationData)

        verify(poolVehicleMigrationService)
            .migratePoolVehicle(poolVehicle = vehicleMigrationData.vehicle, existingVehicle = null)

        assertThat(migrationResult.success).isTrue()
        assertThat(migrationResult.error).isNull()
        assertThat(migrationResult.errorType).isNull()
    }

    @Test
    fun `should correctly set external id of existing vehicle in case of a potential update`() {
        val externalId = UUID.randomUUID()
        val existingVehicle = VehicleBuilder.buildSingle().apply { updateExternalId(externalId) }
        whenever(vehicleOutPort.findVehicle(any())).thenReturn(existingVehicle)
        val vehicleMigrationData =
            VehicleMigrationDataBuilder().fleetInformation(FleetInformation.USAGE).leasingArt(LeasingArt.L9_3).build()
        val vehicleCaptor = argumentCaptor<Vehicle>()

        val migrationResult = vehicleMigrationService.migrateVehicle(vehicleMigrationData = vehicleMigrationData)

        verify(poolVehicleMigrationService)
            .migratePoolVehicle(poolVehicle = vehicleCaptor.capture(), existingVehicle = eq(existingVehicle))

        assertThat(vehicleCaptor.firstValue.externalId).isEqualTo(externalId)
        assertThat(migrationResult.success).isTrue()
        assertThat(migrationResult.error).isNull()
        assertThat(migrationResult.errorType).isNull()
    }
}
