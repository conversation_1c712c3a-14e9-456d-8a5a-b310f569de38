/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.application.vehicleresponsible

import com.emh.damagemanagement.vehiclemigration.VehicleResponsiblePersonBuilder
import com.emh.damagemanagement.vehiclemigration.application.ErrorType
import com.emh.damagemanagement.vehiclemigration.application.port.AddVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.UpdateVehicleResponsiblePersonResult
import com.emh.damagemanagement.vehiclemigration.application.port.VehicleResponsiblePersonOutPort
import com.emh.damagemanagement.vehiclemigration.domain.EmployeeNumber
import com.emh.damagemanagement.vehiclemigration.domain.Fleet
import com.emh.damagemanagement.vehiclemigration.domain.VehicleResponsiblePerson
import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.mockito.Mockito.mock
import org.mockito.Mockito.verify
import org.mockito.kotlin.any
import org.mockito.kotlin.argumentCaptor
import org.mockito.kotlin.times
import org.mockito.kotlin.whenever

class VehicleResponsiblePersonMigrationServiceTest {
    private val vehicleResponsiblePersonOutPort: VehicleResponsiblePersonOutPort = mock()

    private val vehicleResponsiblePersonMigrationService =
        VehicleResponsiblePersonMigrationService(vehicleResponsiblePersonOutPort)

    @BeforeEach
    fun setup() {
        whenever(vehicleResponsiblePersonOutPort.addFleetManager(any()))
            .thenReturn(AddVehicleResponsiblePersonResult(success = true))
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePerson(any()))
            .thenReturn(AddVehicleResponsiblePersonResult(success = true))
        whenever(vehicleResponsiblePersonOutPort.updateVehicleResponsiblePerson(any()))
            .thenReturn(UpdateVehicleResponsiblePersonResult(success = true))
        whenever(vehicleResponsiblePersonOutPort.updateFleetManager(any()))
            .thenReturn(UpdateVehicleResponsiblePersonResult(success = true))
    }

    @Test
    fun `should add vehicle responsible person if it has not been added before`() {
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any())).thenReturn(null)
        val vehicleResponsiblePerson = VehicleResponsiblePersonBuilder.buildSingle()
        val fleet = Fleet.leasingFleets.random()
        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                vehicleResponsiblePerson,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort).addVehicleResponsiblePerson(vehicleResponsiblePerson)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false if adding vehicle responsible person fails`() {
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any())).thenReturn(null)
        whenever(vehicleResponsiblePersonOutPort.addVehicleResponsiblePerson(any()))
            .thenReturn(
                AddVehicleResponsiblePersonResult(
                    success = false,
                    error = "some error",
                    errorType = ErrorType.TECHNICAL,
                )
            )
        val vehicleResponsiblePerson = VehicleResponsiblePersonBuilder.buildSingle()
        val fleet = Fleet.leasingFleets.random()

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                vehicleResponsiblePerson,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort).addVehicleResponsiblePerson(vehicleResponsiblePerson)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("some error")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should update vehicle responsible person if it has actual changes`() {
        val existingVehicleResponsiblePerson =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        val vehicleResponsiblePersonToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingVehicleResponsiblePerson, emptySet()))
        val fleet = Fleet.leasingFleets.random()

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                vehicleResponsiblePersonToBeMigrated,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort).updateVehicleResponsiblePerson(vehicleResponsiblePersonToBeMigrated)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false when updating vehicle responsible person fails`() {
        val existingVehicleResponsiblePerson =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        val vehicleResponsiblePersonToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingVehicleResponsiblePerson, emptySet()))
        whenever(vehicleResponsiblePersonOutPort.updateVehicleResponsiblePerson(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonResult(
                    success = false,
                    error = "some error",
                    errorType = ErrorType.TECHNICAL,
                )
            )
        val fleet = Fleet.leasingFleets.random()

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                vehicleResponsiblePersonToBeMigrated,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort).updateVehicleResponsiblePerson(vehicleResponsiblePersonToBeMigrated)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("some error")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should update vehicle responsible person using fleet manager update if existing person is fleet manager`() {
        val existingVehicleResponsiblePerson =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val vehicleResponsiblePersonToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingVehicleResponsiblePerson, setOf("responsible_harald_00123456")))
        val fleet = Fleet.leasingFleets.random()
        val fleetManagerUpdateArgumentCaptor = argumentCaptor<Pair<VehicleResponsiblePerson, Set<Fleet>>>()

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                vehicleResponsiblePersonToBeMigrated,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort, times(0)).updateVehicleResponsiblePerson(any())
        verify(vehicleResponsiblePersonOutPort).updateFleetManager(fleetManagerUpdateArgumentCaptor.capture())
        val fleetManagerUpdateArgument = fleetManagerUpdateArgumentCaptor.firstValue
        assertThat(fleetManagerUpdateArgument.first).isEqualTo(vehicleResponsiblePersonToBeMigrated)
        assertThat(fleetManagerUpdateArgument.second.single().id).isEqualTo("responsible_harald_00123456")
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should not update vehicle responsible person if it has only case changes`() {
        val existingVehicleResponsiblePerson =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        val vehicleResponsiblePersonToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .build()
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingVehicleResponsiblePerson, emptySet()))
        val fleet = Fleet.leasingFleets.random()

        vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
            vehicleResponsiblePersonToBeMigrated,
            fleet = fleet,
        )

        verify(vehicleResponsiblePersonOutPort, times(0)).updateVehicleResponsiblePerson(any())
    }

    @Test
    fun `should add fleet manager if it has not been added before`() {
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any())).thenReturn(null)
        val fleetManager = VehicleResponsiblePersonBuilder().fleetManager().build()
        val fleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(fleetManager, fleet = fleet)

        verify(vehicleResponsiblePersonOutPort).addFleetManager(Pair(fleetManager, setOf(fleet)))
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false when adding fleet manager fails`() {
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any())).thenReturn(null)
        whenever(vehicleResponsiblePersonOutPort.addFleetManager(any()))
            .thenReturn(
                AddVehicleResponsiblePersonResult(
                    success = false,
                    error = "some error",
                    errorType = ErrorType.TECHNICAL,
                )
            )
        val fleetManager = VehicleResponsiblePersonBuilder().fleetManager().build()
        val fleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(fleetManager, fleet = fleet)

        verify(vehicleResponsiblePersonOutPort).addFleetManager(Pair(fleetManager, setOf(fleet)))
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("some error")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should update fleet manager if it has actual changes`() {
        val existingFleetManager =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleetManagerToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingFleetManager, setOf(fleet.id)))
        val fleetManagerUpdateArgumentCaptor = argumentCaptor<Pair<VehicleResponsiblePerson, Set<Fleet>>>()

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                fleetManagerToBeMigrated,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort).updateFleetManager(fleetManagerUpdateArgumentCaptor.capture())
        val fleetManagerUpdateArgument = fleetManagerUpdateArgumentCaptor.firstValue
        assertThat(fleetManagerUpdateArgument.first).isEqualTo(fleetManagerToBeMigrated)
        assertThat(fleetManagerUpdateArgument.second.single().id).isEqualTo(fleet.id)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should return success false when updating fleet manager fails`() {
        val existingFleetManager =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleetManagerToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingFleetManager, setOf(fleet.id)))
        val fleetManagerUpdateArgumentCaptor = argumentCaptor<Pair<VehicleResponsiblePerson, Set<Fleet>>>()
        whenever(vehicleResponsiblePersonOutPort.updateFleetManager(any()))
            .thenReturn(
                UpdateVehicleResponsiblePersonResult(
                    success = false,
                    error = "some error",
                    errorType = ErrorType.TECHNICAL,
                )
            )

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                fleetManagerToBeMigrated,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort).updateFleetManager(fleetManagerUpdateArgumentCaptor.capture())
        val fleetManagerUpdateArgument = fleetManagerUpdateArgumentCaptor.firstValue
        assertThat(fleetManagerUpdateArgument.first).isEqualTo(fleetManagerToBeMigrated)
        assertThat(fleetManagerUpdateArgument.second.single().id).isEqualTo(fleet.id)
        assertThat(result.success).isFalse()
        assertThat(result.error).isEqualTo("some error")
        assertThat(result.errorType).isEqualTo(ErrorType.TECHNICAL)
    }

    @Test
    fun `should update global fleet manager and keep global fleet manager role`() {
        val existingFleetManager =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .globalFleetManager()
                .build()
        val fleetManagerToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingFleetManager, setOf(fleet.id)))
        val fleetManagerUpdateArgumentCaptor = argumentCaptor<Pair<VehicleResponsiblePerson, Set<Fleet>>>()

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                fleetManagerToBeMigrated,
                fleet = fleet,
            )

        verify(vehicleResponsiblePersonOutPort).updateFleetManager(fleetManagerUpdateArgumentCaptor.capture())
        val fleetManagerUpdateArgument = fleetManagerUpdateArgumentCaptor.firstValue
        assertThat(fleetManagerUpdateArgument.first)
            .usingRecursiveAssertion()
            .ignoringFields("isGlobalFleetManager")
            .isEqualTo(fleetManagerToBeMigrated)
        assertThat(fleetManagerUpdateArgument.first.isGlobalFleetManager).isTrue()
        assertThat(fleetManagerUpdateArgument.second.single().id).isEqualTo(fleet.id)
        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should update fleet manager if it has fleet changes`() {
        val existingFleetManager =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleetManagerToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("NotSoResponsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val existingFleet = Fleet.PRE_USAGE_FLEET
        val newFleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingFleetManager, setOf(existingFleet.id)))
        val fleetManagerUpdateArgumentCaptor = argumentCaptor<Pair<VehicleResponsiblePerson, Set<Fleet>>>()

        val result =
            vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
                fleetManagerToBeMigrated,
                fleet = newFleet,
            )

        verify(vehicleResponsiblePersonOutPort).updateFleetManager(fleetManagerUpdateArgumentCaptor.capture())
        val fleetManagerUpdateArgument = fleetManagerUpdateArgumentCaptor.firstValue
        assertThat(fleetManagerUpdateArgument.first).isEqualTo(fleetManagerToBeMigrated)
        assertThat(fleetManagerUpdateArgument.second.map { it.id })
            .containsExactlyInAnyOrder(newFleet.id, existingFleet.id)

        assertThat(result.success).isTrue()
        assertThat(result.error).isNull()
        assertThat(result.errorType).isNull()
    }

    @Test
    fun `should not update fleet manager if it has only case changes`() {
        val existingFleetManager =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("Responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleetManagerToBeMigrated =
            VehicleResponsiblePersonBuilder()
                .firstName("Harald")
                .lastName("responsible")
                .employeeNumber(EmployeeNumber("00123456"))
                .email("<EMAIL>")
                .fleetManager()
                .build()
        val fleet = Fleet("responsible_harald_00123456", "Responsible Harald 00123456")
        whenever(vehicleResponsiblePersonOutPort.findVehicleResponsiblePerson(any()))
            .thenReturn(Pair(existingFleetManager, setOf(fleet.id)))

        vehicleResponsiblePersonMigrationService.migrateVehicleResponsiblePerson(
            fleetManagerToBeMigrated,
            fleet = fleet,
        )

        verify(vehicleResponsiblePersonOutPort, times(0)).updateFleetManager(any())
    }
}
