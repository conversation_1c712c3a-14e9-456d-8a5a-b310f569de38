/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.domain

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class LeasingArtTest {

    @Test
    fun `should return pool leasing arts`() {
        val poolLeasingArts = LeasingArt.poolLeasingArts

        assertThat(poolLeasingArts)
            .containsExactlyInAnyOrder(
                LeasingArt.L9_0,
                LeasingArt.L9_2,
                LeasingArt.L9_3,
                LeasingArt.L9_5,
                LeasingArt.L9_6,
            )
    }

    @Test
    fun `should return leasing arts`() {
        val leasingArts = LeasingArt.leasingArts

        assertThat(leasingArts)
            .containsExactlyInAnyOrder(
                LeasingArt.L1_1,
                LeasingArt.L1_2,
                LeasingArt.L1_3,
                LeasingArt.L1_4,
                LeasingArt.L2,
                LeasingArt.L9_1,
            )
    }

    @Test
    fun `should return true for pool leasing art`() {
        assertThat(LeasingArt.L9_2.isPool).isTrue()
    }

    @Test
    fun `should return false for non-pool leasing art`() {
        assertThat(LeasingArt.L9_1.isPool).isFalse()
    }
}
