/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.vehiclemigration.domain

import org.assertj.core.api.Assertions.assertThat
import org.junit.jupiter.api.Test

class LicensePlateTest {

    @Test
    fun `should remove 'E' suffix on license plates`() {

        val electricVehicleLicensePlate = "AB-CD-1234 E"
        val licensePlate = LicensePlate(electricVehicleLicensePlate)

        assertThat(licensePlate.value).isEqualTo("AB-CD-1234")
    }

    @Test
    fun `should remove 'H' suffix on license plates`() {

        val electricVehicleLicensePlate = "AB-CD 1234 H"
        val licensePlate = LicensePlate(electricVehicleLicensePlate)

        assertThat(licensePlate.value).isEqualTo("AB-CD 1234")
    }
}
