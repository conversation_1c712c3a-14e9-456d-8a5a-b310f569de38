:toc:
:numbered:
= Wiremock

== Module description

This module will start a wiremock server using `resources/wiremock-files` mappings and responses to provide mocked responses for some 3rd party APIs.
We are using this ONLY in develop configuration (either locally or on target infrastructure) to compensate for the lack of Test-SaaS-Environments (like Repairfix).

WireMock server can be enabled/disabled using `wiremock.enabled` property or `WIREMOCK_ENABLED` environment.

Target port can be changed using `wiremock.port` property or `WIREMOCK_PORT` environment.
