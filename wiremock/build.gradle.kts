import com.diffplug.spotless.LineEnding

plugins {
    alias(libs.plugins.ben.manes.versions)
    alias(libs.plugins.kotlin.jvm)
    alias(libs.plugins.kotlin.spring)
    alias(libs.plugins.openapi.generator)
    alias(libs.plugins.spotless)
    alias(libs.plugins.spring.dependency.management)
    alias(libs.plugins.spring.boot)
    idea
    jacoco
}

evaluationDependsOn(":shared")
val sharedTestSrcSetsOutput = project(":shared").sourceSets.test.get().output

dependencies {
    implementation(project(":shared"))

    implementation(libs.bundles.base)
    implementation(libs.wiremock)

    testImplementation(files(sharedTestSrcSetsOutput))
}

spotless {
    kotlin {
        ktfmt("0.55").kotlinlangStyle().configure {
            it.setMaxWidth(120)
            it.setRemoveUnusedImports(true)
        }
    }
    isEnforceCheck = false
    lineEndings = LineEnding.UNIX
}

tasks.named<org.springframework.boot.gradle.tasks.bundling.BootJar>("bootJar") { enabled = false }
