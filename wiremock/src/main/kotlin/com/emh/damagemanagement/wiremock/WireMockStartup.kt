/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.wiremock

import com.github.tomakehurst.wiremock.WireMockServer
import com.github.tomakehurst.wiremock.core.WireMockConfiguration
import org.apache.juli.logging.LogFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(name = ["wiremock.enabled"], havingValue = "true")
class WireMockStartup(private val wireMockConfiguration: WireMockConfiguration) {

    @EventListener(ApplicationReadyEvent::class)
    fun startWiremockServer() {
        log.info("Starting wiremock server.")
        WireMockServer(wireMockConfiguration).start()
    }

    companion object {
        private val log = LogFactory.getLog(WireMockStartup::class.java)
    }
}
