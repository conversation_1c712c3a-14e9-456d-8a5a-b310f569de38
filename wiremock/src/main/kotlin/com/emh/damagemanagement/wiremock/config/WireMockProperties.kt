/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.wiremock.config

import org.springframework.boot.context.properties.ConfigurationProperties

@Suppress("ConfigurationProperties")
@ConfigurationProperties("wiremock")
class WireMockProperties(@Suppress("unused") val enabled: Boolean, val port: Int)
