/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.emh.damagemanagement.wiremock.config

import com.github.tomakehurst.wiremock.common.ClasspathFileSource
import com.github.tomakehurst.wiremock.core.WireMockConfiguration
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Suppress("SpringJavaInjectionPointsAutowiringInspection")
@Configuration
class WireMockServerConfig(private val wiremockProperties: WireMockProperties) {

    @Bean
    @ConditionalOnProperty(name = ["wiremock.enabled"], havingValue = "true")
    fun config(): WireMockConfiguration {
        val config = WireMockConfiguration().port(wiremockProperties.port).disableRequestJournal()
        val bootJarFiles = ClasspathFileSource("BOOT-INF/classes/$RESOURCES_WIREMOCK_FILES")
        if (bootJarFiles.exists()) {
            // workaround, as wiremock is not able to load jar-nested resources from classpath
            config.fileSource(bootJarFiles)
        } else {
            config.usingFilesUnderClasspath(RESOURCES_WIREMOCK_FILES)
        }
        return config
    }

    companion object {
        // do not rename folder or classloader get even more confused
        private const val RESOURCES_WIREMOCK_FILES = "wiremock-files"
    }
}
